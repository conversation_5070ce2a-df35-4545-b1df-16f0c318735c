const _ = require('lodash');
let chai = require('chai');
const sinon = require('sinon');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MVNO_SERVICES } = require('../../__shared__/constants/mvnoServicesConstants');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const ServicesRestrictSettingModel = require('../../__shared__/models/adminService/servicesRestrictSetting.model');
const { CONTRACT_TYPES } = require('../../__shared__/constants/simConstants');
let expect = chai.expect;

describe('GetPlanListAPI', function () {
    const __request = {
        headers: getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000'),
        body: {
            tenant_id: 'TSA000',
            service: MVNO_SERVICES.ALL,
            is_shinki: false,
            is_shinki_ota: false,
            is_esim: false,
        },
    };

    describe('GetPlanListAPI with normal request', function () {
        const sandbox = sinon.createSandbox();

        afterEach(() => {
            sandbox.restore();
        });

        it('Get plans using super user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader();
            request.body.tenant_id = 'CON000';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
        });

        it('Get MNP plans using tempo user', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);

            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
        });

        it('Get Shinki plans using tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.body.is_shinki = true;

            await httpFunction(context, request);

            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
        });

        it('Get Shinki OTA plans using tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.body.is_shinki_ota = true;

            await httpFunction(context, request);

            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
        });

        it('Get ESIM plans using tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.body.is_esim = true;

            await httpFunction(context, request);

            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
        });

        it('Get non-5G plans for 5G disabled tenant (mocked)', async () => {
            const request = _.cloneDeep(__request);
            request.body.is_shinki = true;

            await httpFunction(context, request);

            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);

            // assert has any 5G(NSA) plan
            expect(context.res.body.result.some((plan) => plan.network == CONTRACT_TYPES.FIVE_G_NSA)).equal(true);

            sandbox.stub(ServicesRestrictSettingModel, 'is5GEnabled').resolves(false);

            request.body.is_shinki = true;

            await httpFunction(context, request);

            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
            // check has no 5G(NSA) plan
            expect(context.res.body.result.some((plan) => plan.network == CONTRACT_TYPES.FIVE_G_NSA)).equal(false);
        });

        it('Get different plans for different service types', async () => {
            const request = _.cloneDeep(__request);
            request.body.is_shinki = true;
            request.body.service = MVNO_SERVICES.FULL;

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);

            /** @type Array<string> */
            const fullPlans = context.res.body.result.map((plan) => plan.plan_id);

            // get lite plans
            request.body.service = MVNO_SERVICES.LITE;
            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);

            /** @type Array<string> */
            const litePlans = context.res.body.result.map((plan) => plan.plan_id);
            // check no full plans inside lite plans
            const intersection = fullPlans.filter((p) => litePlans.includes(p));
            expect(intersection.length).equal(0);
        });
    });

    describe('GetPlanListAPI with bad request', function () {
        it('returns error if tenant_id is empty (super user)', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader();
            delete request.body.tenant_id;

            await httpFunction(context, request);

            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
            expect(context.res.body.nttc_mvno_error_code).equal('993008');
        });
    });
});
