const { CommonError } = require('../__shared__/constants/CommonError');
const { mvnoServiceFromCode } = require('../__shared__/constants/mvnoServicesConstants');
const { CONTRACT_TYPES } = require('../__shared__/constants/simConstants');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const ServicesRestrictSettingModel = require('../__shared__/models/adminService/servicesRestrictSetting.model');
const TenantPlanLineOptionSettingModel = require('../__shared__/models/adminService/tenantPlanLineOptionSetting.model');
const { retrieveMAWPPlanList } = require('../__shared__/pgModels/plans');
const { parseGetPlanListRequestBody } = require('./helpers/parseBody');
const mongoose = require('../__shared__/database/mongoose');

module.exports = async function (context, req) {
    try {
        context.log('GetPlanListAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        const params = parseGetPlanListRequestBody(req.body ?? {});

        let tenant = null;
        switch (roleId) {
            case MAWP_ROLE_ID.TEMPO_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                tenant = tenantId;
                break;
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                tenant = params.tenant_id;
                break;
        }

        if (isNone(tenant)) {
            context.log('GetPlanListAPI invalid parameter 993008 ');
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', '993008', null);
            return;
        }

        await mongoose.init(context);
        const planSeq = await retrieveMAWPPlanList(context, tenant, mvnoServiceFromCode(params.service));

        let selectablePlan = [];
        if (params.is_shinki) {
            selectablePlan = await TenantPlanLineOptionSettingModel.getSelectPlanByShinki(tenant);
        } else if (params.is_shinki_ota) {
            selectablePlan = await TenantPlanLineOptionSettingModel.getSelectPlanByShinkiOTA(tenant);
        } else if (params.is_esim) {
            selectablePlan = await TenantPlanLineOptionSettingModel.getSelectPlanByESIM(tenant);
        } else {
            selectablePlan = await TenantPlanLineOptionSettingModel.getSelectPlanByMnp(tenant);
        }

        let filteredPlan = planSeq.filter((plan) => selectablePlan.some((s) => plan.planID == s));

        // STEP18: 5G利用不可テナントの際は5G専用プランは返さないようにする
        if (!(await ServicesRestrictSettingModel.is5GEnabled(tenant))) {
            filteredPlan = filteredPlan.filter((plan) => plan.network !== CONTRACT_TYPES.FIVE_G_NSA);
        }

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                result: filteredPlan.map((plan) => ({
                    plan_id: plan.planID,
                    resale_plan_id: plan.resalePlanId,
                    plan_name: plan.planName,
                    network: plan.network,
                    sms_enable: plan.smsEnable,
                    voice_flag: plan.voiceFlag,
                    default_sim_flag: plan.defaultSimFlag,
                    sim_type: plan.simType,
                })),
            },
        };
    } catch (exception) {
        context.log.error('GetPlanListAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
