const { getParamBooleanValue, getParamIntValue } = require('../../__shared__/utils/stringUtils');

const parseGetPlanListRequestBody = (body) => {
    return {
        tenant_id: body.tenant_id,
        is_shinki: getParamBooleanValue(body.is_shinki, false),
        is_shinki_ota: getParamBooleanValue(body.is_shinki_ota, false),
        service: getParamIntValue(body.service, 0),
        is_esim: getParamBooleanValue(body.is_esim, false),
    };
};

module.exports = {
    parseGetPlanListRequestBody,
};
