name: Create a pull request for deploy.

on:
    push:
        branches: [develop]

jobs:
    create-pr-pnl:
        strategy:
            matrix:
                branch:
                    [
                        'deploy-t1',
                    ]
        runs-on: ubuntu-latest
        env:
            GH_TOKEN: ${{ secrets.PAT }}
        steps:
            - name: 'Checkout GitHub Action'
              uses: actions/checkout@v2
              with:
                  token: ${{ secrets.PAT }}
                  submodules: recursive
            - name: Check if PR exists
              id: checkPNL
              env:
                  GITHUB_TOKEN: ${{ secrets.PAT }}
              run: |
                  prs=$(gh pr list \
                      --repo "$GITHUB_REPOSITORY" \
                      --json baseRefName,headRefName \
                      --jq '
                          map(select(.baseRefName ==  "${{ matrix.branch }}" and .headRefName == "develop"))
                          | length
                      ')
                  if ((prs > 0)); then
                      echo "skip=true" >> "$GITHUB_OUTPUT"
                  fi
            - name: Create pull request for deploy
              if: '!steps.checkPNL.outputs.skip'
              run: |
                  gh pr create -B  "${{ matrix.branch }}" -t "[${{ matrix.branch }}] Build and deploy to Azure" -b "automated pull request for deploy to Azure" -a "@me"
    dispatch-update-submodule: 
        runs-on: ubuntu-latest
        steps:
            - name: Repository Dispatch
              uses: peter-evans/repository-dispatch@v2
              with:
                  token: ${{ secrets.PAT }}
                  repository: playnext-lab/MVNO_MICROSERVICES
                  event-type: update-submodule-version
                  client-payload: '{"ref": "${{ github.ref }}", "sha": "${{ github.sha }}", "message": "${{ github.repository }}"}'