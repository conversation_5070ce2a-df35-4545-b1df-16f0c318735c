const _ = require('lodash');
const { LINE_OPTION_TYPE, LINE_OPTION_TYPE_NAME } = require('../__shared__/constants/daihyouBangoConstants');
const { isNone } = require('../__shared__/helpers/baseHelper');
const DaihyouBangoHelper = require('../__shared__/helpers/daihyouBangoHelper');
const TenantPlanLineOptionSettingModel = require('../__shared__/models/adminService/tenantPlanLineOptionSetting.model');
const pgLineOptions = require('../__shared__/pgModels/lineOptions');
const pgPlans = require('../__shared__/pgModels/plans');

/** @typedef {import('../__shared__/pgModels/lineOptions').lineOptionInfo} lineOptionInfo */

/**
 *
 * @param {object} context
 * @param {{
 *   tenantId: string,
 *   planId: number,
 *   nno: string,
 *   isShinki: boolean,
 *   iseSIM: boolean
 * }} params
 */
const getDefaultLineOptions = async (context, params) => {
    context.log('getDefaultLineOptions start');
    if (!(await checkPlanExists(params.tenantId, params.planId, params.isShinki, params.iseSIM))) {
        return { ok: false };
    }

    const [{ modifiedLineOptions, allLineOptions }, planName] = await Promise.all([
        DaihyouBangoHelper.getModifiedLineOptions(context, {
            tenantId: params.tenantId,
            planId: params.planId,
            nNo: params.nno,
        }),
        pgPlans.getPlanNameById(context, params.planId),
    ]);

    const query = {
        isShinki: params.iseSIM ? false : params.isShinki,
        isMnp: params.iseSIM ? false : !params.isShinki,
        iseSIM: params.iseSIM ? true : false,
    };
    /** @type {Array<string>} */
    const currentLineOptions = Object.values(
        await TenantPlanLineOptionSettingModel.getDefaultLineOptionByTypeId(
            context,
            params.tenantId,
            params.planId,
            query.isShinki,
            query.isMnp,
            query.iseSIM
        )
    );

    const lineTypeGroup = _.groupBy(modifiedLineOptions, _.property('line_option_type'));
    /** @type {Object<string, Array<[string, string]>>} base data */
    const kaisenOptionsBase = _.transform(
        lineTypeGroup,
        (result, options, type) => {
            if (options.length < 2) {
                result[type] = options.map((o) => [o.line_option_id, 'あり']).concat([['', 'なし']]);
            } else {
                result[type] = options.map((o) => [o.line_option_id, o.option_plan_name]).concat([['', 'なし']]);
            }
        },
        {}
    );

    const currentKaisenOptions = getCurrentKaisenOptions(kaisenOptionsBase, currentLineOptions, allLineOptions);

    const sortedSelectable = Object.entries(kaisenOptionsBase).sort((a, b) => parseInt(a[0]) - parseInt(b[0]));
    const selectableKaisenOptions = sortedSelectable.map((row) => {
        /** @type {[string, [string,string][]]} */
        const [optionTypeID, optionTypeValuesSeq] = row;
        /** @type {string} */
        const line_option_title = LINE_OPTION_TYPE_NAME[optionTypeID] || '';
        return {
            line_option_type: parseInt(optionTypeID),
            line_option_title,
            line_option_values: optionTypeValuesSeq.map((optionTypeVal) => ({
                line_option_id: optionTypeVal[0],
                line_option_name: optionTypeVal[1],
            })),
        };
    });

    return {
        ok: true,
        result: {
            default_kaisen_options: currentKaisenOptions,
            selectable_kaisen_options: selectableKaisenOptions,
            plan_id: +params.planId,
            plan_name: planName ?? '',
        },
    };
};

/**
 *
 * @param {Object<string, Array<string,string>>} kaisenOptionsBase
 * @param {Array<string>} currentLineOptions
 * @param {Array<lineOptionInfo>} allLineOptions
 */
const getCurrentKaisenOptions = (kaisenOptionsBase, currentLineOptions, allLineOptions) => {
    /** @type {Object<string, [string, string]>} filter kaisenOptionsBase by `currentLineOptions` */
    const selectedKaisenOptions = _.transform(
        kaisenOptionsBase,
        (result, kaisenOptions, optionType) => {
            /** @type {[string, string]|undefined} */
            let found;
            if (optionType == LINE_OPTION_TYPE.RUSUBAN || optionType == LINE_OPTION_TYPE.CATCH_HON) {
                const filteredLineOptionsExists = allLineOptions.some(
                    (r) => r.line_option_type == optionType && currentLineOptions.includes(r.line_option_id)
                );
                if (filteredLineOptionsExists) found = kaisenOptions.find((r) => !isNone(r[0]));
            } else {
                found = kaisenOptions.find((r) => currentLineOptions.includes(r[0]));
            }
            result[optionType] = isNone(found) ? ['', 'なし'] : found;
        },
        {}
    );
    const sortedSelectedKaisenOptions = sortMap(selectedKaisenOptions);
    return sortedSelectedKaisenOptions.map((row) => {
        /** @type {[string, string[]]} */
        const [lineOptionType, lineOptionIDAndNameTuple] = row;
        /** @type {string} */
        const line_option_title = LINE_OPTION_TYPE_NAME[lineOptionType] || '';
        return {
            line_option_type: parseInt(lineOptionType),
            line_option_title,
            line_option_id: lineOptionIDAndNameTuple[0],
            line_option_name: lineOptionIDAndNameTuple[1],
        };
    });
};

const checkPlanExists = async (tenantId, planId, isShinki, iseSIM) => {
    let exists = false;
    if (isShinki) {
        exists = await TenantPlanLineOptionSettingModel.checkPlanExists(tenantId, planId, { isShinki: true });
    } else if (iseSIM) {
        exists = await TenantPlanLineOptionSettingModel.checkPlanExists(tenantId, planId, { iseSIM: true });
    } else {
        exists = await TenantPlanLineOptionSettingModel.checkPlanExists(tenantId, planId, { isMnp: true });
    }
    return exists;
};

const sortMap = (mapObj) => {
    return Object.entries(mapObj).sort((a, b) => {
        return (
            pgLineOptions.getDisplayOrderForLineType(parseInt(a[0])) -
            pgLineOptions.getDisplayOrderForLineType(parseInt(b[0]))
        );
    });
};

module.exports = {
    getDefaultLineOptions,
};
