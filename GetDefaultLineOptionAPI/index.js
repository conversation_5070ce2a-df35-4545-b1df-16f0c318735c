const { CommonError } = require('../__shared__/constants/commonError');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { ERROR_MAP } = require('./helpers/errorHelper');
const { validateAndParseGetDefaultLineOptionRequest } = require('./validations/getDefaultLineOptionValidator');
const { getDefaultLineOptions } = require('./getDefaultLineOptions');

module.exports = async function (context, req) {
    try {
        context.log('GetDefaultLineOptionAPI START');

        const { error, errorMessage, params } = validateAndParseGetDefaultLineOptionRequest(req.body ?? {});
        if (error) {
            context.log('GetDefaultLineOptionAPI validation error', errorMessage);
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', error, null);
            return;
        }

        await mongoose.init(context);
        const lineOptionsResult = await getDefaultLineOptions(context, params);
        if (lineOptionsResult.ok) {
            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    ...lineOptionsResult.result,
                },
            };
        } else {
            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    default_kaisen_options: [],
                    selectable_kaisen_options: [],
                    plan_id: '',
                    plan_name: '',
                },
            };
        }
    } catch (exception) {
        context.log.error('GetDefaultLineOptionAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, ERROR_MAP.SERVER_ERROR, null);
        return;
    }
};
