const Joi = require('joi');
const { ERROR_MAP } = require('../helpers/errorHelper');

const GetDefaultLineOptionSchema = Joi.object({
    tenant_id: Joi.string().required(),
    plan_id: Joi.number().required(),
    nnumber: Joi.string().required(),
    is_shinki: Joi.bool().default(false),
    is_esim: Joi.bool().default(false),
}).unknown();

/**
 *
 * @param {object} body
 * @returns {{
 *   error?: string,
 *   errorMessage?: string,
 *   params?: {
 *     tenantId: string,
 *     planId: number,
 *     nno: string,
 *     isShinki: boolean,
 *     iseSIM: boolean
 *   }
 * }}
 */
const validateAndParseGetDefaultLineOptionRequest = (body) => {
    const { error } = GetDefaultLineOptionSchema.validate(body);
    if (error) {
        return { error: ERROR_MAP.INVALID_PARAMETER, errorMessage: error.details[0].message };
    }
    return {
        params: {
            tenantId: body.tenant_id,
            planId: body.plan_id,
            nno: body.nnumber,
            isShinki: body.is_shinki,
            iseSIM: body.is_esim,
        },
    };
};

module.exports = {
    validateAndParseGetDefaultLineOptionRequest,
};
