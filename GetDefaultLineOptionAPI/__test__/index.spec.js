const _ = require('lodash');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { expect } = require('chai');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');

describe('GetDefaultLineOptionAPI', () => {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'TSA000',
            plan_id: '99047',
            nnumber: 'N999999901',
            is_shinki: true,
            is_esim: false,
        },
    };

    describe('GetDefaultLineOptionAPI with normal request', () => {
        it('returns result for shinki', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.is_esim;

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.default_kaisen_options).to.be.a('Array');
            expect(context.res.body.default_kaisen_options.length).greaterThan(0);
            expect(context.res.body.selectable_kaisen_options).to.be.a('Array');
            expect(context.res.body.selectable_kaisen_options.length).greaterThan(0);
            expect(context.res.body.plan_id).not.undefined;
            expect(context.res.body.plan_name).to.be.a('string').not.empty;
        });

        it('returns result for eSIM', async () => {
            const request = _.cloneDeep(__request);
            request.body.plan_id = 99051;
            request.body.is_esim = true;
            delete request.body.is_shinki;

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.default_kaisen_options).to.be.a('Array');
            expect(context.res.body.default_kaisen_options.length).greaterThan(0);
            expect(context.res.body.selectable_kaisen_options).to.be.a('Array');
            expect(context.res.body.selectable_kaisen_options.length).greaterThan(0);
            expect(context.res.body.plan_id).not.undefined;
            expect(context.res.body.plan_name).to.be.a('string').not.empty;
        });

        it('returns result for MNP', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.is_esim;
            delete request.body.is_shinki;

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.default_kaisen_options).to.be.a('Array');
            expect(context.res.body.default_kaisen_options.length).greaterThan(0);
            expect(context.res.body.selectable_kaisen_options).to.be.a('Array');
            expect(context.res.body.selectable_kaisen_options.length).greaterThan(0);
            expect(context.res.body.plan_id).not.undefined;
            expect(context.res.body.plan_name).to.be.a('string').not.empty;
        });

        it('returns empty result if plan is not exists', async () => {
            const request = _.cloneDeep(__request);
            request.body.plan_id = 99887766;

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.default_kaisen_options).to.be.a('Array');
            expect(context.res.body.default_kaisen_options.length).equal(0);
            expect(context.res.body.selectable_kaisen_options).to.be.a('Array');
            expect(context.res.body.selectable_kaisen_options.length).equal(0);
            expect(context.res.body.plan_id).equal('');
            expect(context.res.body.plan_name).equal('');
        });
    });

    describe('GetDefaultLineOption with bad request', () => {
        it('returns error if tenant id is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = '';

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.tenant_id;

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if plan id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.plan_id;

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if nnumber is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.nnumber = '';

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.nnumber;

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
