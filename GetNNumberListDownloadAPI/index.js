const { CommonError } = require('../__shared__/constants/commonError');
const tenantNNumber = require('../__shared__/pgModels/tenantNNumbers');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const nBanHelper = require('./helpers');
const appConfig = require('../__shared__/config/appConfig');
const portalConfig = appConfig.getPortalConfig();
const { validateAndParseGetNNumberListDownloadRequest } = require('./validations/getNNumberListDownloadValidator');

// テナント毎代表N番一覧取得
module.exports = async function (context, req) {
    try {
        context.log('GetNNumberListDownloadAPI START');

        const { error, errorMessage, params } = validateAndParseGetNNumberListDownloadRequest(context, req);
        if (error) {
            context.log('GetNNumberListDownloadAPI request validation error:', errorMessage);
            responseWithError(context, CommonError.INVALID_USER, errorMessage, error, null);
            return;
        }

        const { tenantId } = params;

        if (tenantId == 'CON000') {
            const nBan = portalConfig.COCN_DAIHYOU_NNUMBER || 'N141096449';
            const nBanFullMVNO = portalConfig.COCN_DAIHYOU_NNUMBER_FOR_FULL_MVNO.at(0) || 'N190173862';

            const fullList = [
                { nnumber: nBan, fullMvnoFlag: false },
                { nnumber: nBanFullMVNO, fullMvnoFlag: true },
            ];

            const filteredList = await nBanHelper.filterNBanHelper(fullList);

            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    result: filteredList.map((item) => {
                        return { nnumber: item.nnumber, fullMvnoFlag: item.full_mvno_flag };
                    }),
                },
            };
        } else {
            const nBanArr = await tenantNNumber.retrieveDaihyoNNumbersAndFullMnvoFlagByTenantID(context, {
                tenantId: tenantId,
            });
            context.log('retrieveDaihyoNNumbersAndFullMnvoFlagByTenantID: ', JSON.stringify(nBanArr));

            const filteredList = await nBanHelper.filterNBanHelper(nBanArr);

            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    result: filteredList.map((item) => {
                        return { nnumber: item.nnumber, fullMvnoFlag: item.full_mvno_flag };
                    }),
                },
            };
        }
    } catch (exception) {
        context.log.error('GetNNumberListDownloadAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
