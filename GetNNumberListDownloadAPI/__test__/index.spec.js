const _ = require('lodash');
const sinon = require('sinon');
const httpFunction = require('../index');
const helpers = require('../../__shared__/helpers/baseHelper');
const context = require('../../__shared__/__tests__/defaultContext');
const { expect } = require('chai');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const tenantNNumber = require('../../__shared__/pgModels/tenantNNumbers');
const nBanHelper = require('../helpers');

describe('GetNNumberListDownloadAPI', () => {
    const sandbox = sinon.createSandbox();

    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'TSA000',
        },
    };

    afterEach(() => {
        sandbox.restore();
    });

    describe('GetNNumberListDownloadAPI with normal request', () => {
        it('should not return an error when the execution was successful', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(tenantNNumber, 'retrieveDaihyoNNumbersAndFullMnvoFlagByTenantID').resolves([
                {
                    nnumber: '000000000',
                    full_mvno_flag: false,
                },
            ]);

            sandbox.stub(nBanHelper, 'filterNBanHelper').resolves([
                {
                    nnumber: '000000000',
                    full_mvno_flag: false,
                },
            ]);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.deep.equal([
                {
                    nnumber: '000000000',
                    fullMvnoFlag: false,
                },
            ]);
        });

        it('should not return an error when the execution was successful (CON000 user)', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = 'CON000';

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(nBanHelper, 'filterNBanHelper').resolves([
                {
                    nnumber: 'N141096449',
                    full_mvno_flag: false,
                },
                {
                    nnumber: 'N190173862',
                    full_mvno_flag: true,
                },
            ]);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.deep.equal([
                {
                    nnumber: 'N141096449',
                    fullMvnoFlag: false,
                },
                {
                    nnumber: 'N190173862',
                    fullMvnoFlag: true,
                },
            ]);
        });
    });

    describe('GetNNumberListDownloadAPI with bad request', () => {
        it('should return an error when tenantId does not exist in session', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('サーバにて処理できません。');
            expect(context.res.body.nttc_mvno_error_code).equal('990002');
        });

        it('should return an error when tenantId is not sent and the user is a super user', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('正しくないパラメータが含まれています。');
            expect(context.res.body.nttc_mvno_error_code).equal('991024');
        });
    });
});
