const Joi = require('joi');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const { getSessionData, isNone } = require('../../__shared__/helpers/baseHelper');

const { ERROR_MAP } = require('../helpers/errorHelper');

const GetNNumberListDownloadSchema = Joi.object({
    tenant_id: Joi.string().required(),
}).required();

const validateAndParseGetNNumberListDownloadRequest = (context, req) => {
    const { roleId, tenantId: currentUserTenantId, user } = getSessionData(context, req);
    if (isNone(roleId) || isNone(currentUserTenantId) || isNone(user)) {
        context.log.error('GetNNumberListDownloadAPI sessionVar empty');
        return {
            error: ERROR_MAP.SERVER_ERROR,
            errorMessage: 'tenant id is not set',
        };
    }

    let isValidUser = false;
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            isValidUser = true;
            break;
        default:
            isValidUser = false;
    }

    if (!isValidUser) {
        context.log.error('GetNNumberListDownloadAPI invalid permission');
        return {
            error: ERROR_MAP.SERVER_ERROR,
            errorMessage: 'invalid permission',
        };
    }

    const { error } = GetNNumberListDownloadSchema.validate(req.body ?? {});
    if (error) {
        context.log.error('GetNNumberListDownloadAPI invalid parameter: ' + error.details[0].message);
        return {
            error: ERROR_MAP.INVALID_PARAMETER,
            errorMessage: error.details[0].message,
        };
    }

    let tenantId;
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
            tenantId = req.body.tenant_id;
            break;
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            tenantId = currentUserTenantId;
    }

    return {
        params: {
            tenantId,
        },
    };
};

module.exports = {
    validateAndParseGetNNumberListDownloadRequest,
};
