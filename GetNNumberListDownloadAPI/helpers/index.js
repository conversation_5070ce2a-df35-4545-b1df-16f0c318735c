const { BlobServiceClient } = require('@azure/storage-blob');
const appConfig = require('../../__shared__/config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const filterNBanHelper = async (nBanArr) => {
    const storageConfig = await appConfig.getStorageConfig();
    const kaisenInfoFileNameFormat = portalConfig.KAISEN_INFO_FILENAME_FORMAT || 'nnumber.zip';
    const connectionString = storageConfig.STORAGE_KAISEN_INFO_CONNECTION_STRING;
    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
    const containerName = storageConfig.STORAGE_KAISEN_INFO_CONTAINER_NAME;
    const containerClient = blobServiceClient.getContainerClient(containerName);
    let promises;
    let promiseResults;
    let filteredList;

    promises = nBanArr.map(async (e) => {
        let fileName = kaisenInfoFileNameFormat.replace('nnumber', e.nnumber);
        let blobClient = containerClient.getBlobClient(fileName);

        return await blobClient.exists();
    });

    promiseResults = await Promise.all(promises);

    filteredList = nBanArr.filter((e, index) => {
        return promiseResults[index] === true;
    });

    return filteredList;
};

module.exports = {
    filterNBanHelper,
};
