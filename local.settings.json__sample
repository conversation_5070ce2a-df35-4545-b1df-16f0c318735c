{
    "IsEncrypted": false,
    "Values": {
        "AzureWebJobsStorage": "",
        "FUNCTIONS_WORKER_RUNTIME": "node",
        "MONGO_CONNECTION_STRING": "mongodb://localhost:27017/",
        "ADMIN_SERVICE__DB": "konnect",
        "CORE_SERVICE__API_ROOT": "http://localhost:7702/core-api",
        "CORE_SERVICE__OPF_TENANTID": "OPF000",
        "CORE_SERVICE__PASSWORD": "OPF000",
        "CORE_SERVICE__SENDER_SYSTEM_ID": "0001",
        "CORE_SERVICE__MOCK_MODE": "true",
        "POSTGRE_CONNECTION_STRING": "postgresql://localhost:5432/mawp",
        "COCN_DAIHYONNUMBER": "N141096449",
        "COCN_DAIHYONNUMBER_FULL_MVNO": "N190173862",
        "LOCAL_SETTINGS_CONFIG": true,
    },
    "Host": {
        "LocalHttpPort": 7702
    }
}
