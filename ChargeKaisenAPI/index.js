const baseHelper = require('../__shared__/helpers/baseHelper');
const { CommonError } = require('../__shared__/constants/commonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const lines = require('../__shared__/pgModels/lines');
const { validateDateFormat } = require('./helpers');
const CoreService = require('../__shared__/services/core.service');
const serviceOrder = require('../__shared__/pgModels/serviceOrder');
const { USAGE_STATUS } = require('../__shared__/constants/lineConstants');

module.exports = async function (context, req) {
    try {
        context.log('ChargeKaisenAPI START');
        const { roleId, tenantId, user } = baseHelper.getSessionData(context, req);

        let isAllowed;

        switch (roleId) {
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
                isAllowed = true;
                break;
            default:
                isAllowed = false;
                break;
        }

        if (baseHelper.isNone(roleId) || baseHelper.isNone(tenantId) || baseHelper.isNone(user) || !isAllowed) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const lineNo = req.body['line_no'];
        const tenantID = req.body['tenant_id'];
        const potalPlanId = req.body['potal_plan_id'];
        const reserveDate = req.body['reserve_date'];
        const optionPlanId = req.body['option_plan_id'];

        if (baseHelper.isNone(lineNo)) {
            context.log('ChargeKaisenAPI line_no is required!');
            responseWithError(context, CommonError.BAD_REQUEST, 'Invalid parameter', '994055', null);
            return;
        }

        if (baseHelper.isNone(tenantID)) {
            context.log('ChargeKaisenAPI tenant_id is required!');
            // 元のコードでは994056を返しているがこれが誤りだと思われるので修正した
            responseWithError(context, CommonError.BAD_REQUEST, 'Invalid parameter', '994057', null);
            return;
        }

        if (baseHelper.isNone(potalPlanId)) {
            context.log('ChargeKaisenAPI potal_plan_id is required!');
            responseWithError(context, CommonError.BAD_REQUEST, 'Invalid parameter', '994091', null);
            return;
        }

        if (baseHelper.isNone(optionPlanId)) {
            context.log('ChargeKaisenAPI option_plan_id is required!');
            responseWithError(context, CommonError.BAD_REQUEST, 'Invalid parameter', '994092', null);
            return;
        }

        const isOwned = await lines.isKaisenNoBelongToTenant(context, { tenantId: tenantID, kaisenNo: lineNo });

        if (!isOwned) {
            context.log('ChargeKaisenAPI テナントに回線番号が紐づいていません。');
            responseWithError(
                context,
                CommonError.BAD_REQUEST,
                'テナントに回線番号が紐付いていません。',
                '994088',
                null
            );
            return;
        }

        let reserveDateAtFour;
        if (!baseHelper.isNone(reserveDate)) {
            const validateDateResult = validateDateFormat(reserveDate);

            if (!validateDateResult) {
                context.log('ChargeKaisenAPI reserve_date is required!');
                responseWithError(context, CommonError.BAD_REQUEST, 'Invalid parameter', '994087', null);
                return;
            } else {
                reserveDateAtFour = `${reserveDate} 04:00`;
            }
        }

        const usageStatusInfo = await lines.getUsageStatusByLineNo(context, lineNo);
        if (usageStatusInfo == USAGE_STATUS.SUSPENDED) {
            context.log('ChargeKaisenAPI: Line is suspended.');
            responseWithError(context, CommonError.BAD_REQUEST, 'this line is suspened.', '997011', null);
            return;
        } else if (baseHelper.isNone(usageStatusInfo)) {
            context.log('ChargeKaisenAPI: cannot get line usage.');
            responseWithError(context, CommonError.BAD_REQUEST, 'cannot get line usage.', '994055', null);
            return;
        }

        try {
            const result = await CoreService.callAPICoreLineCouponTsuika(context, {
                tenantId: tenantID,
                lineNo: lineNo,
                potalPlanID: potalPlanId,
                optionPlanId: optionPlanId,
                reserveDate: reserveDateAtFour,
            });

            // 元のコードにある店舗ユーザの分岐は実行されない想定なので削除した
            let userId = user.plusId;
            if (typeof userId === 'string' && userId.length > 16) {
                userId = userId.slice(0, 16);
            }
            await serviceOrder.updateExecSO(context, result.responseHeader.apiProcessID, tenantID, userId);

            if (result.responseHeader.processCode == '000000') {
                context.res = {
                    status: 200,
                    body: {
                        error: CommonError.NO_ERROR,
                    },
                };
            } else {
                context.log('ChargeKaisenAPI - exception: API Call Fail:', result?.responseHeader?.processCode);
                responseWithError(context, CommonError.SERVER_ERROR, 'API call failed', '994089', null);
                return;
            }
        } catch (exception) {
            context.log.error('ChargeKaisenAPI - exception: Cannot connect API server: ', exception);
            responseWithError(context, CommonError.SERVER_ERROR, exception, '994089', null);
            return;
        }
    } catch (exception) {
        context.log.error('GetNNumberListDownloadAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
