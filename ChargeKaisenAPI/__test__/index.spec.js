const _ = require('lodash');
const sinon = require('sinon');
const httpFunction = require('../index');
const helpers = require('../../__shared__/helpers/baseHelper');
const context = require('../../__shared__/__tests__/defaultContext');
const { expect } = require('chai');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const CoreService = require('../../__shared__/services/core.service');
const lines = require('../../__shared__/pgModels/lines');
const serviceOrder = require('../../__shared__/pgModels/serviceOrder');

describe('ChargeKaisenAPI', () => {
    const sandbox = sinon.createSandbox();

    const __request = {
        headers: getDefaultHeader(),
        body: {
            line_no: '12345678901',
            tenant_id: 'TSA000',
            potal_plan_id: 'test',
            reserve_date: '2023/12/31',
            option_plan_id: 'test',
        },
    };

    afterEach(() => {
        sandbox.restore();
    });

    describe('ChargeKaisenAPI with normal request', () => {
        it('should not return an error when the execution was successful', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);
            sandbox.stub(lines, 'getUsageStatusByLineNo').resolves(0);
            sandbox.stub(CoreService, 'callAPICoreLineCouponTsuika').resolves({
                responseHeader: {
                    processCode: '000000',
                    apiProcessID: 'test',
                },
            });
            sandbox.stub(serviceOrder, 'updateExecSO');

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
        });
    });

    describe('ChargeKaisenAPI with bad request', () => {
        it('should return an error if user is 店舗ユーザ', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 999,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('この操作を実行する権限がありません。');
            expect(context.res.body.nttc_mvno_error_code).equal('991002');
        });

        it('should return an error if line_no is not provided', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.line_no;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('回線番号が不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
        });

        it('should return an error if tenant_id is not provided', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('テナントIDが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
        });

        it('should return an error if potal_plan_id is not provided', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.potal_plan_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('プランIDが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994091');
        });

        it('should return an error if option_plan_id is not provided', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.option_plan_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('オプションプランIDが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994092');
        });

        it('should return an error if kaisen does not belong to the tenant', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(false);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('回線所属テナント情報の取得に失敗しました。');
            expect(context.res.body.nttc_mvno_error_code).equal('994088');
        });

        it('should return an error if reserve_date is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.reserve_date = '12/31/2023';

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('予約日が不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994087');
        });

        it('should not return an error when the kaisen is suspended', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);
            sandbox.stub(lines, 'getUsageStatusByLineNo').resolves(2);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('回線番号の利用状態はサスペンド中です。');
            expect(context.res.body.nttc_mvno_error_code).equal('997011');
        });

        it('should not return an error when the kaisen usage is undefined', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);
            sandbox.stub(lines, 'getUsageStatusByLineNo').resolves(null);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('回線番号が不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
        });

        it('should return an error when API call failed', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    name: 'test',
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);
            sandbox.stub(lines, 'getUsageStatusByLineNo').resolves(0);
            sandbox.stub(CoreService, 'callAPICoreLineCouponTsuika').resolves({
                responseHeader: {
                    processCode: '111111',
                    apiProcessID: 'test',
                },
            });
            sandbox.stub(serviceOrder, 'updateExecSO');

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('SO実行が失敗しました。');
            expect(context.res.body.nttc_mvno_error_code).equal('994089');
        });
    });
});
