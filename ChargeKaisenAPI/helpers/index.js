/**
 * @description Check if string format is yyyy/mm/dd
 * @param {string} str
 * @returns {boolean}
 */
const validateDateFormat = (str) => {
    var t = str.match(/^(\d{4})\/(\d{2})\/(\d{2})$/);
    if (t === null) return false;
    var m = +t[2],
        d = +t[3];

    // Below should be a more acurate algorithm
    if (m >= 1 && m <= 12 && d >= 1 && d <= 31) {
        return true;
    }

    return false;
};

module.exports = {
    validateDateFormat,
};
