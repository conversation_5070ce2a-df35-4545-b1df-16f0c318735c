// Service Bus for Calendar Event (maintenance event)
const { ServiceBusClient } = require('@azure/service-bus');
const dayjs = require('dayjs');
// const Long = require('long');
const { getPortalConfig } = require('../config/appConfig');
const { isNone } = require('../helpers/baseHelper');

const portalConfig = getPortalConfig();
const client = new ServiceBusClient(portalConfig.CALENDAR.SERVICE_BUS_CONNECTION_STRING);
const sender = client.createSender(portalConfig.CALENDAR.SERVICE_BUS_QUEUE_NAME);

/**
 * @param {object} context
 * @param {string} eventId
 * @param {string} service MAINTENANCE_SERVICE
 * @param {string} mode MAINTENANCE_MODE (on or off)
 * @param {number} scheduleTime unix seconds
 * @returns {Promise<Array<Long>>}
 */
const registerMaintenanceEvent = async (context, eventId, service, mode, scheduleTime) => {
    // only send future event into queue
    const now = dayjs().unix();
    if (scheduleTime >= now) {
        return await sender.scheduleMessages(
            {
                body: {
                    eventId,
                    service,
                    mode,
                },
            },
            dayjs.unix(scheduleTime).utc().toDate()
        );
    } else {
        context.log('registerMaintenanceEvent skip enqueue past event:', { eventId, service, mode, scheduleTime, now });
        return [{ low: 0, high: 0, unsigned: false }]; // return 0
    }
};

/**
 * @param {Long} sequenceNumbers
 */
const cancelScheduledMessages = async (context, sequenceNumber) => {
    if (isNone(sequenceNumber)) {
        return;
    }
    try {
        await sender.cancelScheduledMessages(sequenceNumber);
    } catch (error) {
        context.log.error('cancelScheduledMessages error:', sequenceNumber, error.message);
    }
};

module.exports = {
    registerMaintenanceEvent,
    cancelScheduledMessages,
};
