const { ServiceBusClient } = require('@azure/service-bus');
const appConfig = require('../config/appConfig');

/**
 * Send payload to service bus which later will be broadcasted by SignalR service
 * @param {object} context
 * @param {string} type NotificationType constant
 * @param {any} message payload
 */
const sendMessage = async (context, type, message) => {
    try {
        const portalConfig = appConfig.getPortalConfig();
        const client = new ServiceBusClient(portalConfig.NOTIFICATION.SERVICE_BUS_CONNECTION_STRING);
        const sender = client.createSender(portalConfig.NOTIFICATION.SERVICE_BUS_QUEUE_NAME);
        context.log('SignalR service sendMessage:', { type, message });
        await sender.sendMessages([{ body: { type, message } }]);
        await sender.close();
    } catch (error) {
        context.log.error('SignalR service sendMessage - error:', error);
    }
};

module.exports = {
    sendMessage,
};
