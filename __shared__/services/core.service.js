const axios = require('axios').default;
const md2 = require('js-md2');
const _ = require('lodash');

const { isNone } = require('../helpers/baseHelper');
const appConfig = require('../config/appConfig');
const { retrieveMAWPTenantPassword } = require('../pgModels/tenants');
const portalConfig = appConfig.getPortalConfig();

/** list of executeApiName which can use internal Core API */
const CORE_INTERNAL_API_NAMES = new Set([
    'callAPISOFrontCancel', // フロント用キャンセル
    'callAPIKaisenHaishi', // 回線廃止
    'callLinesSim', // SIM再発行
    'callAPIRiyouChuudanSaikaiSuspend', // 利用中断/再開/サスペンド
    'callAPINwContractChange', // NW契約変更
    'callAPILineOptionUpdate', // 回線オプション変更
    'callAPINetworkPasswordUpdate', // 暗証番号変更
    'callAPILinePreAdd', // 仮登録回線追加
    'callAPILineEnable', // 回線黒化
    'callAPIVoicePlanChange', // 0035でんわプラン変更
    'callAPICoreLineUnyo', // 回線運用情報参照
    'callAPICoreSoCancel', // 予約キャンセル
    'callAPICoreLineCoupon', // 回線クーポンオン・オフ
    'callAPICoreLineCouponTsuika', // 回線追加チャージ(クーポン)容量・期間追加
    'callAPIKaisenHenkoPlan', // 回線プラン変更
    'callAPIKaisenActivation', // 回線アクティベート/ディアクティベート
]);

/* It's a list of APIs that can be called. */
const CORE_API_LIST = {
    SO_FRONT_CANCEL: {
        url: '/so_frontcancel',
        functionType: '56',
        koukankiSetteiNGErrorCodes: [
            '510101',
            '510102',
            '510103',
            '510104',
            '510105',
            '510106',
            '510107',
            '510108',
            '510111',
            '510109',
            '510110',
            '510401',
            '510301',
        ],
    },
    KAISEN_HAISHI: {
        url: '/Lines/del',
        functionType: '52',
        koukankiSetteiNGErrorCodes: [
            '510101',
            '510102',
            '510103',
            '510104',
            '510105',
            '510106',
            '510107',
            '510108',
            '510111',
            '510109',
            '510110',
            '510401',
            '510301',
        ],
    },
    KARI_TOUROKU_KAISEN_TSUIKA: {
        url: '/Lines/preadd',
        functionType: '51',
        koukankiSetteiNGErrorCodes: [
            '510101',
            '510102',
            '510103',
            '510104',
            '510105',
            '510106',
            '510107',
            '510108',
            '510111',
            '510109',
            '510110',
            '510401',
            '510301',
        ],
        aladinKoujiNGErrorCodes: [],
    },
    SIM_SAIHAKKOU: {
        url: '/Lines/sim',
        functionType: '53',
        koukankiSetteiNGErrorCodes: [
            '510101',
            '510102',
            '510103',
            '510104',
            '510105',
            '510106',
            '510107',
            '510108',
            '510111',
            '510109',
            '510110',
            '510401',
            '510301',
        ],
    },
    RIYOU_CHUUDAN_SAIKAI_SUSPEND: {
        url: '/Lines/suspend',
        functionType: '58',
        koukankiSetteiNGErrorCodes: [
            '510101',
            '510102',
            '510103',
            '510104',
            '510105',
            '510106',
            '510107',
            '510108',
            '510111',
            '510109',
            '510110',
            '510401',
            '510301',
        ],
    },
    KAISEN_KUROKA: {
        url: '/Lines/enable',
        functionType: '55',
        koukankiSetteiNGErrorCodes: [
            '510101',
            '510102',
            '510103',
            '510104',
            '510105',
            '510106',
            '510107',
            '510108',
            '510111',
            '510109',
            '510110',
            '510401',
            '510301',
        ],
    },
    SO_CANCEL: {
        url: '/so_cancel',
        functionType: '18',
    },
    LINE_COUPON: {
        url: '/Lines/coupon',
        functionType: '04',
    },
    LINE_COUPON_TSUIKA: {
        url: '/Lines/charge',
        functionType: '03',
    },
    LINE_PLAN_HENKO: {
        url: '/Lines/plan',
        functionType: '06',
    },
    LINE_UNYO: {
        url: '/Lines/traffic',
        functionType: '02',
    },
    KAISEN_ACTIVATE: {
        url: '/Lines/activation',
        functionType: '05',
    },
    NETWORK_PASSWORD_CHANGE: {
        url: '/Lines/lineop',
        functionType: '54',
    },
    LINE_OPTION_CHANGE: {
        url: '/Lines/lineop',
        functionType: '54',
    },
    NW_CONTRACT_CHANGE: {
        url: '/Lines/access',
        functionType: '60',
    },
    VOICE_PLAN_CHANGE: {
        url: '/Lines/voice',
        functionType: '59',
    },
};

/**
 * Determine if the API should use the new Core API V2
 * @param {object} context
 * @param {string} executeAPIName
 * @returns {Promise<string>}
 */
const isUsingCoreServiceV2 = async (context, executeAPIName) => {
    // TODO use other way to store feature flag instead of app config
    let useCoreApiV2 = false;
    if (CORE_INTERNAL_API_NAMES.has(executeAPIName)) {
        useCoreApiV2 = await appConfig.getFeatureFlag(context, appConfig.FEATURE_FLAG_KEYS.USE_CORE_API_V2);
    }
    context.log('isUsingCoreServiceV2:', executeAPIName, useCoreApiV2);
    return useCoreApiV2;
};

/** old Core endpoint */
let instanceCoreService = null;
/** new Core endpoint */
let instanceCoreServiceV2 = null;
/**
 * @param context - The context object that is passed to the function.
 * @returns {axios.AxiosInstance} A function that returns a promise that resolves to an axios client.
 */
const getCoreServiceClient = async (context, useCoreApiV2 = false) => {
    if (!useCoreApiV2 && instanceCoreService) {
        context.log('getCoreServiceClient: reusing instance v1');
        return instanceCoreService;
    }
    if (useCoreApiV2 && instanceCoreServiceV2) {
        context.log('getCoreServiceClient: reusing instance v2');
        return instanceCoreServiceV2;
    }

    const apiConfig = await appConfig.getAPIConfig(context);

    let baseURL = useCoreApiV2 ? apiConfig.API_CORE_INTERNAL_URL : apiConfig.API_CORE_URL;
    context.log('getCoreServiceClient: create instance:', { useCoreApiV2, baseURL });

    const newInstance = axios.create({
        baseURL,
        headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
        },
    });

    if (useCoreApiV2) {
        // since use mock flag change requires a restart, we can't change it dynamically
        instanceCoreServiceV2 = newInstance;
        mockCoreAPI(context, instanceCoreServiceV2);
        return instanceCoreServiceV2;
    } else {
        instanceCoreService = newInstance;
        mockCoreAPI(context, instanceCoreService);
        return instanceCoreService;
    }
};

const getSeqNoAndAPIKey = () => {
    // val seqNoStr = "G" + (mRandom.nextInt(999998) + 1).formatted("%06d")
    const seqNoStr = 'G' + (Math.floor(Math.random() * 999998) + 1).toString().padStart(6, '0');
    const apiKeyStr = '$02$' + md2(portalConfig.CORE_SERVICE.PASSWORD) + '$02$' + md2(seqNoStr);
    const apiKeyStrLowerCase = apiKeyStr.toLowerCase();
    return { seqNoStr, apiKeyStrLowerCase };
};

const getSeqNoAndAPIKeyforTenant = async (context, tenantId) => {
    const seqNoStr = 'G' + (Math.floor(Math.random() * 999998) + 1).toString().padStart(6, '0');
    const tenantHashedPwdo = await retrieveMAWPTenantPassword(context, { tenantId });
    if (!tenantHashedPwdo) {
        return { seqNoStr, apiKeyStrLowerCase: '' };
    }

    const apiKeyStr = tenantHashedPwdo + '$02$' + md2(seqNoStr);
    const apiKeyStrLowerCase = apiKeyStr.toLowerCase();
    return { seqNoStr, apiKeyStrLowerCase };
};

/**
 * It takes a JSON body, an API name, and a URL, and then it calls the API using the Axios library
 * @param context - The context object that is passed to the function.
 * @param jsonBody - The JSON body of the request.
 * @param executeAPIName - The name of the API you're calling.
 * @param url - The URL of the API to call.
 * @returns The processCode from the responseHeader
 */
const executeAPICall = async (context, jsonBody, executeAPIName, url) => {
    context.log('Core executeAPICall: ' + executeAPIName);
    context.log('Core jsonBody: ', jsonBody);
    const useCoreApiV2 = await isUsingCoreServiceV2(context, executeAPIName);
    // if using v1, remove targetSoId from jsonBody
    if (!useCoreApiV2) {
        delete jsonBody.targetSoId;
        context.log('omit targetSoId', jsonBody);
    }
    const instanceCoreService = await getCoreServiceClient(context, useCoreApiV2);
    const { status, data } = await instanceCoreService.post(url, jsonBody);
    if (status !== 200) {
        throw new Error('Core executeAPICall: ' + executeAPIName + ' failed with status: ' + status);
    }

    context.log('Core executeAPICall: ' + executeAPIName + ' response: ', data);
    return data.responseHeader?.processCode;
};

/**
 * @typedef KaisenOptionUpdateInfo
 * @property {number} kaisenOptionType
 * @property {string} currentKaisenOptionId
 * @property {string} updateKaisenOptionId
 */

/**
 *
 * @param {KaisenOptionUpdateInfo} lineOption - This is the line option object that you get from the getLineOption function.
 */
const getLineOptionRequestParam = (lineOptionInfo = null) => {
    if (isNone(lineOptionInfo)) {
        return {
            changeCode: '0',
        };
    } else {
        let changeCode;
        const beforeOpId = lineOptionInfo.currentKaisenOptionId;
        const afterOpId = lineOptionInfo.updateKaisenOptionId;

        const beforeOpIdEmpty = isNone(beforeOpId);
        const afterOpIdEmpty = isNone(afterOpId);

        if (beforeOpId == afterOpId) {
            changeCode = '0';
        } else if (beforeOpIdEmpty && !afterOpIdEmpty) {
            changeCode = '1';
        } else if (!beforeOpIdEmpty && !afterOpIdEmpty) {
            changeCode = '2';
        } else {
            changeCode = '3';
        }
        return {
            changeCode,
            beforeOpId,
            afterOpId,
        };
    }
};

/**
 * It takes a JSON body, an API name, and a URL, and then it calls the API using the Axios library
 * @param context - The context object that is passed to the function.
 * @param jsonBody - The JSON body of the request.
 * @param executeAPIName - The name of the API you're calling.
 * @param url - The URL of the API to call.
 * @returns Complete response object from API
 */
const executeAPICallWithResponseObject = async (context, jsonBody, executeAPIName, url) => {
    context.log('Core executeAPICall: ' + executeAPIName);
    context.log('Core jsonBody: ', jsonBody);
    const useCoreApiV2 = await isUsingCoreServiceV2(context, executeAPIName);
    const instanceCoreService = await getCoreServiceClient(context, useCoreApiV2);
    const { status, data } = await instanceCoreService.post(url, jsonBody);
    if (status !== 200) {
        throw new Error('Core executeAPICall: ' + executeAPIName + ' failed with status: ' + status);
    }

    context.log('Core executeAPICall: ' + executeAPIName + ' response: ', data);
    return data;
};

/**
 * It takes a JSON body, an API name, and a URL, and then it calls the API using the Axios library
 * @param context - The context object that is passed to the function.
 * @param jsonBody - The JSON body of the request.
 * @param executeAPIName - The name of the API you're calling.
 * @param url - The URL of the API to call.
 * @returns {Promise<{processCode:string,apiProcessID:string}>} The processCode and API Process ID
 */
const executeAPICallWithApiProcessID = async (context, jsonBody, executeAPIName, url) => {
    context.log('Core executeAPICall: ' + executeAPIName);
    context.log('Core jsonBody: ', jsonBody);
    const useCoreApiV2 = await isUsingCoreServiceV2(context, executeAPIName);
    const instanceCoreService = await getCoreServiceClient(context, useCoreApiV2);
    const { status, data } = await instanceCoreService.post(url, jsonBody);
    if (status !== 200) {
        throw new Error('Core executeAPICall: ' + executeAPIName + ' failed with status: ' + status);
    }

    context.log('Core executeAPICall: ' + executeAPIName + ' response: ', data);
    return {
        processCode: data.responseHeader?.processCode,
        apiProcessID: data.responseHeader?.apiProcessID,
    };
};

/**
 * It returns a header object that is used in the request to the Core API.
 * @param functionType - The type of function you are calling.
 * @param {string} tenantId - Provice the tenantId if you want to use a API key for a specific tenant.
 * @param {object} context - The context object that is passed to the function.
 * @returns An object with the following properties:
 *     sequenceNo: A string of the sequence number
 *     senderSystemId: A string of the sender system id
 *     apiKey: A string of the api key
 *     functionType: A string of the function type
 */
const commonRequestHeader = async (functionType, tenantId, context) => {
    let seqNoAndApiKey;

    if (tenantId) {
        seqNoAndApiKey = await getSeqNoAndAPIKeyforTenant(context, tenantId);
        context.log('seqNoAndApiKey: ', seqNoAndApiKey);
    } else {
        seqNoAndApiKey = getSeqNoAndAPIKey();
    }

    const { seqNoStr, apiKeyStrLowerCase } = seqNoAndApiKey;
    return {
        sequenceNo: seqNoStr,
        senderSystemId: portalConfig.CORE_SERVICE.SENDER_SYSTEM_ID,
        apiKey: apiKeyStrLowerCase,
        functionType: functionType,
    };
};

/**
 * This function calls the SO Front Cancel API.
 * @param context - The context object that is passed to the function.
 * @param {object} SOFrontCancelRequestParams - The parameters for the SO Front Cancel API.
 * @returns The response from the API call.
 */
const callAPISOFrontCancel = async (context, { targetTenantId, serviceOrderIdKey, targetSoId, csvUnnecessaryFlag }) => {
    context.log('callAPISOFrontCancel start');
    if (!targetTenantId) throw new Error('targetTenantId is required');
    if (!serviceOrderIdKey) throw new Error('serviceOrderIdKey is required');
    // if (!targetSoId) throw new Error('targetSoId is required');
    if (!csvUnnecessaryFlag) throw new Error('csvUnnecessaryFlag is required');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.SO_FRONT_CANCEL.functionType),
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: targetTenantId,
        targetSoId: targetSoId,
        serviceOrderIdKey: serviceOrderIdKey,
        csvUnnecessaryFlag: csvUnnecessaryFlag,
    };
    return await executeAPICall(context, jsonBody, 'callAPISOFrontCancel', CORE_API_LIST.SO_FRONT_CANCEL.url);
};

/**
 * This function calls the Kaisen Haishi API.
 * @param {object} context  - The context object that is passed to the function.
 * @param {object} KaisenHaishiRequestParams
 * @param {string} KaisenHaishiRequestParams.targetTenantId
 * @param {string} KaisenHaishiRequestParams.targetSoId
 * @param {string} KaisenHaishiRequestParams.lineNo
 * @param {string} KaisenHaishiRequestParams.mnpOutFlag
 * @param {string} [KaisenHaishiRequestParams.reserve_date]
 * @param {string} KaisenHaishiRequestParams.csvUnnecessaryFlag
 * @returns {Promise<{processCode:string,apiProcessID:string}>} The processCode and API Process ID
 */
const callAPIKaisenHaishi = async (
    context,
    { targetTenantId, targetSoId, lineNo, mnpOutFlag, reserve_date, csvUnnecessaryFlag }
) => {
    context.log('callAPIKaisenHaishi start');
    if (!targetTenantId) throw new Error('targetTenantId is required');
    if (!lineNo) throw new Error('lineNo is required');
    if (!mnpOutFlag) throw new Error('mnpOutFlag is required');
    // if (!reserve_date) throw new Error('reserve_date is required'); // optional
    // if (!targetSoId) throw new Error('targetSoId is required'); // v2
    if (!csvUnnecessaryFlag) throw new Error('csvUnnecessaryFlag is required');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.KAISEN_HAISHI.functionType),
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId,
        targetSoId,
        lineNo,
        mnpOutFlag,
        csvUnnecessaryFlag,
    };
    if (!isNone(reserve_date)) {
        jsonBody.reserve_date = reserve_date;
    }
    return await executeAPICallWithApiProcessID(
        context,
        jsonBody,
        'callAPIKaisenHaishi',
        CORE_API_LIST.KAISEN_HAISHI.url
    );
};

/**
 * @typedef LinePreAddRequestParams
 * @property {string} targetSoId
 * @property {string} targetTenantId
 * @property {string} nBan
 * @property {string} lineNo
 * @property {string} portalPlanID
 * @property {string} [id_intlRoaming]
 * @property {string} [id_voiceMail]
 * @property {string} [id_callWaiting]
 * @property {string} [id_intlCall]
 * @property {string} [id_forwarding]
 * @property {string} [id_intlForwarding]
 * @property {string} sim_no
 * @property {string} sim_type
 * @property {string} cardTypeId
 * @property {string} mnpInFlag
 * @property {string} csvUnnecessaryFlag
 * @property {string} [lineDelTenantId]
 * @property {string} reserve_date
 * @property {string} [imsi]
 * @property {string} [puk1]
 * @property {string} [puk2]
 * @property {string} [lineStatus]
 * @property {string} [access]
 */

/**
 * Make API call to `kariTourokuKaisenTsuikaAPI`
 * @param {object} context
 * @param {LinePreAddRequestParams} requestParam
 */
const callAPILinePreAdd = async (context, requestParam) => {
    context.log('callAPILinePreAdd start');
    if (!requestParam.targetTenantId) throw new Error('targetTenantId is required');
    if (!requestParam.nBan) throw new Error('nBan is required');
    if (!requestParam.lineNo) throw new Error('lineNo is required');
    if (!requestParam.portalPlanID) throw new Error('portalPlanID is required');
    if (!requestParam.sim_no) throw new Error('sim_no is required');
    if (!requestParam.sim_type) throw new Error('sim_type is required');
    if (!requestParam.cardTypeId) throw new Error('cardTypeId is required');
    if (!requestParam.mnpInFlag) throw new Error('mnpInFlag is required');
    // if (!requestParam.targetSoId) throw new Error('targetSoId is required');

    let jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.KARI_TOUROKU_KAISEN_TSUIKA.functionType),
        targetSoId: requestParam.targetSoId, // for Core API V2
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: requestParam.targetTenantId,
        nBan: requestParam.nBan,
        lineNo: requestParam.lineNo,
        potalPlanID: requestParam.portalPlanID, // "potal" で仕様上正しい。タイプミスではない。
        sim_no: requestParam.sim_no,
        sim_type: requestParam.sim_type,
        // BEGIN 4.0で追加
        cardTypeId: requestParam.cardTypeId,
        mnpInFlag: requestParam.mnpInFlag,
        csvUnnecessaryFlag: requestParam.csvUnnecessaryFlag,
        // END
    };

    if (requestParam.id_intlRoaming) {
        jsonBody.id_intlRoaming = requestParam.id_intlRoaming;
    }
    if (requestParam.id_voiceMail) {
        jsonBody.id_voicemail = requestParam.id_voiceMail; // request is in lower case
    }
    if (requestParam.id_callWaiting) {
        jsonBody.id_callWaiting = requestParam.id_callWaiting;
    }
    if (requestParam.id_intlCall) {
        jsonBody.id_intlCall = requestParam.id_intlCall;
    }
    if (requestParam.id_forwarding) {
        jsonBody.id_forwarding = requestParam.id_forwarding;
    }
    if (requestParam.id_intlForwarding) {
        jsonBody.id_intlForwarding = requestParam.id_intlForwarding;
    }

    if (requestParam.mnpInFlag === '2' && requestParam.lineDelTenantId) {
        jsonBody.lineDelTenantId = requestParam.lineDelTenantId;
    } else if (requestParam.reserve_date) {
        jsonBody.reserve_date = requestParam.reserve_date;
    }
    // STEP14 フルMVNOのパラメタを追加する
    if (requestParam.imsi) {
        jsonBody.imsi = requestParam.imsi;
    }
    if (requestParam.puk1) {
        jsonBody.puk1 = requestParam.puk1;
    }
    if (requestParam.puk2) {
        jsonBody.puk2 = requestParam.puk2;
    }
    if (requestParam.lineStatus) {
        jsonBody.lineStatus = requestParam.lineStatus;
    }
    // STEP18: 契約種別を追加
    if (requestParam.access) {
        jsonBody.access = requestParam.access;
    }

    return await executeAPICall(context, jsonBody, 'callAPILinePreAdd', CORE_API_LIST.KARI_TOUROKU_KAISEN_TSUIKA.url);
};

/**
 * @typedef SimSaihakkouRequestParams
 * @property {string} targetTenantId
 * @property {string} targetSoId
 * @property {string} lineNo
 * @property {string} sim_no
 * @property {string} sim_type
 * @property {string} cardTypeId
 * @property {string} csvUnnecessaryFlag
 */

/**
 *
 * @param {object} context
 * @param {SimSaihakkouRequestParams} requestParam
 */
const callLinesSim = async (context, requestParam) => {
    context.log('callLinesSim start');
    if (!requestParam.targetTenantId) throw new Error('targetTenantId is required');
    // if (!requestParam.targetSoId) throw new Error('targetSoId is required');
    if (!requestParam.lineNo) throw new Error('lineNo is required');
    if (!requestParam.sim_no) throw new Error('sim_no is required');
    if (!requestParam.sim_type) throw new Error('sim_type is required');
    if (!requestParam.cardTypeId) throw new Error('cardTypeId is required');
    if (!requestParam.csvUnnecessaryFlag) throw new Error('csvUnnecessaryFlag is required');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.SIM_SAIHAKKOU.functionType),
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: requestParam.targetTenantId,
        targetSoId: requestParam.targetSoId,
        lineNo: requestParam.lineNo,
        sim_no: requestParam.sim_no,
        sim_type: requestParam.sim_type,
        cardTypeId: requestParam.cardTypeId,
        csvUnnecessaryFlag: requestParam.csvUnnecessaryFlag,
    };

    return await executeAPICall(context, jsonBody, 'callLinesSim', CORE_API_LIST.SIM_SAIHAKKOU.url);
};

/**
 * @typedef RiyouChuudanSaikaiSuspendRequestParam
 * @property {string} targetTenantId
 * @property {string} targetSoId
 * @property {string} lineNo
 * @property {string} suspendFlag 利用状態変更フラグ
 */

/**
 *
 * @param {object} context
 * @param {RiyouChuudanSaikaiSuspendRequestParam} suspendParams
 */
const callAPIRiyouChuudanSaikaiSuspend = async (context, suspendParams) => {
    context.log('callAPIRiyouChuudanSaikaiSuspend start');
    if (!suspendParams.targetTenantId) throw new Error('targetTenantId is required');
    // if (!suspendParams.targetSoId) throw new Error('targetSoId is required');
    if (!suspendParams.lineNo) throw new Error('lineNo is required');
    if (!suspendParams.suspendFlag) throw new Error('suspendFlag is required');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.RIYOU_CHUUDAN_SAIKAI_SUSPEND.functionType),
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: suspendParams.targetTenantId,
        targetSoId: suspendParams.targetSoId,
        lineNo: suspendParams.lineNo,
        suspendFlag: suspendParams.suspendFlag,
    };
    return await executeAPICall(
        context,
        jsonBody,
        'callAPIRiyouChuudanSaikaiSuspend',
        CORE_API_LIST.RIYOU_CHUUDAN_SAIKAI_SUSPEND.url
    );
};

/**
 * @typedef LineEnableParams
 * @property {string} targetSoId
 * @property {string} targetTenantId
 * @property {string} lineNo
 * @property {string} csvUnnecessaryFlag
 * @property {string} sameMvneInFlag
 */

/**
 *
 * @param {object} context
 * @param {LineEnableParams} lineEnableParams
 */
const callAPILineEnable = async (context, lineEnableParams) => {
    context.log('callAPILineEnable start');
    if (!lineEnableParams.targetTenantId) throw new Error('targetTenantID is required');
    if (!lineEnableParams.lineNo) throw new Error('lineNo is required');
    if (!lineEnableParams.csvUnnecessaryFlag) throw new Error('csvUnnecessaryFlag is required');
    if (!lineEnableParams.sameMvneInFlag) throw new Error('sameMvneInFlag is required');
    // if (!lineEnableParams.targetSoId) throw new Error('targetSoId is required');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.KAISEN_KUROKA.functionType),
        targetSoId: lineEnableParams.targetSoId,
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: lineEnableParams.targetTenantId,
        lineNo: lineEnableParams.lineNo,
        csvUnnecessaryFlag: lineEnableParams.csvUnnecessaryFlag,
        sameMvneInFlag: lineEnableParams.sameMvneInFlag,
    };
    return await executeAPICall(context, jsonBody, 'callAPILineEnable', CORE_API_LIST.KAISEN_KUROKA.url);
};

const callAPICoreSoCancel = async (context, { tenantId, soId }) => {
    context.log('callAPICoreSoCancel start');
    if (isNone(tenantId)) throw new Error('tenantId is required');
    if (isNone(soId)) throw new Error('lineNo is required');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.SO_CANCEL.functionType, tenantId, context),
        tenantId,
        serviceOrderIdKey: soId,
    };
    return await executeAPICallWithResponseObject(
        context,
        jsonBody,
        'callAPICoreSoCancel',
        CORE_API_LIST.SO_CANCEL.url
    );
};

const callAPICoreLineUnyo = async (context, { tenantId, lineNo }) => {
    context.log('callAPICoreLineUnyo start', { tenantId, lineNo });
    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.LINE_UNYO.functionType, tenantId, context),
        tenantId,
        lineNo,
    };
    return await executeAPILineUnyoDetail(context, jsonBody, 'callAPICoreLineUnyo', CORE_API_LIST.LINE_UNYO.url);
};

/**
 * process response data from line unyo API
 * @param {object} context
 * @param {object} jsonBody
 * @param {string} executeAPIName
 * @param {string} url
 */
const executeAPILineUnyoDetail = async (context, jsonBody, executeAPIName, url) => {
    context.log('executeAPILineUnyoDetail start');
    const response = await executeAPICallWithResponseObject(context, jsonBody, executeAPIName, url);
    const responseH = response.responseHeader;
    const { processCode, apiProcessID } = responseH;
    const responseHeader = {
        processCode,
        apiProcessID,
        isCoreAPICalled: true,
    };
    // overwrite responseHeader and set default value for some keys if empty, else keep key/value pairs
    return {
        ...response,
        responseHeader,
        trafficThisMonth: isNone(response.trafficThisMonth) ? '0' : response.trafficThisMonth,
        trafficPreviousMonth: isNone(response.trafficPreviousMonth) ? '0' : response.trafficPreviousMonth,
        trafficBeforehandMonth: isNone(response.trafficBeforehandMonth) ? '0' : response.trafficBeforehandMonth,
    };
};

/**
 *
 * @param {object} context
 * @param {object} params
 * @param {string} params.tenantId
 * @param {string} params.lineNo
 * @param {string} params.couponOnOff
 * @param {string} params.potalPlanID
 * @param {string} params.optionPlanId
 * @param {string} [params.reserve_date]
 */
const callAPICoreLineCoupon = async (context, params) => {
    context.log('callAPICoreLineCoupon start');
    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.LINE_COUPON.functionType, params.tenantId, context),
        tenantId: params.tenantId,
        lineNo: params.lineNo,
        couponOnOff: params.couponOnOff,
        potalPlanID: params.potalPlanID,
        optionPlanId: params.optionPlanId,
        reserve_date: params.reserve_date,
    };
    return await executeAPICallWithResponseObject(
        context,
        jsonBody,
        'callAPICoreLineCoupon',
        CORE_API_LIST.LINE_COUPON.url
    );
};

/**
 *
 * @param {object} context
 * @param {string} tenantId
 * @param {string} lineNo
 * @param {string} potalPlanID
 * @param {string} optionPlanId
 * @param {string} reserve_date
 */
const callAPICoreLineCouponTsuika = async (context, { tenantId, lineNo, potalPlanID, optionPlanId, reserveDate }) => {
    context.log('callAPICoreLineCouponTsuika start');
    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.LINE_COUPON_TSUIKA.functionType, tenantId, context),
        tenantId: tenantId,
        lineNo: lineNo,
        potalPlanID: potalPlanID,
        optionPlanId: optionPlanId,
        reserve_date: reserveDate,
    };
    return await executeAPICallWithResponseObject(
        context,
        jsonBody,
        'callAPICoreLineCouponTsuika',
        CORE_API_LIST.LINE_COUPON_TSUIKA.url
    );
};

const callAPICoreLinePlanHenko = async (
    context,
    { lineNo, tenantID, potalPlanID_pre, potalPlanID, csvUnnecessaryFlag, reserveDate }
) => {
    context.log('call APIKaisenHenkoPlan start');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.LINE_PLAN_HENKO.functionType, tenantID, context),
        tenantId: tenantID,
        lineNo,
        potalPlanID_pre,
        potalPlanID,
        csvUnnecessaryFlag,
        reserve_date: reserveDate,
    };
    return await executeAPICallWithResponseObject(
        context,
        jsonBody,
        'callAPIKaisenHenkoPlan',
        CORE_API_LIST.LINE_PLAN_HENKO.url
    );
};

const callAPIKaisenActivation = async (context, { tenantId, lineNo, status, reserve_date }) => {
    context.log('callAPIKaisenActivation start');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.KAISEN_ACTIVATE.functionType, tenantId, context),
        tenantId,
        lineNo,
        status,
        reserve_date,
    };

    return await executeAPICallWithResponseObject(
        context,
        jsonBody,
        'callAPIKaisenActivation',
        CORE_API_LIST.KAISEN_ACTIVATE.url
    );
};
/**
 * @typedef NetworkPasswordUpdateRequestParams
 * @property {string} targetTenantId
 * @property {string} targetSoId
 * @property {string} lineNo
 * @property {string} pinChange
 * @property {string} [intlRoaming]
 * @property {string} [voiceMail]
 * @property {string} [callWaiting]
 * @property {string} [intlCall]
 * @property {string} [forwarding]
 * @property {string} [intlForwarding]
 * @property {string} csvUnnecessaryFlag
 */

/**
 *
 * @param {object} context
 * @param {NetworkPasswordUpdateRequestParams} networkPasswordUpdateParams
 */
const callAPINetworkPasswordUpdate = async (context, networkPasswordUpdateParams) => {
    context.log('callAPINetworkPasswordUpdate start');
    if (!networkPasswordUpdateParams.targetTenantId) throw new Error('targetTenantId is required');
    // if (!networkPasswordUpdateParams.targetSoId) throw new Error('targetSoId is required');
    if (!networkPasswordUpdateParams.lineNo) throw new Error('lineNo is required');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.NETWORK_PASSWORD_CHANGE.functionType),
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: networkPasswordUpdateParams.targetTenantId,
        targetSoId: networkPasswordUpdateParams.targetSoId,
        lineNo: networkPasswordUpdateParams.lineNo,
        pinChange: '1',
        intlRoaming: getLineOptionRequestParam(null),
        voicemail: getLineOptionRequestParam(null),
        callWaiting: getLineOptionRequestParam(null),
        intlCall: getLineOptionRequestParam(null),
        forwarding: getLineOptionRequestParam(null),
        intlForwarding: getLineOptionRequestParam(null),
        csvUnnecessaryFlag: '0',
    };
    return await executeAPICall(
        context,
        jsonBody,
        'callAPINetworkPasswordUpdate',
        CORE_API_LIST.NETWORK_PASSWORD_CHANGE.url
    );
};

/**
 * @typedef LineOptionUpdateRequestParams
 * @property {string} targetTenantId
 * @property {string} targetSoId
 * @property {string} lineNo
 * @property {KaisenOptionUpdateInfo} [intlRoaming]
 * @property {KaisenOptionUpdateInfo} [voiceMail]
 * @property {KaisenOptionUpdateInfo} [callWaiting]
 * @property {KaisenOptionUpdateInfo} [intlCall]
 * @property {KaisenOptionUpdateInfo} [forwarding]
 * @property {KaisenOptionUpdateInfo} [intlForwarding]
 */

/**
 *
 * @param {object} context
 * @param {LineOptionUpdateRequestParams} requestParam
 */
const callAPILineOptionUpdate = async (context, requestParam) => {
    context.log('callAPILineOptionUpdate start');
    if (!requestParam.targetTenantId) throw new Error('target tenant ID is required');
    // if (!requestParam.targetSoId) throw new Error('targetSoId is required');
    if (!requestParam.lineNo) throw new Error('line no is required');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.LINE_OPTION_CHANGE.functionType),
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: requestParam.targetTenantId,
        targetSoId: requestParam.targetSoId,
        lineNo: requestParam.lineNo,
        pinChange: '0',
        intlRoaming: getLineOptionRequestParam(requestParam.intlRoaming),
        voicemail: getLineOptionRequestParam(requestParam.voiceMail),
        callWaiting: getLineOptionRequestParam(requestParam.callWaiting),
        intlCall: getLineOptionRequestParam(requestParam.intlCall),
        forwarding: getLineOptionRequestParam(requestParam.forwarding),
        intlForwarding: getLineOptionRequestParam(requestParam.intlForwarding),
        csvUnnecessaryFlag: '0',
    };
    return await executeAPICall(context, jsonBody, 'callAPILineOptionUpdate', CORE_API_LIST.LINE_OPTION_CHANGE.url);
};

/**
 * @typedef NwContractChangeRequestParams
 * @property {string} targetTenantId
 * @property {string} targetSoId
 * @property {string} lineNo
 * @property {string} beforeContractType
 * @property {string} newContractType
 * @property {string} cardTypeId
 * @property {string} csvUnnecessaryFlag
 */

/**
 *
 * @param {object} context
 * @param {NwContractChangeRequestParams} requestParam
 */
const callAPINwContractChange = async (context, requestParam) => {
    context.log('callAPINwContractChange start');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.NW_CONTRACT_CHANGE.functionType),
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: requestParam.targetTenantId,
        targetSoId: requestParam.targetSoId,
        lineNo: requestParam.lineNo,
        access_pre: requestParam.beforeContractType,
        access: requestParam.newContractType,
        cardTypeId: requestParam.cardTypeId,
        csvUnnecessaryFlag: requestParam.csvUnnecessaryFlag,
    };
    return await executeAPICall(context, jsonBody, 'callAPINwContractChange', CORE_API_LIST.NW_CONTRACT_CHANGE.url);
};

/**
 * @typedef VoicePlanChangeRequestParams
 * @property {string} targetSoId
 * @property {string} targetTenantId
 * @property {string} lineNo
 * @property {string} voicePlanId
 */

/**
 *
 * @param {object} context
 * @param {VoicePlanChangeRequestParams} requestParam
 * @returns {Promise<string>} processCode
 */

const callAPIVoicePlanChange = async (context, requestParam) => {
    context.log('callAPIVoicePlanChange start');

    const jsonBody = {
        requestHeader: await commonRequestHeader(CORE_API_LIST.VOICE_PLAN_CHANGE.functionType),
        tenantId: portalConfig.CORE_SERVICE.OPF_TENANTID,
        targetTenantId: requestParam.targetTenantId,
        lineNo: requestParam.lineNo,
        voicePlanId: requestParam.voicePlanId,
        targetSoId: requestParam.targetSoId,
    };
    return await executeAPICall(context, jsonBody, 'callAPIVoicePlanChange', CORE_API_LIST.VOICE_PLAN_CHANGE.url);
};

const mockCoreAPI = (context, instanceCoreService) => {
    if (portalConfig.CORE_SERVICE.RUN_WITH_MOCK || portalConfig.CORE_SERVICE.RUN_WITH_MOCK === 'true') {
        context.log('mockCoreAPI start');
        // Mock mos api
        var MockAdapter = require('axios-mock-adapter');
        // This sets the mock adapter on the instanceCoreService
        var mockCoreAPI = new MockAdapter(instanceCoreService);

        //TODO: change mock response here
        /* It's mocking the response from the front cancel API. */
        mockCoreAPI.onPost(CORE_API_LIST.SO_FRONT_CANCEL.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.KAISEN_HAISHI.url).reply(200, {
            responseHeader: {
                processCode: '000000',
                apiProcessID: `AP0900${_.random(1000000, 9999999, false)}`,
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.KARI_TOUROKU_KAISEN_TSUIKA.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.SIM_SAIHAKKOU.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.RIYOU_CHUUDAN_SAIKAI_SUSPEND.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.KAISEN_KUROKA.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.SO_CANCEL.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });
        mockCoreAPI.onPost(CORE_API_LIST.LINE_COUPON.url).reply(200, {
            responseHeader: {
                processCode: '000000',
                apiProcessID: `AP0900${_.random(1000000, 9999999, false)}`,
            },
        });
        mockCoreAPI.onPost(CORE_API_LIST.LINE_PLAN_HENKO.url).reply(200, {
            responseHeader: {
                processCode: '000000',
                apiProcessID: `AP0900${_.random(1000000, 9999999, false)}`,
            },
        });
        mockCoreAPI.onPost(CORE_API_LIST.LINE_UNYO.url).reply(200, {
            responseHeader: {
                sequenceNo: 'G352464',
                receivedDate: '2018/06/19 18:23:29',
                processCode: '000000',
                apiProcessID: `AP0900${_.random(1000000, 9999999, false)}`,
            },
            trafficOneDay: '676357',
            traffic1dayAgo: '1666680',
            traffic2daysAgo: '5355751',
            traffic3daysAgo: '828849',
            traffic4daysAgo: '910575',
            traffic5daysAgo: '994129',
            traffic6daysAgo: '7796097',
            traffic7daysAgo: '2692273',
            traffic8daysAgo: '11128554',
            traffic9daysAgo: '1282425',
            traffic10daysAgo: '69790756',
            traffic11daysAgo: '159720',
            traffic12daysAgo: '826825',
            traffic13daysAgo: '1527317',
            traffic14daysAgo: '1071593',
            traffic15daysAgo: '708637',
            traffic16daysAgo: '263182',
            traffic17daysAgo: '1142958',
            traffic18daysAgo: '953704',
            traffic19daysAgo: '1467592',
            traffic20daysAgo: '987153',
            traffic21daysAgo: '648272',
            traffic22daysAgo: '687820',
            traffic23daysAgo: '1127840',
            traffic24daysAgo: '6748273',
            traffic25daysAgo: '10595060',
            traffic26daysAgo: '7238248',
            traffic27daysAgo: '4565234',
            traffic28daysAgo: '3261278',
            traffic29daysAgo: '6318507',
            traffic30daysAgo: '9274037',
            trafficThisMonth: '7698788',
            trafficPreviousMonth: '269375370',
            trafficBeforehandMonth: '307635097',
            basicCouponRemains: '2160000',
            basicCouponTermValidity: '2013/12/31',
            couponPieceTime: '2014/03/20:16:00',
            carryingOverCoupon1: {
                carryingOverCoupon1RemainCapacity: '500000',
                carryingOverCoupon1TermValidity: '2013/12/31',
            },
            carryingOverCoupon2: {
                carryingOverCoupon2RemainCapacity: '500000',
                carryingOverCoupon2TermValidity: '2013/12/31',
            },
            additionalCoupon1: {
                additionalCoupon1RemainCapacity: '500000',
                additionalCoupon1TermValidity: '2013/12/31',
            },
            additionalCoupon2: {
                additionalCoupon2RemainCapacity: '500000',
                additionalCoupon2TermValidity: '2013/12/31',
            },
            additionalCoupon3: {
                additionalCoupon3RemainCapacity: '500000',
                additionalCoupon3TermValidity: '2013/12/31',
            },
            additionalCoupon4: {
                additionalCoupon4RemainCapacity: '500000',
                additionalCoupon4TermValidity: '2013/12/31',
            },
            additionalCoupon5: {
                additionalCoupon5RemainCapacity: '500000',
                additionalCoupon5TermValidity: '2013/12/31',
            },
            couponOnOff: '1',
            heavyUserMonitorStatus: '03',
            heavyUserMonitorStartTimeMonth: '2013/12/31:20:30',
            heavyUserMonitorStartTimeLatest: '2013/12/31:20:30',
            prepaidTermValidity: '2013/12/31',
            activateStatus: '1',
            regulationCause: '12',
            totalVolumeControlStatus: '1',
            lineGroupId: '1234567',
        });
        mockCoreAPI.onPost(CORE_API_LIST.KAISEN_ACTIVATE.url).reply(200, {
            responseHeader: {
                processCode: '000000',
                apiProcessID: `AP0900${_.random(1000000, 9999999, false)}`,
            },
        });
        mockCoreAPI.onPost(CORE_API_LIST.LINE_COUPON_TSUIKA.url).reply(200, {
            responseHeader: {
                processCode: '000000',
                apiProcessID: `AP0900${_.random(1000000, 9999999, false)}`,
            },
        });
        mockCoreAPI.onPost(CORE_API_LIST.NETWORK_PASSWORD_CHANGE.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.LINE_OPTION_CHANGE.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.NW_CONTRACT_CHANGE.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });

        mockCoreAPI.onPost(CORE_API_LIST.VOICE_PLAN_CHANGE.url).reply(200, {
            responseHeader: {
                processCode: '000000',
            },
        });
    }
};

module.exports = {
    CORE_API_LIST,
    getSeqNoAndAPIKey,
    callAPISOFrontCancel,
    callAPIKaisenHaishi,
    callAPILinePreAdd,
    callLinesSim,
    callAPIRiyouChuudanSaikaiSuspend,
    callAPILineEnable,
    callAPICoreSoCancel,
    callAPICoreLinePlanHenko,
    callAPICoreLineCoupon,
    callAPICoreLineCouponTsuika,
    callAPICoreLineUnyo,
    callAPIKaisenActivation,
    callAPINetworkPasswordUpdate,
    callAPILineOptionUpdate,
    callAPINwContractChange,
    callAPIVoicePlanChange,
};
