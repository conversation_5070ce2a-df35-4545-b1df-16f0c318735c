const appConfig = require('../config/appConfig');
const { NotificationType } = require('../constants/notificationType');
const { getRedisClient } = require('../database/redis');
const NotificationService = require('./notification.service');
const portalConfig = appConfig.getPortalConfig();

const SWIMMY_VLM_TOKEN_KEY_PREFIX = 'swimmyVLMToken';
const HAS_ERROR_0035_KEY = '2_hasError0035';
const HAS_CORE_SWIMMY_ERROR_KEY = '3_hasErrorCoreSwimmy';

/**
 * It sets the swimmyVLMToken in redis and sets the expiration time to be the time when the token
 * expires minus the time before the token expires that we want to check for a new token
 * @param context - The context object that is passed to the function.
 * @param tenantId
 * @param {import('./swimmyVLM.service').AccessToken} swimmyVLMToken
 * @returns The accessToken
 */

const setSwimmyVLMToken = async (context, tenantId, swimmyVLMToken) => {
    const redis = await getRedisClient(context);
    await redis.hmset(SWIMMY_VLM_TOKEN_KEY_PREFIX + '_' + tenantId, swimmyVLMToken);
    const expireAfterSeconds = swimmyVLMToken.expiresIn - portalConfig.SWIMMY_VLM_API.OAUTH.EXPIRES_TIME_CHECK_BEFORE;
    await redis.expire(SWIMMY_VLM_TOKEN_KEY_PREFIX + '_' + tenantId, expireAfterSeconds);
    return swimmyVLMToken.accessToken;
};

/**
 * > This function gets the access token from Redis
 * @param context - The context object that is passed to the function.
 * @param tenantId - The tenant ID of the tenant you want to get the token for.
 * @returns The access token for the tenant.
 */
const getSwimmyVLMToken = async (context, tenantId) => {
    const redis = await getRedisClient(context);
    const accessToken = await redis.hget(SWIMMY_VLM_TOKEN_KEY_PREFIX + '_' + tenantId, 'accessToken');
    return accessToken;
};

/**
 * The function `getHasError0035` retrieves the value of `hasError0035` from a Redis client.
 * @param context - The `context` parameter is an object that contains any additional information or
 * dependencies that the `getHasError0035` function may need to execute its logic. It could include
 * things like configuration settings, database connections, or other context-specific data.
 * @returns The function `getHasError0035` returns the value of the `hasError0035` variable, which is
 * retrieved from a Redis client using the `redis.get` method.
 */
const getHasError0035 = async (context) => {
    const redis = await getRedisClient(context);
    const hasError0035 = await redis.get(HAS_ERROR_0035_KEY);
    if (hasError0035 === null || hasError0035 === undefined) {
        await setHasError0035(context, false);
        return false;
    }
    return hasError0035;
};

const setHasError0035 = async (context, hasError0035) => {
    const redis = await getRedisClient(context);
    await redis.set(HAS_ERROR_0035_KEY, hasError0035);
    await NotificationService.sendMessage(context, NotificationType.HAS_0035_ERROR, hasError0035);
};

const getHasCoreSwimmyError = async (context) => {
    const redis = await getRedisClient(context);
    const hasCoreSwimmyError = await redis.get(HAS_CORE_SWIMMY_ERROR_KEY);
    if (hasCoreSwimmyError === null || hasCoreSwimmyError === undefined) {
        await setHasCoreSwimmyError(context, false);
        return false;
    }
    return hasCoreSwimmyError;
};

const setHasCoreSwimmyError = async (context, hasCoreSwimmyError) => {
    const redis = await getRedisClient(context);
    await redis.set(HAS_CORE_SWIMMY_ERROR_KEY, hasCoreSwimmyError);
    await NotificationService.sendMessage(context, NotificationType.HAS_CORE_SWIMMY_ERROR, hasCoreSwimmyError);
};

module.exports = {
    setSwimmyVLMToken,
    getSwimmyVLMToken,
    getHasError0035,
    setHasError0035,
    getHasCoreSwimmyError,
    setHasCoreSwimmyError,
};
