const axios = require('axios');
const { ServiceBusAdministrationClient } = require('@azure/service-bus');
const dayjs = require('dayjs');
const crypto = require('crypto');
const https = require('https');

const appConfig = require('../config/appConfig');
const { SWIMMY_REQUEST_TYPES, SWIMMY_API_LOG_STATUS, SWIMMY_API_HTTP } = require('../constants/swimmyTransactions');
const { isNone } = require('../helpers/baseHelper');
const { getSwimmyApiPriority } = require('../helpers/swimmyHelper');

const SwimmyApiLogModel = require('../models/swimmyService/swimmyApiLog.model');
const SwimmyApiRestrictSettingModel = require('../models/swimmyService/swimmyApiRestrictSetting.model');

const portalConfig = appConfig.getPortalConfig();

const serviceBusAdministrationClient = portalConfig.SWIMMY_API.POLLING.SERVICE_BUS_CONNECTION_STRING
    ? new ServiceBusAdministrationClient(portalConfig.SWIMMY_API.POLLING.SERVICE_BUS_CONNECTION_STRING)
    : {};

const SWIMMY_HTTP_RESPONSE_CODES_OK = [200, 400, 409, 500, 503];

/**
 * Save to `SwimmyApiLogModel`
 *
 * **_Note_**: Need to call `updateKouteiForRegisterSwimmyTransaction` before calling this
 * function for Swimmy types which not related to notification
 * @param context - The context object that the function receives. The context object provides methods
 * @param {object} data - The data that will be saved
 * @param {string} data.tenantId - The tenantId
 * @param {string} data.swimmyType - The swimmyType, one of the following: SWIMMY_REQUEST_TYPES (swimmyTransactions)
 * @param {string} data.requestOrderId - The requestOrderId
 * @param {string} data.lineNo - The lineNo
 * @param [sessionId=null] - This is the sessionId that will be used to group the messages (for test)
 */
const registerSwimmyTransaction = async (
    context,
    {
        tenantId,
        swimmyType,
        requestOrderId,
        lineNo,
        soKind = null,
        daihyouBango = null,
        transactionId = null,
        resultCode = null,
        errMessageId = null,
        errMessage = null,
        mnpOutDate = null,
        appDate = null,
        salesChannelCode = null,
        simNo = null,
        notifyHost = null,
        processId = null,
        aladinSoNGReason = null,
        otherSystemSendDateTime = null,
        dummyLineNo = null,
        eId = null,
        notifyPattern = null,
        shippingNumber = null,
        swimmyResultCode = null,
    },
    sessionId = null
) => {
    if (!tenantId) throw new Error('tenantId is required');
    if (!requestOrderId) throw new Error('requestOrderId is required');
    if (!swimmyType) throw new Error('swimmyType is required');
    if (lineNo === undefined || lineNo === null) throw new Error('lineNo is required'); // allow empty string lineNo
    // if swimmyType is not valid, throw error
    if (!Object.values(SWIMMY_REQUEST_TYPES).includes(swimmyType)) throw new Error('Invalid swimmyType');

    // STEP25 不要機能の削除: skip creating OTA結果通知 / eSIM結果通知
    if ([SWIMMY_REQUEST_TYPES.OTA_KEKKA_TSUCHI, SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI].includes(swimmyType)) {
        context.log('registerSwimmyTransaction: skip OTA/ESIM結果通知: ', swimmyType, requestOrderId);
        return requestOrderId;
    }

    const priority = getSwimmyApiPriority(context, swimmyType);

    const message = {
        body: {
            tenantId,
            swimmyType,
            requestOrderId,
            lineNo,
            soKind,
            daihyouBango,
            transactionId,
            resultCode,
            errMessageId,
            errMessage,
            mnpOutDate,
            appDate,
            salesChannelCode,
            simNo,
            notifyHost,
            processId,
            aladinSoNGReason,
            otherSystemSendDateTime,
            dummyLineNo,
            eId,
            notifyPattern,
            shippingNumber,
            swimmyResultCode,
        },
        sessionId,
    };

    return await SwimmyApiLogModel.createSwimmyApiLog(context, {
        status: SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_NOT_YET_SENT,
        priority,
        ...message.body,
    });
};

/**
 * The function checks if a service bus queue is in maintenance mode.
 * @returns The function `isMaintenanceMode` returns a boolean value indicating whether the service bus
 * queue status is not "Active", which would mean that the system is in maintenance mode.
 */
const isMaintenanceMode = async () => {
    const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
        portalConfig.SWIMMY_API.POLLING.SERVICE_BUS_QUEUE_NAME
    );
    const isMaintenance = !['Active'].includes(apiRequestsQueue.status);
    return isMaintenance;
};

/**
 * The function toggles the maintenance mode of a service bus queue and returns a boolean indicating if
 * the queue is active or not.
 * @param context - The context object that is passed to the function.
 * @param isMaintenance - isMaintenance is a boolean parameter that indicates whether the system is in
 * maintenance mode or not. If it is set to true, the system will be put into maintenance mode and the
 * service bus queue will be disabled. If it is set to false, the system will be taken out of
 * maintenance mode and the
 * @returns a boolean value that indicates whether the status of a service bus queue has been
 * successfully updated to either 'Disabled' or 'Active' based on the value of the `isMaintenance`
 * parameter passed to the function. If the status has been updated to 'Disabled' during maintenance
 * mode, the function returns `true`. Otherwise, if the status has been updated to 'Active' after
 * maintenance
 */
const toggleMaintenanceMode = async (context, isMaintenance) => {
    const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
        portalConfig.SWIMMY_API.POLLING.SERVICE_BUS_QUEUE_NAME
    );
    apiRequestsQueue.status = isMaintenance ? 'Disabled' : 'Active';

    await serviceBusAdministrationClient.updateQueue(apiRequestsQueue);
    await SwimmyApiRestrictSettingModel.setMaintenanceMode(context, isMaintenance);

    const maintenanceText = isMaintenance ? '「ON」 になりました。' : '「OFF」 になりました。';

    context.log.error('Swimmy-APIメンテナンス：', maintenanceText);

    return !['Active'].includes(apiRequestsQueue.status);
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyRequestParam} requestParam SendAladinKekkaTsuChiRequestParams
 */
const callAPIAladinKekkaTsuchi = async (context, requestParam) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const { status, data } = await executeAPICall(
            context,
            apiConfig,
            requestParam,
            'APIAladinKekkaTsuchiToSwimmy',
            apiConfig.API_SWIMMY_ALADIN_KEKKA_API_INFO
        );

        const { resultCode, errorCode } = getResultCodeAndErrorCode(data);
        result.resultCode = resultCode;
        result.errorCode = errorCode;

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPIAladinKekkaTsuchi error:', error);
    }
    return { isOk, result };
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyRequestParam} requestParam SendMNPTenshutsuKanryouTsuChiRequestParams
 */
const callAPIMNPTenshutsuKanryouTsuchi = async (context, requestParam) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const { status, data } = await executeAPICall(
            context,
            apiConfig,
            requestParam,
            'APIMNPTenshutsuKanryouTsuchi',
            apiConfig.API_SWIMMY_MNP_TENSHUTSU_KANRYOU_TSUCHI_API_INFO
        );

        const { resultCode, errorCode } = getResultCodeAndErrorCode(data);
        result.resultCode = resultCode;
        result.errorCode = errorCode;

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPIMNPTenshutsuKanryouTsuchi error:', error);
    }
    return { isOk, result };
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyCommonRequestParams} requestParam SwimmyRequestParams
 * @param {string} swimmyType requestOrderType
 */
const callAPIMoushikomiHenkou = async (context, requestParam, swimmyType) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        receiptId: '',
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const { status, data } = await executeAPICall(
            context,
            apiConfig,
            requestParam,
            `APIMoshikomiHenkou[${swimmyType}]`,
            apiConfig.API_SWIMMY_MOUSHIKOMI_HENKOU_API_INFO
        );

        if (!isNone(data?.appResponseInfo)) {
            const appResponseInfo = data.appResponseInfo;
            result.resultCode = appResponseInfo.resultCode ?? '';
            if (!isNone(appResponseInfo.appInfoList) && Array.isArray(appResponseInfo.appInfoList)) {
                result.receiptId = appResponseInfo.appInfoList.at(0)?.appBasic?.receiptId ?? '';

                if (!isNone(appResponseInfo.appInfoList.at(0)?.checkErrorInfo)) {
                    const errorInfoList = appResponseInfo.appInfoList.at(0).checkErrorInfo.errorInfoList;
                    if (!isNone(errorInfoList) && Array.isArray(errorInfoList)) {
                        result.errorCode = errorInfoList.at(0)?.messageId;
                    }
                }
            }
        }

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPIMoushikomiHenkou error:', error);
    }
    return { isOk, result };
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyRequestParam} requestParam SendKurokaKanryouTsuChiRequestParams
 * @param {string} notifyHost
 */
const callAPIKurokaKanryouTsuchi = async (context, requestParam, notifyHost) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const url = `${SWIMMY_API_HTTP}${notifyHost}${apiConfig.API_SWIMMY_KUROKA_KANRYOU_TSUCHI_API_INFO}`;
        const { status, data } = await executeAPICall(context, apiConfig, requestParam, 'APIKurokaKanryouTsuchi', url);
        const { resultCode, errorCode } = getResultCodeAndErrorCode(data);
        result.resultCode = resultCode;
        result.errorCode = errorCode;

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPIKurokaKanryouTsuchi error:', error);
    }
    return { isOk, result };
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyRequestParam} requestParam SendOTAKekkaTsuChiRequestParams
 */
const callAPIOTAKekkaTsuchi = async (context, requestParam) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const { status, data } = await executeAPICall(
            context,
            apiConfig,
            requestParam,
            'APIOTAKekkaTsuchi',
            apiConfig.API_SWIMMY_OTA_KEKKA_TSUCHI_API_INFO
        );

        const { resultCode, errorCode } = getResultCodeAndErrorCode(data);
        result.resultCode = resultCode;
        result.errorCode = errorCode;

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPIOTAKekkaTsuchi error:', error);
    }
    return { isOk, result };
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyRequestParam} requestParam SendKaisenOptionHenkouKekkaTsuChiRequestParams
 */
const callAPIKaisenOptionHenkouKekkaTsuchi = async (context, requestParam) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const { status, data } = await executeAPICall(
            context,
            apiConfig,
            requestParam,
            'APIKaisenOptionHenkouKekkaTsuchi',
            apiConfig.API_SWIMMY_KAISEN_OPTION_HENKOU_KEKKA_TSUCHI_API_INFO
        );

        const { resultCode, errorCode } = getResultCodeAndErrorCode(data);
        result.resultCode = resultCode;
        result.errorCode = errorCode;

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPIKaisenOptionHenkouKekkaTsuchi error:', error);
    }
    return { isOk, result };
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyRequestParam} requestParam SendSIMSaihakkouKekkaTsuChiRequestParams
 */
const callAPISIMSaihakkouKekkaTsuchi = async (context, requestParam) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const { status, data } = await executeAPICall(
            context,
            apiConfig,
            requestParam,
            'APISIMSaihakkouKekkaTsuchi',
            apiConfig.API_SWIMMY_SIM_SAIHAKKOU_KEKKA_TSUCHI_API_INFO
        );

        const { resultCode, errorCode } = getResultCodeAndErrorCode(data);
        result.resultCode = resultCode;
        result.errorCode = errorCode;

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPISIMSaihakkouKekkaTsuchi error:', error);
    }
    return { isOk, result };
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyRequestParam} requestParam SendESIMKekkaTsuChiSwimmyV2RequestParams
 */
const callAPIESIMKekkaTsuchiSwimmyV2 = async (context, requestParam) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const { status, data } = await executeAPICall(
            context,
            apiConfig,
            requestParam,
            'APIESIMKekkaTsuchiSwimmmyV2',
            apiConfig.API_SWIMMY_ESIM_KEKKA_TSUCHI_V2_API_INFO
        );

        const { resultCode, errorCode } = getResultCodeAndErrorCode(data);
        result.resultCode = resultCode;
        result.errorCode = errorCode;

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPIESIMKekkaTsuchiSwimmyV2 error:', error);
    }
    return { isOk, result };
};

/**
 *
 * @param {object} context
 * @param {import('../helpers/swimmyRequestResponseHelper').SwimmyRequestParam} requestParam SendESIMKekkaTsuChiSwimmyV3RequestParams
 */
const callAPIESIMKekkaTsuchiSwimmyV3 = async (context, requestParam) => {
    let isOk = false;
    /** @type {import('../helpers/swimmyRequestResponseHelper').SwimmyResponseCommonParams} */
    const result = {
        httpStatusCode: 0,
    };
    try {
        const apiConfig = await appConfig.getAPIConfig(context);
        const { status, data } = await executeAPICall(
            context,
            apiConfig,
            requestParam,
            'APIESIMKekkaTsuchiSwimmmyV3',
            apiConfig.API_SWIMMY_ESIM_KEKKA_TSUCHI_V3_API_INFO
        );

        const { resultCode, errorCode } = getResultCodeAndErrorCode(data);
        result.resultCode = resultCode;
        result.errorCode = errorCode;

        result.responseParams = data;
        result.httpStatusCode = status;
        isOk = true;
    } catch (error) {
        context.log.error('callAPIESIMKekkaTsuchiSwimmyV3 error:', error);
    }
    return { isOk, result };
};

/**
 * Get result code and error code for standard response object
 * (which has commonHeaderInfo and errorInfoList)
 * @param {object} data
 * @returns {{resultCode: string|undefined, errorCode: string|undefined}}
 */
const getResultCodeAndErrorCode = (data) => {
    let resultCode, errorCode;
    const headerInfo = data?.commonHeaderInfo;
    if (!isNone(headerInfo?.resultCode)) {
        resultCode = headerInfo.resultCode;
    }
    if (!isNone(data?.errorInfoList) && Array.isArray(data.errorInfoList)) {
        errorCode = data.errorInfoList.at(0).messageId;
    }
    return { resultCode, errorCode };
};

/**
 * It takes a JSON body, an API name, and a URL, and then it calls the API using the Axios library
 * @param {*} context - The context object that is passed to the function.
 * @param {object} apiConfig - API config object
 * @param {*} jsonBody - The JSON body of the request.
 * @param {string} executeAPIName - The name of the API you're calling.
 * @param {string} url - The URL of the API to call (absolute path)
 * @returns {Promise<object>} response object
 */
const executeAPICall = async (context, apiConfig, jsonBody, executeAPIName, url) => {
    context.log('Swimmy executeAPICall:', executeAPIName);
    context.log('Swimmy jsonBody:', jsonBody);
    context.log('Swimmy jsonBody stringify:', JSON.stringify(jsonBody));
    const instanceSwimmyService = await getSwimmyServiceClient(context, apiConfig);

    try {
        const { status, data } = await instanceSwimmyService.post(url, jsonBody);
        if (!SWIMMY_HTTP_RESPONSE_CODES_OK.includes(status)) {
            throw new Error('Swimmy executeAPICall:', executeAPIName, 'failed with status:', status);
        }
        context.log('Swimmy executeAPICall:', executeAPIName, 'response:', data);
        return { status, data };
    } catch (error) {
        // handle non 2xx response code
        const status = error?.response?.status;
        if (status !== undefined) {
            if (!SWIMMY_HTTP_RESPONSE_CODES_OK.includes(status)) {
                throw new Error('Swimmy executeAPICall:', executeAPIName, 'failed with status:', status);
            }
            const data = error?.response?.data;
            context.log('Swimmy executeAPICall:', executeAPIName, 'status:', status, 'response:', data);
            return { status, data };
        } else {
            // rethrow error for something else (not AxiosError)
            throw error;
        }
    }
};
const allowLegacyRenegotiationforNodeJsOptions = {
    httpsAgent: new https.Agent({
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
    }),
};

/**
 *
 * @param {object} context - The context object that is passed to the function.
 * @param {object} apiConfig
 * @returns {axios.AxiosInstance} A function that returns a promise that resolves to an axios client.
 */
const getSwimmyServiceClient = async (context, apiConfig) => {
    const instanceSwimmyService = axios.create({
        ...allowLegacyRenegotiationforNodeJsOptions,
        headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
        },
    });

    mockSwimmyAPI(context, instanceSwimmyService, apiConfig);

    return instanceSwimmyService;
};

const mockSwimmyAPI = (context, instanceSwimmyService, apiConfig) => {
    if (portalConfig.SWIMMY_SERVICE.RUN_WITH_MOCK || portalConfig.SWIMMY_SERVICE.RUN_WITH_MOCK === 'true') {
        const MockAdapter = require('axios-mock-adapter');
        const mockSwimmyAPI = new MockAdapter(instanceSwimmyService);

        // mock response here
        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_ALADIN_KEKKA_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20191114102010',
                systemReceiptTime: '20191114102505',
                resultCode: '00',
            },
            errorInfoList: [
                {
                    messageId: '00001',
                    message: '',
                },
            ],
        });

        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_MNP_TENSHUTSU_KANRYOU_TSUCHI_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20191114102010',
                systemReceiptTime: '20191114102505',
                resultCode: '00',
            },
            errorInfoList: [
                {
                    messageId: '00001',
                    message: '',
                },
            ],
        });

        const rdString = dayjs().format('YYMMDDHHmmss');
        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_MOUSHIKOMI_HENKOU_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20191114102010',
                systemReceiptTime: '20191114102505',
                operatorId: 'SHIBA',
            },
            appResponseInfo: {
                processType: '01',
                resultCode: '0001',
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: `UK0${rdString}`,
                            inputFromType: '01',
                            salesChannelCode: '784947',
                            salesChannelName: '*',
                            appBasicDtlList: [
                                {
                                    appAttributeValue: 'mock',
                                    appAttributeCode: 'mock',
                                },
                            ],
                        },
                        checkErrorInfo: {
                            errorInfoList: [
                                {
                                    fieldInfoList: {},
                                    messageId: '',
                                },
                            ],
                        },
                    },
                ],
            },
        });

        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_KUROKA_KANRYOU_TSUCHI_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20220201123022',
                systemReceiptTime: '20220201123346',
                resultCode: '00',
            },
            errorInfoList: [
                {
                    messageId: '00001',
                    message: '',
                },
            ],
        });

        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_OTA_KEKKA_TSUCHI_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20220413125006',
                systemReceiptTime: '20220413125510',
                resultCode: '00',
            },
            errorInfoList: [
                {
                    messageId: '00001',
                    message: '',
                },
            ],
        });

        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_KAISEN_OPTION_HENKOU_KEKKA_TSUCHI_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20220801130050',
                systemReceiptTime: '20220801130526',
                resultCode: '00',
            },
            errorInfoList: [
                {
                    messageId: '00001',
                    message: '',
                },
            ],
        });

        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_SIM_SAIHAKKOU_KEKKA_TSUCHI_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20220803122030',
                systemReceiptTime: '20220803122551',
                resultCode: '00',
            },
            errorInfoList: [
                {
                    messageId: '00001',
                    message: '',
                },
            ],
        });

        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_ESIM_KEKKA_TSUCHI_V2_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20221129112506',
                systemReceiptTime: '20221129112813',
                resultCode: '00',
            },
            errorInfoList: [
                {
                    messageId: '00001',
                    message: '',
                },
            ],
        });

        mockSwimmyAPI.onPost(apiConfig.API_SWIMMY_ESIM_KEKKA_TSUCHI_V3_API_INFO).reply(200, {
            commonHeaderInfo: {
                requestFromSystemId: 'MAP',
                otherSystemSendTime: '20221129112507',
                systemReceiptTime: '20221129112814',
                resultCode: '00',
            },
            errorInfoList: [
                {
                    messageId: '00001',
                    message: '',
                },
            ],
        });
    }
};

module.exports = {
    registerSwimmyTransaction,
    isMaintenanceMode,
    toggleMaintenanceMode,
    callAPIAladinKekkaTsuchi,
    callAPIMNPTenshutsuKanryouTsuchi,
    callAPIMoushikomiHenkou,
    callAPIKurokaKanryouTsuchi,
    callAPIOTAKekkaTsuchi,
    callAPIKaisenOptionHenkouKekkaTsuchi,
    callAPISIMSaihakkouKekkaTsuchi,
    callAPIESIMKekkaTsuchiSwimmyV2,
    callAPIESIMKekkaTsuchiSwimmyV3,
};
