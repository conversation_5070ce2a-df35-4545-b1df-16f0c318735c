const Joi = require('joi');
const {
    SWIMMY_VLM_INTERNATIONAL_CALL,
    SWIMMY_VLM_REQUEST_TYPES,
    SWIMMY_VLM_STATUS,
} = require('../constants/swimmyVLMTransactions');
const SwimmyVLMApiLogModel = require('../models/swimmyService/swimmyVLMApiLog.model');
const SwimmyVLMApiRestrictSettingModel = require('../models/swimmyService/swimmyVLMApiRestrictSetting.model');
const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
const axios = require('axios');
const { setSwimmyVLMToken, getSwimmyVLMToken } = require('./redisCache.service');
const { retrieveOneMAWPTenant } = require('../pgModels/tenants');
const { removeNullValue, isNone } = require('../helpers/baseHelper');
const { ServiceBusAdministrationClient } = require('@azure/service-bus');

const serviceBusAdministrationClient = portalConfig.SWIMMY_VLM_API.POLLING.SERVICE_BUS_CONNECTION_STRING
    ? new ServiceBusAdministrationClient(portalConfig.SWIMMY_VLM_API.POLLING.SERVICE_BUS_CONNECTION_STRING)
    : {};

let swimmyVLMOauthClient, swimmyVLMServicesClient;

const TRANSACTION_TYPE = {
    SHINKI: '010', //新規
    HENKOU: '020', //変更
    HAISHI: '030', //廃止
};

const SWIMMY_VLM_OAUTH_HTTP_RESPONSE_CODES_OK = [200, 201];
const SWIMMY_VLM_HTTP_RESPONSE_CODES_OK = [200, 400];

/* A validation for the requestParam. */
const ActiveOrderRequestParam = Joi.object({
    phoneNumber: Joi.string().required(),
    servicePlan: Joi.string().required(),
    internationalCall: Joi.string()
        .valid(...Object.values(SWIMMY_VLM_INTERNATIONAL_CALL))
        .required(),
    costStartDate: Joi.string().allow(null).allow(''),
});

/* A validation for the requestParam. */
const CancelOrderRequestParam = Joi.object({
    phoneNumber: Joi.string().required(),
    costStartDate: Joi.string().allow(null).allow(''),
    costEndDate: Joi.string().allow(null).allow(''),
});

/* A validation for the requestParam */
const ChangeOrderRequestParam = Joi.object({
    phoneNumber: Joi.string().required(),
    servicePlan: Joi.string().required(),
    internationalCall: Joi.string()
        .valid(...Object.values(SWIMMY_VLM_INTERNATIONAL_CALL))
        .required(),
    costStartDate: Joi.string().allow(null).allow(''),
});

const _register0035Transaction = async (
    context,
    {
        tenantId,
        soId,
        requestType,
        transactionType,
        requestParam,
        status = null,
        sameMvneInFlag = null,
        needsReActive = false,
    }
) => {
    if (!tenantId) throw new Error('tenantId is required');
    if (!soId) throw new Error('soId is required');
    if (!requestType) throw new Error('requestType is required');
    if (!Object.values(SWIMMY_VLM_REQUEST_TYPES).includes(requestType)) throw new Error('Invalid requestType');
    if (status && !Object.values(SWIMMY_VLM_STATUS).includes(status)) throw new Error('Invalid status');

    let validateRequestParam;
    switch (transactionType) {
        case TRANSACTION_TYPE.SHINKI:
            validateRequestParam = ActiveOrderRequestParam.validate(requestParam);
            break;
        case TRANSACTION_TYPE.HAISHI:
            validateRequestParam = CancelOrderRequestParam.validate(requestParam);
            break;
        case TRANSACTION_TYPE.HENKOU:
            validateRequestParam = ChangeOrderRequestParam.validate(requestParam);
            break;
        default:
            throw new Error('Invalid transactionType');
    }
    if (validateRequestParam.error) {
        throw new Error(validateRequestParam.error.message);
    }

    return await SwimmyVLMApiLogModel.createNew(
        context,
        soId,
        tenantId,
        requestParam?.phoneNumber,
        requestType,
        transactionType,
        requestParam,
        null,
        status,
        needsReActive,
        sameMvneInFlag
    );
};

const register0035ActiveOrder = async (
    context,
    { tenantId, soId, requestType, requestParam, status = null, sameMvneInFlag = null },
    sessionId = null
) => {
    return await _register0035Transaction(
        context,
        {
            tenantId,
            soId,
            requestType,
            transactionType: TRANSACTION_TYPE.SHINKI,
            requestParam,
            status: isNone(status) ? SWIMMY_VLM_STATUS.ON_HOLD : status,
            sameMvneInFlag,
        },
        sessionId
    );
};

const register0035CancelOrder = async (
    context,
    { tenantId, soId, requestType, requestParam, status = null, sameMvneInFlag = null, needsReActive = false },
    sessionId = null
) => {
    return await _register0035Transaction(
        context,
        {
            tenantId,
            soId,
            requestType,
            transactionType: TRANSACTION_TYPE.HAISHI,
            requestParam,
            status: isNone(status) ? SWIMMY_VLM_STATUS.ON_HOLD : status,
            sameMvneInFlag,
            needsReActive,
        },
        sessionId
    );
};

const register0035ChangeOrder = async (
    context,
    { tenantId, soId, requestType, requestParam, status = null, sameMvneInFlag = null },
    sessionId = null
) => {
    return await _register0035Transaction(
        context,
        {
            tenantId,
            soId,
            requestType,
            transactionType: TRANSACTION_TYPE.HENKOU,
            requestParam,
            status: isNone(status) ? SWIMMY_VLM_STATUS.ON_HOLD : status,
            sameMvneInFlag,
        },
        sessionId
    );
};

/**
 * It creates an axios client that can be used to make requests to the Swimmy VLM API
 * @param context - The context object that is passed to the function.
 * @returns {axios.AxiosInstance} A function that returns a promise that resolves to an axios client.
 */
const getSwimmyVLMOauthClient = async (context) => {
    if (!swimmyVLMOauthClient) {
        const apiConfig = await appConfig.getAPIConfig(context);
        swimmyVLMOauthClient = axios.create({
            baseURL: apiConfig.API_SWIMMY_VLM_OAUTH_URL,
            timeout: 90 * 1000,
            headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
            },
        });
    }

    return swimmyVLMOauthClient;
};

/**
 * It creates an axios client that can be used to make requests to the Swimmy VLM API
 * @param context - The context object that is passed to the function.
 * @returns {Promise<axios.AxiosInstance>} A function that returns a promise that resolves to an axios client.
 */
const getSwimmyVLMServicesClient = async (context) => {
    if (!swimmyVLMServicesClient) {
        const apiConfig = await appConfig.getAPIConfig(context);
        swimmyVLMServicesClient = axios.create({
            baseURL: apiConfig.API_SWIMMY_VLM_SERVICES_URL,
            timeout: 90 * 1000,
            headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
            },
        });
        mockSwimmyVLMAPI(context, swimmyVLMServicesClient);
    }

    return swimmyVLMServicesClient;
};

/** typedef
 * @typedef {Object} AccessToken
 * @property {string} accessToken
 * @property {string} tokenType
 * @property {number} expiresIn
 * @property {string} scope
 * @property {string} issuedAt
 **/

/**
 * It fetches an access token from the Swimmy VLM OAuth server
 * @param context - Azure Function context object
 * @param clientId - The client ID of the application that you created in the Azure portal.
 * @param clientSecret - The client secret of the application that you created in the Azure portal.
 * @returns {AccessToken | Exception}
 */
const fetchAccessToken = async (context, clientId, clientSecret) => {
    context.log('fetchAccessToken start');
    try {
        const swimmyVLMOauthClient = await getSwimmyVLMOauthClient(context);
        const response = await swimmyVLMOauthClient.post('/oauth/accesstokens', {
            grantType: 'client_credentials',
            clientId: clientId,
            clientSecret: clientSecret,
        });
        if (!SWIMMY_VLM_OAUTH_HTTP_RESPONSE_CODES_OK.includes(response.status)) {
            throw new Error('StatusCode of Fetch AccessToken: ' + response.status);
        } else {
            context.log('fetchAccessToken success with status: ' + response.status);

            return response.data;
        }
    } catch (error) {
        context.log.error('fetchAccessToken prepare error: ', error);
    }
};

/**
 * > It retrieves the clientId and clientSecret from the database, then uses them to fetch a new access
 * token from the VLM API, and finally stores the new token in the database
 * @param context - The context object that is passed to the function.
 * @param tenantId - The tenant ID of the tenant you want to get the token for.
 */
const fetchAndUpdateTokenToCache = async (context, tenantId) => {
    context.log('fetchAndUpdateTokenToCache start: ', tenantId);
    try {
        const tenantInfo = await retrieveOneMAWPTenant(context, tenantId);
        const swimmyVLMToken = await fetchAccessToken(context, tenantInfo?.clientId, tenantInfo?.clientSecret);
        return await setSwimmyVLMToken(context, tenantId, swimmyVLMToken);
    } catch (error) {
        context.log('fetchAndUpdateTokenToCache error: ', error);
        context.log.error('fetchAndUpdateTokenToCache error: ' + tenantId, error.message);
    }
};

/**
 * It fetches an access token from the cache, if it's not found, it fetches a new one and stores it in
 * the cache, then it uses the access token to call the Swimmy VLM Services API
 * @param context - The context object that is passed to the function.
 * @param tenantId - The tenant ID of the tenant that you want to access.
 * @param method - The HTTP method to use (GET, POST, PUT, DELETE, etc.)
 * @param path - The path to the API endpoint.
 * @param jsonBody - The body of the request.
 * @returns the data from the response.
 */
const executeSwimmyLVMApi = async (context, tenantId, method, path, jsonBody) => {
    context.log('executeSwimmyLVMApi start: ', tenantId, method, path, jsonBody);
    try {
        let accessToken = await getSwimmyVLMToken(context, tenantId);
        // If the token is not found in the cache, fetch a new one and store it in the cache
        if (!accessToken) {
            accessToken = await fetchAndUpdateTokenToCache(context, tenantId);
        }
        const swimmyVLMServicesClient = await getSwimmyVLMServicesClient(context);
        const response = await swimmyVLMServicesClient({
            headers: {
                Authorization: 'Bearer ' + accessToken,
            },
            method: method,
            url: path,
            data: removeNullValue(jsonBody),
        });

        const { status, data } = response;
        if (!SWIMMY_VLM_HTTP_RESPONSE_CODES_OK.includes(status)) {
            throw new Error('StatusCode of Swimmy VLM Services: ' + status);
        } else {
            context.log('executeSwimmyLVMApi success with status: ' + status);
            return data;
        }
    } catch (error) {
        context.log.error('executeSwimmyLVMApi error: ', error);
        if (error?.response?.status == 401) {
            await fetchAndUpdateTokenToCache(context, tenantId);
            throw new Error('401_Unauthorized');
        }

        throw error;
    }
};

/**
 * It calls the ActiveOrder API of SwimmyLVM
 * @param context - The context object that is passed to the function.
 * @param tenantId - The tenant ID of the tenant that you want to call the API for.
 * @param requestParams - The parameters to be sent to the API.
 * @returns The result of the call to the API.
 */
const callActiveOrderAPI = async (context, tenantId, requestParams) => {
    try {
        const validateRequestParam = ActiveOrderRequestParam.validate(requestParams);
        if (validateRequestParam.error) {
            throw new Error(validateRequestParam.error.message);
        }

        const result = await executeSwimmyLVMApi(context, tenantId, 'POST', '', requestParams);
        return result;
    } catch (error) {
        context.log.error('callActiveOrderAPI error: ', error);
        throw error;
    }
};

/**
 * This function calls the cancel order API of the Swimmy LVM API
 * @param context - The context object that is passed to the function.
 * @param tenantId - The tenant ID of the user who is making the request.
 * @param requestParams - {
 * @returns The result of the callCancelOrderAPI function is the result of the executeSwimmyLVMApi
 * function.
 */
const callCancelOrderAPI = async (context, tenantId, requestParams) => {
    try {
        const validateRequestParam = CancelOrderRequestParam.validate(requestParams);
        if (validateRequestParam.error) {
            throw new Error(validateRequestParam.error.message);
        }

        const result = await executeSwimmyLVMApi(context, tenantId, 'DELETE', '', requestParams);
        return result;
    } catch (error) {
        context.log.error('callCancelOrderAPI error: ', error);
        throw error;
    }
};

/**
 * This function calls the change order API of the Swimmy LVM API
 * @param {object} context - The context object that is passed to the function.
 * @param {string} tenantId - The tenant ID of the user who is making the request.
 * @param {object} requestParams - ChangeOrderRequest object
 * @returns The result of the callChangeOrderAPI function is the result of the executeSwimmyLVMApi
 * function.
 */
const callChangeOrderAPI = async (context, tenantId, requestParams) => {
    try {
        const validateRequestParam = ChangeOrderRequestParam.validate(requestParams);
        if (validateRequestParam.error) {
            throw new Error(validateRequestParam.error.message);
        }

        const result = await executeSwimmyLVMApi(context, tenantId, 'PUT', '', requestParams);
        return result;
    } catch (error) {
        context.log.error('callChangeOrderAPI error: ', error);
        throw error;
    }
};

/**
 * This function checks if a service bus queue is in maintenance mode.
 * @returns The function `isMaintenanceMode` returns a boolean value indicating whether the service bus
 * queue is in maintenance mode or not. If the queue status is not 'Active', the function returns
 * `true`, indicating that the queue is in maintenance mode. Otherwise, it returns `false`.
 */
const isMaintenanceMode = async () => {
    const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
        portalConfig.SWIMMY_VLM_API.POLLING.SERVICE_BUS_QUEUE_NAME
    );
    const isMaintenance = !['Active'].includes(apiRequestsQueue.status);
    return isMaintenance;
};

/**
 * The function toggles the maintenance mode of a service bus queue.
 * @param context
 * @param isMaintenance - isMaintenance is a boolean parameter that indicates whether the system is
 * being put into maintenance mode or not. If it is true, the system will be put into maintenance mode,
 * and if it is false, the system will be taken out of maintenance mode.
 * @returns The function `toggleMaintenanceMode` is returning the result of the
 * `serviceBusAdministrationClient.updateQueue` method, which is a Promise that resolves to the updated
 * queue object.
 */
const toggleMaintenanceMode = async (context, isMaintenance) => {
    const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
        portalConfig.SWIMMY_VLM_API.POLLING.SERVICE_BUS_QUEUE_NAME
    );
    apiRequestsQueue.status = isMaintenance ? 'Disabled' : 'Active';

    await serviceBusAdministrationClient.updateQueue(apiRequestsQueue);
    await SwimmyVLMApiRestrictSettingModel.setMaintenanceMode(context, isMaintenance);

    const maintenanceText = isMaintenance ? '「ON」 になりました。' : '「OFF」 になりました。';

    context.log.error('SwimmyVLM-APIメンテナンス：', maintenanceText);

    return !['Active'].includes(apiRequestsQueue.status);
};

/**
 *
 * @param {object} context
 * @param {axios.AxiosInstance} instance
 */
const mockSwimmyVLMAPI = (context, instance) => {
    if (portalConfig.SWIMMY_VLM_API.RUN_WITH_MOCK || portalConfig.SWIMMY_VLM_API.RUN_WITH_MOCK === 'true') {
        context.log('mockSwimmyVLMAPI start');
        var MockAdapter = require('axios-mock-adapter');
        /** @type {MockAdapter.default} */
        const mockInstance = new MockAdapter(instance);

        // response 200 with original data + empty errorCode and errorMessage, result: 'OK', _mock: true
        const response = (config) => {
            let d;
            try {
                d = typeof config.data === 'string' ? JSON.parse(config.data) : config.data;
            } catch (e) {
                // parse failed
                d = {};
            }
            return [200, { ...(d || {}), errorCode: null, errorMessage: null, result: 'OK', _mock: true }];
        };

        // TODO: add mock response here
        // callActiveOrderAPI: POST
        // callCancelOrderAPI: DELETE
        // callChangeOrderAPI: PUT
        mockInstance.onPost().reply(response);
        mockInstance.onDelete().reply(response);
        mockInstance.onPut().reply(response);
    }
};

module.exports = {
    register0035ActiveOrder,
    register0035CancelOrder,
    register0035ChangeOrder,
    callActiveOrderAPI,
    callCancelOrderAPI,
    callChangeOrderAPI,
    isMaintenanceMode,
    toggleMaintenanceMode,
};
