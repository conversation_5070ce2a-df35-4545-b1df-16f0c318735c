const { ServiceBusAdministrationClient } = require('@azure/service-bus');
const appConfig = require('../config/appConfig');

const portalConfig = appConfig.getPortalConfig();
const serviceBusAdministrationClient = portalConfig.CORE_SWIMMY_API.POLLING.SERVICE_BUS_CONNECTION_STRING
    ? new ServiceBusAdministrationClient(portalConfig.CORE_SWIMMY_API.POLLING.SERVICE_BUS_CONNECTION_STRING)
    : {};

/**
 * The function toggles the maintenance mode of a service bus queue and returns a boolean indicating if
 * the queue is active or not.
 * @param context - The context object that is passed to the function.
 * @param isMaintenance - isMaintenance is a boolean parameter that indicates whether the system is in
 * maintenance mode or not. If it is set to true, the system will be put into maintenance mode and the
 * service bus queue will be disabled. If it is set to false, the system will be taken out of
 * maintenance mode and the
 * @returns a boolean value that indicates whether the status of a service bus queue has been
 * successfully updated to either 'Disabled' or 'Active' based on the value of the `isMaintenance`
 * parameter passed to the function. If the status has been updated to 'Disabled' during maintenance
 * mode, the function returns `true`. Otherwise, if the status has been updated to 'Active' after
 * maintenance
 */
const toggleMaintenanceMode = async (context, isMaintenance) => {
    const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
        portalConfig.CORE_SWIMMY_API.POLLING.SERVICE_BUS_QUEUE_NAME
    );
    // NOTE use `ReceiveDisabled` to match code in MVNO_CORE_API
    apiRequestsQueue.status = isMaintenance ? 'ReceiveDisabled' : 'Active';

    await serviceBusAdministrationClient.updateQueue(apiRequestsQueue);
    // no *RestrictSettingModel to track maintenance mode
    const maintenanceText = isMaintenance ? '「ON」 になりました。' : '「OFF」 になりました。';
    context.log.error('CoreSwimmy-APIメンテナンス：', maintenanceText);

    return !['Active'].includes(apiRequestsQueue.status);
};

module.exports = {
    toggleMaintenanceMode,
};
