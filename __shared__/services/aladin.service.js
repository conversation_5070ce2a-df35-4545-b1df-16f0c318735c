const { TRANSACTION_TYPE, ORDER_TYPE, SUCCESS_SHORI_KEKKA_CODES } = require('../constants/aladinTransactions');
const { KOUTEI_IDS } = require('../constants/orderConstants');
const AladinApiLogModel = require('../models/aladinService/aladinApiLog.model');
const AladinApiRestrictSettingModel = require('../models/aladinService/aladinApiRestrictSetting.model');
const MnpTennyuSoModel = require('../models/mnpTennyuSo.model');
const SimBlackingSoModel = require('../models/simBlackingSo.model');
const KaisenHaishiSoModel = require('../models/kaisenHaishiSo.model');
const SwimmySoModel = require('../models/swimmySo.model');
const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
const { prepareFormData, xml2js } = require('../helpers/aladinApiLogHelper');
const axios = require('axios');
// eslint-disable-next-line no-unused-vars
const https = require('https');
// eslint-disable-next-line no-unused-vars
const fs = require('fs');
const { ServiceBusAdministrationClient } = require('@azure/service-bus');

const serviceBusAdministrationClient = portalConfig.ALADIN_API.POLLING.SERVICE_BUS_CONNECTION_STRING
    ? new ServiceBusAdministrationClient(portalConfig.ALADIN_API.POLLING.SERVICE_BUS_CONNECTION_STRING)
    : {};

let docomoAladinClient = null;
/**
 * An object containing data to create mnpIn's soId
 * @typedef {object} dataObject
 * @property {string} tenantId - テナントID
 * @property {string} tempoId - 店舗ID
 * @property {string} [daihyouBango] - 代表番号
 * @property {string} transactionType - aladinTransactions.TRANSACTION_TYPE
 * @property {string} requestOrderType - aladinTransactions.ORDER_TYPE
 * @property {string} requestOrderId
 * @property {object} requestParam
 * @property {string} requestParam.hanbaitencode
 * @property {string} [requestParam.fukajigyoshacode]
 * @property {string} requestParam.keiyakusyubetu
 * @property {string} requestParam.transactionTYPE
 * @property {string} [requestParam.daihyobango]
 * @property {string} [requestParam.denwabango]
 * @property {string} [requestParam.seizobango]
 * @property {string} [requestParam.cardkeijo]
 * @property {string} [requestParam.MNPyoyakubango]
 * @property {string} [requestParam.MNPzokusei] - aladinTransactions.ZOKUSEI_CODE
 * @property {string} [requestParam.MNPyoyakusyakana]
 * @property {string} [requestParam.MNPyoyakusyakanji]
 * @property {string} [requestParam.MNPseinengappi]
 * @property {string} [requestParam.ansyobango]
 * @property {string} [requestParam.ryokinplan]
 * @property {string} [requestParam.sousaservice] - 割引プラン/オプションサービスにおいて、代表回線と異なる内容にしたい場合に設定する
 * @property {string} [requestParam.WWtukehenhaiFLG]
 * @property {string} [requestParam.WWriyouteisimeyasugaku]
 * @property {string} [requestParam.WWdaisankokuhassinkisei]
 * @property {string} [requestParam.WCtukehenhaiFLG]
 * @property {string} [requestParam.WCriyouteisimeyasugaku]
 * @property {string} [requestParam.WCtuwateisi]
 * @property {string} [requestParam.eid] - eSIM対応
 * @property {number} [status]
 * @property {string} [createdUserId]
 * @property {number} [retryAt] set to delay processing the transaction (unix timestamp)
 */

/**
 * Register a aladin transaction to service bus.
 * TransactionId will be generated when the transaction is sent to service bus,
 * so no need to pass it
 * @param {object} context - Azure Function context
 * @param {dataObject} data - data to send to service bus
 * @param {object} [options]
 * @param {boolean} [options.skipUpdateKoutei] skip adding ALADIN_KOUJI_WAITING to related SO
 * @throws Error if required data is not passed
 * @returns {Promise<string>} transaction ID
 */
const registerAladinTransaction = async (
    context,
    {
        tenantId,
        tempoId,
        daihyouBango,
        transactionType,
        requestOrderType,
        requestOrderId,
        requestParam,
        // eslint-disable-next-line no-unused-vars
        isFullMvno = false,
        status = 10,
        createdUserId,
        retryAt, // optional
    },
    options
) => {
    if (!tenantId) throw new Error('tenantId is required');
    if (!requestOrderId) throw new Error('requestOrderId is required');
    if (!requestParam) throw new Error('requestParam is required');
    // if transactionType is not valid, throw error
    if (!Object.values(TRANSACTION_TYPE).includes(transactionType)) throw new Error('Invalid transactionType');
    // if requestOrderType is not valid, throw error
    if (!Object.values(ORDER_TYPE).includes(requestOrderType)) throw new Error('Invalid requestOrderType');
    let transactionId = '';
    context.log('registerAladinTransaction isFullMvno: ', isFullMvno);

    const payload = {
        tenantId,
        tempoId,
        daihyouBango,
        transactionType,
        requestOrderType,
        requestOrderId,
        requestParam,
        status,
        createdUserId,
    };
    if (typeof retryAt === 'number' && retryAt > 0) {
        payload.retryAt = retryAt;
    }

    if (isFullMvno) {
        transactionId = await AladinApiLogModel.registerTransactionHLR_HSS(context, payload);
    } else {
        transactionId = await AladinApiLogModel.registerTransaction(context, payload);
    }
    const koutei = {
        kouteiId: KOUTEI_IDS.ALADIN_KOUJI_WAITING,
    };

    if (options?.skipUpdateKoutei === true) {
        return transactionId;
    }
    // update koutei of requestOrder to ALADIN_KOUJI_WAITING
    switch (requestOrderType) {
        case ORDER_TYPE.KAISEN_HAISHI:
        case ORDER_TYPE.KAISEN_HAISHI_FULL_MVNO:
            await KaisenHaishiSoModel.updateKouteiInfo(context, {
                so_id: requestOrderId,
                updatedUserId: portalConfig.API_USER_ID,
                koutei,
            });
            break;
        case ORDER_TYPE.SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU:
        case ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU:
        case ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU:
        case ORDER_TYPE.IKKATSU_SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU:
        case ORDER_TYPE.IKKATSU_MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU:
        case ORDER_TYPE.IKKATSU_KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU:
            await SimBlackingSoModel.updateKouteiInfo(context, {
                so_id: requestOrderId,
                updatedUserId: portalConfig.API_USER_ID,
                koutei,
            });
            break;
        case ORDER_TYPE.OTA_IKKATSU_KAITSU:
        case ORDER_TYPE.OTA_IKKATSU_HAISHI:
            // TODO need to confirm:
            // since there is no koutei for this type, do nothing
            break;
        case ORDER_TYPE.KISETSU_HENKOU_RIYOU_CHUUDAN_SOKUJI:
            // STEP28: there is no mnpTennyuSoModel, so do nothing
            // we will update `immediateSuspensionSuccessful` instead of koutei later in queue handler
            break;
        case ORDER_TYPE.KAISEN_HAISHI_SOKUJI:
            await SwimmySoModel.updateKouteiInfo(context, {
                so_id: requestOrderId,
                updatedUserId: portalConfig.API_USER_ID,
                koutei,
            });
            break;
        default:
            await MnpTennyuSoModel.updateKouteiInfo(context, {
                so_id: requestOrderId,
                updatedUserId: portalConfig.API_USER_ID,
                koutei,
            });
            break;
    }

    return transactionId;
};

/**
 * It creates an axios client that can be used to make requests to the Docomo Aladin API
 * @param context - The context object that is passed to the function.
 * @returns {axios.AxiosInstance} A function that returns a promise that resolves to an axios client.
 */
const getDocomoAladinClient = async (context) => {
    if (!docomoAladinClient) {
        const apiConfig = await appConfig.getAPIConfig(context);
        // const httpsAgent = new https.Agent({
        //     rejectUnauthorized: false, // (NOTE: this will disable client verification)
        //     cert: fs.readFileSync("./usercert.pem"),
        //     key: fs.readFileSync("./key.pem"),
        //     passphrase: "YYY"
        //   })
        docomoAladinClient = axios.create({
            baseURL: apiConfig.API_ALADIN_URL,
            timeout: 90 * 1000,
            headers: {
                Host: apiConfig.API_ALADIN_HOST,
                'Content-Type': 'application/x-www-form-urlencoded;charset=Windows-31J',
                Connection: 'close',
            },
        });
    }

    return docomoAladinClient;
};

/**
 * It calls the Aladin API with the given request data, and returns the response data
 * @param context - The context object that is passed to the function.
 * @param req - The request object that contains the parameters for the API call.
 * @param executeAPIName - The name of the API to be called.
 * @returns An object with the following properties:
 * - isOk: boolean
 * - result: object
 */
const callAladinAPI = async (context, req, executeAPIName) => {
    try {
        context.log('callAladinAPI: ', executeAPIName);
        const docomoAladinClient = await getDocomoAladinClient(context);
        const reqData = prepareFormData(req);

        const response = await docomoAladinClient.post('/VIEW_NMC/mif/MI/api', reqData);

        if (response.status !== 200) {
            if (response.status === 502) {
                throw new Error('502');
            } else {
                throw new Error('Error calling Aladin API with status code: ' + response.status);
            }
        } else {
            context.log('callAladinAPI response: ', response.data);
            const responseObj = xml2js(response.data);
            const { result } = responseObj;
            const symbol = result.syorikekkaKbn;
            const isOk = SUCCESS_SHORI_KEKKA_CODES.includes(symbol);

            return {
                isOk,
                result,
            };
        }
    } catch (error) {
        context.log.error('callAladinAPI error: ', error.message);
        const isTimeoutException = error.code === 'ECONNABORTED' || error.message === '502';
        if (isTimeoutException) {
            throw new Error('502');
        }
        throw error;
    }
};

/**
 * This function checks if a service bus queue is in maintenance mode.
 * @returns The function `isMaintenanceMode` returns a boolean value indicating whether the service bus
 * queue is in maintenance mode or not. If the queue status is not 'Active', the function returns
 * `true`, indicating that the queue is in maintenance mode. Otherwise, it returns `false`.
 */
const isMaintenanceMode = async () => {
    const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
        portalConfig.ALADIN_API.POLLING.SERVICE_BUS_QUEUE_NAME
    );
    const isMaintenance = !['Active'].includes(apiRequestsQueue.status);
    return isMaintenance;
};

/**
 * The function toggles the maintenance mode of a service bus queue.
 * @param context
 * @param isMaintenance - isMaintenance is a boolean parameter that indicates whether the system is
 * being put into maintenance mode or not. If it is true, the system will be put into maintenance mode,
 * and if it is false, the system will be taken out of maintenance mode.
 * @returns The function `toggleMaintenanceMode` is returning the result of the
 * `serviceBusAdministrationClient.updateQueue` method, which is a Promise that resolves to the updated
 * queue object.
 */
const toggleMaintenanceMode = async (context, isMaintenance) => {
    const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
        portalConfig.ALADIN_API.POLLING.SERVICE_BUS_QUEUE_NAME
    );
    apiRequestsQueue.status = isMaintenance ? 'Disabled' : 'Active';

    await serviceBusAdministrationClient.updateQueue(apiRequestsQueue);
    await AladinApiRestrictSettingModel.setMaintenanceMode(context, isMaintenance);

    const maintenanceText = isMaintenance ? '「ON」 になりました。' : '「OFF」 になりました。';

    context.log.error('ALADIN-APIメンテナンス：', maintenanceText);

    return !['Active'].includes(apiRequestsQueue.status);
};

module.exports = { registerAladinTransaction, callAladinAPI, isMaintenanceMode, toggleMaintenanceMode };
