const psql = require('../database/postgre');
const { isNone } = require('../helpers/baseHelper');

// 社内コードは[1,2,3]、社外コードは[99]
const TENANT_TYPES = {
    C_OCN: 1,
    B_OCN: 2,
    UNO_MOBILE: 3,
    RINK_MOBILE: 5,
    GUEST: 99,
};

/**
 * An object containing lineOption details
 * @typedef {object} lineOptionInfo
 * @property {string} line_option_id
 * @property {number} line_option_type
 * @property {string} option_plan_name
 * @property {boolean} c_ocn
 * @property {boolean} b_ocn
 * @property {boolean} uno
 * @property {boolean} whole
 * @property {boolean} id
 */

/**
 * getLineOptionsList
 * find lineOptions by tenantId, planId, nNo
 * @param  {object} context - API context
 * @param  {object} data    - required params for query
 * @param  {string} data.tenantId
 * @param  {string} data.planId
 * @param  {string} [data.nNo]
 * @return {Promise<Array<lineOptionInfo>>}  - all lineOptions of tenant
 */
const findLineOptions = async (context, { tenantId, planId, nNo }) => {
    if (!tenantId) throw new Error('tenantId is required');
    if (planId === null || planId === undefined) throw new Error('planId is required'); // allow 0 to pass

    context.log('findLineOptions start :' + tenantId + ':' + planId + ':' + nNo);
    const sql = `
        SELECT
            LO.*
        FROM
            line_options AS LO
            INNER JOIN
                tenant_plan_line_options AS TLO
            ON LO.line_option_type = TLO.line_option_type
            AND TLO.tenant_id = $1
            AND TLO.plan_id = $2
        ORDER BY
            LO.line_option_type,
            LO.line_option_id`;

    const lineOptions = await psql.query(context, {
        sql,
        params: [tenantId, planId],
    });
    if (lineOptions.rows.length === 0) return [];

    // 社内テナント種別, 帯域卸フラグを取得
    let bExtendWhere = '';
    let params = [tenantId];
    if (nNo) {
        bExtendWhere = 'AND B.nnumber = $2';
        params.push(nNo);
    }
    const sql2 = `
        SELECT
            COALESCE(A.tenant_type, 99) AS tenant_type,
            COALESCE(B.whole_flag, FALSE) AS whole_flag
        FROM tenants AS A
            LEFT OUTER JOIN
                tenant_nnumbers AS B
            ON A.tenant_id = B.tenant_id
            ${bExtendWhere}
        WHERE
            A.tenant_id = $1`;

    const tenantInfo = await psql.query(context, { sql: sql2, params: params });
    const tenantType = tenantInfo.rows[0].tenant_type;
    const wholeFlag = tenantInfo.rows[0].whole_flag;

    // 不明なテナントコード
    if (!Object.values(TENANT_TYPES).includes(tenantType)) return [];

    if (tenantType == 99) {
        if (tenantInfo.rows.length > 1) {
            // 社外の場合は、帯域卸とID卸とを区別する必要があるが、代表N番が複数個数登録されている場合、かつ
            // 代表N番の指定が無い場合に、ここで複数個数のレコードが取得されてしまい、どのレコードの
            // 帯域卸フラグを使用して良いのか判断できなくなるため、エラーにする
            context.log.warn('multiple tenant_nnumbers records retrieved.');

            /**
             * https://mobilus.backlog.jp/view/MVNO_M-1477
             * 【暫定対応】1テナントで複数N番があった場合は、それぞれのN番の帯域卸フラグ（tenant_nnumbersテーブル）をチェックし、
             * 同一であれば処理を続行、異なる場合はエラー
             */
            const checkAllSame = tenantInfo.rows.every((row) => row.whole_flag == wholeFlag);
            if (!checkAllSame) return [];
        }
    }

    let result = [];
    switch (tenantType) {
        case TENANT_TYPES.C_OCN:
            result = lineOptions.rows.filter((row) => row.c_ocn); // C-OCN
            break;
        case TENANT_TYPES.B_OCN:
            result = lineOptions.rows.filter((row) => row.b_ocn); // B-OCN
            break;
        case TENANT_TYPES.UNO_MOBILE:
            result = lineOptions.rows.filter((row) => row.uno); // uno mobile
            break;
        default:
            // 社外
            if (wholeFlag) {
                // 帯域卸フラグ = true
                result = lineOptions.rows.filter((row) => row.whole);
            } else {
                // 帯域卸フラグ != true
                result = lineOptions.rows.filter((row) => row.id);
            }
    }

    return result;
};

/**
 * retrieveAllMAWPLineOptions
 * get all lineOptions from DB
 * @param  {object} context - API context
 * @return {Array.<lineOptionInfo>}  - data from DB
 */
const findAllMAWPLineOptions = async (context) => {
    const sql = 'SELECT * FROM line_options';
    const { rows } = await psql.query(context, { sql });
    return rows;
};

/**
 * get display order for lineOptionType
 * @param  {number} lineOptionType
 * @return {number}                  sort order for lineOptionType
 */
const getDisplayOrderForLineType = (lineOptionType) => {
    switch (lineOptionType) {
        case 2:
            return 1;
        case 3:
            return 2;
        case 5:
            return 3;
        case 6:
            return 4;
        case 4:
            return 5;
        case 1:
            return 6;
        default:
            return 999;
    }
};

/**
 * get all line option by tenantId
 * @param {object} context - API context
 * @param {string} tenantId - tenantId
 * @return {Promise<Array<lineOptionInfo>}> - all lineOptions of tenants
 */
const findLineOptionsByTenantId = async (context, { tenantId }) => {
    if (!tenantId) throw new Error('tenantId is required');

    context.log('findLineOptionsByTenantId start: ' + tenantId);
    const sql = `
        SELECT 
            A.*
        FROM
            line_options AS A
        ORDER BY
            A.line_option_type, A.line_option_id`;

    const lineOptions = await psql.query(context, { sql });
    if (lineOptions.rows.length === 0) return null;
    // 社内テナント種別, 帯域卸フラグを取得
    const sql2 = `
        SELECT
            COALESCE(A.tenant_type, 99) AS tenant_type, COALESCE(B.whole_flag, false) AS whole_flag
        FROM
            (SELECT tenant_id, tenant_type FROM tenants WHERE tenant_id = $1) AS A
            LEFT OUTER JOIN
                tenant_nnumbers AS B
            ON A.tenant_id = B.tenant_id`;

    const tenantInfo = await psql.query(context, { sql: sql2, params: [tenantId] });
    const tenantType = tenantInfo.rows[0].tenant_type;
    const wholeFlag = tenantInfo.rows[0].whole_flag;

    // 不明なテナントコード
    if (!Object.values(TENANT_TYPES).includes(tenantType)) return [];

    if (tenantType == 99) {
        if (tenantInfo.rows.length > 1) {
            // 社外の場合は、帯域卸とID卸とを区別する必要があるが、代表N番が複数個数登録されている場合、かつ
            // 代表N番の指定が無い場合に、ここで複数個数のレコードが取得されてしまい、どのレコードの
            // 帯域卸フラグを使用して良いのか判断できなくなるため、エラーにする
            context.log.warn('multiple tenant_nnumbers records retrieved.');

            /**
             * https://mobilus.backlog.jp/view/MVNO_M-1477
             * 【暫定対応】1テナントで複数N番があった場合は、それぞれのN番の帯域卸フラグ（tenant_nnumbersテーブル）をチェックし、
             * 同一であれば処理を続行、異なる場合はエラー
             */
            const checkAllSame = tenantInfo.rows.every((row) => row.whole_flag == wholeFlag);
            if (!checkAllSame) return [];
        }
    }

    let result = [];
    const mnpKakinOption = {
        line_option_id: 'A0999',
        line_option_type: 7,
        option_plan_name: 'MNP課金情報',
        c_ocn: false,
        b_ocn: false,
        uno: false,
        whole: false,
        id: false,
    };
    switch (tenantType) {
        case TENANT_TYPES.C_OCN:
            result = lineOptions.rows.filter((row) => row.c_ocn); // C-OCN
            break;
        case TENANT_TYPES.B_OCN:
            result = lineOptions.rows.filter((row) => row.b_ocn); // B-OCN
            break;
        case TENANT_TYPES.UNO_MOBILE:
            result = lineOptions.rows.filter((row) => row.uno); // uno mobile
            break;
        default:
            // 社外
            if (wholeFlag) {
                // 帯域卸フラグ = true
                result = lineOptions.rows.filter((row) => row.whole).concat(mnpKakinOption);
            } else {
                // 帯域卸フラグ != true
                result = lineOptions.rows.filter((row) => row.id).concat(mnpKakinOption);
            }
    }

    return result;
};

/**
 * get_option_plans
 * @param {object} context
 * @param {number} planId
 * @param {string} planType
 * @returns {Promise<Array<{
 *   optionPlanId: number,
 *   optionPlanType: string|undefined,
 *   optionPlanName: string,
 *   optionPlanDescription: string|undefined,
 * }>>}
 */
const getOptionPlans = async (context, planId, planType) => {
    context.log('GetOptionPlan start', planId, planType);
    const sql = `
        SELECT
            A.option_plan_id as option_plan_id,
            A.option_plan_name as option_plan_name,
            A.option_plan_type as option_plan_type,
            A.option_plan_description as option_plan_description
        FROM
            option_plans as A
        JOIN
            plan_option_plans as B ON A.option_plan_id = B.option_plan_id
        WHERE
            B.plan_id = $1 AND A.option_plan_type = $2`;
    const { rows } = await psql.query(context, { sql, params: [planId, planType] });
    if (rows && rows.length > 0) {
        return rows.map((r) => ({
            optionPlanId: isNone(r.option_plan_id) ? -1 : +r.option_plan_id,
            optionPlanType: r.option_plan_type,
            optionPlanName: r.option_plan_name ?? '',
            optionPlanDescription: r.option_plan_description,
        }));
    }
    return [];
};

module.exports = {
    findLineOptions,
    findAllMAWPLineOptions,
    getDisplayOrderForLineType,
    findLineOptionsByTenantId,
    getOptionPlans,
};
