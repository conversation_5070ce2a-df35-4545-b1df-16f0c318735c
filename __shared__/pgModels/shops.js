const psql = require('../database/postgre');
const { isNone } = require('../helpers/baseHelper');
const { getSuperTempoCode } = require('../utils/stringUtils');
const queryUtils = require('../utils/queryUtils');

/**
 * It checks if the tempoId is valid
 * @param context - This is the context object that is passed to the function.
 * @param tempoId - The tempoId of the shop you want to check.
 * @returns A boolean value
 */
const isTempoValid = async (context, tempoId) => {
    if (!tempoId) throw new Error('tempoId is required');
    context.log('isTempoValid start :' + tempoId);

    let sql = 'SELECT 1 AS one FROM shops WHERE shop_id = $1 AND status = true';
    let { rows } = await psql.query(context, { sql, params: [tempoId] });

    return rows.length > 0;
};

/**
 * 店舗一覧取得
 * @param  {object} context - context object
 * @return {array.<shopsInfo>} - shop list
 */
const retrieveTempoList = async (context) => {
    const sql = 'SELECT * FROM shops WHERE status = true';
    const { rows } = await psql.query(context, { sql });
    return rows;
};

/**
 * Similar to `isTempoValid` but with different search strategy for `ZMK` tempo
 * @param {object} context
 * @param {string} tempoId
 * @param {string} tenantId
 */
const getTempoData = async (context, tempoId, tenantId) => {
    if (tenantId && tenantId.length > 3) {
        let sql = 'SELECT shop_id, shop_name FROM shops WHERE shop_id = $1 AND status = true';
        const params = [tempoId];
        if (!tempoId.startsWith(`ZMK${tenantId.substring(0, 3)}`)) {
            // add filter by tenant if tempo ID does not start with ZMK
            sql += ' AND tenant_id = $2';
            params.push(tenantId);
        }
        const { rows } = await psql.query(context, { sql, params });
        if (rows && rows.length > 0) {
            return {
                shopID: rows[0].shop_id,
                shopName: rows[0].shop_name,
            };
        }
    }
    return null;
};

const retrieveMAWPAllTenantsTempoList = async (context) => {
    context.log('retrieveMAWPAllTenantsTempoList START');

    const sql = 'SELECT shop_id, tenant_id, shop_name FROM shops WHERE status=true ';
    const { rows } = await psql.query(context, { sql });
    if (rows && rows.length > 0) {
        const result = new Map();

        rows.forEach((r) => {
            if (!result.has(r.tenant_id)) {
                result.set(r.tenant_id, [{ shopID: r.shop_id, shopName: r.shop_name }]);
            } else {
                let current = result.get(r.tenant_id);
                current = [...current, { shopID: r.shop_id, shopName: r.shop_name }];
                result.set(r.tenant_id, current);
            }
        });
        return result;
    }
    return null;
};

/**
 * テナントの店舗一覧を取得
 * @param {object} context
 * @param {string} tenantId
 * @returns {Promise<Array<{shop_id: string, shop_name: string}>>}
 */
const retrieveMAWPTempoList = async (context, tenantId) => {
    context.log('retrieveMAWPTempoList start');

    const sql = `SELECT shop_id, shop_name FROM shops WHERE tenant_id = $1 AND (NOT shop_name LIKE '【廃止】%') ORDER BY UPPER(shop_id)`;
    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    if (rows && rows.length > 0) {
        return rows;
    }
    return [];
};

/**
 * original: retrieveMAWPTempoList
 * @param {object} context
 * @param {Array<string>} tenantIds
 * @returns {Promsie<Array<Map<string, Array<string>>>>}
 */
const retrieveMAWPTempoListByTenantId = async (context, tenantIds) => {
    context.log('retrieveMAWPTempoList start tenantIds: ' + tenantIds.toString());

    const sql = `SELECT tenant_id,shop_id, shop_name FROM shops WHERE tenant_id IN ${queryUtils.buildListOfValues(
        tenantIds
    )} AND status=true ORDER BY shop_id`;
    const { rows } = await psql.query(context, { sql, params: tenantIds });
    if (rows && rows.length > 0) {
        const result = new Map();

        rows.forEach((r) => {
            if (!result.has(r.tenant_id)) {
                result.set(r.tenant_id, [{ shopID: r.shop_id, shopName: r.shop_name }]);
            } else {
                let current = result.get(r.tenant_id);
                current = [...current, { shopID: r.shop_id, shopName: r.shop_name }];
                result.set(r.tenant_id, current);
            }
        });
        return result;
    }
    return null;
};

/**
 * 新規SO店舗一覧を取得
 * @param {object} context
 * @param {string} tenantId
 * @returns {Promise<Array<{shop_id: string, shop_name: string}>>}
 */
const retrieveMAWPTempoListForNewSo = async (context, tenantId) => {
    context.log('retrieveMAWPTempoListForNewSo start');

    const likeCondition = 'ZMK' + tenantId.substring(0, 3) + '%';
    const sql = `SELECT shop_id, shop_name
        FROM shops
        WHERE status = true
        AND ((shop_id LIKE $1)
        OR (tenant_id = $2 AND shop_id LIKE 'Sxx%'))
        ORDER BY UPPER(shop_id)`;
    const { rows } = await psql.query(context, { sql, params: [likeCondition, tenantId] });
    if (rows && rows.length > 0) {
        return rows;
    }
    return [];
};

/**
 * Get list of tempoId
 * @param {Array<string>|null} tenantIds - null/empty array = no filtering by tenant ID
 * @param {string} searchValue search keyword for tempo name - must not be empty
 * @param {boolean} ignoreCase search with case sensitive query
 * @returns {Promise<Array<string>>} list of tempoId
 */
const searchTempoIdByNameAndTenants = async (context, tenantIds, searchValue, ignoreCase = false) => {
    const baseSql = `
        SELECT
            shop_id
        FROM
            shops
        WHERE
            status = true `;
    const query = [],
        params = [];
    if (!isNone(tenantIds)) {
        const whereQuery = [];
        for (const tenant of tenantIds) {
            params.push(tenant);
            whereQuery.push(`$${params.length}`);
        }
        if (!isNone(whereQuery)) {
            query.push(`tenant_id IN ( ${whereQuery.join(',')} )`);
        }
    }
    if (ignoreCase) {
        params.push(`%${searchValue}%`);
        query.push(`shop_name LIKE $${params.length}`);
    } else {
        params.push(`%${searchValue.toUpperCase()}%`);
        query.push(`UPPER(shop_name) LIKE $${params.length}`);
    }
    const sql = `${baseSql} AND ${query.join(' AND ')}`;
    const { rows } = await psql.query(context, { sql, params });
    if (!isNone(rows)) {
        return rows.map((r) => r.shop_id);
    } else {
        return [];
    }
};

/**
 * get list of tempo ID based on super tempo ID
 * @param {object} context
 * @param {string} tenantId
 * @param {string} superTempoId
 * @returns {Promise<Array<string>>}
 */
const getManagedTempoData = async (context, tenantId, superTempoId) => {
    const superTempoCode = getSuperTempoCode(superTempoId);
    if (isNone(superTempoCode)) return [];
    const sql = `
        SELECT
            shop_id
        FROM
            shops
        WHERE
            status = true
            AND tenant_id = $1
            AND shop_id LIKE $2`;
    const { rows } = await psql.query(context, { sql, params: [tenantId, `%${superTempoCode}%`] });
    return !isNone(rows) ? rows.map((r) => r.shop_id) : [];
};

/**
 * Get shop ID and names
 * @param {object} context
 * @param {Array<string>?} tenantIds
 * @returns {Promise<Object<string, string>>} key=shopID : value=shopName
 */
const getTempoNameMap = async (context, tenantIds) => {
    let sql = `SELECT shop_id, shop_name FROM shops WHERE status = true `;
    const params = [];
    if (!isNone(tenantIds) && Array.isArray(tenantIds)) {
        const query = [];
        for (const tenant of tenantIds) {
            params.push(tenant);
            query.push(`$${params.length}`);
        }
        if (query.length > 0) {
            // also include ZMK tempo
            const zmkTempoQuery = [];
            for (const tenant of tenantIds) {
                params.push('ZMK' + tenant.substring(0, 3) + '%');
                zmkTempoQuery.push(`shop_id LIKE $${params.length}`);
            }

            sql += ` AND (tenant_id IN ( ${query.join(', ')} ) OR ( ${zmkTempoQuery.join(' OR ')} ) )`;
        }
    }
    const { rows } = await psql.query(context, { sql, params });
    let result = {};
    if (!isNone(rows)) {
        result = rows.reduce((res, row) => {
            res[row.shop_id] = row.shop_name;
            return res;
        }, {});
    }
    return result;
};

/**
 * Get list of tempo and their name grouped by tenantId
 * @param {object} context
 * @param {Array<string>} tenantIds
 * @returns {Promise<{[tenantId: string]: {[tempoId: string]: string}}>}
 */
const getTempoNameMapByTenants = async (context, tenantIds) => {
    const tempoNames = await Promise.all(tenantIds.map((tenantId) => getTempoNameMap(context, [tenantId])));
    const result = {};
    for (const [index, tenantId] of tenantIds.entries()) {
        result[tenantId] = tempoNames[index];
    }
    return result;
};

/**
 * ALADIN-APIに対応しているテナントの専用店舗を取得
 * @param {object} context
 * @param {string} tenantId
 * @returns {Promise<{shop_id: string, shop_name: string}>|null}
 */
const getDefaultTempoData = async (context, tenantId) => {
    context.log('getDefaultTempoData START', tenantId);
    if (!tenantId) throw new Error('tenantId is required');

    if (tenantId && tenantId.length > 3) {
        let sql = 'SELECT shop_id, shop_name FROM shops';
        const { rows } = await psql.query(context, { sql });
        if (rows && rows.length > 0) {
            return rows.find((r) => r.shop_id.startsWith('TMK' + tenantId.substring(0, 3)));
        }
    }
    return null;
};

/**
 * ALADIN-APIに対応しているテナントの専用店舗リストを取得
 * @param {object} context
 * @returns {Promise<Array<{shop_id: string, shop_name: string}>>|[]}
 */
const getDefaultTempoList = async (context) => {
    context.log('getDefaultTempoList START');

    let sql = `SELECT shop_id, shop_name FROM shops WHERE shop_id LIKE 'TMK%'`;
    const { rows } = await psql.query(context, { sql });
    
    if (rows && rows.length > 0) {
        return rows;
    }

    return [];
};

module.exports = {
    isTempoValid,
    retrieveTempoList,
    getTempoData,
    retrieveMAWPAllTenantsTempoList,
    retrieveMAWPTempoList,
    retrieveMAWPTempoListByTenantId,
    retrieveMAWPTempoListForNewSo,
    searchTempoIdByNameAndTenants,
    getTempoNameMap,
    getTempoNameMapByTenants,
    getManagedTempoData,
    getDefaultTempoData,
    getDefaultTempoList
};
