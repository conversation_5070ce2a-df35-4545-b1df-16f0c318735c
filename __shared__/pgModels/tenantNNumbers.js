const psql = require('../database/postgre');
const { MVNO_SERVICES } = require('../constants/mvnoServicesConstants');

/**
 * find all nNumbers coresspond to tenantId
 * 旧メソッド: retrieveDaihyoNNumbersByTenantID
 * @param  {object} context - API context
 * @param  {object} data    - required params for query
 * @param  {string} data.tenantId
 * @param  {number} [data.serviceType] - MVNO_SERVICES(LITE/FULL/ALL)
 * @return {Promise<Array<string>>}  - array of nNumbers
 */
const findDaihyoNNumbersByTenantId = async (context, { tenantId, serviceType = MVNO_SERVICES.ALL }) => {
    if (!tenantId) throw new Error('tenantId is required');
    context.log('findDaihyoNNumbersByTenantId start :' + tenantId);

    let whereCondition;

    //STEP 14: FULL MVNO
    switch (serviceType) {
        case MVNO_SERVICES.LITE:
            whereCondition = 'AND (full_mvno_flag != true OR full_mvno_flag IS NULL)';
            break;
        case MVNO_SERVICES.FULL:
            whereCondition = 'AND full_mvno_flag = true';
            break;
        default:
            whereCondition = '';
    }

    let sql = `
        SELECT nnumber
        FROM tenant_nnumbers
        WHERE tenant_id = $1
            ${whereCondition}
        ORDER BY
            nnumber ASC`;

    const { rows } = await psql.query(context, { sql, params: [tenantId] });

    return rows?.map((doc) => doc.nnumber);
};

/**
 * テナントN番有効判定
 * @param {object} context
 * @param {object} data
 * @param {string} data.tenantId
 * @param {string} [data.Nno]
 * @returns {Promise<boolean>}
 */
const tenantNBanYukoHantei = async (context, { tenantId, Nno }) => {
    if (!tenantId) throw new Error('tenantId is required');
    context.log('tenantNBanYukoHantei start');
    let result = false;
    const sql = `
        SELECT
            office
        FROM
            tenants
        WHERE
            tenant_id = $1`;
    const sql2 = `
        SELECT
            nnumber
        FROM
            tenant_nnumbers
        WHERE
            tenant_id = $1
            AND nnumber = $2`;

    const officeVal = await psql.query(context, { sql, params: [tenantId] });
    if (officeVal && officeVal.rows && officeVal.rows.length > 0) {
        const officeVal0 = officeVal.rows[0];
        if (!officeVal0) {
            if (Nno) {
                const { rows } = await psql.query(context, { sql: sql2, params: [tenantId, Nno] });
                result = rows && rows.length > 0;
            } // else: N番指定無し ... 社外なのにN番指定無しはエラー (false)
        } else {
            // 社内 ... 社内はN番指定無しでも指定有りでもOK
            result = true;
        }
    } // else: テナントのレコードなし (false)
    return result;
};

const retrieveDaihyoNNumbersAndFullMnvoFlagByTenantID = async (context, { tenantId }) => {
    context.log(`retrieveDaihyoNNumbersAndFullMnvoFlagByTenantID START: ${tenantId}`);

    const sql = 'SELECT nnumber, full_mvno_flag FROM tenant_nnumbers WHERE tenant_id = $1 ORDER BY nnumber ASC ';
    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    const parsedRows = rows.map((doc) => ({
        full_mvno_flag: !!doc.full_mvno_flag,
        nnumber: doc.nnumber,
    }));
    return parsedRows;
};

module.exports = {
    findDaihyoNNumbersByTenantId,
    tenantNBanYukoHantei,
    retrieveDaihyoNNumbersAndFullMnvoFlagByTenantID,
};
