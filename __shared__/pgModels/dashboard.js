const psql = require('../database/postgre');
const dayjs = require('dayjs');

const execLinesSummarySql = async (context, year, month, day) => {
    //end of day
    let _date = dayjs(`${year}-${month}-${day}`).endOf('day').format();

    let sql = `
        SELECT COUNT_TABLE.tenant as tenant, COUNT_TABLE.contract_type, COUNT_TABLE.sim_type, COUNT_TABLE.user_bbuni_ex_flag as bb_type, SUM(count) as count
        FROM
        (
        SELECT count(line_id),
            CASE WHEN LIN.nnumber IN ('N141096449','N141029466','N131030891','N121150564','N121007408','N190173862')
                    THEN  'CON000'
                    ELSE TEN.tenant_id
            END AS tenant,
            LIN.contract_type, LIN.sim_type,
              CASE WHEN LIN.user_bbuni_ex_flag = true
                THEN '対象' 
              WHEN LIN.user_bbuni_ex_flag = false
                THEN '対象外'
                ELSE null
                END AS user_bbuni_ex_flag
        FROM lines as LIN
        LEFT JOIN tenant_nnumbers as TNN ON LIN.nnumber = TNN.nnumber
        LEFT JOIN tenants as TEN On TEN.tenant_id = TNN.tenant_id
        WHERE ((LIN.line_status != '03' AND LIN.open_date <= $1) )
        GROUP BY tenant, LIN.contract_type, LIN.sim_type, LIN.user_bbuni_ex_flag
        UNION
        SELECT count(line_id), ABL.old_tenant_id as tenant, ABL.contract_type, ABL.sim_type, null as user_bbuni_ex_flag
        FROM abone_lines ABL
        WHERE ABL.line_status = '03'
        AND ABL.service_date > $1
        AND ABL.open_date < $1
        GROUP BY tenant, ABL.contract_type, ABL.sim_type
        ) as COUNT_TABLE
        GROUP BY tenant, contract_type, sim_type, user_bbuni_ex_flag`;

    const result = await psql.query(context, { sql, params: [_date] });
    return result?.rows || null;
};

const execLinesIncreaseSql = async (context, year, month, day) => {
    let _date = dayjs(`${year}-${month}-${day}`).format();

    let sql = `
    SELECT
        CASE WHEN LIN.nnumber IN ('N141096449','N141029466','N131030891','N121150564','N121007408','N190173862')
        THEN  'CON000'
        ELSE TEN.tenant_id
        END AS tenant,
        LIN.contract_type, LIN.sim_type, count(line_id) as count
    FROM lines as LIN
    LEFT JOIN tenant_nnumbers as TNN ON LIN.nnumber = TNN.nnumber
    LEFT JOIN tenants as TEN On TEN.tenant_id = TNN.tenant_id
    WHERE open_date::date = $1
    AND (TEN.status = true OR TEN.status is NULL)
    GROUP BY tenant, LIN.contract_type, LIN.sim_type`;

    const result = await psql.query(context, { sql, params: [_date] });
    return result?.rows || null;
};

const execLinesDecreaseSql = async (context, year, month, day) => {
    let _date = dayjs(`${year}-${month}-${day}`).format();

    let sql = `
    SELECT ABL.old_tenant_id as tenant, ABL.contract_type, ABL.sim_type , count(ABL.service_date) as count
    FROM abone_lines as ABL
    WHERE ABL.line_status = '03'
    AND ABL.service_date::date = $1
    GROUP BY ABL.old_tenant_id, ABL.contract_type, ABL.sim_type`;

    const result = await psql.query(context, { sql, params: [_date] });
    return result?.rows || null;
};

const execPlanSummarySql = async (context, year, month, day) => {
    //end of day
    let _date = dayjs(`${year}-${month}-${day}`).endOf('day').format();

    let sql = `
    SELECT COUNT_TABLE.tenant as tenant_id, COUNT_TABLE.plan_id, COUNT_TABLE.service_plan_id, COUNT_TABLE.resale_plan_id, COUNT_TABLE.plan_name, SUM(count) as count
    FROM
    (
      SELECT 
        CASE WHEN LIN.nnumber IN ('N141096449','N141029466','N131030891','N121150564','N121007408','N190173862') 
                THEN  'CON000'
                ELSE TEN.tenant_id  
        END AS tenant,
        PLAN.plan_id,
        PLAN.service_plan_id,
        PLAN.resale_plan_id,
        PLAN.plan_name,
        count(LIN.line_id) 
      FROM lines as LIN
      LEFT JOIN tenant_nnumbers as TNN ON LIN.nnumber = TNN.nnumber
      LEFT JOIN tenants as TEN ON TEN.tenant_id = TNN.tenant_id 
      LEFT JOIN plans as PLAN ON PLAN.resale_plan_id = LIN.resale_plan_id
      WHERE ((LIN.line_status != '03' AND LIN.open_date <= $1) )
      GROUP BY tenant, PLAN.plan_id, PLAN.plan_name, PLAN.service_plan_id, PLAN.resale_plan_id  
      UNION
      SELECT ABL.old_tenant_id as tenant, ABL.old_plan_id as plan_id, PLAN.service_plan_id, PLAN.resale_plan_id, ABL.old_plan_name as plan_name, count(ABL.line_id)
      FROM abone_lines ABL
      LEFT JOIN plans as PLAN ON PLAN.plan_id = ABL.old_plan_id
      WHERE ABL.line_status = '03' 
      AND ABL.service_date > $1
      AND ABL.open_date < $1
      GROUP BY tenant, ABL.old_plan_id, ABL.old_plan_name, PLAN.service_plan_id, PLAN.resale_plan_id
    ) as COUNT_TABLE
    GROUP BY tenant_id, plan_id, plan_name, service_plan_id, resale_plan_id`;

    const result = await psql.query(context, { sql, params: [_date] });
    return result?.rows || null;
};

const execPlansTrafficAvgSql = async (context, year, month, day = 1) => {
    //date start of month
    let _dateStart = dayjs(`${year}-${month}-${day}`).startOf('month').format();
    //date end of month
    let _dateEnd = dayjs(`${year}-${month}-${day}`).endOf('month').format();

    let sql = `
    SELECT TENANT_PLAN_TRAFFIC.tenant_id, TENANT_PLAN_TRAFFIC.plan_id, PLA2.service_plan_id, PLA2.resale_plan_id, TENANT_PLAN_TRAFFIC.plan_name, TENANT_PLAN_TRAFFIC.year, TENANT_PLAN_TRAFFIC.month,
      ROUND(AVG(TENANT_PLAN_TRAFFIC.traffic) / (1024*1024*1024), 4) as avg_traffic_gb
    FROM (
      SELECT CASE WHEN LIN.nnumber IN ('N141096449','N141029466','N131030891','N121150564','N121007408','N190173862') 
                      THEN  'CON000'
                      ELSE COALESCE(TEN.tenant_id, ABL.old_tenant_id) 
              END AS tenant_id,
        COALESCE(PLA.plan_id, ABL.old_plan_id) as plan_id,
        CASE WHEN PLA.plan_id IS NOT NULL
                      THEN  PLA.plan_name
                      ELSE ABL.old_plan_name 
              END AS plan_name,
        LTR.line_id, LTR.year, LTR.month, LTR.traffic
      FROM line_traffics as LTR
      LEFT JOIN lines as LIN ON LTR.line_id = LIN.line_id AND LIN.line_status != '03' AND LIN.open_date <= $2
      LEFT JOIN tenant_nnumbers as TNN ON LIN.nnumber = TNN.nnumber
      LEFT JOIN tenants as TEN ON TEN.tenant_id = TNN.tenant_id 
      LEFT JOIN plans as PLA ON LIN.resale_plan_id = PLA.resale_plan_id
      LEFT JOIN abone_lines as ABL ON LTR.line_id = ABL.line_id AND ABL.line_status = '03' AND (ABL.open_date <= $2 OR ABL.service_date > $1)
      WHERE LTR.year = $3
      AND LTR.month =  $4
      AND plan_id IS NOT NULL
      AND LTR.traffic > 0
    ) TENANT_PLAN_TRAFFIC
    LEFT JOIN plans as PLA2 ON TENANT_PLAN_TRAFFIC.plan_id = PLA2.plan_id
    GROUP BY TENANT_PLAN_TRAFFIC.tenant_id, TENANT_PLAN_TRAFFIC.plan_id, PLA2.service_plan_id, PLA2.resale_plan_id, TENANT_PLAN_TRAFFIC.plan_name, TENANT_PLAN_TRAFFIC.year, TENANT_PLAN_TRAFFIC.month`;

    const result = await psql.query(context, { sql, params: [_dateStart, _dateEnd, year, month] });
    return result?.rows || null;
};

const execUsersTop20Sql = async (context, year, month, day = 1) => {
    //date start of month
    let _dateStart = dayjs(`${year}-${month}-${day}`).startOf('month').format();
    //date end of month
    let _dateEnd = dayjs(`${year}-${month}-${day}`).endOf('month').format();

    let sql = `
    SELECT RANK_FILTER.*
    FROM (
        SELECT TENANT_LINE_TRAFFIC.*, ROUND(TENANT_LINE_TRAFFIC.traffic / (1024*1024*1024), 4)  as traffic_gb,
        RANK() OVER (
          PARTITION BY tenant_id, year, month
            ORDER BY traffic DESC
        ) as rank
        FROM(
          SELECT CASE WHEN LIN.nnumber IN ('N141096449','N141029466','N131030891','N121150564','N121007408','N190173862') 
                          THEN  'CON000'
                          ELSE COALESCE(TEN.tenant_id, ABL.old_tenant_id) 
                  END AS tenant_id,
            LTR.line_id, LTR.year, LTR.month, LTR.traffic
          FROM line_traffics as LTR
          LEFT JOIN lines as LIN ON LTR.line_id = LIN.line_id AND LIN.line_status != '03' AND LIN.open_date <= $2
          LEFT JOIN tenant_nnumbers as TNN ON LIN.nnumber = TNN.nnumber
          LEFT JOIN tenants as TEN ON TEN.tenant_id = TNN.tenant_id 
          LEFT JOIN abone_lines as ABL ON LTR.line_id = ABL.line_id AND ABL.line_status = '03' AND (ABL.open_date <= $2 OR ABL.service_date > $1)
          WHERE LTR.year = $3
          AND LTR.month =  $4
          AND LTR.traffic > 0
        ) TENANT_LINE_TRAFFIC
      ) RANK_FILTER
      WHERE RANK <= 100`;

    const result = await psql.query(context, { sql, params: [_dateStart, _dateEnd, year, month] });
    return result?.rows || null;
};

module.exports = {
    execLinesSummarySql,
    execLinesIncreaseSql,
    execLinesDecreaseSql,
    execPlanSummarySql,
    execPlansTrafficAvgSql,
    execUsersTop20Sql,
};
