const { COCN_TENANT_ID } = require('../constants/specialDefinitions');
const psql = require('../database/postgre');
const { isNone } = require('../helpers/baseHelper');
const queryUtils = require('../utils/queryUtils');

// テナントパスワード取得(パスワードだけ別に取るようにする)
const retrieveMAWPTenantPassword = async (context, { tenantId }) => {
    const sql = 'SELECT hashed_password FROM tenants WHERE tenant_id = $1 AND status = true';
    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    if (rows.length === 0) return null;

    return rows[0].hashed_password;
};

/**
 * テナントN番有効判定
 * tenantNBanYukoHantei
 * check whether nNumber is valid in DB
 * @param  {object} context - API context
 * @param  {object} data    - required params for query
 * @param  {string} data.tenantId - required
 * @param  {string} data.nBan     - required
 * @return {boolean}              - true indicate valid nNumber
 */
const isNNumberValid = async (context, { tenantId, nBan }) => {
    if (!tenantId) throw new Error('tenantId is required');
    context.log('isNNumberValid start :' + tenantId + ':' + nBan);

    let sql = 'SELECT office FROM tenants WHERE tenant_id = $1';
    let { rows } = await psql.query(context, { sql, params: [tenantId] });
    // テナントのレコードなし
    if (rows.length === 0) return false;

    // 社内 ... 社内はN番指定無しでも指定有りでもOK
    if (rows[0].office) return true;

    // 社外
    // N番指定無し ... 社外なのにN番指定無しはエラー
    if (!nBan) return false;

    // N番指定あり
    sql = 'SELECT 1 AS one FROM tenant_nnumbers WHERE tenant_id = $1 AND nnumber = $2';
    ({ rows } = await psql.query(context, { sql, params: [tenantId, nBan] }));
    return rows.length > 0;
};

/**
 * テナント一覧取得
 * @param  {object} context - context object
 * @return {array.<tenantsInfo>} - tenant list
 */
const retrieveTenantList = async (context) => {
    const sql = 'SELECT * FROM tenants WHERE status = true';
    const { rows } = await psql.query(context, { sql });
    return rows;
};

/**
 * It checks if the tenantId is valid
 * @param context - This is the context object that is passed to the function.
 * @param tenantId - The tenantId you want to check.
 * @returns A boolean value
 */
const isTenantValid = async (context, tenantId) => {
    if (!tenantId) throw new Error('tenantId is required');
    context.log('isTenantValid start :' + tenantId);

    let sql = 'SELECT 1 AS one FROM tenants WHERE tenant_id = $1 AND status = true';
    let { rows } = await psql.query(context, { sql, params: [tenantId] });

    return rows.length > 0;
};

/**
 * Checks multiple tenant IDs are valid or not.
 * Returns set of valid tenant IDS
 * @param {object} context
 * @param {Array<string>} tenantIds
 * @returns {Promise<Set<string>>}
 */
const isTenantValidMultiple = async (context, tenantIds) => {
    if (!Array.isArray(tenantIds) || tenantIds.length === 0) return new Set();
    context.log('isTenantValidMultiple start:', tenantIds.length, 'tenants to check');
    const sql = `
        SELECT
            tenant_id
        FROM
            tenants
        WHERE
            tenant_id IN ${queryUtils.buildListOfValues(tenantIds)}
            AND status = true`;
    const { rows } = await psql.query(context, { sql, params: tenantIds });
    if (rows && rows.length > 0) {
        return new Set(rows.map((r) => r.tenant_id));
    }
    return new Set();
};

/**
 * テナントが社内か社外かをチェック
 * @param {object} context
 * @param {string} tenantId
 * @returns {Promise<boolean>} 社内テナントの場合は`true`を返却
 */
const isShanaiTenantType = async (context, tenantId) => {
    const sql = `SELECT COUNT(tenant_type) FROM tenants WHERE tenant_id = $1 AND tenant_type IS NOT NULL`;
    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    let count = 0;
    if (rows && rows.length > 0) count = rows[0].count;
    return count > 0;
};

/**
 * Get shanai flag for multiple tenants
 * @param {object} context
 * @param {Array<string>} tenantIdList
 * @returns {Promise<{[tenantId: string]: boolean}>}
 */
const getShanaiFlagMultiple = async (context, tenantIdList) => {
    if (!Array.isArray(tenantIdList) || tenantIdList.length === 0) return {};
    const sql = `
        SELECT
            tenant_id, office 
        FROM
            tenants
        WHERE
            tenant_id IN ${queryUtils.buildListOfValues(tenantIdList)}`;
    const { rows } = await psql.query(context, { sql, params: tenantIdList });
    if (rows && rows.length > 0) {
        return rows.reduce((acc, cur) => {
            acc[cur.tenant_id] = cur.office;
            return acc;
        }, {});
    }
    return {};
};

/**
 * Get one tenant
 * @param object} context
 * @param {string} tenantId
 * @returns {Promise<{tenantId: string, tenantName: string, shanaiFlag: boolean, tpc: string, customizeMaxConnection: number, clientId: string, clientSecret: string}|null>}
 */
const retrieveOneMAWPTenant = async (context, tenantId) => {
    context.log('retrieveOneMAWPTenant start');
    const sql = `
    SELECT
        tenant_id, tenant_name, office, tpc, customize_max_connection, client_id, client_secret
    FROM
        tenants
    WHERE
        tenant_id = $1 AND status = true `;
    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    if (rows && rows.length > 0) {
        const row = rows[0];
        return {
            tenantId: row.tenant_id,
            tenantName: row.tenant_name || '',
            shanaiFlag: row.office ?? false,
            tpc: row.tpc || '',
            customizeMaxConnection: row.customize_max_connection ?? 0,
            clientId: row.client_id || '',
            clientSecret: row.client_secret || '',
        };
    } else {
        return null;
    }
};

/**
 * Get all tenants
 * @param {object} context
 * @param {boolean} skipComTenant
 * @param {boolean} isGetAll
 * @returns {Promise<Array<{tenantId: string, tenantName: string, shanaiFlag: boolean, tpc: string, customizeMaxConnection: number, fullFlag: boolean}>>}
 */
const retrieveMAWPTenantList = async (context, skipComTenant = false, isGetAll = false) => {
    context.log('retrieveMAWPTenantList start');
    let sql = '';
    if (skipComTenant) {
        sql = `
            SELECT
                TN.tenant_id, TN.tenant_name, TN.office, TN.tpc, TN.customize_max_connection, COALESCE(TNN.full_mvno_flag, false) as full_mvno_flag
            FROM
                tenants as TN
            LEFT JOIN
                tenant_nnumbers as TNN ON TN.tenant_id = TNN.tenant_id AND TNN.full_mvno_flag = true
            WHERE
                TN.status = true AND (NOT(TN.p_tenant_id IS NULL))
            GROUP BY
                TN.tenant_id, TNN.full_mvno_flag
            ORDER BY
                TN.tenant_id`;
    } else if (isGetAll) {
        sql = `
            SELECT
                tenant_id, tenant_name, office, tpc, customize_max_connection, status, false as full_mvno_flag
            FROM
                tenants
            ORDER BY tenant_id`;
    } else {
        sql = `
            SELECT
                TN.tenant_id, TN.tenant_name, TN.office, TN.tpc, TN.customize_max_connection, COALESCE(TNN.full_mvno_flag, false) as full_mvno_flag
            FROM
                tenants as TN
            LEFT JOIN
                tenant_nnumbers as TNN ON TN.tenant_id = TNN.tenant_id AND TNN.full_mvno_flag = true
            WHERE
                TN.status = true
            GROUP BY
                TN.tenant_id, TNN.full_mvno_flag
            ORDER BY
                TN.tenant_id`;
    }
    let result = [];
    const { rows } = await psql.query(context, { sql });
    if (rows && rows.length > 0) {
        result = rows.map((row) => ({
            tenantId: row.tenant_id,
            tenantName: row.tenant_name || '',
            shanaiFlag: row.office ?? false,
            tpc: row.tpc || '',
            customizeMaxConnection: row.customize_max_connection ?? 0,
            fullFlag: row.full_mvno_flag ?? false,
        }));
    }
    return result;
};

/**
 * check plan_change_flag value
 * @param {object} context
 * @param {string} tenantId
 * @returns {Promise<boolean>} return true if value is not null
 */
const checkPlanHenKouFlag = async (context, tenantId) => {
    if (!tenantId) throw new Error('tenantId is required');
    context.log('checkPlanHenKouFlag start: ' + tenantId);

    let sql = 'select COUNT(plan_change_flag) from tenants where tenant_id = $1';
    let { rows } = await psql.query(context, { sql, params: [tenantId] });

    let count = 0;
    if (rows && rows.length > 0) count = rows[0].count;
    return count > 0;
};

/**
 * Get tenant ID and names
 * @param {object} context
 * @param {Array<string>?} tenantIds filter for specific tenants
 * @returns {Promise<Object<string, string>>} key=tenantId : value=tenantName
 */
const getTenantNameMap = async (context, tenantIds) => {
    let sql = `SELECT tenant_id, tenant_name FROM tenants WHERE status = true `;
    const params = [];
    if (!isNone(tenantIds) && Array.isArray(tenantIds)) {
        const query = [];
        for (const tenant of tenantIds) {
            params.push(tenant);
            query.push(`$${params.length}`);
        }
        if (query.length > 0) {
            sql += ` AND tenant_id IN ( ${query.join(', ')} )`;
        }
    }
    /** @type {{ rows: Array<Object>}} */
    const { rows } = await psql.query(context, { sql, params });
    let result = {};
    if (!isNone(rows)) {
        result = rows.reduce((res, row) => {
            res[row.tenant_id] = row.tenant_name;
            return res;
        }, {});
    }
    return result;
};

const isTenantFullMVNO = async (context, tenantId) => {
    //If all params is Null or empty => False
    if (!tenantId) return false;
    //if CON return true
    if (tenantId === COCN_TENANT_ID) return true;
    context.log('isTenantFullMVNO start :' + tenantId);
    let sql = 'SELECT DISTINCT(tenant_id) FROM tenant_nnumbers WHERE tenant_id=$1 and full_mvno_flag = true';
    let { rows } = await psql.query(context, { sql, params: [tenantId] });

    return rows.length > 0;
};

/**
 *
 * @param {object} context
 * @param {string|null} tenantId
 * @returns {Promise<Array<object>|null>} [tenants] or null
 */
const getAllTenantsData = async (context, tenantId) => {
    context.log('getAllTenantsData START', tenantId);

    const resultList = await retrieveMAWPTenantList(context);
    if (!isNone(tenantId)) {
        return resultList.filter((r) => r.tenantId === tenantId);
    }
    return resultList;
};

module.exports = {
    retrieveMAWPTenantPassword,
    isNNumberValid,
    retrieveTenantList,
    isTenantValid,
    isTenantValidMultiple,
    isShanaiTenantType,
    getShanaiFlagMultiple,
    retrieveOneMAWPTenant,
    retrieveMAWPTenantList,
    checkPlanHenKouFlag,
    getTenantNameMap,
    isTenantFullMVNO,
    getAllTenantsData,
};
