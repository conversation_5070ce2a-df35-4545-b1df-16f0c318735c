const { MVNO_SERVICES } = require('../constants/mvnoServicesConstants');
const { CONTRACT_TYPES } = require('../constants/simConstants');
const psql = require('../database/postgre');
const { isNone } = require('../helpers/baseHelper');
const DaihyouBangoModel = require('../models/daihyouBango.model');
const { DaihyouBangoConstants } = DaihyouBangoModel;
const { checkPlanHenKouFlag, isShanaiTenantType } = require('../pgModels/tenants');

/**
 * An object containing network details
 * @typedef {object} networkInfo
 * @property {string} network
 * @property {boolean} sms_enable
 * @property {boolean} voice_flag
 * @property {boolean} full_mvno_flag
 */

/**
 * @typedef {object} MAWPPlan
 * @property {number} planID
 * @property {string} resalePlanId
 * @property {string} servicePlanId
 * @property {string} planName
 * @property {boolean} isFullMvno
 * @property {string} network
 * @property {boolean?} smsEnable
 * @property {boolean?} voiceFlag
 * @property {boolean?} defaultSimFlag
 */

/**
 * get networkInfo by planId
 * 旧メソッド: getNwTypeByPlanId
 * @param  {object} context - API context
 * @param  {string} planId
 * @return {networkInfo}    - data from DB
 */
const getNetworkInfoByPlanId = async (context, planId) => {
    if (!planId) throw new Error('planId is required');
    context.log('getNetworkInfoByPlanId start :' + planId);

    const sql = `
        SELECT
            network,
            sms_enable,
            voice_flag,
            full_mvno_flag
        FROM
            plans
        WHERE
            plan_id = $1`;

    const { rows } = await psql.query(context, { sql, params: [planId] });
    if (rows.length === 0) return null;

    return rows[0];
};

/**
 * convert planId to docomo's resalePlan
 * 旧メソッド: retrieveResalePlanOfDocomo
 * @param  {object} context - API context
 * @param  {object} data    - required params for query
 * @param  {number|string} data.planId
 * @param  {string} [data.contractType]
 * @return {Promise<string|null>} ryoukinPlan        - docomo's resalePlanId
 */
const convertPlanIdToRyoukinPlan = async (context, { planId, contractType }) => {
    if (!planId) throw new Error('planId is required');
    context.log('convertPlanIdToRyoukinPlan start :' + planId);

    const sql = `
        SELECT
            network,
            sms_enable,
            voice_flag
        FROM
            plans
        WHERE
            plan_id = $1`;

    const { rows } = await psql.query(context, { sql, params: [planId] });
    if (rows.length === 0) {
        context.log('cannot retrieve ResalePlan from planId :' + planId);
        return null;
    }

    // COM料金プランからドコモの料金プランに変換するルール
    const { network, sms_enable, voice_flag } = rows[0];
    switch (network) {
        case '3G':
            if (sms_enable === false && voice_flag === false) {
                return DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN;
            } else if (sms_enable === true && voice_flag === false) {
                return DaihyouBangoConstants.FOMA_YUPIKITASU_PLAN;
            }
            break;
        case 'LTE':
            if (sms_enable === false && voice_flag === false) {
                if (contractType === 'SH') return DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN;
                return DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN;
            } else if (sms_enable === true && voice_flag === false) {
                return DaihyouBangoConstants.XI_YUPIKITASU;
            } else if (voice_flag === true) {
                if (contractType === 'SJ') return DaihyouBangoConstants.OROSHI_TYPE_FIVE_G;
                return DaihyouBangoConstants.TYPE_XI;
            }
            break;
        case '5G(NSA)':
            if (sms_enable === false && voice_flag === false) {
                return DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN;
            } else if (voice_flag === true) {
                return DaihyouBangoConstants.OROSHI_TYPE_FIVE_G;
            }
    }

    return null;
};

/**
 * 旧メソッド: retrieveResalePlanOfDocomoByKaisenNo
 * @param {object} context
 * @param {string} lineId
 */
const convertPlanIdToRyoukinPlanByKaisenNo = async (context, lineId) => {
    if (isNone(lineId)) throw new Error('lineId is required');
    context.log('convertPlanIdToRyoukinPlanByKaisenNo start:', lineId);
    const sql = 'SELECT contract_type FROM lines WHERE line_id = $1';
    const { rows } = await psql.query(context, { sql, params: [lineId] });

    /** @type {string|null} */
    let result = null;

    if (rows && rows.length > 0) {
        const row = rows[0];
        /**
         * COM料金プランからドコモの料金プランに変換するルール
         */
        switch (row.contract_type) {
            case CONTRACT_TYPES.THREE_G:
                result = DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN; // 卸ＦＯＭＡ特定接続プラン
                break;
            case CONTRACT_TYPES.THREE_G_SMS:
                result = DaihyouBangoConstants.FOMA_YUPIKITASU_PLAN; // 卸ＦＯＭＡユビキタスプラン
                break;
            case CONTRACT_TYPES.LTE:
                result = DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN; // 卸Ｘｉ特定接続プラン
                break;
            case CONTRACT_TYPES.LTE_SMS:
                result = DaihyouBangoConstants.XI_YUPIKITASU; // 卸Ｘｉユビキタス
                break;
            case CONTRACT_TYPES.LTE_VOICE:
                result = DaihyouBangoConstants.TYPE_XI; // タイプＸｉ
                break;
            case CONTRACT_TYPES.FIVE_G_NSA:
                result = DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN; // 卸５G特定接続プラン
                break;
            case CONTRACT_TYPES.FIVE_G_NSA_VOICE:
                result = DaihyouBangoConstants.OROSHI_TYPE_FIVE_G; // 卸タイプ５G
                break;
        }
        context.log('convertPlanIdToRyoukinPlanByKaisenNo found:', lineId, result ?? '(null)');
    } else {
        context.log('convertPlanIdToRyoukinPlanByKaisenNo not found:', lineId);
    }

    return result;
};

/**
 * get defaultSimFlag search by planId
 * @param  {object} context - API context
 * @param  {string} planId
 * @return {boolean|null} defaulSimFlag
 */
const getDefaultSimFlagByPlanId = async (context, planId) => {
    if (!planId) throw new Error('planId is required');
    context.log('getDefaultSimFlag start :' + planId);

    const sql = `
        SELECT default_sim_flag
        FROM plans
        WHERE plan_id = $1`;

    const { rows } = await psql.query(context, { sql, params: [planId] });
    if (rows.length === 0) return null;

    return rows[0].default_sim_flag;
};

/**
 * 卸ポータルコアのマスタデータを用いて、N番に対応するカード用途と プラン の整合性をチェックする
 * 卸ポータルコアのマスタデータを用いて、N番に対応するカード用途と カード の整合性をチェックする
 * @param  {object} context - API context
 * @param  {object} data    - required params for query
 * @param  {number|string} data.planId
 * @param  {string} data.nNumber
 * @param  {string} data.simType
 * @return {Promise<boolean|string>} defaulSimFlag
 */
const checkIngerityCardAndPlan2 = async (context, { planId, nNumber, simType }) => {
    if (!planId) throw new Error('planId is required');
    if (!nNumber) throw new Error('nNumber is required');
    if (!simType) throw new Error('simType is required');

    context.log('checkIngerityCardAndPlan2 start :' + planId);

    const sqlCheckCardAndPlan = `
        SELECT
            COUNT(*)
        FROM
            plans AS A
            INNER JOIN card_check_plan AS B ON A.resale_plan_id = B.resale_plan_id
            INNER JOIN customer_info AS C ON B.purpose = C.purpose
        WHERE
            A.plan_id = $1
            AND C.nnumber = $2`;

    const sqlCheckPlan = `
        SELECT
            COUNT(*)
        FROM
            plans AS A
            INNER JOIN card AS B ON A.network = B.network
                AND A.sms_enable = B.sms_enable
                AND A.voice_flag = B.voice_flag
            INNER JOIN card_check_card AS C ON B.device_type_id = C.device_type_id
            INNER JOIN customer_info AS D ON C.purpose = D.purpose
        WHERE
            A.plan_id = $1
            AND B.sim_type = $2
            AND B.priority_line = FALSE
            AND D.nnumber = $3`;

    const [checkVal1, checkVal2] = await Promise.all([
        psql.query(context, { sql: sqlCheckCardAndPlan, params: [planId, nNumber] }),
        psql.query(context, { sql: sqlCheckPlan, params: [planId, simType, nNumber] }),
    ]);
    if (checkVal1.rows[0].count == 0) return '994009';
    if (checkVal2.rows[0].count == 0) return '994010';

    return true;
};

/**
 * Uses `checkIngerityCardAndPlan2`, return false if the original function returns error code
 * - 卸ポータルコアのマスタデータを用いて、N番に対応するカード用途と プラン の整合性をチェックする
 * - 卸ポータルコアのマスタデータを用いて、N番に対応するカード用途と カード の整合性をチェックする
 * @param  {object} context - API context
 * @param  {object} data    - required params for query
 * @param  {number|string} data.planId
 * @param  {string} data.nNumber
 * @param  {string} data.simType
 * @return {Promise<boolean>}
 */
const checkIngerityCardAndPlan = async (context, { planId, nNumber, simType }) => {
    // if both checkVal1[0].count > 0 && checkVal2[0].count > 0 return true else false
    const result = await checkIngerityCardAndPlan2(context, { planId, nNumber, simType });
    if (typeof result == 'boolean') {
        return result;
    } else {
        return false;
    }
};

/**
 * check if planId is ライトMVNO and 音声プラン
 * @param {number} planId
 * @returns {Promise<boolean?>}
 */
const isVoicePlanIdWithLiteMVNO = async (context, planId) => {
    if (!planId) throw new Error('planId is required');
    context.log('isVoicePlanIdWithLiteMVNO start :' + planId);

    const sql = `
        SELECT
            voice_flag
        FROM
            plans
        WHERE
            plan_id = $1
            AND (full_mvno_flag = false OR full_mvno_flag IS NULL) `;
    const { rows } = await psql.query(context, { sql, params: [planId] });
    return rows && rows.length > 0 ? rows[0].voice_flag : null;
};

/**
 * カード種別IDを取得
 *
 * 旧メソッド: retrieveCardTypeId(nNo, planId, simType, networkOpt)
 * @param {object} context
 * @param {string} nNo
 * @param {number} planId
 * @param {string} simType
 * @param {string} [networkOpt]
 * @returns {Promise<string>}
 */
const retrieveCardTypeIdByNno = async (context, nNo, planId, simType, networkOpt) => {
    let query, params;
    if (networkOpt) {
        query = `
            SELECT DISTINCT
                B.device_type_id
            FROM
                plans AS A
            INNER JOIN
                card AS B 
            ON
                B.network = $1
                AND A.sms_enable = B.sms_enable
                AND A.voice_flag = B.voice_flag 
            INNER JOIN
                card_check_card AS C
            ON
                B.device_type_id = C.device_type_id 
            INNER JOIN
                customer_info AS D
            ON
                C.purpose = D.purpose 
            WHERE
                A.plan_id = $2
                AND B.sim_type = $3
                AND B.priority_line = FALSE
                AND D.nnumber = $4`;
        params = [networkOpt, planId, simType, nNo];
    } else {
        query = `
            SELECT DISTINCT
                B.device_type_id
            FROM
                plans AS A
            INNER JOIN
                card AS B 
            ON
                A.network=B.network
                AND A.sms_enable = B.sms_enable
                AND A.voice_flag = B.voice_flag 
            INNER JOIN
                card_check_card AS C
            ON
                B.device_type_id = C.device_type_id 
            INNER JOIN
                customer_info AS D
            ON
                C.purpose = D.purpose 
            WHERE
                A.plan_id = $1
                AND B.sim_type = $2
                AND B.priority_line = FALSE
                AND D.nnumber = $3`;
        params = [planId, simType, nNo];
    }

    const { rows } = await psql.query(context, { sql: query, params });
    return rows && rows.length !== 0 ? rows[0].device_type_id : '';
};

/**
 * カード種別IDを取得
 *
 * 旧メソッド: retrieveCardTypeId(kaisenNo, simType)
 * @param {object} context
 * @param {string} kaisenNo
 * @param {string} simType
 * @returns {Promise<string>}
 */
const retrieveCardTypeIdByKaisenNo = async (context, kaisenNo, simType) => {
    const sqlGetCardTypeId = `
        SELECT DISTINCT
            B.device_type_id
        FROM
            plans AS A
        INNER JOIN
            card AS B 
        ON
            B.network = $1
            AND A.sms_enable = B.sms_enable
            AND A.voice_flag = B.voice_flag 
        INNER JOIN
            card_check_card AS C
        ON
            B.device_type_id = C.device_type_id 
        INNER JOIN
            customer_info AS D
        ON
            C.purpose = D.purpose 
        WHERE
            A.plan_id = (
                select plan_id
                from lines as l
                INNER JOIN plans as p
                on l.resale_plan_id=p.resale_plan_id
                where line_id=$2)
            AND B.sim_type = $3
            AND B.priority_line = FALSE
            AND D.nnumber = (
                select nnumber
                from lines as l
                INNER JOIN plans as p
                on l.resale_plan_id=p.resale_plan_id
                where line_id=$4)`;
    const sqlGetNWTypeOfLine = `
        SELECT CASE contract_type
            WHEN '5G(NSA)(音声)' THEN '5G(NSA)'
            WHEN '5G(NSA)' THEN '5G(NSA)'
            WHEN 'LTE(音声)' THEN 'LTE'
            WHEN 'LTE' THEN 'LTE'
            WHEN 'LTE(SMS)' THEN 'LTE'
            WHEN '3G(SMS)' THEN '3G'
            WHEN '3G' THEN '3G'
            ELSE 'LTE' END network
        FROM lines
        WHERE line_id = $1`;
    const lineNWType = await psql.query(context, { sql: sqlGetNWTypeOfLine, params: [kaisenNo] });
    const lineNWTypeVal = lineNWType.rows ? lineNWType.rows[0].network : '';
    if (!lineNWTypeVal) context.log.error("Didn't find network of line: ", kaisenNo);

    const { rows } = await psql.query(context, {
        sql: sqlGetCardTypeId,
        params: [lineNWTypeVal, kaisenNo, simType, kaisenNo],
    });
    return rows && rows.length > 0 ? rows[0].device_type_id : '';
};

/**
 * STEP18: NW契約変更時に新しい契約種別に基づいてカード種別を取得する
 * @param {object} context
 * @param {string} simType
 * @param {string} network
 * @param {boolean} smsEnabled
 * @param {boolean} voiceFlag
 * @param {boolean} priorityLine
 * @returns {Promise<string|null>}
 */
const retrieveCardTypeId = async (context, simType, network, smsEnabled, voiceFlag, priorityLine) => {
    const sql = `
        SELECT DISTINCT
            device_type_id
        FROM
            card
        WHERE
            sim_type = $1
            AND network = $2
            AND sms_enable = $3 
            AND voice_flag = $4
            AND priority_line = $5`;
    const { rows } = await psql.query(context, {
        sql,
        params: [simType, network, smsEnabled, voiceFlag, priorityLine],
    });
    return rows && rows.length !== 0 ? rows[0].device_type_id : null;
};

/**
 *
 * @param {object} context
 * @param {string} kaisenNo
 */
const getNWType = async (context, kaisenNo) => {
    const sql = `
        SELECT
            A.contract_type as contract_type,
            A.sim_flag as sim_flag,
            B.network as network,
            B.sms_enable as sms_enable,
            B.voice_flag as voice_flag,
            B.full_mvno_flag as full_mvno_flag 
        FROM
            lines as A
        LEFT OUTER JOIN
            plans as B
        ON
            A.resale_plan_id = B.resale_plan_id 
        WHERE
            A.line_id = $1`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    if (rows && rows.length > 0) {
        return {
            contractType: rows[0].contract_type,
            network: rows[0].network,
            smsEnabled: rows[0].sms_enable,
            voiceFlag: rows[0].voice_flag,
            fullMvnoFlag: rows[0].fullMvnoFlag,
            simFlag: rows[0].sim_flag,
        };
    } else return {};
};

/**
 * Get selected plan detail
 * @param {object} context
 * @param {string} tenantId
 * @param {number} planId
 * @param {number} service default: MVNO_SERVICES.ALL
 * @returns {Promise<{
 *   planID: number|any,
 *   resalePlanId: string,
 *   servicePlanId: string,
 *   planName: string,
 *   isFullMvno: boolean,
 *   network: string,
 *   smsEnable: string?,
 *   voiceFlag: string?,
 *   defaultSimFlag: boolean?,
 * }|null>}
 */
const retrieveSelectedMAWPPlan = async (context, tenantId, planId, service = MVNO_SERVICES.ALL) => {
    context.log('retrieveSelectedMAWPPlan start:', tenantId);

    let serviceQuery = '';
    if (service === MVNO_SERVICES.LITE) {
        serviceQuery = ' AND (B.full_mvno_flag = false OR B.full_mvno_flag IS NULL) ';
    } else if (service === MVNO_SERVICES.FULL) {
        serviceQuery = ' AND B.full_mvno_flag = true ';
    }

    let sql = `
        SELECT
            A.plan_id AS plan_id, B.plan_name AS plan_name, B.resale_plan_id as resale_plan_id, B.full_mvno_flag, 
            B.network AS network, B.sms_enable AS sms_enable, B.voice_flag AS voice_flag, B.default_sim_flag AS default_sim_flag, B.sim_type AS sim_type
        FROM
            tenant_plans A
        LEFT JOIN
             plans AS B ON A.plan_id = B.plan_id
        WHERE
            A.tenant_id = $1
            AND A.plan_id = $2 
            AND (NOT B.plan_name LIKE '【廃止】%') ${serviceQuery}
        LIMIT 1`; // for removing duplicates from tenant_plans

    const { rows } = await psql.query(context, { sql, params: [tenantId, planId] });
    if (rows && rows.length > 0) {
        return {
            planID: isNaN(parseInt(rows[0].plan_id, 10)) ? rows[0].plan_id : parseInt(rows[0].plan_id, 10),
            resalePlanId: rows[0].resale_plan_id ?? '',
            servicePlanId: rows[0].service_plan_id ?? '',
            planName: rows[0].plan_name ?? '',
            isFullMvno: rows[0].full_mvno_flag ?? false,
            network: rows[0].network ?? '',
            smsEnable: rows[0].sms_enable,
            voiceFlag: rows[0].voice_flag,
            defaultSimFlag: rows[0].default_sim_flag,
            simType: rows[0].sim_type ?? '', //STEP24.0 set eSIM in sim_type column of eSIM plan
        };
    } else {
        return null;
    }
};

/**
 * Get all plans
 */
const retrieveAllMAWPPlan = async (context) => {
    context.log('retrieveAllMAWPPlan');

    const sql = 'SELECT * FROM plans ORDER BY plan_id ASC';
    const { rows } = await psql.query(context, { sql });
    if (rows && rows.length > 0) {
        return rows.map((r) => {
            const item = {
                planID: isNaN(parseInt(r.plan_id, 10)) ? r.plan_id : parseInt(r.plan_id, 10),
                resalePlanId: r.resale_plan_id ?? '',
                servicePlanId: r.service_plan_id ?? '',
                planName: r.plan_name ?? '',
                isFullMvno: r.full_mvno_flag ?? false,
            };
            return item;
        });
    }
};

/**
 *
 * @param {object} context
 * @param {string} tenantId
 * @param {number} service default: MVNO_SERVICES.ALL
 * @returns {Promise<Array<{
 *   planID: number|any,
 *   resalePlanId: string,
 *   servicePlanId: string,
 *   planName: string,
 *   isFullMvno: boolean,
 *   network: string,
 *   smsEnable: string?,
 *   voiceFlag: string?,
 *   defaultSimFlag: boolean?,
 * }>>}
 */
const retrieveMAWPPlanList = async (context, tenantId, service = MVNO_SERVICES.ALL) => {
    context.log('retrieveMAWPPlanList start:', tenantId);

    let sql;
    if (service === MVNO_SERVICES.LITE) {
        sql = `
            SELECT
                A.plan_id AS plan_id, B.plan_name AS plan_name, B.resale_plan_id as resale_plan_id, B.full_mvno_flag, 
                B.network AS network, B.sms_enable AS sms_enable, B.voice_flag AS voice_flag, B.default_sim_flag AS default_sim_flag, B.sim_type AS sim_type
            FROM 
                (SELECT DISTINCT plan_id FROM tenant_plans WHERE tenant_id = $1 ) AS A 
            INNER JOIN
                plans AS B ON A.plan_id = B.plan_id AND (B.full_mvno_flag = false OR B.full_mvno_flag IS NULL) 
            WHERE
                (NOT B.plan_name LIKE '【廃止】%') ORDER BY A.plan_id ASC`;
    } else if (service === MVNO_SERVICES.FULL) {
        sql = `
            SELECT
                A.plan_id AS plan_id, B.plan_name AS plan_name, B.resale_plan_id as resale_plan_id, B.full_mvno_flag, 
                B.network AS network, B.sms_enable AS sms_enable, B.voice_flag AS voice_flag, B.default_sim_flag AS default_sim_flag, B.sim_type AS sim_type
            FROM
                (SELECT DISTINCT plan_id FROM tenant_plans WHERE tenant_id = $1 ) AS A 
            INNER JOIN
                plans AS B ON A.plan_id = B.plan_id AND B.full_mvno_flag = true 
            WHERE
                (NOT B.plan_name LIKE '【廃止】%') ORDER BY A.plan_id ASC`;
    } else {
        sql = `
            SELECT
                A.plan_id AS plan_id, B.plan_name AS plan_name, B.resale_plan_id as resale_plan_id, B.full_mvno_flag, 
                B.network AS network, B.sms_enable AS sms_enable, B.voice_flag AS voice_flag, B.default_sim_flag AS default_sim_flag, B.sim_type AS sim_type
            FROM
                (SELECT DISTINCT plan_id FROM tenant_plans WHERE tenant_id = $1 ) AS A 
            INNER JOIN
                plans AS B ON A.plan_id = B.plan_id 
            WHERE
                (NOT B.plan_name LIKE '【廃止】%') ORDER BY A.plan_id ASC`;
    }

    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    if (rows && rows.length > 0) {
        return rows.map((r) => ({
            planID: isNaN(parseInt(r.plan_id, 10)) ? r.plan_id : parseInt(r.plan_id, 10),
            resalePlanId: r.resale_plan_id ?? '',
            servicePlanId: r.service_plan_id ?? '',
            planName: r.plan_name ?? '',
            isFullMvno: r.full_mvno_flag ?? false,
            network: r.network ?? '',
            smsEnable: r.sms_enable,
            voiceFlag: r.voice_flag,
            defaultSimFlag: r.default_sim_flag,
            simType: r.sim_type ?? '', // STEP24.0 set eSIM in sim_type column of eSIM plan
        }));
    } else {
        return [];
    }
};

/**
 * Get plan ID and N番 from line number
 * @param {object} context
 * @param {string} lineId
 */
const retrieveKaisenInfo = async (context, lineId) => {
    context.log('retrieveKaisenInfo start', lineId);
    const sql = `
        SELECT
            B.plan_id as plan_id, A.nnumber as nban, B.plan_name as plan_name
        FROM
            lines as A 
        LEFT OUTER JOIN
            plans as B ON A.resale_plan_id = B.resale_plan_id 
        WHERE
            A.line_id= $1 AND A.line_status != '03'`;
    const { rows } = await psql.query(context, { sql, params: [lineId] });
    if (rows && rows.length > 0) {
        return {
            planId: isNone(rows[0].plan_id) ? 0 : rows[0].plan_id,
            nBan: rows[0].nban,
            planName: rows[0].plan_name ?? '',
        };
    }
    return null;
};

/**
 *
 * @param {object} context
 * @param {Array<string>} tenantIds
 * @param {number} service
 * @returns {Promise<Array<MAWPPlan>>}
 */
const retrieveMAWPPlanListByMultiTenantId = async (context, tenantIds, service) => {
    context.log('retrieveMAWPPlanListByMultiTenantId start:', tenantIds);

    let params = ['not_exists_tenant_id']; // 0
    let tenantPlaceholder = '$1';

    if (tenantIds && tenantIds.length > 0) {
        // $1,$2,$3,...
        tenantPlaceholder = tenantIds.map((_t, index) => `$${index + 1}`).join(',');
        params = tenantIds;
    }

    let serviceQuery = ' ';
    if (service == MVNO_SERVICES.LITE) {
        serviceQuery = ' AND B.full_mvno_flag IS NOT TRUE ';
    } else if (service == MVNO_SERVICES.FULL) {
        serviceQuery = ' AND B.full_mvno_flag IS TRUE ';
    }

    const sql = `
        SELECT
            A.plan_id AS plan_id, B.plan_name AS plan_name, B.resale_plan_id as resale_plan_id, B.full_mvno_flag
        FROM
            ( 
                SELECT DISTINCT plan_id FROM tenant_plans WHERE tenant_id IN (${tenantPlaceholder}) 
            ) AS A 
        INNER JOIN
            plans AS B ON A.plan_id = B.plan_id ${serviceQuery}
        WHERE
            (NOT B.plan_name LIKE '【廃止】%')
        ORDER BY A.plan_id ASC`;
    const { rows } = await psql.query(context, { sql, params });
    if (rows && rows.length > 0) {
        return rows.map((r) => {
            const result = {
                planID: isNaN(parseInt(r.plan_id, 10)) ? r.plan_id : parseInt(r.plan_id, 10),
                resalePlanId: r.resale_plan_id ?? '',
                servicePlanId: r.service_plan_id ?? '',
                planName: r.plan_name ?? '',
                isFullMvno: r.full_mvno_flag ?? false,
                network: r.network ?? '',
                smsEnable: r.sms_enable,
                voiceFlag: r.voice_flag,
                defaultSimFlag: r.default_sim_flag,
            };
            for (const f of ['smsEnable', 'voiceFlag', 'defaultSimFlag']) {
                if (result[f] === null || result[f] === undefined) {
                    delete result[f];
                }
            }
            return result;
        });
    } else {
        return [];
    }
};

/**
 *
 * @param {object} context
 * @param {number} planId
 * @returns {Promise<boolean | null>}
 */
const retrieveDefaultSIMFlag = async (context, planId) => {
    context.log('retrieveDefaultSIMFlag start:', planId);

    const sql = 'SELECT default_sim_flag FROM plans WHERE plan_id = $1';

    const { rows } = await psql.query(context, { sql, params: [planId] });

    if (rows && rows.length > 0) {
        return rows[0].default_sim_flag;
    } else {
        return null;
    }
};

/**
 *
 * @param {object} context
 * @param {number} planId
 * @param {string} tenantId
 * @param {string} current_network
 * @returns {Promise<Option<PlanDetail>>}
 */
const getKaisenPlanDetail = async (context, planId, tenantId, current_network) => {
    context.log('getKaisenPlanDetail start');

    const sql_plan = `
        SELECT A.plan_id, A.plan_name, A.plan_description
        FROM plans as A
        WHERE A.plan_id = $1`;
    const sql_plan_order = `
        SELECT A.option_plan_id, A.option_plan_name, A.option_plan_description, A.option_plan_type
        FROM option_plans as A
        JOIN plan_option_plans as B ON A.option_plan_id = B.option_plan_id
        WHERE B.plan_id = $1`;
    const sql_plan_henko_old = `
        SELECT B.plan_name, A.change_plan_id as plan_id, B.plan_description
        FROM tenant_plans as A
        JOIN plans as B ON A.change_plan_id = B.plan_id
        WHERE A.tenant_id = '${tenantId}'
        AND A.plan_id = '${planId}'
        AND change_plan_id != '${planId}'`;

    //STEP 14.0 UPDATE FULL MVNO FLAG
    //STEP 18: (5G対応）プラン変更画面の改修: (pla.network = 'LTE' AND plb.network = '5G') OR (pla.network = 'LTE(音声)' AND plb.network = '5G(音声)
    const sql_plan_henko_new = `
        SELECT plb.plan_id, plb.plan_name, plb.plan_description
        FROM tenants AS tnt, plans AS pla, plans AS plb
        WHERE plb.plan_id IN (
            SELECT tp.change_plan_id
            FROM tenant_plans AS tp
            WHERE tp.tenant_id = '${tenantId}'
            AND tp.change_plan_id != '${planId}'
        )
        AND pla.plan_id = '${planId}'
        AND (plb.network = pla.network
        OR (pla.network = 'LTE' AND plb.network = '${current_network}')
        OR (pla.network = '5G(NSA)' AND plb.network = 'LTE'))
        AND tnt.tenant_id = '${tenantId}'
        AND plb.sms_enable = pla.sms_enable
        AND plb.voice_flag = pla.voice_flag
        AND (TNT.plan_change_flag = 1 OR (tnt.plan_change_flag = 2 AND (plb.plan_change_class = 0 OR plb.plan_change_class IS NULL)))
        AND ((pla.full_mvno_flag = TRUE AND plb.full_mvno_flag = TRUE) OR ((pla.full_mvno_flag = FALSE OR pla.full_mvno_flag IS NULL) AND (plb.full_mvno_flag = FALSE OR plb.full_mvno_flag IS NULL)))
        GROUP BY plb.plan_id, plb.plan_name, plb.plan_description
        ORDER BY plb.plan_id`;

    //STEP 23:
    // - ライトMVNO⇔フルMVNO間のプラン変更は不可。
    // - その他はすべてプラン変更可能。
    // - 3GプランからLTEプランなど、NW種別をまたがった変更も可能。
    const sql_plan_henko_patern_3 = `
        SELECT plb.plan_id, plb.plan_name, plb.plan_description
        FROM plans AS pla ,plans AS plb
        WHERE plb.plan_id IN (
            SELECT DISTINCT tp.plan_id FROM tenant_plans AS tp
            WHERE tp.tenant_id = '${tenantId}'
        ) AND plb.plan_id != '${planId}'
        AND pla.plan_id = '${planId}'
        AND plb.sms_enable = pla.sms_enable
        AND plb.voice_flag = pla.voice_flag
        AND ((pla.full_mvno_flag = TRUE AND plb.full_mvno_flag = TRUE) OR ((pla.full_mvno_flag = FALSE OR pla.full_mvno_flag IS NULL) AND (plb.full_mvno_flag = FALSE OR plb.full_mvno_flag IS NULL)))
        GROUP BY plb.plan_id, plb.plan_name, plb.plan_description
        ORDER BY plb.plan_id`;

    let sql_plan_henko = sql_plan_henko_old;
    const [isUseNewPlanHenkou, shanaiTenant] = await Promise.all([
        checkPlanHenKouFlag(context, tenantId),
        isShanaiTenantType(context, tenantId),
    ]);
    if (shanaiTenant) {
        sql_plan_henko = sql_plan_henko_patern_3;
    } else if (isUseNewPlanHenkou) {
        sql_plan_henko = sql_plan_henko_new;
    }

    const [plan, plan_order, plan_henko] = await Promise.all([
        psql.query(context, { sql: sql_plan, params: [planId] }),
        psql.query(context, { sql: sql_plan_order, params: [planId] }),
        psql.query(context, { sql: sql_plan_henko }),
    ]);

    let result,
        planOrderObjList = [],
        planHenkoObjList = [];
    if (plan.rows.length === 0 || plan.rows.length > 1) {
        result = null;
    } else {
        plan_order.rows.map((po) => {
            const optionPlanObj = {
                optionPlanId: po.option_plan_id ?? -1,
                optionPlanName: po.option_plan_name ?? '',
                optionPlanDescription: po.option_plan_description ?? '',
                optionPlanType: po.option_plan_type ?? '',
            };
            planOrderObjList.push(optionPlanObj);
        });
        plan_henko.rows.map((ph) => {
            const planHenkoPlanObj = {
                henkoOrderDescription: ph.plan_description ?? '',
                henkoOrderName: ph.plan_name ?? '',
                henkoOrderId: ph.plan_id ?? -1,
            };
            planHenkoObjList.push(planHenkoPlanObj);
        });
        result = {
            planId: plan.rows[0].plan_id,
            planName: plan.rows[0].plan_name,
            planDescription: plan.rows[0].plan_description,
            planOrder: planOrderObjList,
            planHenko: planHenkoObjList,
        };
    }

    return result;
};

/**
 * get_henko_plan_list
 * @param {object} context
 * @param {string} tenantId
 * @param {number} planId
 * @param {string} currentNetwork
 * @returns {Promise<Array<{changePlanId: number, planName: string, changeTiming: string, changeCount: number}>>}
 */
const getHenkoPlanList = async (context, tenantId, planId, currentNetwork) => {
    context.log('Get Henko Plan start', { tenantId, planId, currentNetwork });
    const [isUseNewPlanHenkou, shanaiTenant] = await Promise.all([
        checkPlanHenKouFlag(context, tenantId),
        isShanaiTenantType(context, tenantId),
    ]);
    let sql = '';
    let params = [];

    if (shanaiTenant) {
        //STEP 23:
        // - ライトMVNO⇔フルMVNO間のプラン変更は不可。
        // - その他はすべてプラン変更可能。
        // - 3GプランからLTEプランなど、NW種別をまたがった変更も可能。
        sql = `
            SELECT
                plb.plan_id as change_plan_id, plb.plan_name, 0 as change_count, null as change_timing
            FROM
                plans AS pla ,plans AS plb
            WHERE
                plb.plan_id IN (
                    SELECT DISTINCT tp.plan_id FROM tenant_plans AS tp
                    WHERE tp.tenant_id = $1
                )
                AND plb.plan_id != $2
                AND pla.plan_id = $3
                AND plb.sms_enable = pla.sms_enable
                AND plb.voice_flag = pla.voice_flag
                AND (
                    (pla.full_mvno_flag = TRUE AND plb.full_mvno_flag = TRUE)
                    OR (
                        (pla.full_mvno_flag = FALSE OR pla.full_mvno_flag IS NULL)
                        AND (plb.full_mvno_flag = FALSE OR plb.full_mvno_flag IS NULL)
                    )
                )
                GROUP BY
                    plb.plan_id, plb.plan_name, plb.plan_description
                ORDER BY
                    plb.plan_id;`;
        params = [tenantId, planId, planId];
    } else if (isUseNewPlanHenkou) {
        // sql_sdm_new_ver
        sql = `
            SELECT
                change_plan_id, plan_name, change_timing, change_count
            FROM (
                SELECT
                    PLB.plan_name, PLB.plan_id as change_plan_id,
                    CASE
                        WHEN TNT.plan_change_flag = 2 AND (PLA.plan_change_class = 0 OR PLA.plan_change_class IS NULL)
                                                AND (PLB.plan_change_class = 0 OR PLB.plan_change_class IS NULL)
                            THEN '月初'
                        ELSE null
                    END as change_timing,
                    CASE
                        WHEN TNT.plan_change_flag = 2 AND (PLA.plan_change_class = 0 OR PLA.plan_change_class IS NULL)
                                                AND (PLB.plan_change_class = 0 OR PLB.plan_change_class IS NULL)
                            THEN 1
                        ELSE 0
                    END as change_count
                FROM
                    tenants TNT, plans PLA
                JOIN
                    tenant_plans TP ON TP.tenant_id = $1
                JOIN
                    plans PLB ON TP.plan_id = PLB.plan_id
                WHERE
                    (PLB.network = PLA.network OR (PLA.network = 'LTE' AND PLB.network = $2) OR (PLA.network = '5G(NSA)' AND PLB.network = 'LTE'))
                    AND PLB.sms_enable = PLA.sms_enable
                    AND PLB.voice_flag = PLA.voice_flag
                    AND (
                            (
                                TNT.plan_change_flag = 1
                            )
                            OR
                            (
                                TNT.plan_change_flag = 2 AND (PLB.plan_change_class = 0 OR PLB.plan_change_class IS NULL)
                            )
                        )
                    AND TP.plan_id != $3
                    AND PLA.plan_id = $4
                    AND TNT.tenant_id = $5
                    AND ((PLA.full_mvno_flag = TRUE AND PLB.full_mvno_flag = TRUE) OR ((PLA.full_mvno_flag = FALSE OR PLA.full_mvno_flag IS NULL) AND (PLB.full_mvno_flag = FALSE OR PLB.full_mvno_flag IS NULL)))
            ) as PLAN_HENKO
            GROUP BY
                change_plan_id, plan_name, change_timing, change_count
            ORDER BY
                change_plan_id`;
        params = [tenantId, currentNetwork, planId, planId, tenantId];
    } else {
        // sql_sdm_old_ver
        sql = `
            SELECT
                B.plan_name, A.change_plan_id, A.change_timing, 0 as change_count
            FROM
                tenant_plans as A JOIN plans as B ON A.change_plan_id = B.plan_id
            WHERE
                A.tenant_id = $1 AND A.plan_id = $2
                AND change_plan_id != $3`;
        params = [tenantId, planId, planId];
    }

    const { rows } = await psql.query(context, { sql, params });
    if (rows && rows.length > 0) {
        return rows.map((r) => ({
            changePlanId: isNone(r.change_plan_id) ? -1 : +r.change_plan_id,
            planName: r.plan_name ?? '',
            changeTiming: r.change_timing ?? '',
            changeCount: isNone(r.change_count) ? 0 : +r.change_count,
        }));
    }
    return [];
};

/**
 * Get plan name from plan ID
 * @param {object} context
 * @param {number|string} planId
 * @returns {Promise<string|null>}
 */
const getPlanNameById = async (context, planId) => {
    const sql = 'SELECT plan_name from plans WHERE plan_id = $1';
    const { rows } = await psql.query(context, { sql, params: [planId] });
    if (rows && rows.length > 0) return rows[0].plan_name;
    return null;
};

/**
 * returns dictionary of plan names where keys are plan IDs and values are plan names
 * @param {object} context
 * @returns {Promise<Object<string, string>>}
 */
const getAllPlanNames = async (context) => {
    const sql = 'SELECT plan_id, plan_name from plans';
    const { rows } = await psql.query(context, { sql, params: [] });
    return isNone(rows)
        ? {}
        : rows.reduce((acc, cur) => {
              acc[cur.plan_id] = cur.plan_name;
              return acc;
          }, {});
};

/**
 * same as `retrieveSimTypeOption` but for multiple planIds
 * @param {object} context
 * @param {Array<number>} planIds
 * @returns {Promise<Array<{planId: number, smsEnable?: boolean, voiceFlag?: boolean}>>}
 */
const retrieveSimTypeOptionList = async (context, planIds) => {
    if (!Array.isArray(planIds) || planIds.length === 0) return [];

    // $1, $2, $3, ...
    const inArray = Array(planIds.length)
        .fill()
        .map((_, i) => `$${i + 1}`)
        .join(', ');

    const sql = `
        SELECT DISTINCT
            p.plan_id, c.sms_enable as sms_enable, c.voice_flag as voice_flag
        FROM
            card AS c 
        INNER JOIN
            plan_card AS pc
        ON
            c.device_type_id=pc.device_type_id 
        INNER JOIN
            plans as p
        ON
            pc.resale_plan_id=p.resale_plan_id 
        WHERE
            p.plan_id IN ( ${inArray} )`;
    const { rows } = await psql.query(context, { sql, params: planIds });
    if (rows) {
        return rows.map((r) => ({
            planId: r.plan_id,
            smsEnable: r.sms_enable,
            voiceFlag: r.voice_flag,
        }));
    }
    return [];
};

/**
 * カード種別IDを取得
 * @param {object} context
 * @param {string} nNo
 * @param {number} planId
 * @param {string} simType
 * @param {boolean} is5GNSA
 * @returns {Promise<string>}
 */
const retrieveCardTypeName = async (context, nNo, planId, simType, is5GNSA) => {
    context.log('retrieveCardTypeName START', { nNo, planId, simType, is5GNSA });

    const network = is5GNSA ? '5G(NSA)' : 'LTE';
    const sql = `
        SELECT DISTINCT B.device_type_name
        FROM plans AS A
        INNER JOIN card AS B
        ON A.sms_enable = B.sms_enable AND A.voice_flag = B.voice_flag
        INNER JOIN card_check_card AS C ON B.device_type_id = C.device_type_id
        INNER JOIN customer_info AS D ON C.purpose = D.purpose
        WHERE A.plan_id = $1 AND B.sim_type = $2 AND B.network = $3 AND B.priority_line = FALSE AND D.nnumber = $4`;

    const { rows } = await psql.query(context, { sql, params: [planId, simType, network, nNo] });
    if (rows && rows.length > 0) {
        return rows[0].device_type_name;
    } else {
        return '';
    }
};

/**
 * 複数のカード種別IDを取得
 * @param {object} context
 * @param {string[]} nNumbers
 * @param {number[]} planIds
 * @param {string[]} simTypes
 * @returns {Promise<Map<string, string>>|null}
 */
const retrieveMultiCardTypeName = async (context, nNumbers, planIds, simTypes) => {
    context.log('retrieveMultiCardTypeName START');

    let sql = `
        SELECT 
            DISTINCT D.nnumber, A.plan_id, B.network, B.sim_type, B.device_type_name
        FROM
            plans AS A
        INNER JOIN
            card AS B ON A.sms_enable = B.sms_enable AND A.voice_flag = B.voice_flag
        INNER JOIN
            card_check_card AS C ON B.device_type_id = C.device_type_id
        INNER JOIN
            customer_info AS D ON C.purpose = D.purpose
        WHERE
            B.priority_line = FALSE`;

    let params = [];
    if (!isNone(planIds)) {
        let whereIn = [];
        for (const planId of planIds) {
            params.push(planId);
            whereIn.push(`$${params.length}`);
        }
        if (whereIn.length > 0) {
            sql += ` AND A.plan_id IN ( ${whereIn.join(',')} )`;
        }
    }

    if (!isNone(simTypes)) {
        let whereIn = [];
        for (const simType of simTypes) {
            params.push(simType);
            whereIn.push(`$${params.length}`);
        }
        if (whereIn.length > 0) {
            sql += ` AND B.sim_type IN ( ${whereIn.join(',')} )`;
        }
    }

    if (!isNone(nNumbers)) {
        let whereIn = [];
        for (const nNo of nNumbers) {
            params.push(nNo);
            whereIn.push(`$${params.length}`);
        }
        if (whereIn.length > 0) {
            sql += ` AND D.nnumber IN ( ${whereIn.join(',')} )`;
        }
    }

    const { rows } = await psql.query(context, { sql, params });
    if (rows && rows.length > 0) {
        const map = new Map();
        rows.map((r) => {
            map.set(
                `${r.nnumber ?? ''}_${r.plan_id ?? ''}_${r.sim_type ?? ''}_${r.network ?? ''}`,
                r.device_type_name ?? ''
            );
        });
        return map;
    } else {
        return null;
    }
};

module.exports = {
    getNetworkInfoByPlanId,
    convertPlanIdToRyoukinPlan,
    convertPlanIdToRyoukinPlanByKaisenNo,
    getDefaultSimFlagByPlanId,
    checkIngerityCardAndPlan2,
    checkIngerityCardAndPlan,
    isVoicePlanIdWithLiteMVNO,
    retrieveCardTypeIdByNno,
    retrieveCardTypeIdByKaisenNo,
    retrieveCardTypeId,
    getNWType,
    retrieveAllMAWPPlan,
    retrieveSelectedMAWPPlan,
    retrieveMAWPPlanList,
    retrieveKaisenInfo,
    retrieveMAWPPlanListByMultiTenantId,
    retrieveDefaultSIMFlag,
    getKaisenPlanDetail,
    getHenkoPlanList,
    getPlanNameById,
    getAllPlanNames,
    retrieveSimTypeOptionList,
    retrieveCardTypeName,
    retrieveMultiCardTypeName,
};
