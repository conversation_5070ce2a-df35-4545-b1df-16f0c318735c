const dayjs = require('dayjs');
const psql = require('../database/postgre');
const { DaihyouBangoConstants } = require('../models/daihyouBango.model');
const { getPortalConfig } = require('../config/appConfig');
// const { isNone } = require('../helpers/baseHelper');
const { USAGE_STATUS } = require('../constants/lineConstants');
const { COCN_TENANT_ID } = require('../constants/specialDefinitions');
const queryUtils = require('../utils/queryUtils');
const { SIM_STATUS } = require('../constants/simConstants');

// TODO fix circular dependency with baseHelper.js
const isNone = (v) => {
    return (
        v === null ||
        v === undefined ||
        v === '' ||
        (typeof v === 'number' && isNaN(v)) ||
        (Array.isArray(v) && v.length == 0) ||
        (typeof v === 'object' && v !== null && Object.keys(v).length == 0)
    );
};

const portalConfig = getPortalConfig();
const DEACTIVATED_STATUS = '03';
const ORDER_TYPE_KAISEN_HAISHI = 52;

const SO_CORE_FUNCTION_TYPE_COUPON_TSUIKA = '03';
const SO_CORE_FUNCTION_TYPE_COUPON_ONOFF = '04';
const SO_CORE_FUNCTION_TYPE_ACTIVATE_DEACTIVATE = '05';
const SO_CORE_FUNCTION_TYPE_PLAN_CHANGE = '06';

const TARGET_LINE = {
    TOUROKUSUMIKAISEN: '登録済回線',
    HAISHIKAISEN: '廃止回線',
    KARITOUROKUKAISEN: '仮登録回線',
    TOUROKUZUMI_KARITOUROKU_KAISEN: '登録済/仮登録回線', // STEP24.0で追加
    KUROKA_OVERDUE_KAISEN: '黒化期限超過回線', // STEP24.0で追加
};
const LIMIT = 20;
const OFFSET = 0;

const SIM_FLAG = {
    黒: false,
    半黒: true,
};
const BB_UNIBA_FLAG = {
    対象: false,
    対象外: true,
};
const HAISHI_SORT_CONDITION_LIST = {
    lineId: 'line_id',
    contractType: 'contract_type',
    oldPlanName: 'old_plan_name',
    simType: 'sim_type',
    serviceDate: 'service_date',
    simFlag: 'sim_flag',
};

const KAISEN_SORT_CONDITION = {
    lineId: 'line_id',
    contractType: 'contract_type',
    planName: 'plan_name',
    simType: 'sim_type',
    openDate: 'open_date',
    simFlag: 'sim_flag',
    imsi: 'imsi',
    simNumber: 'sim_number',
    usageStatus: 'usage_status',
    voicePlanName: 'voice_plan_name',
};

const KAISEN_KANRI_SEARCH_TYPE = {
    KAISEN_NO: '1', // 回線番号
    IMSI: '2', // IMSI
    SIM_NUMBER: '3', // SIM番号
};
/**
 * check isFullMVNO Service or not
 * @param  {object} context - API context
 * @param  {object} data    - required params for query
 * @param  {string} data.planId
 * @param  {string} data.kaisenNo
 * @param  {string} data.nBan
 * @return {boolean}
 */
const isFullMVNO = async (context, { planId, kaisenNo, nBan, soId }) => {
    //If all params is Null or empty => False
    if (!planId && !kaisenNo && !nBan) {
        if (soId) {
            return soId?.startsWith('FM');
        }
        return false;
    }

    const planCheckSql = 'SELECT plan_id FROM plans WHERE plan_id::varchar(255)=$1 AND full_mvno_flag = true';
    const kaisenCheckSql =
        'SELECT line_id FROM lines as LIN JOIN plans as PLN ON LIN.resale_plan_id = PLN.resale_plan_id WHERE LIN.line_id=$1 AND PLN.full_mvno_flag = true';
    const nBanCheckSql = 'SELECT nnumber FROM tenant_nnumbers WHERE nnumber=$1 AND full_mvno_flag = true';

    const [planCheckGet, kaisenCheckGet, nBanCheckGet] = await Promise.all([
        psql.query(context, { sql: planCheckSql, params: [planId] }),
        psql.query(context, { sql: kaisenCheckSql, params: [kaisenNo] }),
        psql.query(context, { sql: nBanCheckSql, params: [nBan] }),
    ]);

    const flagPlan = planCheckGet.rows.length === 1 || !planId;
    const flagKaisen = kaisenCheckGet.rows.length === 1 || !kaisenNo;
    const flagNBan =
        nBanCheckGet.rows.length === 1 || !nBan || portalConfig.COCN_DAIHYOU_NNUMBER_FOR_FULL_MVNO.includes(nBan);

    return flagPlan && flagKaisen && flagNBan;
};

/**
 * Check line numbers is full MVNO or not and return a Set of full MVNO line numbers.
 * (based on `isFullMVNO` function)
 * @param {object} context
 * @param {Array<string>} kaisenNoList
 * @returns {Promise<Set<string>>}
 */
const isFullMVNOKaisenMultiple = async (context, kaisenNoList) => {
    if (!Array.isArray(kaisenNoList) || kaisenNoList.length === 0) return new Set();

    const sql = `
        SELECT
            LIN.line_id
        FROM
            lines AS LIN
        JOIN
            plans AS PLN
        ON
            LIN.resale_plan_id = PLN.resale_plan_id
        WHERE
            LIN.line_id IN ${queryUtils.buildListOfValues(kaisenNoList)}
            AND PLN.full_mvno_flag = true
        GROUP BY
            LIN.line_id
        HAVING
            COUNT(LIN.line_id) = 1;`;

    const { rows } = await psql.query(context, { sql, params: kaisenNoList });

    if (rows && rows.length > 0) {
        return new Set(rows.map((r) => r.line_id));
    } else {
        return new Set();
    }
};

/**
 * check for existence of kaisenNo
 * 旧メソッド: kisetsuKaisenSonzaiHantei
 * @param  {object} context - API context
 * @param  {string} kaisenNo  required
 * @return {boolean}        true - indicate exists in DB
 *                          false - indicate NOT exists in DB
 */
const isKaisenNoActivated = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('isKaisenNoActivated start :' + kaisenNo);

    const sql = `SELECT 1 AS one FROM lines WHERE line_id = $1 AND line_status != '${DEACTIVATED_STATUS}' LIMIT 1`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    return rows.length === 1;
};

/**
 * get tenantId by kaisenNo
 * 旧メソッド: getBelongsToTenantByKaisenNo
 * @param  {object} context - API context
 * @param  {string} kaisenNo   - required
 * @return {string|null} tenantId
 */
const getTenantIdByKaisenNo = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('getTenantIdByKaisenNo start :' + kaisenNo);

    //回線テナント取得
    //line_tenantsテーブルを検索する
    const lineTenantSql = `
        SELECT
            B.tenant_id
        FROM
            lines AS A
            INNER JOIN line_tenants AS B ON A.line_id = B.line_id
        WHERE
            A.line_id = $1
            AND A.line_status != '${DEACTIVATED_STATUS}'`;
    const lineTenant = await psql.query(context, { sql: lineTenantSql, params: [kaisenNo] });
    if (lineTenant.rows.length === 1) return lineTenant.rows[0].tenant_id;

    //代表N番チェック
    //line_tenantsに未登録する場合、tenant_nnumbersテーブルから検索
    const tenantNNumberSql = `
        SELECT
            B.tenant_id
        FROM
            lines AS A
            INNER JOIN tenant_nnumbers AS B ON A.nnumber = B.nnumber
        WHERE
            A.line_id = $1
            AND A.line_status != '${DEACTIVATED_STATUS}'`;
    const tenantNNumber = await psql.query(context, { sql: tenantNNumberSql, params: [kaisenNo] });
    if (tenantNNumber.rows.length === 1) return tenantNNumber.rows[0].tenant_id;

    //再販料金プランチェック + 社内フラグ判定
    const tenantPlansSql = `
        SELECT
            C.tenant_id
        FROM
            lines AS A
            INNER JOIN plans AS B ON A.resale_plan_id = B.resale_plan_id
            INNER JOIN tenant_plans AS C ON B.plan_id = C.plan_id
        WHERE
            A.line_id = $1
            AND A.line_status != '${DEACTIVATED_STATUS}'`;
    const tenantPlans = await psql.query(context, { sql: tenantPlansSql, params: [kaisenNo] });
    if (tenantPlans.rows.length === 0) return null;

    const tenantId = tenantPlans.rows[0].tenant_id;
    const comTenantSql = `
        SELECT 1 AS ONE
        FROM tenants
        WHERE
            status = true
            AND office = true
            AND tenant_id = $1
        LIMIT 1`;
    const isComTenant = await psql.query(context, { sql: comTenantSql, params: [tenantId] });
    return isComTenant.rows.length === 0 ? null : tenantId;
};

/**
 * Check whether kaisenNo is belong to tenantId
 * 旧メソッド: kaisenNoBelongsToTenantCheck
 * @param {object} context - API context
 * @param {object} data
 * @param {string} data.tenantId
 * @param {string} data.kaisenNo
 * @returns {Promise<boolean>}
 */
const isKaisenNoBelongToTenant = async (context, { tenantId, kaisenNo }) => {
    if (!tenantId || !kaisenNo) throw new Error('tenantId and kaisenNo are required');
    context.log('isKaisenNoBelongToTenant start :' + tenantId + ' ' + kaisenNo);

    //テナント情報
    const tenantInfoSql = `
        SELECT
            tenant_id,
            office,
            status
        FROM
            tenants
        WHERE
            tenant_id = $1
            AND status = TRUE`;
    //回線テナント取得
    const lineTenantSql = `
        SELECT 1 AS ONE
        FROM
            lines AS A
            INNER JOIN line_tenants AS B ON A.line_id = B.line_id
        WHERE
            B.tenant_id = $1
            AND A.line_id = $2
            AND A.line_status != '${DEACTIVATED_STATUS}'`;
    //代表N番チェック
    const tenantNNumberSql = `
        SELECT 1 AS ONE
        FROM
            lines AS A
            INNER JOIN tenant_nnumbers AS B ON A.nnumber = B.nnumber
        WHERE
            B.tenant_id = $1
            AND A.line_id = $2
            AND A.line_status != '${DEACTIVATED_STATUS}'`;
    //再販料金プランチェック
    const tenantPlansSql = `
        SELECT 1 AS ONE
        FROM
            lines AS A
            INNER JOIN plans AS B ON A.resale_plan_id = B.resale_plan_id
            INNER JOIN tenant_plans AS C ON B.plan_id = C.plan_id
        WHERE
            C.tenant_id = $1
            AND A.line_id = $2
            AND A.line_status != '${DEACTIVATED_STATUS}'`;

    const [tenantInfo, lineTenant, tenantNNumber, tenantPlans] = await Promise.all([
        psql.query(context, { sql: tenantInfoSql, params: [tenantId] }),
        psql.query(context, { sql: lineTenantSql, params: [tenantId, kaisenNo] }),
        psql.query(context, { sql: tenantNNumberSql, params: [tenantId, kaisenNo] }),
        psql.query(context, { sql: tenantPlansSql, params: [tenantId, kaisenNo] }),
    ]);

    context.log(
        'isKaisenNoBelongToTenant result ' +
            JSON.stringify({
                tenantInfo: tenantInfo?.rows?.length,
                lineTenant: lineTenant?.rows?.length,
                tenantNNumber: tenantNNumber?.rows?.length,
                tenantPlans: tenantPlans?.rows?.length,
                office: tenantInfo?.rows?.length > 0 ? tenantInfo.rows[0].office : null,
            })
    );

    // テナントIDがDBに存在しない場合、FALSE
    if (tenantInfo.rows.length === 0) return false;

    //回線テナントの紐付けが存在する場合、TRUE
    if (lineTenant.rows.length > 0) return true;

    //N番の紐付けが存在する場合、TRUE
    if (tenantNNumber.rows.length > 0) return true;

    //社内の場合は再販料金プランに紐つくかチェック
    if (tenantInfo.rows[0].office === true && tenantPlans.rows.length > 0) return true;

    // 社外または、DBに該当する情報が存在しない
    return false;
};

/**
 * Check whether kaisenNos is belong to a tenantId
 * @param {object} context
 * @param {string} tenantId no checking on tenant's `status=true` like `isKaisenNoBelongToTenant`,
 *                          instead requires `office` field for `shanaiFlag` parameter
 * @param {Array<string>} kaisenNoList
 * @param {boolean} shanaiFlag `office` field from `tenants` table
 * @returns {Promise<Set<string>>} set of kaisenNo which belong to `tenantId`
 */
const isKaisenNoBelongToTenantMultiple = async (context, tenantId, kaisenNoList, shanaiFlag) => {
    const params = [tenantId].concat(kaisenNoList);
    const lineIdQuery = queryUtils.buildListOfValues(kaisenNoList, 2);
    //回線テナント取得
    const lineTenantSql = `
        SELECT
            A.line_id
        FROM
            lines AS A
        INNER JOIN
            line_tenants AS B ON A.line_id = B.line_id
        WHERE
            B.tenant_id = $1
            AND A.line_id IN ${lineIdQuery}
            AND A.line_status != '${DEACTIVATED_STATUS}'`;

    //代表N番チェック
    const tenantNNumberSql = `
        SELECT
            A.line_id
        FROM
            lines AS A
        INNER JOIN
            tenant_nnumbers AS B ON A.nnumber = B.nnumber
        WHERE
            B.tenant_id = $1
            AND A.line_id IN ${lineIdQuery}
            AND A.line_status != '${DEACTIVATED_STATUS}'`;

    //再販料金プランチェック
    const tenantPlansSql = `
        SELECT
            A.line_id
        FROM
            lines AS A
        INNER JOIN
            plans AS B ON A.resale_plan_id = B.resale_plan_id
        INNER JOIN
            tenant_plans AS C ON B.plan_id = C.plan_id
        WHERE
            C.tenant_id = $1
            AND A.line_id IN ${lineIdQuery}
            AND A.line_status != '${DEACTIVATED_STATUS}'`;

    const [lineTenant, tenantNNumber, tenantPlans] = await Promise.all([
        psql.query(context, { sql: lineTenantSql, params }),
        psql.query(context, { sql: tenantNNumberSql, params }),
        psql.query(context, { sql: tenantPlansSql, params }),
    ]);

    const result = new Set();

    //回線テナントの紐付けが存在する場合、TRUE
    if (lineTenant.rows && lineTenant.rows.length > 0) {
        for (const row of lineTenant.rows) {
            result.add(row.line_id);
        }
    }

    //N番の紐付けが存在する場合、TRUE
    if (tenantNNumber.rows && tenantNNumber.rows.length > 0) {
        for (const row of tenantNNumber.rows) {
            result.add(row.line_id);
        }
    }

    //社内の場合は再販料金プランに紐つくかチェック
    if (shanaiFlag && tenantPlans.rows && tenantPlans.rows.length > 0) {
        for (const row of tenantPlans.rows) {
            result.add(row.line_id);
        }
    }

    return result;
};

/**
 * convert kaisenNo to docomo's resalePlan
 * 旧メソッド: retrieveResalePlanOfDocomoByKaisenNo
 * @param   {object} context - API context
 * @param   {string} kaisenNo - The kaisenNo to check.
 * @returns {Promise<string|null>} - docomo's resalePlan
 */
const convertKaisenInfoToResalePlan = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('convertKaisenInfoToResalePlan start :' + kaisenNo);
    const sql = 'SELECT contract_type FROM lines WHERE line_id = $1';
    const { rows } = await psql.query(context, { sql: sql, params: [kaisenNo] });
    if (rows.length === 0) {
        context.log('cannot retrieve KaisenInfo from lineId :' + kaisenNo);
        return null;
    }

    // COM料金プランからドコモの料金プランに変換するルール
    switch (rows[0].contract_type) {
        case '3G':
            return DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN; //卸ＦＯＭＡ特定接続プラン
        case '3G(SMS)':
            return DaihyouBangoConstants.FOMA_YUPIKITASU_PLAN; //卸ＦＯＭＡユビキタスプラン
        case 'LTE':
            return DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN; //卸Ｘｉ特定接続プラン
        case 'LTE(SMS)':
            return DaihyouBangoConstants.XI_YUPIKITASU; //卸Ｘｉユビキタス
        case 'LTE(音声)':
            return DaihyouBangoConstants.TYPE_XI; //タイプＸｉ
        case '5G(NSA)':
            return DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN; //卸５G特定接続プラン
        case '5G(NSA)(音声)':
            return DaihyouBangoConstants.OROSHI_TYPE_FIVE_G; //卸タイプ５G
        default:
            context.log('cannot retrieve ResalePlan from contractType :' + rows[0].contract_type);
            return null;
    }
};

/**
 * 旧メソッド：retrieveResalePlanFullMVNOOfDocomoByKaisenNo
 * @param {object} context
 * @param {string} kaisenNo
 */
const convertKaisenInfoToResalePlanFullMVNO = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    const sql = `
        SELECT DISTINCT
            p.network, p.sms_enable, p.voice_flag
        FROM
            plans AS p 
        INNER JOIN
            lines as l ON p.resale_plan_id=l.resale_plan_id 
        WHERE
            l.line_id=$1 AND p.full_mvno_flag=true`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    if (rows.length === 0) {
        context.log('cannot retrieve kaisenInfo from lineId:', kaisenNo, '(Full MVNO)');
        return null;
    }

    const row0 = rows[0];
    const network = row0.network;
    const voiceFlag = row0.voice_flag;
    const smsEnable = row0.sms_enable;

    // COM料金プランからドコモの料金プランに変換するルール
    /** @type {string|null} */
    let resalePlanId = null;
    if (!isNone(network) && !isNone(voiceFlag) && !isNone(smsEnable)) {
        if (network === 'LTE' && !smsEnable && !voiceFlag) {
            resalePlanId = DaihyouBangoConstants.HLR_HSS_XI_SPECIFIC_CONNECTION;
        } else if (network === 'LTE' && smsEnable && !voiceFlag) {
            //TODO: SMSの判定をアップデートする
            resalePlanId = DaihyouBangoConstants.HLR_HSS_XI_UBIQUITOUSU;
        }
    }
    context.log(
        'convertKaisenInfoToResalePlanFullMVNO found: network:',
        network ?? 'null',
        'voiceFlag:',
        voiceFlag,
        'smsEnable:',
        smsEnable
    );
    return resalePlanId;
};

/**
 * convert kaisenNo to docomo's resalePlan (multiple kaisen)
 * 旧メソッド: retrieveResalePlanOfDocomoByKaisenNo
 * @param {object} context
 * @param {Array<string>} kaisenNoList
 * @returns {Promise<{[kaisenNo: string]: string|null}>} map of kaisen no and contract tyoe
 */
const convertKaisenInfoToResalePlanMultiple = async (context, kaisenNoList) => {
    if (isNone(kaisenNoList) || !Array.isArray(kaisenNoList)) return {}; // throw new Error('kaisenNoList is required');
    context.log('convertKaisenInfoToResalePlanMultiple start:', kaisenNoList.length, 'lines');
    const sql = `
        SELECT
            line_id, contract_type
        FROM
            lines
        WHERE
            line_id IN ${queryUtils.buildListOfValues(kaisenNoList)}`;
    const { rows } = await psql.query(context, { sql: sql, params: kaisenNoList });

    if (isNone(rows)) return {};
    const result = rows.reduce((acc, cur) => {
        // COM料金プランからドコモの料金プランに変換するルール
        switch (cur.contract_type) {
            case '3G':
                acc[cur.line_id] = DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN; //卸ＦＯＭＡ特定接続プラン
                break;
            case '3G(SMS)':
                acc[cur.line_id] = DaihyouBangoConstants.FOMA_YUPIKITASU_PLAN; //卸ＦＯＭＡユビキタスプラン
                break;
            case 'LTE':
                acc[cur.line_id] = DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN; //卸Ｘｉ特定接続プラン
                break;
            case 'LTE(SMS)':
                acc[cur.line_id] = DaihyouBangoConstants.XI_YUPIKITASU; //卸Ｘｉユビキタス
                break;
            case 'LTE(音声)':
                acc[cur.line_id] = DaihyouBangoConstants.TYPE_XI; //タイプＸｉ
                break;
            case '5G(NSA)':
                acc[cur.line_id] = DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN; //卸５G特定接続プラン
                break;
            case '5G(NSA)(音声)':
                acc[cur.line_id] = DaihyouBangoConstants.OROSHI_TYPE_FIVE_G; //卸タイプ５G
                break;
            default:
                context.log('cannot retrieve ResalePlan from contractType :' + cur.contract_type);
                acc[cur.line_id] = null;
        }
        return acc;
    }, {});
    return result;
};

/**
 * 旧メソッド：retrieveResalePlanFullMVNOOfDocomoByKaisenNo (multiple kaisen)
 * @param {object} context
 * @param {Array<string>} kaisenNoList
 * @returns {Promise<{[kaisenNo: string]: string|null}>} map of kaisen no and contract tyoe
 */
const convertKaisenInfoToResalePlanFullMVNOMultiple = async (context, kaisenNoList) => {
    if (isNone(kaisenNoList) || !Array.isArray(kaisenNoList)) return {}; //throw new Error('kaisenNoList is required');
    context.log('convertKaisenInfoToResalePlanFullMVNOMultiple start:', kaisenNoList.length, 'lines');

    const sql = `
        SELECT DISTINCT
            l.line_id, p.network, p.sms_enable, p.voice_flag
        FROM
            plans AS p 
        INNER JOIN
            lines as l ON p.resale_plan_id=l.resale_plan_id 
        WHERE
            l.line_id IN ${queryUtils.buildListOfValues(kaisenNoList)}
            AND p.full_mvno_flag=true`;
    const { rows } = await psql.query(context, { sql, params: kaisenNoList });
    if (isNone(rows)) return {};

    const result = rows.reduce((acc, cur) => {
        const network = cur.network;
        const voiceFlag = cur.voice_flag;
        const smsEnable = cur.sms_enable;

        // COM料金プランからドコモの料金プランに変換するルール
        /** @type {string|null} */
        let resalePlanId = null;
        if (!isNone(network) && !isNone(voiceFlag) && !isNone(smsEnable)) {
            if (network === 'LTE' && !smsEnable && !voiceFlag) {
                resalePlanId = DaihyouBangoConstants.HLR_HSS_XI_SPECIFIC_CONNECTION;
            } else if (network === 'LTE' && smsEnable && !voiceFlag) {
                //TODO: SMSの判定をアップデートする
                resalePlanId = DaihyouBangoConstants.HLR_HSS_XI_UBIQUITOUSU;
            }
        }
        acc[cur.line_id] = resalePlanId;
        return acc;
    }, {});
    return result;
};

/**
 * get kaisenInfo by kaisenNo
 * @param  {object} context - API context
 * @param  {string} kaisenNo   - required
 * @return {string|null} kaisenInfo
 */
const getKaisenInfo = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('getKaisenInfo start :' + kaisenNo);

    const sql = 'SELECT * FROM lines WHERE line_id = $1';
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    return rows.length === 0 ? null : rows[0];
};

/**
 * get_kaisen_info
 * @param {object} context
 * @param {string} lineNo
 * @returns {Promise<{
 *   lineNo: string,
 *   simFlag: string,
 *   nBan: string,
 *   tenantId: string,
 *   planName: string,
 *   planId: number,
 *   lineGroupId: string,
 *   simType: string,
 *   startDate: number,
 *   contractType: string,
 *   interationalRoamingCreditLine: string,
 *   voiceMail: string,
 *   intlCall: string,
 *   forwarding: string,
 *   callWaiting: string,
 *   intlForwarding: string,
 *   lineRegStatus: string,
 *   openDate: number,
 *   tempRegist: string,
 *   activateDate: number,
 *   usageStatus: number,
 *   puk1: string,
 *   puk2: string,
 *   imsi: string,
 *   simNumber: string,
 *   fullMvnoFlag: boolean,
 *   voicePlanId: string,
 *   voicePlanName: string
 * }?>}
 */
const getKaisenInfoDetail = async (context, lineNo) => {
    context.log('getKaisenInfoDetail start!');
    const sql_select = `SELECT
       A.line_id as line_no,
       A.sim_flag as sim_flag,
       A.nnumber as nban,
       A.group_id as line_group_id,
       extract(epoch from A.start_date at time zone 'jst') as start_date,
       A.sim_type as sim_type,
       extract(epoch from A.open_date at time zone 'jst') as open_date,
       A.temp_regist,
       extract(epoch from A.activate_date at time zone 'jst') as activate_date,
       A.contract_type as contract_type,
       A.temp_regist as line_reg_status,
       B.tenant_id as tenant_id,
       C.plan_id as plan_id,
       C.plan_name as plan_name,
       E1.option_plan_name as interational_roaming_credit_line, 
       E2.option_plan_name as voice_mail,
       E3.option_plan_name as call_waiting,
       E4.option_plan_name as intl_call,
       E5.option_plan_name as forwarding,
       E6.option_plan_name as intl_forwarding, 
       A.usage_status as usage_status,
       A.puk_1 as puk1,
       A.puk_2 as puk2,
       A.imsi,
       A.sim_number as sim_number,
       B.full_mvno_flag,
       A.voice_plan_id,
       A.voice_plan_name,
       A.user_bbuni_ex_flag as user_bbuni_ex_flag,
       C.plan_bbuni_ex_flag as plan_bbuni_ex_flag`;

    const sql_from = `
       FROM
           lines as A 
       INNER JOIN
           tenant_nnumbers as B ON A.nnumber = B.nnumber 
       JOIN
           tenants as D On B.tenant_id = D.tenant_id 
       JOIN
           plans as C ON A.resale_plan_id = C.resale_plan_id 
       LEFT OUTER JOIN
           line_options as E1 ON A.roaming_max_id = E1.line_option_id 
       LEFT OUTER JOIN
           line_options as E2 ON A.voice_mail_id = E2.line_option_id 
       LEFT OUTER JOIN
           line_options as E3 ON A.call_waiting_id = E3.line_option_id 
       LEFT OUTER JOIN
           line_options as E4 ON A.intl_call_id = E4.line_option_id 
       LEFT OUTER JOIN
           line_options as E5 ON A.forwarding_id = E5.line_option_id 
       LEFT OUTER JOIN
           line_options as E6 ON A.intl_forwarding_id = E6.line_option_id `;

    const sql_where = `WHERE
       A.line_id = $1
       AND D.status = true
       AND A.line_status != '03'`;
    const { rows } = await psql.query(context, { sql: `${sql_select} ${sql_from} ${sql_where}`, params: [lineNo] });
    if (rows && rows.length == 1) {
        const r = rows[0];
        return {
            lineNo: r.line_no ?? '',
            simFlag: r.sim_flag,
            nBan: r.nban,
            tenantId: r.tenant_id ?? '',
            planName: r.plan_name ?? '',
            planId: isNone(r.plan_id) ? -1 : +r.plan_id,
            lineGroupId: r.line_group_id ?? '',
            simType: r.sim_type ?? '',
            startDate: isNone(r.start_date) ? -1 : +r.start_date,
            contractType: r.contract_type ?? '',
            interationalRoamingCreditLine: r.interational_roaming_credit_line ?? 'なし',
            voiceMail: r.voice_mail ?? 'なし',
            intlCall: r.intl_call ?? 'なし',
            forwarding: r.forwarding ?? 'なし',
            callWaiting: r.call_waiting ?? 'なし',
            intlForwarding: r.intl_forwarding ?? 'なし',
            lineRegStatus: r.line_reg_status,
            openDate: isNone(r.open_date) ? -1 : +r.open_date,
            tempRegist: r.temp_regist,
            activateDate: isNone(r.activate_date) ? -1 : +r.activate_date,
            usageStatus: isNone(r.usage_status) ? -1 : +r.usage_status,
            puk1: r.puk1 ?? '',
            puk2: r.puk2 ?? '',
            imsi: r.imsi ?? '',
            simNumber: r.sim_number ?? '',
            fullMvnoFlag: isNone(r.full_mvno_flag) ? false : r.full_mvno_flag,
            voicePlanId: r.voice_plan_id ?? '',
            voicePlanName: r.voice_plan_name ?? '',
            userBbuniExFlag: isNone(r.user_bbuni_ex_flag) ? null : r.user_bbuni_ex_flag,
            planBbuniExFlag: isNone(r.plan_bbuni_ex_flag) ? null : r.plan_bbuni_ex_flag,
        };
    }
    return null;
};

/**
 * get simFlag by kaisenNo
 * 旧メソッド: retrieveSimFlag
 * @param  {object} context - API context
 * @param  {string} kaisenNo   - required
 * @return {Promise<boolean|null>} simFlag
 */
const getSimFlagByKaisenNo = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('getSimFlagByKaisenNo start :' + kaisenNo);

    const kaisenInfo = await getKaisenInfo(context, kaisenNo);
    return kaisenInfo?.sim_flag;
};

/**
 *
 * @param {object} context
 * @param {Array<string>} kaisenNoList
 * @returns {Promise<{[kaisenNo: string]: boolean}>}
 */
const getSimFlagByKaisenNoMultiple = async (context, kaisenNoList) => {
    if (!Array.isArray(kaisenNoList) || kaisenNoList.length === 0) return {};
    context.log('getSimFlagByKaisenNoMultiple :', kaisenNoList.length, 'line numbers');
    const sql = `
        SELECT
            line_id, sim_flag
        FROM
            lines
        WHERE
            line_id IN ${queryUtils.buildListOfValues(kaisenNoList)}`;
    const { rows } = await psql.query(context, { sql, params: kaisenNoList });
    const result = {};
    if (rows && rows.length > 0) {
        for (const row of rows) {
            result[row.line_id] = row.sim_flag ?? false;
        }
    }
    return result;
};

/**
 * get sim flag and usage status
 * 旧メソッド: retrieveSimFlagAndUsageStatus
 * @param {object} context - API context
 * @param {string} kaisenNo - required
 * @returns {Promise<{sim_flag:boolean,usage_status:number}|undefined>}
 */
const getSimFlagAndUsageStatus = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('getSimFlagAndUsageStatus start :' + kaisenNo);
    const sql = `
        SELECT
            sim_flag, usage_status
        FROM
            lines
        WHERE
            line_id = $1`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    if (rows.length > 0) {
        return {
            sim_flag: rows[0].sim_flag,
            usage_status: rows[0].usage_status,
        };
    }
};

/**
 * STEP 16 - 0035チェック
 * 0035回線かどうかの判定
 * @param  {object} context - API context
 * @param  {string} kaisenNo   - required
 * @return {boolean} simFlag
 */
const isUsingVoicePlan = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('isUsingVoicePlan start :' + kaisenNo);
    const sql = `
        SELECT
            1 AS one
        FROM
            lines AS LIN
            INNER JOIN plans AS PLN ON LIN.resale_plan_id = PLN.resale_plan_id
        WHERE
            PLN.voice_flag = TRUE
            AND LIN.line_id = $1`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    return rows.length > 0;
};

/**
 * STEP 16 - 0035チェック
 * 0035回線かどうかの判定
 * @param {object} context
 * @param {Array<string>} kaisenNoList
 * @returns {Promise<Set<string>>} set of line numbers where `voice_flag=TRUE`
 */
const isUsingVoicePlanMultiple = async (context, kaisenNoList) => {
    if (!Array.isArray(kaisenNoList) || kaisenNoList.length === 0) return new Set();
    context.log('isUsingVoicePlanMultiple start:', kaisenNoList.length, 'line numbers');
    context.log('isUsingVoicePlan start :' + kaisenNoList);
    const sql = `
        SELECT
            LIN.line_id
        FROM
            lines AS LIN
            INNER JOIN plans AS PLN ON LIN.resale_plan_id = PLN.resale_plan_id
        WHERE
            PLN.voice_flag = TRUE
            AND LIN.line_id IN ${queryUtils.buildListOfValues(kaisenNoList)}`;
    const { rows } = await psql.query(context, { sql, params: kaisenNoList });
    return rows && rows.length > 0 ? new Set(rows.map((r) => r.line_id)) : new Set();
};

/**
 * 対象回線の廃止オーダが受付中でないか
 * It checks if there is a service order for the given line that is either in the "完了" or "予約中" state
 * @param context - The context object that is passed to the function.
 * @param kaisenNo - The number of the line to be checked.
 * @returns A boolean value.
 */
const isInHaishiOrderProcess = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('isInHaishiOrderProcess start :' + kaisenNo);
    const sql =
        'SELECT DISTINCT A.service_order_id FROM service_orders AS A LEFT JOIN lines AS B ' +
        'ON A.line_id=B.line_id ' +
        "WHERE A.line_id=$1 AND A.function_type='" +
        ORDER_TYPE_KAISEN_HAISHI +
        "' AND " +
        "((A.order_status='完了' AND A.exec_date>=B.open_date AND A.exec_date<=CURRENT_TIMESTAMP AT TIME ZONE 'JST') OR " +
        "(A.order_status='予約中' AND A.reserve_date>=B.open_date AND A.reserve_date<=CURRENT_TIMESTAMP AT TIME ZONE 'JST'))";

    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    return rows.length > 0;
};

/**
 * Check if lines are either in the "完了" or "予約中" state, and matching line numbers
 * will be included in return set
 * @param {object} context
 * @param {Array<string>} kaisenNoList
 * @returns {Promise<Set<string>>}
 */
const isInHaishiOrderProcessMultiple = async (context, kaisenNoList) => {
    if (!Array.isArray(kaisenNoList) || kaisenNoList.length === 0) return new Set();
    const sql = `
        SELECT
            A.line_id
        FROM
            service_orders AS A
        LEFT JOIN
            lines AS B ON A.line_id=B.line_id
        WHERE
            A.line_id IN ${queryUtils.buildListOfValues(kaisenNoList)}
            AND A.function_type = '${ORDER_TYPE_KAISEN_HAISHI}'
            AND (
                (
                    A.order_status='完了'
                    AND A.exec_date>=B.open_date
                    AND A.exec_date<=CURRENT_TIMESTAMP AT TIME ZONE 'JST'
                ) OR (
                    A.order_status='予約中'
                    AND A.reserve_date>=B.open_date
                    AND A.reserve_date<=CURRENT_TIMESTAMP AT TIME ZONE 'JST'
                )
            )`;
    const { rows } = await psql.query(context, { sql, params: kaisenNoList });
    return rows && rows.length > 0 ? new Set(rows.map((r) => r.line_id)) : new Set();
};

/**
 * 契約中の回線オプションを取得
 * @param {object} context
 * @param {string} kaisenNo
 * @returns {Promise<Array<string>>}
 */
const retrieveUsingMAWPLineOptions = async (context, kaisenNo) => {
    context.log('retrieveUsingMAWPLineOptions start');
    const sql = `
        SELECT
            roaming_max_id,
            voice_mail_id,
            call_waiting_id,
            intl_call_id,
            forwarding_id,
            intl_forwarding_id
        FROM
            lines
        WHERE
            line_id = $1`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    if (rows && rows.length > 0) {
        const result = [
            rows[0].roaming_max_id,
            rows[0].voice_mail_id,
            rows[0].call_waiting_id,
            rows[0].intl_call_id,
            rows[0].forwarding_id,
            rows[0].intl_forwarding_id,
        ];
        return result.filter((k) => k !== null || k !== undefined || k !== '');
    }
    return [];
};

const retrieveUsingMAWPLineOptionsAndNBan2 = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('retrieveUsingMAWPLineOptionsAndNBan2 start :' + kaisenNo);

    const sql =
        'SELECT B.plan_id AS plan_id, A.nnumber AS nnumber, ' +
        'A.roaming_max_id AS roaming_max_id, A.voice_mail_id AS voice_mail_id, ' +
        'A.call_waiting_id AS call_waiting_id, A.intl_call_id AS intl_call_id, ' +
        'A.forwarding_id AS forwarding_id, A.intl_forwarding_id AS intl_forwarding_id ' +
        "FROM (SELECT nnumber, COALESCE(resale_plan_id, 'EEF7D11C-FFC4-46E5-8212-9AA58ED7EA0C') AS resale_plan_id, " +
        '  roaming_max_id, voice_mail_id, call_waiting_id, intl_call_id, forwarding_id, intl_forwarding_id ' +
        "  FROM lines WHERE line_id = $1 AND line_status <> '03' ) AS A LEFT OUTER JOIN plans AS B ON " +
        '  A.resale_plan_id = B.resale_plan_id LIMIT 1 ';
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    return rows.length === 0 ? null : rows[0];
};

/**
 * 廃止SOのS日以降のSOが存在しないか
 * @param {string} lineNo line number
 * @param {number} reserveDate seconds from unix epoch
 */
const hasHaishiSOAfterReserveDate = async (context, lineNo, reserveDate) => {
    const sql =
        'SELECT COUNT(SO.service_order_id) AS count ' +
        'FROM lines AS L ' +
        'LEFT JOIN service_orders AS SO ON SO.line_id = L.line_id ' +
        'WHERE L.line_id = $1 ' +
        "  AND SO.order_type != '回線グループ所属回線変更(割当解除)' " +
        '  AND ( ' +
        "    ( SO.order_status = '完了' " +
        '      AND SO.exec_date >= L.open_date ' +
        "      AND extract( epoch FROM SO.exec_date AT TIME zone 'jst' ) >= $2 " +
        '    ) ' +
        '    OR ( ' +
        "      SO.order_status = '予約中' " +
        '      AND SO.reserve_date >= L.open_date ' +
        "      AND extract( epoch FROM SO.reserve_date AT TIME zone 'jst' ) >= $3 " +
        '    )' +
        '  )';
    const { rows } = await psql.query(context, { sql, params: [lineNo, reserveDate, reserveDate] });
    if (rows.length === 0) {
        context.log.error('hasHaishiSOAfterReserveDate: query returns 0 row');
    }
    return rows && rows.length > 0 ? parseInt(rows[0].count) > 0 : false;
};

/**
 * Returns line numbers which has another SO(s) after reserve date of haishi SO
 * @param {object} context
 * @param {Array<string>} lineNoList
 * @param {number} reserveDate
 * @returns {Promise<Set<string>>}
 */
const hasHaishiSOAfterReserveDateMultiple = async (context, lineNoList, reserveDate) => {
    if (!Array.isArray(lineNoList) || lineNoList.length === 0) return new Set();
    context.log('hasHaishiSOAfterReserveDateMultiple start:', lineNoList.length, 'line numbers');
    const params = lineNoList.concat(reserveDate, reserveDate);
    const sql = `
        SELECT
            L.line_id
        FROM
            service_orders AS SO
        LEFT JOIN
            lines AS L ON L.line_id = SO.line_id
        WHERE
            L.line_id IN ${queryUtils.buildListOfValues(lineNoList)}
            AND SO.order_type != '回線グループ所属回線変更(割当解除)'
            AND (
                (
                    SO.order_status = '完了'
                    AND SO.exec_date >= L.open_date
                    AND extract(
                        epoch FROM SO.exec_date AT TIME zone 'jst'
                    ) >= $${params.length - 1}
                ) OR (
                    SO.order_status = '予約中'
                    AND SO.reserve_date >= L.open_date
                    AND extract(
                        epoch FROM SO.reserve_date AT TIME zone 'jst'
                    ) >= $${params.length}
                )
            )`;
    const { rows } = await psql.query(context, { sql, params });
    return rows && rows.length > 0 ? new Set(rows.map((r) => r.line_id)) : new Set();
};

/**
 * check whether a line has open order (予約中)
 * @param {object} context
 * @param {string} lineNo
 * @returns {Promise<boolean>}
 */
const isInOpenOrderProcess = async (context, lineNo) => {
    if (!lineNo) throw new Error('lineNo is required');
    context.log(`isInOpenOrderProcess start : ${lineNo}`);
    const sql =
        'SELECT DISTINCT A.service_order_id ' +
        'FROM service_orders as A ' +
        'WHERE A.line_id = $1 ' +
        'AND A.function_type IN ($2, $3, $4, $5) ' +
        'AND A.order_status = $6';
    const { rows } = await psql.query(context, {
        sql,
        params: [
            lineNo,
            SO_CORE_FUNCTION_TYPE_COUPON_TSUIKA,
            SO_CORE_FUNCTION_TYPE_COUPON_ONOFF,
            SO_CORE_FUNCTION_TYPE_ACTIVATE_DEACTIVATE,
            SO_CORE_FUNCTION_TYPE_PLAN_CHANGE,
            '予約中',
        ],
    });
    return rows && rows.length > 0 ? true : false;
};

/**
 * @param {object} context
 * @param {string} lineId
 * @returns {Promise<number?>}
 */
const getUsageStatusByLineNo = async (context, lineId) => {
    context.log('getUsageStatusByLineNo start:', lineId);
    const sql = 'SELECT usage_status FROM lines WHERE line_id = $1';
    const { rows } = await psql.query(context, { sql, params: [lineId] });
    if (rows && rows.length > 0) {
        return rows[0].usage_status ?? USAGE_STATUS.UNKNOWN;
    } else {
        context.log('getUsageStatusByLineNo not found');
        return null;
    }
};

/**
 * get_haishi_kaisen_list (full mvno API)
 * @param {object} context
 * @param {object} d request query param
 * @param {number?} d.startDateST
 * @param {number?} d.startDateEN
 * @param {string?} d.contractType
 * @param {string?} d.simType
 * @param {Array<number>} d.planID
 * @param {string?} d.searchValue
 * @param {Array<string>} d.tenantID
 * @param {string?} d.sortBy
 * @param {string?} d.sortOrder
 * @param {string?} d.simFlag
 * @param {number} d.limit
 * @param {number} d.offset
 * @param {boolean?} d.fullMvnoFlag null = accept either, true = full only, false = lite only
 * @param {string?} d.searchType
 */
const getHaishiKaisenList = async (context, d) => {
    context.log('GetHaishiKaisenList start');
    // TODO: imsi will be added to abone_lines, when it is added, remove the join with lines and just use abone_lines.imsi
    const sql_select = `
        SELECT
            A.line_id, extract(epoch from A.service_date at time zone 'jst') as service_date, 
            extract(epoch from A.start_date at time zone 'jst') as start_date, A.old_plan_id,
            A.old_plan_name, A.contract_type, A.sim_type, A.contract_type, A.old_tenant_id,
            extract(epoch from A.created_at at time zone 'jst') as created_at, A.sim_flag, A.nnumber, 
            D.imsi, A.sim_number `;
    const sql_select_count = 'SELECT COUNT(A.line_id) ';

    let sql_from = `
        FROM 
            abone_lines as A
        JOIN 
            tenants as B On A.old_tenant_id = B.tenant_id 
        LEFT JOIN
            tenant_nnumbers as C ON A.nnumber = C.nnumber
        LEFT JOIN
            lines as D on D.line_id = A.line_id `;

    let sql_where = 'WHERE B.status = true ';

    let params = [];
    let whereSql = [];

    if (d.tenantID && Array.isArray(d.tenantID)) {
        let whereIn = [];
        for (const tenant of d.tenantID) {
            params.push(tenant);
            whereIn.push(`$${params.length}`);
        }
        if (whereIn.length > 0) {
            whereSql.push(`A.old_tenant_id IN ( ${whereIn.join(',')} )`);
        }
    }

    if (d.planID && Array.isArray(d.planID)) {
        let whereIn = [];
        for (const plan of d.planID) {
            params.push(plan);
            whereIn.push(`$${params.length}`);
        }
        if (whereIn.length > 0) {
            whereSql.push(`A.old_plan_id IN ( ${whereIn.join(',')} )`);
        }
    }

    if (!isNone(d.startDateST)) {
        params.push(d.startDateST);
        whereSql.push(`A.service_date >= to_timestamp($${params.length})`);
    }
    if (!isNone(d.startDateEN)) {
        params.push(d.startDateEN);
        whereSql.push(`A.service_date < to_timestamp($${params.length})`);
    }

    if (d.contractType) {
        params.push(d.contractType);
        whereSql.push(`A.contract_type = $${params.length}`);
    }

    if (d.simType) {
        params.push(d.simType);
        whereSql.push(`A.sim_type = $${params.length}`);
    }
    
    if (!isNone(d.bbUnibaFlag)) {
        if (BB_UNIBA_FLAG[d.bbUnibaFlag]) {
            //対象外
            // params.push(BB_UNIBA_FLAG[d.bbUnibaFlag]);
            // whereSql.push(`D.user_bbuni_ex_flag = $${params.length}`);
            
        } else {
            // 対象
            
            // whereSql.push(`(D.user_bbuni_ex_flag IS NULL OR D.user_bbuni_ex_flag = false)`);
        }
    }

    if (!isNone(d.searchValue)) {
        switch (d.searchType) {
            case KAISEN_KANRI_SEARCH_TYPE.KAISEN_NO:
                params.push(`%${d.searchValue}%`);
                whereSql.push(`A.line_id LIKE $${params.length}`);
                break;
            case KAISEN_KANRI_SEARCH_TYPE.IMSI:
                params.push(`%${d.searchValue}%`);
                whereSql.push(`D.imsi LIKE $${params.length}`);
                break;
            case KAISEN_KANRI_SEARCH_TYPE.SIM_NUMBER:
                params.push(`%${d.searchValue}%`);
                whereSql.push(`A.sim_number LIKE $${params.length}`);
                break;
        }
    }

    if (!isNone(d.simFlag)) {
        params.push(SIM_FLAG[d.simFlag]);
        whereSql.push(`A.sim_flag = $${params.length}`);
    }

    const tenantIdCON = COCN_TENANT_ID;
    const conDaihyouBangoFull = portalConfig.COCN_DAIHYOU_NNUMBER_FOR_FULL_MVNO;

    switch (d.fullMvnoFlag) {
        case true:
            // In case of CON000, there is nothing on the tenant_nnumbers table to join -> C.full_mvno_flag
            // will never be true -> we need to grab them directly by nnumber
            if (!d.tenantID.includes(tenantIdCON)) {
                whereSql.push('C.full_mvno_flag IS TRUE');
            } else {
                let whereIn = [];
                for (const daihyoBango of conDaihyouBangoFull) {
                    params.push(daihyoBango);
                    whereIn.push(`$${params.length}`);
                }
                if (whereIn.length > 0) {
                    whereSql.push(`(C.full_mvno_flag IS TRUE OR A.nnumber IN ( ${whereIn.join(',')} ))`);
                }
            }
            break;
        case false:
            whereSql.push('C.full_mvno_flag IS NOT TRUE');
            break;
        // default -> keep both
    }

    sql_where += whereSql.length > 0 ? ` AND ${whereSql.join(' AND ')} ` : '';

    let sql_where_sort = ' ';
    if (d.sortBy && Object.keys(HAISHI_SORT_CONDITION_LIST).includes(d.sortBy)) {
        const sortOrder = d.sortOrder ? (d.sortOrder.toLocaleLowerCase() == 'asc' ? 'ASC' : 'DESC') : 'DESC';
        switch (d.sortBy) {
            case 'contractType':
                sql_where_sort = ` ORDER BY
                    (CASE  WHEN A.contract_type is NULL
                    THEN ' '
                    ELSE A.contract_type END) ${sortOrder} `;
                break;
            case 'planName': // ?
                sql_where_sort = ` ORDER BY
                    (CASE  WHEN A.plan_name is NULL
                    THEN ' '
                    ELSE A.plan_name END) ${sortOrder} `;
                break;
            case 'simType':
                sql_where_sort = ` ORDER BY
                    (CASE  WHEN A.sim_type is NULL
                    THEN ' '
                    ELSE A.sim_type END) ${sortOrder} `;
                break;
            case 'serviceDate':
                sql_where_sort = ` ORDER BY
                    (CASE  WHEN extract(epoch from A.service_date at time zone 'jst') is NULL
                    THEN -1
                    ELSE extract(epoch from A.service_date at time zone 'jst') END) ${sortOrder} `;
                break;
            default:
                sql_where_sort = ` ORDER BY ${HAISHI_SORT_CONDITION_LIST[d.sortBy]} ${sortOrder} `;
        }
    }

    const limitParams = [d.offset, d.limit];
    const sql_where_limit = ` OFFSET $${params.length + 1} LIMIT $${params.length + 2}`;

    const searchQuery = sql_select + sql_from + sql_where + sql_where_sort + sql_where_limit;
    const countQuery = sql_select_count + sql_from + sql_where;
    const [{ rows }, countResult] = await Promise.all([
        psql.query(context, { sql: searchQuery, params: params.concat(limitParams) }),
        psql.query(context, { sql: countQuery, params }),
    ]);

    let results = [];
    if (rows && rows.length > 0) {
        results = rows.map((r) => ({
            lineId: r.line_id,
            contractType: r.contract_type ?? '',
            oldPlanName: r.old_plan_name ?? '',
            oldPlanId: r.old_plan_id ?? -1,
            simType: r.sim_type ?? '',
            startDate: isNone(r.start_date) ? -1 : +r.start_date,
            tenantId: r.old_tenant_id ?? '',
            serviceDate: isNone(r.service_date) ? -1 : +r.service_date,
            createdAt: isNone(r.created_at) ? -1 : +r.created_at,
            simFlag: r.sim_flag == false ? '黒' : '半黒',
            nNumber: r.nnumber ?? '',
            simNumber: r.sim_number ?? '',
            imsi: r.imsi ?? '',
        }));
    }

    let count = 0;
    if (countResult && countResult.rows && countResult.rows.length > 0) {
        count = +(countResult.rows[0].count ?? 0);
    }
    return {
        results,
        count,
    };
};
const compareBB = (user_flag, plan_flag) => {
    
    if(user_flag === true){
        return true;
    }

    if (user_flag === false){
        return false;
    }
    
    if(user_flag === null){
        if (plan_flag === true) {
            return true;
        } else {
            return false;
       }
    }
}

/**
 * get_kaisen_list (full mvno API)
 * @param {object} context
 * @param {object} d request query param
 * @param {number?} d.startDateST
 * @param {number?} d.startDateEN
 * @param {string?} d.contractType
 * @param {string?} d.simType
 * @param {Array<number>} d.planID
 * @param {string?} d.targetLine
 * @param {string?} d.searchValue
 * @param {Array<string>} d.tenantID
 * @param {string?} d.sortBy
 * @param {string?} d.sortOrder
 * @param {string?} d.simFlag
 * @param {string?} d.bbUnibaFlag
 * @param {number} d.limit
 * @param {number} d.offset
 * @param {boolean?} d.fullMvnoFlag null = accept either, true = full only, false = lite only
 * @param {string?} d.usageStatusFlag
 * @param {string?} d.searchType
 */
const getKaisenList = async (context, d) => {
    context.log('GetKaisenList start');
    const sql_select = `
        SELECT A.line_id, extract(epoch from A.start_date at time zone 'jst') as start_date, 
            extract(epoch from A.open_date at time zone 'jst') as open_date, A.temp_regist,
            A.nnumber, A.contract_type, A.sim_type, A.sim_flag, B.plan_id, B.plan_name, C.tenant_id,
            A.imsi, A.sim_number, A.usage_status, A.voice_plan_id, A.voice_plan_name, A.user_bbuni_ex_flag, B.plan_bbuni_ex_flag, A.swimmy_bbuni_ex_flag`;
    const sql_select_count = 'SELECT COUNT(A.line_id) ';
    let sql_from = `
        FROM
            lines as A 
        LEFT JOIN
            plans as B ON A.resale_plan_id = B.resale_plan_id 
        LEFT JOIN
            tenant_nnumbers as C ON A.nnumber = C.nnumber 
        LEFT JOIN
            tenants as D On C.tenant_id = D.tenant_id `;
    let sql_where = "WHERE A.line_status != '03' AND (D.status = true OR D.status is NULL) ";

    let params = [];
    let whereSql = [];

    const cONDaihyouBango = portalConfig.COCN_DAIHYOU_NNUMBER_LIST;
    const cONDaihyouBangoFull = portalConfig.COCN_DAIHYOU_NNUMBER_FOR_FULL_MVNO;

    if (d.tenantID && Array.isArray(d.tenantID)) {
        let whereIn = [];
        let whereNumbers = [];

        for (const tenant of d.tenantID) {
            params.push(tenant);
            whereIn.push(`$${params.length}`);
        }

        if (d.tenantID.includes(COCN_TENANT_ID)) {
            let validNumbers = [];
            switch (d.fullMvnoFlag) {
                case true:
                    validNumbers = cONDaihyouBangoFull;
                    break;
                case false:
                    validNumbers = cONDaihyouBango;
                    break;
                default:
                    validNumbers = cONDaihyouBango.concat(cONDaihyouBangoFull);
            }

            for (const nNo of validNumbers) {
                params.push(nNo);
                whereNumbers.push(`$${params.length}`);
            }
        }

        if (whereIn.length > 0) {
            if (whereNumbers.length > 0) {
                whereSql.push(`( C.tenant_id IN (${whereIn.join(',')}) OR A.nnumber IN (${whereNumbers.join(',')}) )`);
            } else {
                whereSql.push(`C.tenant_id IN (${whereIn.join(',')})`);
            }
        }
    }

    if (d.planID && Array.isArray(d.planID)) {
        let whereIn = [];
        for (const plan of d.planID) {
            params.push(plan);
            whereIn.push(`$${params.length}`);
        }
        if (whereIn.length > 0) {
            whereSql.push(`B.plan_id IN ( ${whereIn.join(',')} )`);
        }
    }

    if (!isNone(d.startDateST) && d.targetLine !== TARGET_LINE.KUROKA_OVERDUE_KAISEN) {
        params.push(d.startDateST);
        whereSql.push(`A.open_date >= to_timestamp($${params.length})`);
    }

    if (!isNone(d.startDateEN) && d.targetLine !== TARGET_LINE.KUROKA_OVERDUE_KAISEN) {
        params.push(d.startDateEN);
        whereSql.push(`A.open_date < to_timestamp($${params.length})`);
    }

    if (d.contractType) {
        params.push(d.contractType);
        whereSql.push(`A.contract_type = $${params.length}`);
    }

    if (d.simType) {
        params.push(d.simType);
        whereSql.push(`A.sim_type = $${params.length}`);
    }

    // if (!isNone(d.bbUnibaFlag)) {
    //     if (BB_UNIBA_FLAG[d.bbUnibaFlag]) {
    //         params.push(BB_UNIBA_FLAG[d.bbUnibaFlag]);
    //         whereSql.push(`A.user_bbuni_ex_flag = $${params.length}`);
    //     } else {
    //         whereSql.push(`(A.user_bbuni_ex_flag IS NULL OR A.user_bbuni_ex_flag = false)`);
    //     }
    // }
    if (!isNone(d.bbUnibaFlag)) {
        if (BB_UNIBA_FLAG[d.bbUnibaFlag]) {
            whereSql.push(`(A.user_bbuni_ex_flag = true OR (B.plan_bbuni_ex_flag = true and A.user_bbuni_ex_flag is null))`);
        } else {
            whereSql.push(`(A.user_bbuni_ex_flag = false or (A.user_bbuni_ex_flag is null and B.plan_bbuni_ex_flag is not true))`);
        }
    }

    if (d.targetLine === TARGET_LINE.KARITOUROKUKAISEN) {
        whereSql.push(`A.temp_regist = true`);
    } else if (d.targetLine === TARGET_LINE.TOUROKUSUMIKAISEN) {
        whereSql.push(`(A.temp_regist = false OR A.temp_regist IS NULL)`);
    } else if (d.targetLine === TARGET_LINE.TOUROKUZUMI_KARITOUROKU_KAISEN) {
        // STEP24.0で「登録済/仮登録回線」を検索
        whereSql.push(`(A.temp_regist = true OR A.temp_regist = false OR A.temp_regist IS NULL)`);
    } else if (d.targetLine === TARGET_LINE.KUROKA_OVERDUE_KAISEN) {
        // STEP24.0: 詳細検索にて、半黒有効化期限超過のものを検索可能とする。条件は、「開通月が「現在月-4より以前」であること」。
        if (!isNone(d.simFlag) && d.simFlag === SIM_STATUS.KURO) {
            whereSql.push(`A.sim_flag IS NULL`);
        } else {
            whereSql.push(`A.sim_flag = true`);
        }

        // ライトMVNO回線：開通月が「現在月-3より以前」である
        // フルMVNO回線：開通月が「現在月-12より以前」である
        const currentMonth = dayjs().startOf('month').unix(); // start with day 1 of current month
        // check startDateEN
        if (!isNone(d.fullMvnoFlag)) {
            // lite mvno or full mvno case
            const _month =
                d.fullMvnoFlag === true
                    ? portalConfig.HANKURO_ACTIVATION_PERIOD_EXCESS_MONTH_FOR_FULL_MVNO
                    : portalConfig.HANKURO_ACTIVATION_PERIOD_EXCESS_MONTH_FOR_LITE_MVNO;
            const _monthBeforeCurrentMonth = dayjs.unix(currentMonth).subtract(_month, 'month').endOf('month').unix();
            // no need to check full_mvno_flag in whereSql condition bcz the following "switch (d.fullMvnoFlag) case" had already handled for lite and full mvno cases
            if (!isNone(d.startDateEN) && d.startDateEN < _monthBeforeCurrentMonth) {
                params.push(d.startDateEN);
                whereSql.push(`A.open_date <= to_timestamp($${params.length})`);
            } else {
                params.push(_monthBeforeCurrentMonth);
                whereSql.push(`A.open_date <= to_timestamp($${params.length})`);
            }
        } else {
            // both case
            const _monthBeforeCurrentMonthForLite = dayjs
                .unix(currentMonth)
                .subtract(portalConfig.HANKURO_ACTIVATION_PERIOD_EXCESS_MONTH_FOR_LITE_MVNO, 'month')
                .endOf('month')
                .unix();
            const _monthBeforeCurrentMonthForFull = dayjs
                .unix(currentMonth)
                .subtract(portalConfig.HANKURO_ACTIVATION_PERIOD_EXCESS_MONTH_FOR_FULL_MVNO, 'month')
                .endOf('month')
                .unix();
            // need to check for both(両方) case and COCN case too
            if (!isNone(d.tenantID) && !d.tenantID.includes(COCN_TENANT_ID)) {
                if (!isNone(d.startDateEN) && d.startDateEN < _monthBeforeCurrentMonthForLite) {
                    if (d.startDateEN < _monthBeforeCurrentMonthForFull) {
                        params.push(d.startDateEN);
                        whereSql.push(`A.open_date <= to_timestamp($${params.length})`);
                    } else {
                        whereSql.push(`(
                            (C.full_mvno_flag IS TRUE AND A.open_date <= to_timestamp(${_monthBeforeCurrentMonthForFull}))
                            OR
                            (C.full_mvno_flag IS NOT TRUE AND A.open_date <= to_timestamp(${d.startDateEN}))
                        )`);
                    }
                } else {
                    whereSql.push(`(
                        (C.full_mvno_flag IS TRUE AND A.open_date <= to_timestamp(${_monthBeforeCurrentMonthForFull}))
                        OR
                        (C.full_mvno_flag IS NOT TRUE AND A.open_date <= to_timestamp(${_monthBeforeCurrentMonthForLite}))
                    )`);
                }
            } else {
                let whereIn = [];
                const allCONDaihyouBango = cONDaihyouBango.concat(cONDaihyouBangoFull);
                if (!isNone(d.startDateEN) && d.startDateEN < _monthBeforeCurrentMonthForLite) {
                    if (d.startDateEN < _monthBeforeCurrentMonthForFull) {
                        params.push(d.startDateEN);
                        whereSql.push(`A.open_date <= to_timestamp($${params.length})`);
                    } else {
                        for (const daihyoBango of allCONDaihyouBango) {
                            params.push(daihyoBango);
                            whereIn.push(`$${params.length}`);
                        }
                        if (whereIn.length > 0) {
                            whereSql.push(`(
                                ((C.full_mvno_flag IS TRUE OR A.nnumber IN (${whereIn.join(
                                    ','
                                )})) AND A.open_date <= to_timestamp(${_monthBeforeCurrentMonthForFull}))
                                OR
                                ((C.full_mvno_flag IS NOT TRUE OR A.nnumber IN (${whereIn.join(
                                    ','
                                )})) AND A.open_date <= to_timestamp(${d.startDateEN}))
                            )`);
                        }
                    }
                } else {
                    for (const daihyoBango of allCONDaihyouBango) {
                        params.push(daihyoBango);
                        whereIn.push(`$${params.length}`);
                    }
                    if (whereIn.length > 0) {
                        whereSql.push(`(
                            ((C.full_mvno_flag IS NOT TRUE OR A.nnumber IN (${whereIn.join(
                                ','
                            )})) AND A.open_date <= to_timestamp(${_monthBeforeCurrentMonthForLite}))
                            OR
                            ((C.full_mvno_flag IS TRUE OR A.nnumber IN (${whereIn.join(
                                ','
                            )})) AND A.open_date <= to_timestamp(${_monthBeforeCurrentMonthForFull}))
                        )`);
                    }
                }
            }
        }

        // check startDateST
        // because open_date upper limit has already set by either startDateEN or _monthBeforeCurrentMonth or _monthBeforeCurrentMonthForLite/Full
        // no need to check startDateST to any of those value
        // if startDateST > upper limit, then the query will not return any kaisens
        //   (case when startDateEN > upper limit)
        if (!isNone(d.startDateST)) {
            params.push(d.startDateST);
            whereSql.push(`A.open_date >= to_timestamp($${params.length})`);
        }
    }

    if (!isNone(d.searchValue)) {
        switch (d.searchType) {
            case KAISEN_KANRI_SEARCH_TYPE.KAISEN_NO:
                params.push(`%${d.searchValue}%`);
                whereSql.push(`A.line_id LIKE $${params.length}`);
                break;
            case KAISEN_KANRI_SEARCH_TYPE.IMSI:
                params.push(`%${d.searchValue}%`);
                whereSql.push(`A.imsi LIKE $${params.length}`);
                break;
            case KAISEN_KANRI_SEARCH_TYPE.SIM_NUMBER:
                params.push(`%${d.searchValue}%`);
                whereSql.push(`A.sim_number LIKE $${params.length}`);
                break;
        }
    }

    if (!isNone(d.simFlag) && d.targetLine !== TARGET_LINE.KUROKA_OVERDUE_KAISEN) {
        params.push(SIM_FLAG[d.simFlag]);
        whereSql.push(`A.sim_flag = $${params.length}`);
    }

    switch (d.fullMvnoFlag) {
        case true:
            if (!isNone(d.tenantID) && !d.tenantID.includes(COCN_TENANT_ID)) {
                whereSql.push('C.full_mvno_flag IS TRUE');
            } else {
                let whereIn = [];
                for (const daihyoBango of cONDaihyouBangoFull) {
                    params.push(daihyoBango);
                    whereIn.push(`$${params.length}`);
                }
                if (whereIn.length > 0) {
                    whereSql.push(`(C.full_mvno_flag IS TRUE OR A.nnumber IN ( ${whereIn.join(',')} ))`);
                }
            }
            break;
        case false:
            whereSql.push('C.full_mvno_flag IS NOT TRUE');
            break;
        // default -> keep both
    }

    if (!isNone(d.usageStatusFlag) && d.usageStatusFlag != USAGE_STATUS.UNKNOWN) {
        params.push(d.usageStatusFlag);
        whereSql.push(`A.usage_status = $${params.length}`);
    }

    sql_where += whereSql.length > 0 ? ` AND ${whereSql.join(' AND ')}` : '';

    let sql_where_sort = ' ';
    if (d.sortBy && Object.keys(KAISEN_SORT_CONDITION).includes(d.sortBy)) {
        const sortOrder = d.sortOrder ? (d.sortOrder.toLocaleLowerCase() == 'asc' ? 'ASC' : 'DESC') : 'DESC';
        switch (d.sortBy) {
            case 'contractType':
                sql_where_sort = ` ORDER BY
                    (CASE  WHEN A.contract_type is NULL
                    THEN ' '
                    ELSE A.contract_type END) ${sortOrder}`;
                break;
            case 'planName':
                sql_where_sort = ` ORDER BY
                    (CASE  WHEN B.plan_name is NULL
                    THEN ' '
                    ELSE B.plan_name END) ${sortOrder}`;
                break;
            case 'simType':
                sql_where_sort = ` ORDER BY
                    (CASE  WHEN A.sim_type is NULL
                    THEN ' '
                    ELSE A.sim_type END) ${sortOrder}`;
                break;
            case 'openDate':
                sql_where_sort = ` ORDER BY
                    (CASE  WHEN extract(epoch from A.open_date at time zone 'jst') is NULL
                    THEN -1
                    ELSE extract(epoch from A.open_date at time zone 'jst') END) ${sortOrder}`;
                break;
            default:
                sql_where_sort = ` ORDER BY ${KAISEN_SORT_CONDITION[d.sortBy]} ${sortOrder}`;
        }
    }
    const limitParams = [d.offset, d.limit];
    const sql_where_limit = ` OFFSET $${params.length + 1} LIMIT $${params.length + 2}`;

    const searchQuery = sql_select + sql_from + sql_where + sql_where_sort + sql_where_limit;
    const countQuery = sql_select_count + sql_from + sql_where;

    // context.log({ d, params });

    const [{ rows }, countResult] = await Promise.all([
        psql.query(context, { sql: searchQuery, params: params.concat(limitParams) }),
        psql.query(context, { sql: countQuery, params }),
    ]);

    let results = [];
    if (rows && rows.length > 0) {
        const getTenantId = (row) => {
            return cONDaihyouBango.includes(row.nnumber) || cONDaihyouBangoFull.includes(row.nnumber)
                ? COCN_TENANT_ID
                : row.tenant_id ?? '';
        };
        results = rows.map((r) => ({
            lineId: r.line_id,
            contractType: r.contract_type ?? '',
            planName: r.plan_name ?? '',
            planId: isNone(r.plan_id) ? -1 : +r.plan_id,
            simType: r.sim_type ?? '',
            startDate: isNone(r.start_date) ? -1 : +r.start_date,
            tenantId: getTenantId(r),
            openDate: isNone(r.open_date) ? -1 : +r.open_date,
            simFlag: r.sim_flag == false ? '黒' : '半黒',
            simNumber: r.sim_number ?? '',
            imsi: r.imsi ?? '',
            usageStatus: r.usage_status,
            voicePlanId: r.voice_plan_id,
            voicePlanName: r.voice_plan_name,
            planBbuniExFlag: isNone(r.plan_bbuni_ex_flag) ? null : r.plan_bbuni_ex_flag,
            userBbuniExFlag: isNone(r.user_bbuni_ex_flag) ? null : r.user_bbuni_ex_flag,
            swimmyBbuniExFlag: isNone(r.swimmy_bbuni_ex_flag) ? null : r.swimmy_bbuni_ex_flag,
            isBbuniEnabled: compareBB(r.user_bbuni_ex_flag, r.plan_bbuni_ex_flag),
        }));
    }

    let count = 0;
    if (countResult && countResult.rows && countResult.rows.length > 0) {
        count = +(countResult.rows[0].count ?? 0);
    }
    return {
        results,
        count,
    };
};

/**
 * 旧メソッド：get_haishi_kaisen_info
 * @param {object} context
 * @param {string} lineNo
 * @param {number} createdAt
 * @returns {Promise<{
 *  lineNo: string, nBan: string, oldPlanName: string, oldPlanId: number, simType: string,
 *  startDate: number, contractType: string, serviceDate: number, openDate: number
 * }|null}
 */
const getHaishiKaisenInfo = async (context, lineNo, createdAt) => {
    const params = [lineNo, createdAt];
    context.log('getHaishiKaisenInfo start', params);
    const sql = `
        SELECT
            A.line_id as line_no, A.nnumber as n_ban,
            extract(epoch from A.start_date at time zone 'jst') as start_date,
            A.sim_type as sim_type,
            extract(epoch from A.service_date at time zone 'jst') as service_date, 
            extract(epoch from A.open_date at time zone 'jst') as open_date,
            A.contract_type as contract_type, A.old_plan_id as old_plan_id,
            A.old_plan_name as old_plan_name
        FROM
            abone_lines as A 
        WHERE
            A.line_id = $1 AND A.created_at = to_timestamp($2)`;
    const { rows } = await psql.query(context, { sql, params });
    if (rows && rows.length == 1) {
        const r = rows[0];
        return {
            lineNo: r.line_no ?? '',
            nBan: r.n_ban ?? '',
            oldPlanName: r.old_plan_name ?? '',
            oldPlanId: isNone(r.old_plan_id) ? -1 : +r.old_plan_id,
            simType: r.sim_type ?? '',
            startDate: isNone(r.start_date) ? -1 : +r.start_date,
            contractType: r.contract_type ?? '',
            serviceDate: isNone(r.service_date) ? -1 : +r.service_date,
            openDate: isNone(r.open_date) ? -1 : +r.open_date,
        };
    } else {
        return null;
    }
};

/**
 * has_update_kaisen
 * @param {object} context
 * @param {number} roleId
 * @returns {Promise<boolean>}
 */
const hasUpdateKaisen = async (context, roleId) => {
    context.log('hasUpdateKaisen start', roleId);
    const sql = `
        SELECT
            A.priv_name
        FROM
            privileges as A
        JOIN
            role_privs as B ON A.priv_id = B.priv_id
        WHERE
            B.role_id=$1 AND A.priv_name='SO投入'`;
    const { rows } = await psql.query(context, { sql, params: [roleId] });
    return rows && rows.length ? true : false;
};

/**
 * 廃止済み判定
 * 旧メソッド：haishizumiHantei
 * @param  {object} context - API context
 * @param  {string} kaisenNo required
 * @return {boolean}        true - indicate exists in DB
 *                          false - indicate NOT exists in DB
 */
const isKaisenNoDeactivated = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('isKaisenNoDeactivated start :' + kaisenNo);

    const sql = `SELECT 1 AS one FROM lines WHERE line_id = $1 AND line_status = '${DEACTIVATED_STATUS}' LIMIT 1`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    return rows.length === 1;
};

/**
 * 廃止済み判定
 * @param {object} context
 * @param {Array<string>} kaisenNoList
 * @returns {Promise<Set<string>>} a set containing line numbers which have been deactivated
 */
const isKaisenNoDeactivatedMultiple = async (context, kaisenNoList) => {
    if (!Array.isArray(kaisenNoList) || kaisenNoList.length === 0) return new Set();
    context.log('isKaisenNoDeactivatedMultiple start :', kaisenNoList.length, 'line numbers');
    const sql = `
        SELECT line_id
        FROM lines
        WHERE line_id IN ${queryUtils.buildListOfValues(kaisenNoList)}
            AND line_status = '${DEACTIVATED_STATUS}'
        LIMIT 1`;
    const { rows } = await psql.query(context, { sql, params: kaisenNoList });
    return rows && rows.length > 0 ? new Set(rows.map((r) => r.line_id)) : new Set();
};

/**
 * It takes an array of [lineID, soData] pairs, and returns an array of [lineID, soData, found]
 * triples, where found is true if the lineID is found in the database, and false otherwise
 * @param context - The context object that is passed to the Azure Function.
 * @param kaisenNoAndExtraSeq - an array of arrays, each of which has the following structure:
 *                             [lineID, soData]
 * @returns An array of arrays.
 */
const karitourokuKanryoCheckByKaisenNos = async (context, kaisenNoAndExtraSeq) => {
    context.log('karitourokuKanryoCheckByKaisenNos start');

    let lineIDsCommaSeparatedPart = "'not_exists_line_id'";
    if (kaisenNoAndExtraSeq.length > 0) {
        lineIDsCommaSeparatedPart = kaisenNoAndExtraSeq.map(([lineID]) => `'${lineID}'`).join(',');
    }
    const sql = `
        SELECT line_id
        FROM lines
        WHERE line_id IN (${lineIDsCommaSeparatedPart}) AND ((temp_regist IS NULL) OR (temp_regist = false)) AND line_status != '03'`;
    const { rows } = await psql.query(context, { sql });
    const retVal = kaisenNoAndExtraSeq.map(([lineID, soData]) => {
        const found = rows.some((row) => row.line_id === lineID);
        return [lineID, soData, found];
    });
    return retVal;
};

/**
 *
 * @param {object} context
 * @param {string} kaisenNo
 * @returns {Promise<{simType: string, priorityLine: boolean}|null>}
 */
const isPriorityLine = async (context, kaisenNo) => {
    const sql = `
        SELECT
            A.sim_type as sim_type, B.priority_line as priority_line
        FROM
            lines as A 
        LEFT OUTER JOIN
            card as B ON B.device_type_id = A.device_type_id
        WHERE
            A.line_id = $1`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    if (rows && rows.length !== 0) {
        return {
            simType: rows[0].sim_type ?? '',
            priorityLine: rows[0].priority_line ?? false,
        };
    }
    return null;
};

/**
 * @param {object} context
 * @param {string} lineNo
 * @returns {Promise<number|null>} unix timestamp in seconds
 */
const getOpenDate = async (context, lineNo) => {
    context.log('getOpenDate start:', lineNo);
    const sql = `
        SELECT
            line_id,
            extract(epoch from open_date at time zone 'jst') as open_date_in_epoch
        FROM
            lines
        WHERE
            line_id = $1 AND open_date is not null`;
    const { rows } = await psql.query(context, { sql, params: [lineNo] });
    if (isNone(rows) || !Array.isArray(rows)) {
        return null;
    } else {
        return +rows[0].open_date_in_epoch;
    }
};

/**
 * @param {object} context
 * @param {string} lineNo
 * @returns {Promise<{[lineNo: string]: number}>} unix timestamp in seconds
 */
const getOpenDateMultiple = async (context, lineNoList) => {
    if (isNone(lineNoList) || !Array.isArray(lineNoList)) return {};
    context.log('getOpenDate start:', lineNoList.length, 'lines');
    const sql = `
        SELECT
            line_id, extract(epoch from open_date at time zone 'jst') as open_date_in_epoch
        FROM
            lines
        WHERE
            line_id IN ${queryUtils.buildListOfValues(lineNoList)}
            AND open_date is not null`;
    const { rows } = await psql.query(context, { sql, params: lineNoList });
    if (isNone(rows) || !Array.isArray(rows)) {
        return {};
    } else {
        return rows.reduce((acc, cur) => {
            acc[cur.line_id] = +cur.open_date_in_epoch;
            return acc;
        }, {});
    }
};

/**
 * 回線の0035でんわプランIDを取得
 * @param {object} context
 * @param {string} lineNo
 * @returns {Promise<{lineNo: string, voicePlanId: string, voicePlanName: string} | null>}
 */
const getLineDetail = async (context, lineNo) => {
    context.log('getLineDetail start:', lineNo);
    if (isNone(lineNo)) return null;
    const sql = `
        SELECT
            line_id, voice_plan_id, voice_plan_name
        FROM
            lines
        WHERE
            line_id = $1 AND line_status != '${DEACTIVATED_STATUS}'`;
    const { rows } = await psql.query(context, { sql, params: [lineNo] });
    if (!isNone(rows) && Array.isArray(rows)) {
        return {
            lineNo: rows[0].line_id,
            voicePlanId: rows[0].voice_plan_id,
            voicePlanName: rows[0].voice_plan_name,
        };
    } else {
        return null;
    }
};

module.exports = {
    DEACTIVATED_STATUS,
    SIM_FLAG,
    BB_UNIBA_FLAG,
    HAISHI_SORT_CONDITION_LIST,
    KAISEN_SORT_CONDITION,
    KAISEN_KANRI_SEARCH_TYPE,
    TARGET_LINE,
    LIMIT,
    OFFSET,
    isFullMVNO,
    isFullMVNOKaisenMultiple,
    isKaisenNoActivated,
    isKaisenNoBelongToTenant,
    isKaisenNoBelongToTenantMultiple,
    isUsingVoicePlan,
    isUsingVoicePlanMultiple,
    getTenantIdByKaisenNo,
    getKaisenInfo,
    getKaisenInfoDetail,
    getSimFlagByKaisenNo,
    getSimFlagByKaisenNoMultiple,
    getSimFlagAndUsageStatus,
    convertKaisenInfoToResalePlan,
    convertKaisenInfoToResalePlanFullMVNO,
    convertKaisenInfoToResalePlanMultiple,
    convertKaisenInfoToResalePlanFullMVNOMultiple,
    isInHaishiOrderProcess,
    isInHaishiOrderProcessMultiple,
    retrieveUsingMAWPLineOptions,
    retrieveUsingMAWPLineOptionsAndNBan2,
    hasHaishiSOAfterReserveDate,
    hasHaishiSOAfterReserveDateMultiple,
    isInOpenOrderProcess,
    getUsageStatusByLineNo,
    getHaishiKaisenList,
    getKaisenList,
    getHaishiKaisenInfo,
    hasUpdateKaisen,
    isKaisenNoDeactivated,
    isKaisenNoDeactivatedMultiple,
    karitourokuKanryoCheckByKaisenNos,
    isPriorityLine,
    getOpenDate,
    getOpenDateMultiple,
    getLineDetail,
};
