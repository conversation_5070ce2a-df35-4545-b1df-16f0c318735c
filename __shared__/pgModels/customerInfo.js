const psql = require('../database/postgre');
const { isNone } = require('../helpers/baseHelper');
const queryUtils = require('../utils/queryUtils');

/**
 * get voicemailCallWaitingOption by nNo
 * @param  {object} context - API context
 * @param  {string} nNo - required
 * @return {Promise<{voicemail_option_id:string, callwaiting_option_id:string}>} customerInfo - data from DB
 * @throws Error if required data is not passed
 */
const getVoicemailCallWaitingOptionByNNo = async (context, nNo) => {
    // if (!nNo) throw new Error('nNo is required');
    context.log('getVoicemailCallWaitingOptionByNNo start :' + nNo);
    // allow empty nNo
    if (isNone(nNo)) return null;

    const sql = `
        SELECT
            voicemail_option_id,
            callwaiting_option_id
        FROM
            customer_info
        WHERE nnumber = $1`;

    const { rows } = await psql.query(context, { sql, params: [nNo] });
    if (rows.length === 0) return null;

    return rows[0];
};

/**
 * get addressInfo by nNo
 * @param  {object} context - API context
 * @param  {string} nNo - required
 * @return {object} addressInfo - data from DB
 * @throws Error if required data is not passed
 */
const getAddressInfoByNNo = async (context, nNo) => {
    if (!nNo) throw new Error('nNo is required');
    context.log('getAddressInfoByNNo start :' + nNo);

    const sql = `
        SELECT
            contractor_name_kana,
            contractor_section_name,
            addressee_postcode,
            addressee_prefectures,
            addressee_cities,
            addressee_daiji,
            addressee_ji,
            addressee_block,
            addressee_mansion,
            addressee_name,
            addressee_section_name,
            addressee_representative_name,
            addressee_phone_number
        FROM
            customer_info
        WHERE
            nnumber = $1`;

    const { rows } = await psql.query(context, { sql, params: [nNo] });
    if (rows.length === 0) return null;

    return rows[0];
};

/**
 * get agentCode by kaisenNo
 * @param  {object} context - API context
 * @param  {string} kaisenNo   - required
 * @return {string|null} agentCode
 */
const getAgentCode = async (context, kaisenNo) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    context.log('getAgentCode start :' + kaisenNo);

    const sql = `
        SELECT
            agent_code
        FROM
            customer_info
            INNER JOIN lines ON customer_info.nnumber = lines.nnumber
        WHERE
            lines.line_id = $1`;
    const { rows } = await psql.query(context, { sql, params: [kaisenNo] });
    if (rows.length === 0) return null;

    return rows[0].agent_code;
};

/**
 * get map of agentCode by kaisenNos
 * @param {object} context
 * @param {Array<string>} kaisenNoList
 * @returns {Promise<{[kaisenNo: string]: string|null}>}
 */
const getAgentCodeMultiple = async (context, kaisenNoList) => {
    if (!Array.isArray(kaisenNoList)) throw new Error('kaisenNoList must be an array');
    if (kaisenNoList.length === 0) return {};
    context.log('getAgentCodeMultiple start:', kaisenNoList.length, 'line numbers');
    const sql = `
        SELECT
            lines.line_id, customer_info.agent_code
        FROM
            customer_info
            INNER JOIN lines ON customer_info.nnumber = lines.nnumber
        WHERE
            lines.line_id IN ${queryUtils.buildListOfValues(kaisenNoList)}`;
    const { rows } = await psql.query(context, { sql, params: kaisenNoList });
    let result = {};
    if (rows && rows.length > 0) {
        result = rows.reduce((acc, row) => {
            acc[row.line_id] = row.agent_code;
            return acc;
        }, {});
    }
    return result;
};

/**
 *
 * @param {object} context
 * @param {Array<string>} nNumbers
 * @returns {Promise<Map<string, CustomerInfoFromCore>>|null}
 */
const retrieveMultiCustomerInfo = async (context, nNumbers) => {
    context.log('retrieveMultiCustomerInfo START: ', nNumbers);

    let paramNNo = ['not_exists_nnumber']; // 0個の場合
    let nNumberPlaceholder = '$1';
    if (!isNone(nNumbers)) {
        nNumberPlaceholder = nNumbers.map((_t, index) => `$${index + 1}`).join(','); // $1,$2,$3,...
        paramNNo = nNumbers;
    }

    const sql = `
        SELECT
            nnumber, contractor_name_kana, contractor_section_name, addressee_postcode, addressee_prefectures, addressee_cities, addressee_daiji, addressee_ji,
            addressee_block, addressee_mansion, addressee_name, addressee_section_name, addressee_representative_name, addressee_phone_number
        FROM
            customer_info
        WHERE
            nnumber IN (${nNumberPlaceholder})`;
    const { rows } = await psql.query(context, { sql, params: paramNNo });
    if (rows && rows.length > 0) {
        const map = new Map();
        rows.map((r) => {
            const result = {
                contractor_name_kana: r.contractor_name_kana ?? '',
                contractor_section_name: r.contractor_section_name ?? '',
                addressee_postcode: r.addressee_postcode ?? '',
                addressee_prefectures: r.addressee_prefectures ?? '',
                addressee_cities: r.addressee_cities ?? '',
                addressee_daiji: r.addressee_daiji ?? '',
                addressee_ji: r.addressee_ji ?? '',
                addressee_block: r.addressee_block ?? '',
                addressee_mansion: r.addressee_mansion ?? '',
                addressee_name: r.addressee_name ?? '',
                addressee_section_name: r.addressee_section_name ?? '',
                addressee_representative_name: r.addressee_representative_name ?? '',
                addressee_phone_number: r.addressee_phone_number ?? '',
            };
            map.set(r.nnumber ?? '', result);
        });
        return map;
    } else {
        return null;
    }
};

module.exports = {
    getVoicemailCallWaitingOptionByNNo,
    getAddressInfoByNNo,
    getAgentCode,
    getAgentCodeMultiple,
    retrieveMultiCustomerInfo,
};
