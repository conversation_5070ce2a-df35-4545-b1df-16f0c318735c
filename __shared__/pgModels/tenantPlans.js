const psql = require('../database/postgre');
const { MVNO_SERVICES } = require('../constants/mvnoServicesConstants');

/**
 * An object containing plan details
 * @typedef {object} PlanInfo
 * @property {number} plan_id
 * @property {string} plan_name
 * @property {string} resale_plan_id
 * @property {boolean} full_mvno_flag
 * @property {boolean} network
 * @property {boolean} sms_enable
 * @property {boolean} voice_flag
 * @property {boolean} default_sim_flag
 */

/**
 * find tenantPlans by tenantId & serviceType
 * @param  {object} context - API context
 * @param  {object} data    - required params for query
 * @param  {string} data.tenantId
 * @param  {number} [data.serviceType] - MVNO_SERVICES(LITE/FULL/ALL)
 * @return {Array.<PlanInfo>} tenantPlans - data from DB
 */
const findMAWPPlans = async (context, { tenantId, serviceType = MVNO_SERVICES.ALL }) => {
    if (!tenantId) throw new Error('tenantId is required');
    context.log('findMAWPPlans start :' + tenantId);

    let bExtendWhere;

    //STEP 14: FULL MVNO
    switch (serviceType) {
        case MVNO_SERVICES.LITE:
            bExtendWhere = 'AND (B.full_mvno_flag != true OR B.full_mvno_flag IS NULL)';
            break;
        case MVNO_SERVICES.FULL:
            bExtendWhere = 'AND B.full_mvno_flag = true';
            break;
        default:
            bExtendWhere = '';
    }

    let sql = `
        SELECT
            A.plan_id,
            B.plan_name,
            B.resale_plan_id,
            B.full_mvno_flag,
            B.network,
            B.sms_enable,
            B.voice_flag,
            B.default_sim_flag
        FROM
            (SELECT DISTINCT plan_id FROM tenant_plans WHERE tenant_id = $1) AS A
            INNER JOIN
                plans AS B
            ON B.plan_id = A.plan_id
            WHERE (B.plan_name NOT LIKE '【廃止】%')
            ${bExtendWhere}
        ORDER BY
            A.plan_id ASC`;

    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    return rows;
};

/**
 * Check tenant-plan pair exists
 * @param {object} context API context
 * @param {string} tenantId
 * @param {string|number} planId
 * @param {string} [serviceType] MVNO_SERVICES(ALL/LITE/FULL)
 */
const checkTenantPlanExists = async (context, tenantId, planId, serviceType = MVNO_SERVICES.ALL) => {
    if (!tenantId) throw new Error('tenantId is required');
    context.log(`checkTenantPlanExists start : ${tenantId}, ${planId}`);

    let bExtendWhere;

    //STEP 14: FULL MVNO
    switch (serviceType) {
        case MVNO_SERVICES.LITE:
            bExtendWhere = 'AND (B.full_mvno_flag != true OR B.full_mvno_flag IS NULL)';
            break;
        case MVNO_SERVICES.FULL:
            bExtendWhere = 'AND B.full_mvno_flag = true';
            break;
        default:
            bExtendWhere = '';
    }

    let sql = `
        SELECT
            COUNT(DISTINCT A.plan_id)
        FROM
            tenant_plans AS A
            INNER JOIN
                plans AS B
            ON
                B.plan_id = A.plan_id
        WHERE
            A.tenant_id = $1
            AND A.plan_id = $2
            AND (B.plan_name NOT LIKE '【廃止】%')
            ${bExtendWhere}`;

    const { rows } = await psql.query(context, { sql, params: [tenantId, planId] });
    if (!rows || rows.length === 0) {
        context.log.error('checkTenantPlanExists: query returns 0 row');
    }
    return rows && rows.length > 0 ? parseInt(rows[0].count) > 0 : false;
};

module.exports = {
    findMAWPPlans,
    checkTenantPlanExists,
};
