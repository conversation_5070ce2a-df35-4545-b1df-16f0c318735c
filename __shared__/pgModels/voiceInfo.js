const psql = require('../database/postgre');
const { isNone } = require('../helpers/baseHelper');
const queryUtils = require('../utils/queryUtils');

/**
 * get defaultPlan by tenantId
 * 旧メソッド: getDefaultPlan
 * @param  {object} context - API context
 * @param  {string} tenantId
 * @return {{voice_plan_id:string, voice_plan_name:string}} defaultPlan - data from DB
 */
const getDefaultPlanByTenantId = async (context, tenantId) => {
    if (!tenantId) throw new Error('tenantId is required');
    context.log('getDefaultPlanByTenantId start :' + tenantId);

    const sql = `
        SELECT voice_plan_id, voice_plan_name
        FROM voice_info
        WHERE (tenant_id = $1)
          AND default_flag = true`;

    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    if (rows.length === 0) return null;

    return rows[0];
};

/**
 * Get default plan for each tenant IDs
 * @param {object} context
 * @param {Array<string>} tenantIdList
 * @returns {Promise<{[tenantId: string]: {voice_plan_id:string, voice_plan_name:string}}>}
 */
const getDefaultPlanMultiple = async (context, tenantIdList) => {
    if (!Array.isArray(tenantIdList) || tenantIdList.length === 0) return {};
    context.log('getDefaultPlanMultiple start:', tenantIdList);

    const sql = `
        SELECT tenant_id, voice_plan_id, voice_plan_name
        FROM voice_info
        WHERE tenant_id IN ${queryUtils.buildListOfValues(tenantIdList)}
        AND default_flag = true`;

    const { rows } = await psql.query(context, { sql, params: tenantIdList });
    if (rows && rows.length > 0) {
        return rows.reduce((acc, row) => {
            acc[row.tenant_id] = {
                voice_plan_id: row.voice_plan_id,
                voice_plan_name: row.voice_plan_name,
            };
            return acc;
        }, {});
    }
    return {};
};

/**
 * 0035でんわプラン情報を取得
 * @param {object} context
 * @param {string} tenantId
 * @param {string} voicePlanId
 * @returns {Promise<{tenantId:string,voicePlanId:string,voicePlanName:string,defaultFlag:boolean,voicePlanNnumber:string}|null>}
 */
const getVoicePlanDetail = async (context, tenantId, voicePlanId) => {
    context.log('getVoicePlan start:', tenantId, voicePlanId);
    if (isNone(tenantId) || isNone(voicePlanId)) return null;
    const sql = `
        SELECT
            tenant_id, voice_plan_id, voice_plan_name, default_flag, voice_plan_nnumber
        FROM
            voice_info
        WHERE
            tenant_id = $1 AND voice_plan_id = $2`;
    const { rows } = await psql.query(context, { sql, params: [tenantId, voicePlanId] });
    if (isNone(rows) || !Array.isArray(rows)) return null;
    return {
        tenantId: rows[0].tenantId,
        voicePlanId: rows[0].voice_plan_id,
        voicePlanName: rows[0].voicePlanName,
        defaultFlag: rows[0].default_flag,
        voicePlanNnumber: rows[0].voice_plan_nnumber,
    };
};

/**
 * Get multiple voice plans from a tenant
 * @param {object} context
 * @param {string} tenantId
 * @param {Array<string>} voicePlanIdList
 * @returns {Promise<{[voicePlanId:string]: {tenantId:string,voicePlanId:string,voicePlanName:string,defaultFlag:boolean,voicePlanNnumber:string}}}
 */
const getVoicePlanDetailMultiple = async (context, tenantId, voicePlanIdList) => {
    context.log('getVoicePlanDetailMultiple start:', tenantId, voicePlanIdList);
    if (isNone(tenantId) || !Array.isArray(voicePlanIdList) || isNone(voicePlanIdList)) return {};
    const sql = `
        SELECT
            tenant_id, voice_plan_id, voice_plan_name, default_flag, voice_plan_nnumber
        FROM
            voice_info
        WHERE
            tenant_id = $1 AND voice_plan_id IN ${queryUtils.buildListOfValues(voicePlanIdList, 2)}`;
    const { rows } = await psql.query(context, { sql, params: [tenantId].concat(voicePlanIdList) });
    return rows.reduce((acc, cur) => {
        acc[cur.voice_plan_id] = {
            tenantId: cur.tenantId,
            voicePlanId: cur.voice_plan_id,
            voicePlanName: cur.voice_plan_name,
            defaultFlag: cur.default_flag,
            voicePlanNnumber: cur.voice_plan_nnumber,
        };
        return acc;
    }, {});
};

/**
 *
 * @param {object} context
 * @param {string} tenantId
 * @returns {Promise<Array<{voicePlanId:string,voicePlanName:string,defaultFlag:boolean,voicePlanNnumber:string}>>}
 */
const getTenantVoicePlans = async (context, tenantId) => {
    context.log('getTenantVoicePlans start: ', tenantId);
    if (isNone(tenantId)) return [];

    const sql = `
        SELECT
            voice_plan_id, voice_plan_name, default_flag, voice_plan_nnumber
        FROM
            voice_info
        WHERE
            tenant_id = $1`;
    const { rows } = await psql.query(context, { sql, params: [tenantId] });
    if (isNone(rows) || !Array.isArray(rows)) return [];
    return rows.map((r) => ({
        voicePlanId: r.voice_plan_id,
        voicePlanName: r.voice_plan_name,
        defaultFlag: r.default_flag,
        voicePlanNnumber: r.voice_plan_nnumber,
    }));
};

module.exports = {
    getDefaultPlanByTenantId,
    getDefaultPlanMultiple,
    getVoicePlanDetail,
    getVoicePlanDetailMultiple,
    getTenantVoicePlans,
};
