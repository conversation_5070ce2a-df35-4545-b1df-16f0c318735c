const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const psql = require('../database/postgre');
const { FULL_ORDER_TYPE, SUB_ORDER_TYPE } = require('../constants/orderConstants');
const { isNone } = require('../helpers/baseHelper');
const queryUtils = require('../utils/queryUtils');

dayjs.extend(utc);
dayjs.extend(timezone);

/** sortProp */
const SORT_PROP = {
    so_id: 'service_order_id',
    order_date: 'unix_order_date',
    reserve_date: 'unix_reserve_date',
    exec_date: 'unix_exec_date',
    order_type: 'order_type',
    order_status: 'order_status',
    line_no: 'line_id',
};

/**
 * @typedef {object} SoSearchData
 * @property {string} [line_no]
 * @property {string} [so_id]
 * @property {Array<string>} tenant_ids
 * @property {Array<string} [order_status]
 * @property {string} [order_type]
 * @property {number} [order_date_from]
 * @property {number} [order_date_to]
 * @property {number} [exec_date_from]
 * @property {number} [exec_date_to]
 * @property {number} [limit]
 * @property {number} [offset]
 * @property {string} [sort_by]
 * @property {string} [sort_order]
 * @property {boolean} isSuperTenant
 */

/**
 * @typedef {object} SoListResponse
 * @property {string} tenant_id
 * @property {string} so_id
 * @property {string|number} order_date
 * @property {string|number} reserve_date
 * @property {string|number} exec_date
 * @property  {string} order_type
 * @property {string} order_status
 * @property {string} line_no
 */

/**
 * 旧メソッド：ServiceOrderInfoDAO.searchSo
 * @param {object} context
 * @param {SoSearchData} d search queries
 * @returns {Promise<{results: Array<SoListResponse>, count: number}>} paginated result and query rows count
 */
const searchServiceOrder = async (context, d) => {
    context.log('searchSo start');
    const sql_select = `
        SELECT
            SO.tenant_id as tenant_id, SO.service_order_id,
            extract(epoch FROM SO.order_date at time zone 'jst') AS unix_order_date,
            extract(epoch FROM SO.reserve_date at time zone 'jst') AS unix_reserve_date,
            extract(epoch FROM SO.exec_date at time zone 'jst') AS unix_exec_date,
            SO.order_type, SO.order_status, SO.line_id `;
    let sql_table = `
        FROM
            service_orders AS SO
        JOIN
            tenants as T ON SO.tenant_id = T.tenant_id
        WHERE
            (T.status = TRUE)
            AND SO.function_type NOT IN ('51', '52', '53', '54', '55') `;

    // Queryパラメータ
    let params = [];
    let whereSql = [];

    if (d.line_no) {
        params.push(`%${d.line_no}%`);
        whereSql.push(`SO.line_id LIKE $${params.length}`);
    } else if (d.so_id) {
        params.push(`%${d.so_id}%`);
        whereSql.push(`SO.service_order_id LIKE $${params.length}`);
    }

    if (d.tenant_ids && Array.isArray(d.tenant_ids)) {
        let whereIn = [];
        for (const tenant of d.tenant_ids) {
            params.push(tenant);
            whereIn.push(`$${params.length}`);
        }
        // SO.tenant_id IN ($2,$3,...)
        if (whereIn.length > 0) whereSql.push(`SO.tenant_id IN ( ${whereIn.join(',')} )`);
    }

    if (d.order_status && Array.isArray(d.order_status)) {
        let whereIn = [];
        for (const order_status of d.order_status) {
            params.push(order_status);
            whereIn.push(`$${params.length}`);
        }
        // SO.order_status IN ($2,$3,...)
        if (whereIn.length > 0) whereSql.push(`SO.order_status IN ( ${whereIn.join(',')} )`);
    }

    if (d.order_type) {
        params.push(d.order_type);
        whereSql.push(`SO.order_type = $${params.length}`);
    }

    if (!isNone(d.order_date_from) && !isNone(d.order_date_to)) {
        params.push(d.order_date_from);
        whereSql.push(`SO.order_date >= to_timestamp($${params.length})`); // order_date >= from
        params.push(d.order_date_to);
        whereSql.push(`SO.order_date < to_timestamp($${params.length})`); // order_date < to
    }

    if (!isNone(d.exec_date_from) && !isNone(d.exec_date_to)) {
        params.push(d.exec_date_from);
        whereSql.push(`SO.exec_date >= to_timestamp($${params.length})`); // exec_date >= from
        params.push(d.exec_date_to);
        whereSql.push(`SO.exec_date < to_timestamp($${params.length})`); // exec_date < to
    }

    if (whereSql.length == 0) {
        // デフォルトで30日以内結果のみ検索できます。
        const today = dayjs().tz('Asia/Tokyo');
        const dateTo = today.add(1, 'day');
        const dateFrom = today.subtract(30, 'days');
        params.push(dateFrom.format('YYYY-MM-DD 00:00:00'));
        whereSql.push(`SO.order_date >= $${params.length}`);
        params.push(dateTo.format('YYYY-MM-DD 00:00:00'));
        whereSql.push(`SO.order_date < $${params.length}`);
    }

    // 種別は指定した種別のみQueryができます。
    if (isNone(d.order_type)) {
        const orderTypeList = d.isSuperTenant ? FULL_ORDER_TYPE : SUB_ORDER_TYPE;
        let whereIn = [];
        for (const orderType of orderTypeList) {
            params.push(orderType);
            whereIn.push(`$${params.length}`);
        }
        if (whereIn.length > 0) whereSql.push(`SO.order_type IN ( ${whereIn.join(',')} )`);
    }

    // 全ての条件をANDする (whereSql.length > 0)
    sql_table += ' AND ' + whereSql.join(' AND ');

    // Paging又はsorting
    let searchQuery = sql_select + sql_table;
    let paginationParams = [];
    if (d.sort_by && Object.keys(SORT_PROP).includes(d.sort_by)) {
        const sortOrder = d.sort_order ? (d.sort_order.toLowerCase() == 'asc' ? 'ASC' : 'DESC') : 'DESC';
        switch (d.sort_by) {
            case 'reserve_date':
                searchQuery += ` ORDER BY 
                    (CASE  WHEN extract(epoch FROM SO.reserve_date at time zone 'jst') is NULL
                    THEN -1
                    ELSE extract(epoch FROM SO.reserve_date at time zone 'jst') END)
                    ${sortOrder}`;
                break;
            case 'exec_date':
                searchQuery += ` ORDER BY
                    (CASE  WHEN extract(epoch FROM SO.exec_date at time zone 'jst') is NULL
                    THEN -1
                    ELSE extract(epoch FROM SO.exec_date at time zone 'jst') END)
                    ${sortOrder}`;
                break;
            case `line_no`:
                searchQuery += ` ORDER BY
                    (CASE  WHEN SO.line_id is NULL
                    THEN ' '
                    ELSE SO.line_id END)
                    ${sortOrder}`;
                break;
            default:
                searchQuery += ` ORDER BY ${SORT_PROP[d.sort_by]} ${sortOrder}`;
                break;
        }
    } else {
        searchQuery += ' ORDER BY unix_order_date DESC';
    }

    if (!isNone(d.offset)) {
        paginationParams.push(d.offset);
        searchQuery += ` OFFSET $${params.length + paginationParams.length}`;
    }
    if (!isNone(d.limit)) {
        paginationParams.push(d.limit);
        searchQuery += ` LIMIT $${params.length + paginationParams.length}`;
    }

    const countQuery = 'SELECT COUNT(SO.service_order_id)' + sql_table;

    const [{ rows }, countResult] = await Promise.all([
        psql.query(context, { sql: searchQuery, params: params.concat(paginationParams) }),
        psql.query(context, { sql: countQuery, params }),
    ]);

    let results = [];
    if (rows && rows.length > 0) {
        results = rows.map((r) => _SoListResponse(r));
    }
    let count = 0;
    if (countResult && countResult.rows && countResult.rows.length > 0) {
        count = +(countResult.rows[0].count ?? 0);
    }
    return { results, count };
};

/**
 * build SoListResponse from result row
 * @param {object} row
 * @returns {SoListResponse}
 */
const _SoListResponse = (row) => {
    return {
        tenant_id: row.tenant_id ?? '',
        so_id: row.service_order_id ?? '',
        order_date: !isNone(row.unix_order_date) ? +row.unix_order_date : '',
        reserve_date: !isNone(row.unix_reserve_date) ? +row.unix_reserve_date : '',
        exec_date: !isNone(row.unix_exec_date) ? +row.unix_exec_date : '',
        order_type: row.order_type ?? '',
        order_status: row.order_status ?? '',
        line_no: row.line_id ?? '',
    };
};

const updateExecSO = async (context, service_order_id, tenant_id, user_id) => {
    context.log('updateExecSO start');
    const sql = `
        UPDATE
            service_orders
        SET
            exec_user_id = $1,
            exec_tenant_id = $2
        WHERE
            service_order_id = $3`;
    await psql.query(context, { sql, params: [user_id, tenant_id, service_order_id] });
    context.log('updateExecSO end');
};

/**
 *
 * @param {object} context
 * @param {Array<string>} service_order_id_array
 * @param {string} tenant_id
 * @param {string} user_id
 */
const updateExecSOMultiple = async (context, service_order_id_array, tenant_id, user_id) => {
    if (!Array.isArray(service_order_id_array)) throw new Error('service_order_id_array is not an array!');
    context.log('updateExecSOMultiple start:', service_order_id_array.length);
    if (service_order_id_array.length === 0) return;
    const sql = `
        UPDATE
            service_orders
        SET
            exec_user_id = $1,
            exec_tenant_id = $2
        WHERE
            service_order_id IN ${queryUtils.buildListOfValues(service_order_id_array, 3)}`;
    await psql.query(context, { sql, params: [user_id, tenant_id].concat(service_order_id_array) });
    context.log('updateExecSOMultiple end');
};

/**
 * check_plan_henko_chuu
 * @param {object} context
 * @param {string} line_id
 * @returns {Promise<boolean>}
 */
const checkPlanHenkoChuu = async (context, line_id) => {
    context.log('Get info service order plan henko start!');
    const sql = `
        SELECT
            service_order_id
        FROM
            service_orders
        WHERE
            line_id = $1
            AND order_type = 'プラン変更'
            AND order_status = '予約中'`;
    const { rows } = await psql.query(context, { sql, params: [line_id] });
    return rows && rows.length > 0 ? true : false;
};

/**
 * check_plan_henko_chuu for multiple line numbers
 * @param {object} context
 * @param {Array<string>} lineArray
 * @returns {Promise<Set<string>>} set of line_id which has service order
 */
const checkPlanHenkoChuuMultiple = async (context, lineArray) => {
    if (!Array.isArray(lineArray)) throw new Error('lineArray must be an array!');
    context.log('checkPlanHenkoChuuMultiple start', lineArray.length);
    const sql = `
        SELECT
            service_order_id, line_id
        FROM
            service_orders
        WHERE
            line_id IN ${queryUtils.buildListOfValues(lineArray)}
            AND order_type = 'プラン変更'
            AND order_status = '予約中'`;
    const { rows } = await psql.query(context, { sql, params: lineArray });
    if (rows && rows.length > 0) {
        return new Set(rows.map((r) => r.line_id));
    } else {
        return new Set();
    }
};

const getSoDetail = async (context, tenantId, soId) => {
    context.log('getSoDetail start', tenantId, soId);
    const sql = `SELECT service_order_id, extract(epoch FROM order_date at time zone 'jst') AS unix_order_date,
        extract(epoch FROM reserve_date at time zone 'jst') AS unix_reserve_date,
        extract(epoch FROM exec_date at time zone 'jst') AS unix_exec_date,
        order_type, order_status, line_id, content, tenant_id, exec_user_id, exec_tenant_id FROM service_orders
        WHERE function_type NOT IN ('51', '52', '53', '54', '55') AND service_order_id=$1 AND tenant_id=$2`;
    const { rows } = await psql.query(context, { sql, params: [soId, tenantId] });

    if (rows && rows.length > 0) {
        const tenantId = rows[0].tenant_id || '';
        const soId = rows[0].service_order_id || '';
        const orderDate = rows[0].unix_order_date || '';
        const reserveDate = rows[0].unix_reserve_date || '';
        const execDate = rows[0].unix_exec_date || '';
        const orderType = rows[0].order_type || '';
        const orderStatus = rows[0].order_status || '';
        const lineNo = rows[0].line_id || '';
        const orderContent = rows[0].content || '';
        const execUserId = rows[0].exec_user_id || '';
        const execTenantId = rows[0].exec_tenant_id || '';

        return {
            tenant_id: tenantId,
            so_id: soId,
            order_date: orderDate,
            reserve_date: reserveDate,
            exec_date: execDate,
            order_type: orderType,
            order_status: orderStatus,
            line_no: lineNo,
            order_content: orderContent,
            exec_user_id: execUserId,
            exec_tenant_id: execTenantId,
        };
    } else {
        return null;
    }
};

module.exports = {
    searchServiceOrder,
    checkPlanHenkoChuu,
    checkPlanHenkoChuuMultiple,
    updateExecSO,
    updateExecSOMultiple,
    getSoDetail,
};
