/* eslint-disable no-unused-vars */
const appConfig = require('@azure/app-configuration');

const isLocalConfig = process.env.LOCAL_SETTINGS_CONFIG || process.env.RUN_IN_DOCKER ? true : false;

/** @type {appConfig.AppConfigurationClient} */
const configClient = isLocalConfig
    ? {}
    : new appConfig.AppConfigurationClient(process.env.APP_CONFIGURATION_CONNECTION_STRING);

const FEATURE_FLAG_PREFIX = '.appconfig.featureflag/';
/**
 * list of feature flag keys
 */
const FEATURE_FLAG_KEYS = {
    USE_CORE_API_V2: process.env['FF_CORE_API_V2_KEY'] || 'useCoreApiV2',
};

let DB_CONFIG = {};
let API_CONFIG = {};
let STORAGE_CONFIG = {};
let OIDC_CONFIG = {};
let CICD_CONFIG = {};
let FEATURE_FLAGS = {};

/**
 * It gets the database configuration settings from the Azure App Configuration service and returns them as a
 * JavaScript object
 * @returns An object with the keys and values of the database configuration settings.
 * @example
 * {
 *      "Database:MongoDB:ConnectionString": 'localhost',
 *      "DATABASE_POSTGRESQL_CONNECTION_STRING": 'adminService',
 * }
 */
const getDBConfig = async (context) => {
    if (Object.keys(DB_CONFIG).length > 0) {
        return DB_CONFIG;
    }

    if (isLocalConfig) {
        DB_CONFIG['DATABASE_MONGODB_CONNECTION_STRING'] =
            process.env['MONGO_CONNECTION_STRING'] || process.env['DATABASE_MONGODB_CONNECTION_STRING'];
        DB_CONFIG['DATABASE_POSTGRESQL_CONNECTION_STRING'] =
            process.env['POSTGRE_CONNECTION_STRING'] || process.env['DATABASE_POSTGRESQL_CONNECTION_STRING'];
        DB_CONFIG['DATABASE_MONGODB_CONNECTION_OPTIONS'] = process.env['DATABASE_MONGODB_CONNECTION_OPTIONS'] || '';
        DB_CONFIG['DATABASE_REDIS_HOSTNAME'] = process.env['DATABASE_REDIS_HOSTNAME'] || '127.0.0.1';
        DB_CONFIG['DATABASE_REDIS_PORT'] = process.env['DATABASE_REDIS_PORT'] || '6379';
        DB_CONFIG['DATABASE_REDIS_ACCESS_KEY'] = process.env['DATABASE_REDIS_ACCESS_KEY'] || '';
    } else {
        const settings = await configClient.listConfigurationSettings({
            keyFilter: 'DATABASE_*',
        });
        for await (const setting of settings) {
            DB_CONFIG[setting.key] = setting.value;
        }
    }

    DB_CONFIG['DATABASE_POSTGRESQL_POOL_MAX_CLIENT'] = process.env['DATABASE_POSTGRESQL_POOL_MAX_CLIENT'] || 200;
    DB_CONFIG['DATABASE_POSTGRESQL_POOL_IDLE_TIMEOUT_MILLIS'] =
        process.env['DATABASE_POSTGRESQL_POOL_IDLE_TIMEOUT_MILLIS'] || 10000;
    DB_CONFIG['DATABASE_POSTGRESQL_POOL_CONNECTION_TIMEOUT_MILLIS'] =
        process.env['DATABASE_POSTGRESQL_POOL_CONNECTION_TIMEOUT_MILLIS'] || 0;

    DB_CONFIG['DATABASE_POSTGRESQL_RETRY_COUNT'] = process.env['DATABASE_POSTGRESQL_RETRY_COUNT'] ?? 3;
    DB_CONFIG['DATABASE_POSTGRESQL_RETRY_WAIT'] = process.env['DATABASE_POSTGRESQL_RETRY_WAIT'] ?? 5;

    return DB_CONFIG;
};

/**
 * It takes the configuration settings from the Azure App Configuration service and returns them as a
 * JavaScript object
 * @returns An object with the API configuration settings.
 * @example
 * {
 *      "API_ALADIN_URL": 'xxxx',
 *      "API_ALADIN_HOST": 'xxxx',
 *      "API_CORE_URL": 'xxxx',
 * }
 */
const getAPIConfig = async (context) => {
    const { LOCAL_SETTINGS_CONFIG } = process.env;
    if (Object.keys(API_CONFIG).length > 0) {
        return API_CONFIG;
    }

    if (LOCAL_SETTINGS_CONFIG) {
        API_CONFIG['API_ALADIN_URL'] = process.env['API_ALADIN_URL'];
        API_CONFIG['API_ALADIN_HOST'] = process.env['API_ALADIN_HOST'] || 'docomo-mvno-api';
        API_CONFIG['API_CORE_URL'] = process.env['API_CORE_URL']; // old core endpoint
        API_CONFIG['API_CORE_INTERNAL_URL'] = process.env['API_CORE_INTERNAL_URL']; // new core endpoint (MVNO_CORE_API)
        API_CONFIG['API_SWIMMY_VLM_OAUTH_URL'] = process.env['API_SWIMMY_VLM_OAUTH_URL'];
        API_CONFIG['API_SWIMMY_VLM_SERVICES_URL'] = process.env['API_SWIMMY_VLM_SERVICES_URL'];
        API_CONFIG['API_PLUS_API_KEY'] = process.env['API_PLUS_API_KEY']; // plus api key (konnect.conf konnect.apiKey)

        // Swimmy API list
        API_CONFIG['API_SWIMMY_ALADIN_KEKKA_API_INFO'] = process.env['API_SWIMMY_ALADIN_KEKKA_API_INFO'];
        API_CONFIG['API_SWIMMY_MNP_TENSHUTSU_KANRYOU_TSUCHI_API_INFO'] =
            process.env['API_SWIMMY_MNP_TENSHUTSU_KANRYOU_TSUCHI_API_INFO'];
        API_CONFIG['API_SWIMMY_MOUSHIKOMI_HENKOU_API_INFO'] = process.env['API_SWIMMY_MOUSHIKOMI_HENKOU_API_INFO'];
        API_CONFIG['API_SWIMMY_KUROKA_KANRYOU_TSUCHI_API_INFO'] =
            process.env['API_SWIMMY_KUROKA_KANRYOU_TSUCHI_API_INFO'];
        API_CONFIG['API_SWIMMY_OTA_KEKKA_TSUCHI_API_INFO'] = process.env['API_SWIMMY_OTA_KEKKA_TSUCHI_API_INFO'];
        API_CONFIG['API_SWIMMY_KAISEN_OPTION_HENKOU_KEKKA_TSUCHI_API_INFO'] =
            process.env['API_SWIMMY_KAISEN_OPTION_HENKOU_KEKKA_TSUCHI_API_INFO'];
        API_CONFIG['API_SWIMMY_SIM_SAIHAKKOU_KEKKA_TSUCHI_API_INFO'] =
            process.env['API_SWIMMY_SIM_SAIHAKKOU_KEKKA_TSUCHI_API_INFO'];
        API_CONFIG['API_SWIMMY_ESIM_KEKKA_TSUCHI_V2_API_INFO'] =
            process.env['API_SWIMMY_ESIM_KEKKA_TSUCHI_V2_API_INFO'];
        API_CONFIG['API_SWIMMY_ESIM_KEKKA_TSUCHI_V3_API_INFO'] =
            process.env['API_SWIMMY_ESIM_KEKKA_TSUCHI_V3_API_INFO'];
    } else {
        const settings = await configClient.listConfigurationSettings({
            keyFilter: 'API_*',
        });

        for await (const setting of settings) {
            API_CONFIG[setting.key] = setting.value;
        }
    }

    return API_CONFIG;
};

const getStorageConfig = async () => {
    const { LOCAL_SETTINGS_CONFIG } = process.env;
    if (Object.keys(STORAGE_CONFIG).length > 0) {
        return STORAGE_CONFIG;
    }
    if (LOCAL_SETTINGS_CONFIG) {
        STORAGE_CONFIG['STORAGE_KAISEN_INFO_CONNECTION_STRING'] = process.env['STORAGE_KAISEN_INFO_CONNECTION_STRING'];
        STORAGE_CONFIG['STORAGE_KAISEN_INFO_CONTAINER_NAME'] = process.env['STORAGE_KAISEN_INFO_CONTAINER_NAME'];
        STORAGE_CONFIG['STORAGE_GUI_MANUAL_CONNECTION_STRING'] = process.env['STORAGE_GUI_MANUAL_CONNECTION_STRING'];
        STORAGE_CONFIG['STORAGE_GUI_MANUAL_CONTAINER_NAME'] = process.env['STORAGE_GUI_MANUAL_CONTAINER_NAME'];
        STORAGE_CONFIG['STORAGE_GUI_MANUAL_MANAGER_FOLDER'] = process.env['STORAGE_GUI_MANUAL_MANAGER_FOLDER'];
        STORAGE_CONFIG['STORAGE_GUI_MANUAL_TENANT_FOLDER'] = process.env['STORAGE_GUI_MANUAL_TENANT_FOLDER'];
        STORAGE_CONFIG['STORAGE_RYOUKIN_MESAI_CONTAINER_NAME'] = process.env['STORAGE_RYOUKIN_MESAI_CONTAINER_NAME'];
    } else {
        const settings = await configClient.listConfigurationSettings({
            keyFilter: 'STORAGE_*',
        });

        for await (const setting of settings) {
            STORAGE_CONFIG[setting.key] = setting.value;
        }
    }

    return STORAGE_CONFIG;
};

//static config, values rarely changed
const PORTAL_CONFIG = {
    DB_NAME: {
        ORDER_SERVICE: 'konnect',
        ADMIN_SERVICE: 'konnect',
        CORE_SERVICE: 'konnect',
        ALADIN_SERVICE: 'konnect',
        SIM_INVENTORY_SERVICE: 'konnect',
        SWIMMY_SERVICE: 'konnect',
        SWIMMY_VLM_0035_SERVICE: 'konnect',
        USER_SERVICE: 'konnect',
        DASHBOARD_SERVICE: 'konnect',
    },
    CORE_SERVICE: {
        OPF_TENANTID: 'OPF000',
        PASSWORD: 'OPF000',
        SENDER_SYSTEM_ID: '0001',
        RUN_WITH_MOCK: process.env.API_CORE_MOCK_MODE || false,
    },
    SWIMMY_SERVICE: {
        RUN_WITH_MOCK: process.env.API_SWIMMY_MOCK_MODE || false,
    },
    ALADIN_API: {
        HANBAITENCODE: '0330000000300',
        JIGYOSHACODE: '04',
        HANBAITENCODE_HLRHSS: '0330000000301',
        JIGYOSHACODE_HLRHSS: '42',

        POLLING: {
            MAX_CONNECTION: 7,
            RETRY_INTERVAL: process.env.ALADIN_POLLING_RETRY_INTERVAL || 90,
            SERVICE_BUS_CONNECTION_STRING: process.env.SERVICEBUS_CONNECTION_STRING,
            SERVICE_BUS_QUEUE_NAME: process.env.SERVICEBUS_QUEUE_ALADIN_API_REQUESTS,
            IKKATSU_OTA_HAISHI_DELAY: process.env.IKKATSU_OTA_HAISHI_DELAY ?? 300,
        },

        ERROR_MSG_LIST: {
            OTHER: 'その他',
            S3004_1B10: 'MNP予約がありません',
            S3006_1B10: 'MNP予約がありません',
            S3004_1B20: '予約有効期限切れです',
            S3006_1B20: '予約有効期限切れです',
            S3004_1D10: '移転元事業者の名義と一致しませんでした',
            S3006_1D10: '移転元事業者の名義と一致しませんでした',
            S3002: 'MNP予約番号エラー',
            I0038: '規定以外の漢字が含まれています',
            S3004_1C10: '移転元事業者の契約状態に問題がある為、MNPできません',
            S3006_1C10: '移転元事業者の契約状態に問題がある為、MNPできません',
            S3004_1E20: '移転元事業者への照会ができませんでした',
            S3006_1E20: '移転元事業者への照会ができませんでした',
            S3004_1A30: '他事業者にて解約受付中です',
            S3006_1A30: '他事業者にて解約受付中です',
            S3007_1B10: 'MNP予約がありません',
            S3007_1B20: '予約有効期限切れです',
            I0010: '黒化のできない状態になっている可能性があります',
            S3007_1C10: '移転元事業者の契約状態に問題がある為、MNPできません',
            S3010_1A30: '他事業者にて解約受付中です',
            S3010_1C10: '移転元事業者の契約状態に問題がある為、MNPできません',
            S1012: 'OTAROMのドコモUIMカード以外設定できません',
            S1012_1000: 'OTAROMのドコモUIMカード以外設定できません',
            W1003_1000: '排他等にてエラーが発生しています。時間が経ってから再申込してください',
            W1009: '排他等にてエラーが発生しています。時間が経ってから再申込してください',
            W1019: '排他等にてエラーが発生しています。時間が経ってから再申込してください',
            S1022: '黒化未実施の可能性があります',
            S1023: '既にMNP転出予約済の可能性があります',
            W1015_1000: '排他等にてエラーが発生しています。時間が経ってから再申込してください',
            W1008: '排他等にてエラーが発生しています。時間が経ってから再申込してください',
            S3006_1A40: 'ALADIN関連システムの一時的な障害です',
            S3007_1A40: 'ALADIN関連システムの一時的な障害です',
            S3004_DA90: '他事業社にて受付中の為、MNPできません',
            S3004_7A30: '他事業者にて解約受付中です',
            S3006_7A30: '他事業者にて解約受付中です',
            S3010_7A30: '他事業者にて解約受付中です',
            S3006_7A40: 'ALADIN関連システムの一時的な障害です',
            S3007_7A40: 'ALADIN関連システムの一時的な障害です',
            S3004_7B10: 'MNP予約がありません',
            S3006_7B10: 'MNP予約がありません',
            S3007_7B10: 'MNP予約がありません',
            S3004_7C10: '移転元事業者の契約状態に問題がある為、MNPできません',
            S3006_7C10: '移転元事業者の契約状態に問題がある為、MNPできません',
            S3007_7C10: '移転元事業者の契約状態に問題がある為、MNPできません',
            S3010_7C10: '移転元事業者の契約状態に問題がある為、MNPできません',
            S3004_7D10: '移転元事業者の名義と一致しませんでした',
            S3006_7D10: '移転元事業者の名義と一致しませんでした',
            S3005: 'ALADIN関連システムの一時的なエラーの可能性があります',
            S1004: 'SIM再発行できない回線状態です',
            S1020: '黒化実施中の可能性があります',
            I1004: '開通日にMNP転出予約はできません',
            I1016: 'ネットワーク契約変更できない回線状態です',
            S1051: '利用中断中に、利用中断が実行されました',
            S1052: '利用中に、利用再開が実行されました',
        },

        PRIORITY_LIST: {
            shinkiMoushikomiTsujouKaitsu: 50,
            shinkiMoushikomiHankuroRomSakusei: 50,
            shinkiMoushikomiHankuroRomKaitsu: 30,
            shinkiMoushikomiOTAROM: 10,
            mnpTennyuUketsukeTsujouKaitsu: 20,
            mnpTennyuUketsukeHankuroRomSakusei: 20,
            mnpTennyuUketsukeHankuroRomKaitsu: 30,
            mnpTennyuUketsukeOtaRomKaitsu: 10,
            kisetsuHenkouMnpYoyakuKaizokuRiyou: 40,
            kisetsuHenkouKaisenOption: 40,
            kisetsuHenkouAnshoBangou: 40,
            kisetsuHenkouRiyouChuudanSaikai: 40,
            kisetsuHenkouRiyouChuudan: 40,
            kisetsuHenkouRiyouSaikai: 40,
            kisetsuHenkouRiyouChuudanSokuji: 55, // STEP28: 利用中断(即時利用停止)
            kisetsuHenkouMnpYoyakuKaijo: 40,
            kisetsuHenkouSaihakkouShiroRom: 20,
            kisetsuHenkouSaihakkouOtaRomKaitsu: 20,
            kisetsuHenkouSaihakkouShiroRomMikaitsu: 20,
            kisetsuHenkouSaihakkouShiroRomMikaitsuToKaitsyu: 20,
            kaisenHaishi: 60,
            kensaku: 40,
            keiyakuHenkou: 40,
            //FULL MVNO CONFIG
            shinkiMoushiKomiDaiDaihyou: 70,
            shinkiMoushiKomiDaihyou: 70,
            shinkiMoushiKomiKoKaisen: 30,
            shinkiMoushiKomiKoKaisenYuusen: 50,
            kaisenHaishiSokuji: 50,
            //STEP 17: 一括開通・廃止
            ikkatsuOTAKaitsu: 100,
            ikkatsuOTAHaishi: 100,
            //STEP 23: eSIM
            eSIMIn: 20,
            eSIMMNPIn: 20,
            eSIMReissue: 20,
            ikkatsuShinkiMoushikomiHankuroRomKaitsu: 50,
            ikkatsuMnpTennyuUketsukeHankuroRomKaitsu: 50,
            ikkatsuKisetsuHenkouSaihakkouShiroRomMikaitsuToKaitsyu: 50,
        },
    },
    SWIMMY_API: {
        POLLING: {
            MAX_CONNECTION: process.env.SWIMMY_API_POLLING_MAX_CONNECTION || 10,
            SERVICE_BUS_CONNECTION_STRING: process.env.SERVICEBUS_CONNECTION_STRING,
            SERVICE_BUS_QUEUE_NAME: process.env.SERVICEBUS_QUEUE_SWIMMY_API_REQUESTS,
        },
    },

    CORE_SWIMMY_API: {
        POLLING: {
            SERVICE_BUS_CONNECTION_STRING: process.env.SERVICEBUS_CONNECTION_STRING,
            SERVICE_BUS_QUEUE_NAME: process.env.SERVICEBUS_QUEUE_CORE_SWIMMY_API_REQUESTS,
        },
    },

    /** MVNO2SwimmyVLMApi */
    SWIMMY_VLM_API: {
        RUN_WITH_MOCK: process.env.API_SWIMMY_VLM_MOCK_MODE || false,
        LINK_BY_FILE_TENANTS: ['CON000'],
        OAUTH: {
            EXPIRES_TIME_CHECK_BEFORE: 300, //Token切れるかどうかを確認する時間前 (s)
        },
        POLLING: {
            MAX_CONNECTION: 5,
            SERVICE_BUS_CONNECTION_STRING: process.env.SERVICEBUS_CONNECTION_STRING,
            SERVICE_BUS_QUEUE_NAME: process.env.SERVICEBUS_QUEUE_0035_API_REQUESTS,
            MONTHLY_MAINTENANCE_START: {
                //23:55
                HOUR: 23,
                MINUTE: 55,
            },
            MONTHLY_MAINTENANCE_END: {
                // 2:00
                HOUR: 2,
                MINUTE: 0,
            },
        },
        API_PRIORITY_LIST: {
            SHINKI_UKETSUKE_KURO: 50,
            SHINKI_UKETSUKE_HANKURO: 40,
            SHINKI_UKETSUKE_OTA: 10,
            MNP_TENNYUU_KURO: 20,
            MNP_TENNYUU_HANKURO: 20,
            MNP_TENNYUU_OTA: 10,
            KAISEN_HAISHI: 60,
            KAISEN_HAISHI_MNP_TENNYUU: 60,
            SAME_MVNE_HAISHI_MNP_TENNYUU_KURO: 20,
            SAME_MVNE_HAISHI_MNP_TENNYUU_HANKURO: 20,
            SAME_MVNE_HAISHI_MNP_TENNYUU_OTA: 10,
            DENWA_PLAN_HENKOU_0035: 50,
        },
    },

    /** MVNO2Swimmy */
    SWIMMY: {
        // need to update: constants/swimmyTransaction.js SWIMMY_REQUEST_TYPE_PRIORITY_KEY
        API_PRIORITY_LIST: {
            ALADIN_KEKKA_TSUCHI: 10,
            KUROKA: 30,
            KAISEN_RIYOU_CHUUDAN: 40,
            KAISEN_RIYOU_SAIKAI: 40,
            KAISEN_SUPPEND: 40,
            KAISEN_SUPPEND_KAIJO: 40,
            SIM_SAIHAKKO: 40,
            MNP_TENSHUTSU_KANRYOU_TSUCHI: 50,
        },
    },

    CALENDAR: {
        SERVICE_BUS_CONNECTION_STRING: process.env.SERVICEBUS_CONNECTION_STRING,
        SERVICE_BUS_QUEUE_NAME: process.env.SERVICEBUS_QUEUE_CALENDAR_MAINTENANCE_EVENTS,
    },

    GUI_MANUAL_FILENAME: {
        MANAGER: 'MVNOマニュアル_manager.zip',
        NORMAL: 'MVNOマニュアル.zip',
    },

    NOTIFICATION: {
        SERVICE_BUS_CONNECTION_STRING: process.env.SERVICEBUS_CONNECTION_STRING,
        SERVICE_BUS_QUEUE_NAME: process.env.SERVICEBUS_QUEUE_NOTIFICATION,
    },

    GAIBU_API_PROCESS_ID_PREFIX: process.env.GAIBU_API_PROCESS_ID_PREFIX || 'PF0',
    GAIBU_API_FULL_PROCESS_ID_PREFIX: process.env.GAIBU_API_FULL_PROCESS_ID_PREFIX || 'FM0',
    COCN_DAIHYOU_NNUMBER: 'N141096449',
    COCN_DAIHYOU_NNUMBER_LIST: ['N141096449', 'N141029466', 'N131030891', 'N121150564', 'N121007408'],
    COCN_DAIHYOU_NNUMBER_FOR_FULL_MVNO: ['N190173862'],
    KARITOUROKU_KAISEN_TSUIKA_UKETSUKE_FUKA_JIKAN: '00:00-04:00',
    MNP_UKETSUKE_FUKA_JIKAN: '20:30-09:00',
    ALADIN_API_REQUEST_UKETSUKE_FUKA_JIKAN: '20:58-09:00',
    API_USER_ID: 'API',
    KAISEN_INFO_CONTAINER: 'kaiseninfo',
    KAISEN_INFO_FILENAME_FORMAT: 'nnumber.zip',
    TEMPO_LOGIN_ID_PREFIX: '@',
    GAIBU_API_SEARCH_SO_RETURN_LIMIT: parseInt(process.env.GAIBU_API_SEARCH_SO_RETURN_LIMIT) || 10000,
    KAISEN_HAISHI_CANCEL_IS_OK_BEFORE_X_DAY: process.env.KAISEN_HAISHI_CANCEL_IS_OK_BEFORE_X_DAY || 1,
    KAISEN_HAISHI_ALADIN_IS_OK_AFTER_X_DAY: process.env.KAISEN_HAISHI_ALADIN_IS_OK_AFTER_X_DAY || 1,
    KAISEN_HAISHI_RESERVATION_ACCEPTABLE_PERIOD_DAYS:
        process.env.KAISEN_HAISHI_RESERVATION_ACCEPTABLE_PERIOD_DAYS || 60,
    KAISEN_HAISHI_RESERVATION_ACCEPTABLE_BEFORE_X_DAY:
        process.env.KAISEN_HAISHI_RESERVATION_ACCEPTABLE_BEFORE_X_DAY || 5,
    CIPHER_KEY: 'NsSE6srKAOJYdvU=',
    MASKING_CHAR: '*',

    KAISEN_HAISHI_ALADIN_RENKEI_ERROR_CODE: 'S1023', // STEP17.0 半黒SIM再発行を黒化せずに廃止する(ALADIN-API連携エラー)

    ALADIN_KOUJI_NG_REASON: 'その他(F1001_1000)', // STEP21でSwimmyにOTA結果通知のため、ALADIN工事NG理由
    ALADIN_KOUJI_NG_SPECIAL_REASON_FOR_NEW_ESIM: 'その他(F1001)',
    FORCE_UPDATE_COMPLETE_KOUTEI_SO: process.env.FORCE_UPDATE_COMPLETE_KOUTEI_SO || false,
    ALADIN_REQUEST_ORDER_PREFIX: 'RO',
    SEARCH_SOID_PREFIX: 'S',
    ZAIKO_KANRI_ID_PREFIX: 'ZM',
    OTA_KEKKA_NOTIFY_TENANT_LIST: ['CON000'],
    USER_PASSWORD_EXPIRATION_PERIOD_SECS: process.env.USER_PASSWORD_EXPIRATION_PERIOD_SECS || 7776000,
    HANKURO_ACTIVATION_PERIOD_EXCESS_MONTH_FOR_LITE_MVNO: 3, // STEP24.0: 半黒有効化期限超過「ライトMVNO回線：開通月が「現在月-3より以前」である」
    HANKURO_ACTIVATION_PERIOD_EXCESS_MONTH_FOR_FULL_MVNO: 12, // STEP24.0: 半黒有効化期限超過「フルMVNO回線：開通月が「現在月-12より以前」である」

    /** STEP24.0:回線一括処理 */
    KAISEN_IKKATSU: {
        /** 一括処理の回線数の上限 */
        MAX_LINES: process.env.KAISEN_IKKATSU_MAX_LINES ?? 500,
        /** 一括プラン変更の並行処理数の上限 */
        PLAN_CHANGE_RATE: process.env.KAISEN_IKKATSU_PLAN_CHANGE_RATE ?? 3,
    },
};

/**
 * It returns a promise that resolves to the value of the constant PORTAL_CONFIG
 * @returns the value of the variable PORTAL_CONFIG.
 */
const getPortalConfig = (context) => {
    return PORTAL_CONFIG;
};

/**
 * The function returns an object containing authentication configuration values retrieved from
 * environment variables.
 * @returns The function `getAuthenticationConfig` is returning an object with properties
 * `ACCESS_TOKEN_PRIVATE_KEY`, `ACCESS_TOKEN_EXPIRES_IN`, `REFRESH_TOKEN_PRIVATE_KEY`,
 * `REFRESH_TOKEN_EXPIRES_IN`, `PRE_LOGIN_TOKEN_PRIVATE_KEY`, and `PRE_LOGIN_TOKEN_EXPIRES_IN`. The
 * values of these properties are being retrieved from environment variables using `process.env`.
 */
const getAuthenticationConfig = () => {
    return {
        ACCESS_TOKEN_PRIVATE_KEY: process.env.ACCESS_TOKEN_PRIVATE_KEY,
        ACCESS_TOKEN_EXPIRES_IN: process.env.ACCESS_TOKEN_EXPIRES_IN,
        REFRESH_TOKEN_PRIVATE_KEY: process.env.REFRESH_TOKEN_PRIVATE_KEY,
        REFRESH_TOKEN_EXPIRES_IN: process.env.REFRESH_TOKEN_EXPIRES_IN,
        PRE_LOGIN_TOKEN_PRIVATE_KEY: process.env.PRE_LOGIN_TOKEN_PRIVATE_KEY,
        PRE_LOGIN_TOKEN_EXPIRES_IN: process.env.PRE_LOGIN_TOKEN_EXPIRES_IN,
    };
};

const getOIDCConfig = async () => {
    const { LOCAL_SETTINGS_CONFIG } = process.env;
    if (Object.keys(OIDC_CONFIG).length > 0) {
        return OIDC_CONFIG;
    }

    if (LOCAL_SETTINGS_CONFIG) {
        OIDC_CONFIG['OIDC_CLIENT_ID'] = process.env['OIDC_CLIENT_ID'];
        OIDC_CONFIG['OIDC_TENANT_ID'] = process.env['OIDC_TENANT_ID'];
        OIDC_CONFIG['OIDC_JWKS_URI'] = process.env['OIDC_JWKS_URI'];
        OIDC_CONFIG['OIDC_STATE'] = process.env['OIDC_STATE'];
        OIDC_CONFIG['OIDC_NONCE'] = process.env['OIDC_NONCE'];
        OIDC_CONFIG['OIDC_REDIRECT_URI'] = process.env['OIDC_REDIRECT_URI'];
        OIDC_CONFIG['OIDC_AUTHORIZE_URI'] = process.env['OIDC_AUTHORIZE_URI'];
    } else {
        const settings = await configClient.listConfigurationSettings({
            keyFilter: 'OIDC_*',
        });

        for await (const setting of settings) {
            OIDC_CONFIG[setting.key] = setting.value;
        }
    }

    return OIDC_CONFIG;
};

const getCICDConfig = async () => {
    const { LOCAL_SETTINGS_CONFIG } = process.env;
    if (Object.keys(CICD_CONFIG).length > 0) {
        return CICD_CONFIG;
    }
    if (LOCAL_SETTINGS_CONFIG) {
        CICD_CONFIG['CICD_GHA'] = process.env['CICD_GHA'];
    } else {
        const settings = await configClient.listConfigurationSettings({
            keyFilter: 'CICD_*',
        });

        for await (const setting of settings) {
            CICD_CONFIG[setting.key] = setting.value;
        }
    }

    return CICD_CONFIG;
};

/**
 * Get feature flag given the feature flag key
 * @param {object} context
 * @param {string} featureFlagKey without prefix (from `FEATURE_FLAG_KEYS`)
 * @returns {Promise<boolean>}
 */
const getFeatureFlag = async (context, featureFlagKey) => {
    if (isLocalConfig) {
        // return false for local development
        // context.log.warn('>> getFeatureFlag isLocalConfig');
        return false;
    }
    if (!featureFlagKey) {
        // throw new Error('Feature flag key is required');
        context.log.warn('getFeatureFlag: Feature flag key is required');
        return false;
    }
    const key = `${FEATURE_FLAG_PREFIX}${featureFlagKey}`;
    try {
        // check if the feature flag key exists in the static list and is not undefined or null
        if (FEATURE_FLAGS[key] !== undefined && FEATURE_FLAGS[key] !== null) {
            return FEATURE_FLAGS[key];
        } else {
            const config = await configClient.getConfigurationSetting({ key });
            const value = JSON.parse(config.value);
            FEATURE_FLAGS[key] = value.enabled;
            return value.enabled;
        }
    } catch (error) {
        context.log.warn(`getFeatureFlag: Error fetching feature flag (${key})`, error);
        // throw error;
        return false;
    }
};

module.exports = {
    FEATURE_FLAG_KEYS,
    getPortalConfig,
    getDBConfig,
    getAPIConfig,
    getAuthenticationConfig,
    getStorageConfig,
    getOIDCConfig,
    getCICDConfig,
    getFeatureFlag,
};
