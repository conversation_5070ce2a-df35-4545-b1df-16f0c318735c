# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Di<PERSON>atch update submodule CI

on:
    push:
        branches: ['develop']

jobs:
    dispatch-to-repos:
        strategy:
            matrix:
                repo: ['playnext-lab/MVNO_COMMON_SHARED', 'playnext-lab/MVNO_AUTHENTICATION_SERVICE', 'playnext-lab/MVNO_MICROSERVICES']
        runs-on: ubuntu-latest
        steps:
            - name: Repository Dispatch
              uses: peter-evans/repository-dispatch@v2
              with:
                  token: ${{ secrets.PAT }}
                  repository: ${{ matrix.repo }}
                  event-type: update-submodule-version
                  client-payload: '{"ref": "${{ github.ref }}", "sha": "${{ github.sha }}", "message": "${{ github.repository }}"}'