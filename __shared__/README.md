# MVNO COMMON SHARED - AZURE

## ローカル開発環境

### 環境を準備

```
node v20.18.3
npm 10.8.2
mongo 4.2
postgre 14
```

-   Nodejs: https://nodejs.org/en/
-   Mongo: https://www.mongodb.com/docs/manual/installation/
-   Azure Functions Core Tools: https://github.com/Azure/azure-functions-core-tools
-   Postgre: https://www.postgresql.org/download/

### プロジェクトのパッケージをインストール

```
npm install
```

### 単体テスト

→ 全てテスト実行:

```
npm run test
```

```
npm run test-unit-file --file=<file name>

ex:
npm run test-unit-file --file=validations.requestHeaeder.spec.js
```

## 利用パッケージ

-   [axios](https://github.com/axios/axios). Promise based HTTP client for the browser and node.js
-   [Day.js](https://day.js.org/en/). Parses, validates, manipulates, and displays dates and times
-   [joi](https://joi.dev/api/?v=17.6.0). Describe your data using a simple, intuitive, and readable language.
-   [mongoose](https://mongoosejs.com/). Elegant MongoDB object modeling for Node.js

-   [mocha](https://www.npmjs.com/package/mocha). Simple, flexible, fun JavaScript test framework for Node.js & The Browser.
-   [chai](https://github.com/axios/axios). Chai is an assertion library, similar to Node's built-in assert. It makes testing much easier by giving you lots of assertions you can run against your code.
-   [sinon](https://sinonjs.org/). Standalone test spies, stubs and mocks for JavaScript.

## フォルダ-構造

    .
    ├── constants                   # 定数値定義
    ├── database                    # データベース定義
    └── helpers                     # 独立したロジックでビジネスロジック
    └── models                      # Mongooseのモデル定義
    └── pgModels                    # Postgreの関数定義
    └── services                    # 外部のサービス定義
    └── utils                       # 独立したロジック
    └── validations                 # バリデーション定義
    ├── __tests__                       # Test files (alternatively `spec`)
    │   ├── hooks.js                    # mocha hook
    │   ├── defaultContext.js           # Azure Function Test context 定義
    │   ├── xxx.spect.js                # 各単体テストファイル
    │   ├──

