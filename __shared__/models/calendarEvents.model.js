const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { isNone } = require('../helpers/baseHelper');
const { MAINTENANCE_EVENT_TITLE_LIST } = require('../constants/maintenanceEventConstant');

const Schema = mongoose.Schema;

/** @typedef {import('mongoose').HydratedDocumentFromSchema<calendarEventSchema>} CalendarEventDocument */

const calendarEventSchema = new Schema({
    eventId: { type: String, required: true, trim: true },
    status: { type: String, required: true, trim: true },
    title: { type: String, trim: true },
    description: { type: String, trim: true },
    start: {},
    end: {},
    originalStartTime: {}, // for schedule change of recurring event
    location: { type: String, trim: true },
    owner: { type: String, trim: true },
    colorId: { type: String, trim: true },
    color: { type: String, trim: true },
    recurrence: { type: String, trim: true },
    recurringEventId: { type: String, trim: true },

    updatedAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },

    isMaintenanceEvent: { type: Boolean, default: false },
    // flag whether messages have been removed from ASB if event is cancelled (maintenance event only)
    // will be updated to false when new messages added to ASB
    isRemovedFromQueue: { type: Boolean, default: true },
    // sequence number for maintenance start
    startSequence: {
        low: { type: Number },
        high: { type: Number },
        unsigned: { type: Boolean },
    },
    // sequence number for maintenance end
    endSequence: {
        low: { type: Number },
        high: { type: Number },
        unsigned: { type: Boolean },
    },
});

/**
 * insert or update event
 * 旧メソッド：insertEvent
 * @param {object} context
 * @param {object} event
 * @param {string} event.eventId
 * @param {string} event.status
 * @param {string} [event.title]
 * @param {string} [event.description]
 * @param {object} [event.start]
 * @param {object} [event.end]
 * @param {object} [event.originalStartTime]
 * @param {string} [event.location]
 * @param {string} [event.owner]
 * @param {string} [event.colorId]
 * @param {string} [event.color]
 * @param {string} [event.recurrence]
 * @param {string} [event.recurringEventId]
 * @param {boolean} [event.isMaintenanceEvent]
 */
const upsertEvent = async (context, event) => {
    const nowSecs = dayjs().unix();
    const data = {};
    data.status = event.status;

    if (!isNone(event.start)) {
        const key = Object.keys(event.start)[0];
        data.start = { [key]: event.start[key] };
    }

    if (!isNone(event.end)) {
        const key = Object.keys(event.end)[0];
        data.end = { [key]: event.end[key] };
    }
    if (!isNone(event.originalStartTime)) {
        const key = Object.keys(event.originalStartTime)[0];
        data.originalStartTime = { [key]: event.originalStartTime[key] };
    }

    if (!isNone(event.title)) data.title = event.title;
    if (!isNone(event.owner)) data.owner = event.owner;
    if (!isNone(event.description)) data.description = event.description;
    if (!isNone(event.location)) data.location = event.location;
    if (!isNone(event.color)) data.color = event.color;
    if (!isNone(event.colorId)) data.colorId = event.colorId;
    if (!isNone(event.recurrence)) data.recurrence = event.recurrence;
    if (!isNone(event.recurringEventId)) data.recurringEventId = event.recurringEventId;
    if (!isNone(event.isMaintenanceEvent)) data.isMaintenanceEvent = event.isMaintenanceEvent;
    data.updatedAt = nowSecs;

    const result = await CalendarEventModel.updateOne(
        { eventId: event.eventId },
        { $set: data },
        { runValidators: true, upsert: true, rawResult: true }
    ).exec();

    return {
        isOk: result.acknowledged,
        isNew: result.acknowledged && result.upsertedCount !== 0,
        eventId: event.eventId,
    };
};

const getSequenceNumbers = async (context, eventId) => {
    const event = await CalendarEventModel.findOne({ eventId });
    if (event && !event.isRemovedFromQueue) {
        return { startSequence: event.startSequence, endSequence: event.endSequence };
    } else {
        return null;
    }
};

/**
 * Save sequence numbers from ASB
 * @param {object} context
 * @param {string} eventId
 * @param {{low: number, high: number, unsigned: boolean}} startSequence sequence number (Long) empty object
 * @param {{low: number, high: number, unsigned: boolean}} endSequence sequence number (Long) empty object
 */
const updateSequenceNumber = async (context, eventId, startSequence, endSequence) => {
    await CalendarEventModel.updateOne(
        { eventId },
        { $set: { startSequence, endSequence, isRemovedFromQueue: false } }
    ).exec();
};

/**
 * Set `isRemovedFromQueue` flag to true so that sequence numbers will not be returned
 * when calling `getSequenceNumbers` function
 * @param {object} context
 * @param {string} eventId
 */
const markRemovedFromQueue = async (context, eventId) => {
    return await CalendarEventModel.updateOne({ eventId }, { $set: { isRemovedFromQueue: true } }).exec();
};

/**
 * Get events which was maintenance event but changed to normal event (case title update)
 * and has been scheduled to ASB
 * @param {object} context
 * @param {Array<string>} eventIdList
 */
const findInvalidMaintenanceEvents = async (context, eventIdList) => {
    return await CalendarEventModel.find({ eventId: { $in: eventIdList }, isRemovedFromQueue: false }).exec();
};

/**
 *
 * @param {object} context
 * @param {object} criteria
 * @param {number} [criteria.fromDate]
 * @param {number} [criteria.toDate]
 * @param {boolean} criteria.isGetFullEvent
 */
const findEvents = async (context, { fromDate, toDate, isGetFullEvent }) => {
    context.log('CalendarEvents findEvents start');
    const query = {
        status: 'confirmed',
    };
    if (!isNone(fromDate) && !isNone(toDate)) {
        const startd = 'start.date',
            endd = 'end.date',
            startdt = 'start.dateTime',
            enddt = 'end.dateTime';
        const orConditions = [
            // case 1: fromDate <= start < toDate
            // { [startd]: { $gte: fromDate } },
            { [startd]: { $lt: toDate } },
            // { [startdt]: { $gte: fromDate } },
            { [startdt]: { $lt: toDate } },
            // case 2: fromDate <= end < toDate
            // { [endd]: { $gte: fromDate } },
            { [endd]: { $lt: toDate } },
            // { [enddt]: { $gte: fromDate } },
            { [enddt]: { $lt: toDate } },
            // case 3: fromDate <= start and end >= toDate
            { [startd]: { $lte: fromDate }, [endd]: { $gte: toDate } },
            { [startdt]: { $lte: fromDate }, [enddt]: { $gte: toDate } },

            // case 4: get all recurring event
            { recurrence: { $ne: null } },
        ];

        query.$or = orConditions;
        // STEP 13: MVNO_N_M-1700 - 【ドコモメンテON/OFF】googleカレンダーとの連携
        // Check is get full events / false -> exclude event title "[商用]ALADIN連携抑止"
        if (!isGetFullEvent) {
            query.title = { $nin: MAINTENANCE_EVENT_TITLE_LIST };
        }
    }
    // context.log(JSON.stringify(query));
    return await CalendarEventModel.find(query).exec();
};

calendarEventSchema.statics = {
    upsertEvent,
    getSequenceNumbers,
    updateSequenceNumber,
    markRemovedFromQueue,
    findInvalidMaintenanceEvents,
    findEvents,
};

const CalendarEventModel = mongoose.model('calendar_events', calendarEventSchema, 'calendar_events');
module.exports = CalendarEventModel;
