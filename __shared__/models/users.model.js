// user model (not inside tenant collection)
// const dayjs = reqire('dayjs');
const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { isNone } = require('../helpers/baseHelper');
const Schema = mongoose.Schema;
const { PERMIT_LEVEL, TWO_FA_STATUS } = require('../constants/userConstants');
const { getSha521Hash } = require('../helpers/cryptoHelper');

const SORT_BY_FIELDS = {
    userId: 'plusId',
    userName: 'name',
    tenantId: 'tenantId',
    roleName: 'roleId',
    twoFAStatus: 'twoFAStatus',
    accountStatus: 'accountLock',
    canDownloadMeisai: 'canDownloadMeisai',
};

const userSchema = new Schema({
    _id: { type: String },
    plusId: { type: String, required: true },
    domainId: { type: String },
    removed: { type: Boolean, required: true },
    name: { type: String },
    permitLevel: { type: Number },
    tenantId: { type: String },
    roleId: { type: Number },
    accountLock: { type: Boolean, required: true, default: false },
    accountLockMemo: { type: String },
    createdDate: { type: Number }, //, default: dayjs(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    updatedDate: { type: Number }, //, default: dayjs(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    lastLoginAt: { type: Number }, //, default: dayjs(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    passwdDigest: { type: String },
    passwdFailCount: { type: Number },
    passwdResetTime: { type: Number },
    passwdUpdateHistory: [String],

    //二段階認証ステータス //0: 未利用 1: 必須(未設定) 2: 必須(設定済)
    twoFAStatus: { type: Number },
    authCode: { type: String }, //二段階認証秘密鍵
    canDownloadMeisai: { type: Boolean, default: false }, //通信明細DL設定
});

/**
 * @param {string} password
 * @returns {boolean}
 */
userSchema.methods.isPasswordMatch = function (password) {
    return getSha521Hash(password) === this.passwdDigest;
};

/**
 * @param {string} password
 * @returns {boolean}
 */
userSchema.methods.isPasswordInHistory = function (password) {
    return this.passwdUpdateHistory.includes(getSha521Hash(password));
};

/**
 * @param {object} context
 * @param {object} params
 * @param {string} params.password
 * @returns {Promise<userSchema>}
 */
userSchema.methods.setNewPassword = async function (context, { password }) {
    context.log('UserModel instance setNewPassword START');

    const now = dayjs().unix();
    const hashPass = getSha521Hash(password);

    this.passwdDigest = hashPass;
    this.passwdFailCount = 0;
    this.passwdResetTime = now;
    this.accountLock = false;
    this.accountLockMemo = '';
    this.updatedDate = now;
    this.passwdUpdateHistory.push(hashPass);
    this.passwdUpdateHistory = this.passwdUpdateHistory.slice(-3);

    return await this.save();
};

/**
 * NOTE: This is a expanded version of the original function createUser.
 * @param {object} context
 * @param {object} params
 * @param {string} params.userId
 * @param {string} params.tenantId
 * @param {string} params.userName
 * @param {string} params.password
 * @param {number} params.roleId
 * @param {number} params.twoFAStatus
 * @param {boolean} params.canDownloadMeisai
 * @param {boolean} params.isAzureUser
 * @returns {Promise<userSchema>}
 * @throws {Error} user already exists
 */
const createUser = async (
    context,
    { userId, tenantId, userName, password, roleId, twoFAStatus, canDownloadMeisai, isAzureUser }
) => {
    context.log('UserModel createUser START');

    if (await findByPlusIdAndTenantId(context, { userId, tenantId })) {
        throw new Error('user already exists');
    }

    const now = dayjs().unix();

    let userData = {
        _id: `${tenantId}_${userId}`,
        plusId: userId,
        name: userName,
        roleId,
        tenantId,
        permitLevel: PERMIT_LEVEL.USER,
        removed: false,
        twoFAStatus,
        createdDate: now,
    };
    if (!isNone(canDownloadMeisai)) {
        userData.canDownloadMeisai = canDownloadMeisai;
    }

    //only set password and name when user is not a azure user
    if (!isAzureUser) {
        userData = {
            ...userData,
            passwdDigest: password,
            passwdFailCount: 0,
            passwdResetTime: now,
            accountLock: false,
            accountLockMemo: '',
            passwdUpdateHistory: [password],
        };
    }

    const user = await UserModel.create(userData);
    return user;
};

/**
 *
 * @param {object} context
 * @param {string} tenantId tenant id
 * @param {string?} userId find by plusId
 * @param {string?} userNo find by `_id`
 * @param {boolean} ignoreTenantCondition don't filter based on tenant (userNo `_id` is required)
 * @returns
 */
const findUserInfoByUserIDOrUserNo = async (context, tenantId, userId, userNo, ignoreTenantCondition = false) => {
    const q = { removed: false };
    if (!isNone(userId)) {
        q.plusId = userId;
        q.tenantId = tenantId;
    } else if (!ignoreTenantCondition) {
        q._id = userNo;
        q.tenantId = tenantId;
    } else {
        q._id = userNo;
    }
    return await UserModel.findOne(q);
};

/**
 *
 * @param {object} context
 * @param {string} tenantId
 * @param {Array<string>} userId
 * @param {Array<string>} userNo
 * @param {boolean} ignoreTenantCondition
 * @returns
 */
const findMultipleUserInfoByUserIDOrUserNo = async (
    context,
    tenantId,
    userId,
    userNo,
    ignoreTenantCondition = false
) => {
    const q = { removed: false };
    if (!isNone(userId)) {
        q.plusId = { $in: userId };
        q.tenantId = tenantId;
    } else if (!ignoreTenantCondition) {
        q._id = { $in: userNo };
        q.tenantId = tenantId;
    } else {
        q._id = { $in: userNo };
    }
    return await UserModel.find(q).exec();
};

/**
 * @param {object} context
 * @param {object} params
 * @param {string} params.userId
 * @param {string} params.tenantId
 * @returns {Promise<userSchema>}
 */
const findByPlusIdAndTenantId = async (context, { userId, tenantId }) => {
    context.log('UserModel findByPlusIdAndTenantId START');

    return await UserModel.findOne({ plusId: userId, tenantId, removed: false });
};

/**
 * @param {object} context
 * @param {object} params
 * @param {string} params.userId
 * @param {string} params.tenantId
 * @param {number} params.roleId
 * @returns {Promise<userSchema>}
 */
const findUserToUpdate = async (context, { userId, tenantId, roleId }) => {
    context.log('UserModel findUserToUpdate START');

    return await UserModel.findOne({ plusId: userId, tenantId, roleId, removed: false });
};

/**
 * Get user list
 * @param {object} context
 * @param {object} params
 * @param {number} params.userType
 * @param {string} params.userId
 * @param {string} params.userName
 * @param {string} params.tenantId
 * @param {number} params.limit
 * @param {number} params.skip
 * @param {string} params.sortBy
 * @param {boolean} params.isSortOrderAscending
 * @returns {Promise<{result: Array<userSchema>, resultCount: number}>}
 */
const searchUser = async (
    context,
    { userType, userId, userName, tenantId, limit, skip, sortBy, isSortOrderAscending }
) => {
    context.log('UserModel searchUser START');
    const filter = {
        removed: false,
        roleId: userType,
    };

    if (!isNone(userId)) {
        filter.plusId = { $regex: userId };
    }

    if (!isNone(userName)) {
        filter.name = { $regex: userName };
    }

    if (!isNone(tenantId)) {
        filter.tenantId = tenantId;
    }

    const sortAscendingInt = isSortOrderAscending ? 1 : -1;

    let sort = { createdDate: sortAscendingInt };
    if (Object.hasOwn(SORT_BY_FIELDS, sortBy)) {
        sort = { [SORT_BY_FIELDS[sortBy]]: sortAscendingInt };
    }

    const query = UserModel.find(filter);
    const resultCountQuery = query.clone().countDocuments();
    const resultQuery = query.sort(sort).skip(skip).limit(limit);
    const [resultCount, result] = await Promise.all([resultCountQuery, resultQuery]);
    return {
        result,
        resultCount,
    };
};

/**
 * @param {object} context
 * @param {object} params
 * @param {userSchema} params.user
 * @param {string} params.userName
 * @param {number} params.twoFAStatus
 * @param {boolean} params.canDownloadMeisai
 * @returns {Promise<userSchema>}
 */
const updateUser = async (context, { user, userName, twoFAStatus, canDownloadMeisai }) => {
    context.log('UserModel updateUser START');

    user.name = userName;
    if (!isNone(twoFAStatus)) {
        user.twoFAStatus = twoFAStatus;
    }
    if (!isNone(canDownloadMeisai)) {
        user.canDownloadMeisai = canDownloadMeisai;
    }
    user.updatedDate = dayjs().unix();

    return await user.save();
};

/**
 * @param {object} context
 * @param {object} params
 * @param {userSchema} params.user
 * @param {string} params.password
 * @returns {Promise<userSchema>}
 */
const setPassword = async (context, { user, password }) => {
    context.log('UserModel setPassword START');

    const now = dayjs().unix();

    user.passwdDigest = password;
    user.passwdFailCount = 0;
    user.passwdResetTime = now;
    user.accountLock = false;
    user.accountLockMemo = '';
    user.updatedDate = now;
    user.passwdUpdateHistory.push(password);
    user.passwdUpdateHistory = user.passwdUpdateHistory.slice(-3);

    return await user.save();
};

/**
 * @param {object} context
 * @param {object} params
 * @param {Array<string>} params.userIds
 * @returns {Promise<UpdateResult>}
 */
const enable2FAStatusByIds = async (context, { userIds }) => {
    context.log('UserModel enable2FAStatusByIds START');

    const now = dayjs().unix();
    return await UserModel.updateMany(
        { _id: { $in: userIds } },
        {
            $set: {
                twoFAStatus: TWO_FA_STATUS.NOT_SETUP_YET,
                updatedDate: now,
            },
        }
    );
};

/**
 * @param {object} context
 * @param {object} params
 * @param {Array<string>} params.userIds
 * @returns {Promise<UpdateResult>}
 */
const enableCanDownloadMeisaiByIds = async (context, { userIds }) => {
    context.log('UserModel enableCanDownloadMeisaiByIds START');

    const now = dayjs().unix();
    return await UserModel.updateMany(
        { _id: { $in: userIds } },
        {
            $set: {
                canDownloadMeisai: true,
                updatedDate: now,
            },
        }
    );
};

/**
 * @param {object} context
 * @param {object} params
 * @param {Array<string>} params.userIds
 * @returns {Promise<UpdateResult>}
 */
const deleteUsers = async (context, { userIds }) => {
    context.log('UserModel deleteUsers START');

    const now = dayjs().unix();
    return await UserModel.updateMany(
        { _id: { $in: userIds } },
        {
            $set: {
                removed: true,
                updatedDate: now,
            },
        }
    );
};

userSchema.statics = {
    createUser,
    findUserInfoByUserIDOrUserNo,
    findMultipleUserInfoByUserIDOrUserNo,
    findByPlusIdAndTenantId,
    findUserToUpdate,
    searchUser,
    updateUser,
    setPassword,
    enable2FAStatusByIds,
    enableCanDownloadMeisaiByIds,
    deleteUsers,
};
const UserModel = mongoose.model('users', userSchema, 'users');

module.exports = UserModel;
