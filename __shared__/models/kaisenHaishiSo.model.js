const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Constants
const { MVNO_SERVICES } = require('../constants/mvnoServicesConstants');
const { KOUTEI_IDS, SO_KIND_TYPE } = require('../constants/orderConstants');
const { getTimeInJST, getTimeAfterXDay } = require('../utils/datetimeUtils');

// utils
const { createPrefixTempoFind } = require('../utils/stringUtils');
const appConfig = require('../config/appConfig');
const { removeNullValue, isNone } = require('../helpers/baseHelper');
const portalConfig = appConfig.getPortalConfig();

const kaisenHaishiSoSchema = new Schema({
    so_id: { type: String, required: true, unique: true },
    tenantId: { type: String, trim: true, required: true },
    tempoId: { type: String, trim: true, required: true },
    uketsukeDateTime: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    reservedDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    lastKoutei: {
        kouteiId: { type: Number, trim: true, required: true },
        timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
        userId: { type: String, trim: true },
        ngReason: { type: String, trim: true },
    },
    kouteis: [
        {
            _id: false,
            kouteiId: { type: Number, trim: true, required: true },
            timestamp: { type: Number },
            userId: { type: String, trim: true },
            ngReason: { type: String, trim: true },
        },
    ],
    kaisenNo: { type: String, trim: true, required: true },
    aladinIsOK: { type: Boolean },
    soKind: { type: String, trim: true, required: true },
    //STEP14: add flag isFullMVNO
    isFM: { type: Boolean },
    //TODO: define more fields here
    apiProcessID: { type: String, trim: true },
    contractType: { type: String, trim: true },
    createUserId: { type: String, trim: true },
    voicePlanId: { type: String, trim: true },

    isQueued: { type: Boolean },
    isSwimmyVLMCancelCreated: { type: Boolean },
    // STEP28:即時利用停止有無
    immediate: { type: Boolean },
    // STEP28:即時利用停止のALADIN連携結果フラグ (null/undefined:未連携, false:失敗, true:成功)
    immediateSuspensionSuccessful: { type: Boolean },
});

kaisenHaishiSoSchema.virtual('isComeFromShinMVNO').get(function () {
    // STEP 14.0 : + F,G // STEP 22: + H,J for 5G
    const regex = /^S[A-J]/;
    return regex.test(this.contractType ?? '');
});
kaisenHaishiSoSchema.virtual('koutei').get(function () {
    if (this.lastKoutei) {
        return this.lastKoutei.kouteiId;
    } else {
        return KOUTEI_IDS.UKETSUKE_CHECK_NG;
    }
});
kaisenHaishiSoSchema.virtual('ngReason').get(function () {
    if (this.lastKoutei) {
        return this.lastKoutei.ngReason ?? '';
    } else {
        return '';
    }
});

/**
 *
 * @param {object} context - context object
 * @param {number} searchTypeo - searchType
 * @param {string} searchValueo - searchValue
 * @param {string[]} tenantIdo - tenantId
 * @param {string[]} tempoIdo - tempoId
 * @param {number[]} processIdo - processId
 * @param {number} fromDateo - receptDateBeginKey
 * @param {number} toDateo - receptDateEndKey
 * @param {number} limito - limit
 * @param {string[]} prefixTempoIdo - prefixTempoId
 * @returns {array.<kaisenHaishiSO>} - kaisenHaishiSO list
 */
const searchSO2 = async (
    context,
    { searchTypeo, searchValueo, tenantIdo, tempoIdo, processIdo, fromDateo, toDateo, limito, prefixTempoIdo }
) => {
    const soList = await searchSO(
        context,
        searchTypeo,
        searchValueo,
        tenantIdo,
        tempoIdo,
        processIdo,
        fromDateo,
        toDateo,
        null,
        limito,
        null,
        true,
        MVNO_SERVICES.ALL,
        prefixTempoIdo
    );

    return soList?.cursor;
};

/**
 *
 * @param {object} context - context object
 * @param {number} searchTypeo - searchType
 * @param {string} searchValueo - searchValue
 * @param {string[]} tenantIdo - tenantId
 * @param {string[]} tempoIdo - tempoId
 * @param {number[]} processIdo - processId
 * @param {number} fromDateo - receptDateBeginKey
 * @param {number} toDateo - receptDateEndKey
 * @param {number} skipo - skip
 * @param {number} limito - limit
 * @param {string} sortByo - sortBy
 * @param {boolean} isSortOrderAscending - sorting order up or down
 * @param {number} service - liteMVNO or fullMVNO or both
 * @param {string[]} prefixTempoIdo - prefixTempoId
 * @returns {array.<kaisenHaishiSO>, number} - kaisenHaishiSO list and count
 */
const searchSO = async (
    context,
    searchTypeo,
    searchValueo,
    tenantIdo,
    tempoIdo,
    processIdo,
    fromDateo,
    toDateo,
    skipo,
    limito,
    sortByo,
    isSortOrderAscending,
    service,
    prefixTempoIdo
) => {
    context.log('KaisenHaishiSoModel searchSO START');
    let query = {};
    var searchBySoIdFlag = false;
    var searchByKaisenNoFlag = false;
    var searchByTenantFlag = false;
    switch (searchTypeo) {
        case 0:
            // so_idで検索する
            query.so_id = { $regex: searchValueo, $options: 'i' };
            searchBySoIdFlag = true;
            break;
        case 1:
            // 回線番号で検索する
            query.kaisenNo = { $regex: searchValueo, $options: 'i' };
            searchByKaisenNoFlag = true;
            break;
        default:
            break;
    }

    if (Array.isArray(tenantIdo) && tenantIdo.length > 0) {
        query.tenantId = { $in: tenantIdo };
        searchByTenantFlag = true;
    }

    if (Array.isArray(tempoIdo) && tempoIdo.length > 0) {
        query.tempoId = { $in: tempoIdo };
    }

    //部分一致かつ複数検索: https://mobilus.backlog.jp/view/MVNO_N_M-2123#comment-131332889
    if (Array.isArray(prefixTempoIdo) && prefixTempoIdo.length > 0) {
        const prefixRegex = createPrefixTempoFind(prefixTempoIdo);
        query.tempoId = { $regex: prefixRegex };
    }

    if (Array.isArray(processIdo) && processIdo.length > 0) {
        query['lastKoutei.kouteiId'] = { $in: processIdo };
    }

    if (fromDateo && !toDateo) {
        query.uketsukeDateTime = { $gte: fromDateo };
    } else if (!fromDateo && toDateo) {
        query.uketsukeDateTime = { $lt: toDateo };
    } else if (fromDateo && toDateo) {
        query.uketsukeDateTime = { $gte: fromDateo, $lt: toDateo };
    }

    //STEP 14 FULL MVNO
    switch (service) {
        case MVNO_SERVICES.LITE:
            query.isFM = { $ne: true };
            break;
        case MVNO_SERVICES.FULL:
            query.isFM = true;
            break;
        default:
            break;
    }

    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = {};
    if (sortByo) {
        switch (sortByo) {
            case 'so_id':
                sort.so_id = sortAscendingInt;
                break;
            case 'tenant_id':
                sort.tenantId = sortAscendingInt;
                break;
            case 'tempo_id':
                sort.tempoId = sortAscendingInt;
                break;
            case 'uketsuke_date':
                sort.uketsukeDateTime = sortAscendingInt;
                break;
            case 'reserved_date':
                sort.reservedDate = sortAscendingInt;
                break;
            case 'process_id':
                sort['lastKoutei.kouteiId'] = sortAscendingInt;
                break;
            case 'kaisen_no':
                sort.kaisenNo = sortAscendingInt;
                break;
            case 'so_kind':
                sort.aladinIsOK = sortAscendingInt;
                break;
            case 'additional_so':
                sort.aladinIsOK = sortAscendingInt * -1;
                break;
            default:
                sort.uketsukeDateTime = sortAscendingInt;
                break;
        }
    } else {
        sort.uketsukeDateTime = sortAscendingInt;
    }

    let hintObj = {},
        doc = {};
    if (searchBySoIdFlag && !searchByTenantFlag) {
        hintObj.so_id = 1;
    } else if (searchBySoIdFlag && searchByTenantFlag) {
        hintObj.tenantId = 1;
        hintObj.so_id = 1;
    } else if (searchByKaisenNoFlag && !searchByTenantFlag) {
        hintObj.kaisenNo = 1;
    } else if (searchByKaisenNoFlag && searchByTenantFlag) {
        hintObj.tenantId = 1;
        hintObj.kaisenNo = 1;
    } else {
        hintObj;
    }

    const count = await KaisenHaishiSoModel.find(query).count();

    switch (true) {
        case count === 0:
        case count === 1:
            if (Object.keys(hintObj).length === 0) {
                doc.cursor = await KaisenHaishiSoModel.find(query);
            } else {
                doc.cursor = await KaisenHaishiSoModel.find(query).hint(hintObj);
            }
            doc.count = count;
            break;
        case count <= limito:
            if (Object.keys(hintObj).length === 0) {
                doc.cursor = await KaisenHaishiSoModel.find(query).sort(sort);
            } else {
                doc.cursor = await KaisenHaishiSoModel.find(query).hint(hintObj).sort(sort);
            }
            doc.count = count;
            break;
        default:
            if (limito && !skipo) {
                if (Object.keys(hintObj).length === 0) {
                    doc.cursor = await KaisenHaishiSoModel.find(query).sort(sort).limit(limito);
                } else {
                    doc.cursor = await KaisenHaishiSoModel.find(query).hint(hintObj).sort(sort).limit(limito);
                }
            } else if (!limito && skipo) {
                if (Object.keys(hintObj).length === 0) {
                    doc.cursor = await KaisenHaishiSoModel.find(query).sort(sort).skip(skipo);
                } else {
                    doc.cursor = await KaisenHaishiSoModel.find(query).hint(hintObj).sort(sort).skip(skipo);
                }
            } else {
                if (Object.keys(hintObj).length === 0) {
                    doc.cursor = await KaisenHaishiSoModel.find(query).sort(sort).limit(limito).skip(skipo);
                } else {
                    doc.cursor = await KaisenHaishiSoModel.find(query)
                        .hint(hintObj)
                        .sort(sort)
                        .limit(limito)
                        .skip(skipo);
                }
            }
            doc.count = count;
            break;
    }
    return doc;
};

/**
 * 工程履歴追加
 * @param {string} soId
 * @param {number} kouteiId
 * @param {string} userId
 * @param {string} [ngReason]
 * @param {boolean} [isNeedUpdateLastKoutei] lastKouteiも更新する (default to true)
 */
const addKouteiInfo = async (soId, kouteiId, userId, ngReason, isNeedUpdateLastKoutei = true) => {
    const newKoutei = {
        kouteiId,
        timestamp: getTimeInJST().unix(),
        userId,
    };
    if (ngReason) {
        newKoutei.ngReason = ngReason;
    }
    const query = {
        $push: { kouteis: newKoutei },
    };
    if (isNeedUpdateLastKoutei) {
        query.$set = { lastKoutei: newKoutei };
    }
    return await KaisenHaishiSoModel.updateOne({ so_id: soId }, query).exec();
};

/**
 * Create new kaisenHaishiSo for ShinMVNO
 * @param {string} createUserId
 * @param {string} tenantId
 * @param {string} tempoId
 * @param {string} koutei
 * @param {string} kaisenNo
 * @param {number} reserveDate unix seconds
 * @param {string} contractType
 * @param {boolean} isFM
 * @param {boolean} [immediate] optional immediate flag
 * @returns {string} soId
 */
const createKaisenHaishi = async (
    createUserId,
    tenantId,
    tempoId,
    koutei,
    kaisenNo,
    reserveDate,
    contractType,
    isFM,
    immediate = false
) => {
    const now = getTimeInJST().unix();

    const kouteiObj = { kouteiId: koutei, timestamp: now, userId: createUserId };

    const kaisenHaishiObj = {
        createUserId,
        tenantId,
        tempoId,
        kaisenNo,
        aladinIsOk: false,
        kouteis: [kouteiObj],
        lastKoutei: kouteiObj,
        reservedDate: reserveDate,
        uketsukeDateTime: now,
        contractType,
        soKind: SO_KIND_TYPE.KAISEN_HAISHI_SERVICE_ORDER,
        immediate,
    };

    let newSO = new KaisenHaishiSoModel(kaisenHaishiObj);
    newSO.isFM = isFM; // step 14.0
    if (isFM) {
        newSO.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + newSO._id;
    } else {
        newSO.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSO._id;
    }
    newSO = await newSO.save();
    return newSO.so_id;
};

/**
 * Create new kaisenHaishiSo for normal MVNO
 * @param {string} createUserId
 * @param {string} tenantId
 * @param {string} tempoId
 * @param {string} koutei
 * @param {string} kaisenNo
 * @param {number} reserveDate unix seconds
 * @param {boolean} isFM
 * @param {string} voicePlanId
 * @param {boolean} [immediate] optional immediate flag
 * @returns {string} soId
 */
const createKaisenHaishiNormalMvno = async (
    createUserId,
    tenantId,
    tempoId,
    koutei,
    kaisenNo,
    reserveDate,
    isFM,
    voicePlanId,
    immediate = false
) => {
    const now = getTimeInJST().unix();

    const kouteiObj = { kouteiId: koutei, timestamp: now, userId: createUserId };

    const kaisenHaishiObj = {
        createUserId,
        tenantId,
        tempoId,
        kaisenNo,
        aladinIsOk: false,
        kouteis: [kouteiObj],
        lastKoutei: kouteiObj,
        reservedDate: +reserveDate,
        uketsukeDateTime: now,
        soKind: SO_KIND_TYPE.KAISEN_HAISHI_SERVICE_ORDER,
        immediate,
    };

    if (voicePlanId && voicePlanId !== '') {
        kaisenHaishiObj.voicePlanId = voicePlanId;
    }

    let newSO = new KaisenHaishiSoModel(kaisenHaishiObj);
    newSO.isFM = isFM; // step 14.0
    if (isFM) {
        newSO.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + newSO._id;
    } else {
        newSO.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSO._id;
    }
    newSO = await newSO.save();
    return newSO.so_id;
};

/**
 * Update API Process ID for Kaisen Haishi SO
 * @param {string} so_id soId to be updated
 * @param {string} apiProcessID API Process ID value
 * @returns
 */
const updateApiProcessID = async (so_id, apiProcessID) => {
    return await KaisenHaishiSoModel.updateOne({ so_id }, { $set: { apiProcessID } }).exec();
};

/**
 *
 * @param {object} context - context object
 * @param {string} soId - soId
 * @param {string} tenantId - tenantId
 * @returns {object|null} - kaisen haishi SO object or null
 */
const findBySOIdAndTenantId = async (context, { soId, tenantId }) => {
    context.log('KaisenHaishiSoModel findBySOIdAndTenantId START');
    if (!soId) throw new Error('soId is required');
    if (!tenantId) throw new Error('tenantId is required');

    const query = {
        so_id: soId,
        tenantId,
    };
    return await KaisenHaishiSoModel.findOne(query).exec();
};

/**
 *
 * @param {object} context - context object
 * @param {string} soId - soId
 */
const findBySOID = async (context, soId) => {
    context.log('KaisenHaishiSoModel findBySOID START');
    if (!soId) throw new Error('soId is required');

    const query = {
        so_id: soId,
    };
    return await KaisenHaishiSoModel.findOne(query).exec();
};

/**
 * find documents by so_id and update kouteis info
 * 旧メソッド: addKouteiInfo
 * @param  {object} context - API context
 * @param  {object} data
 * @param  {string} data.so_id                - for find query
 * @param  {string} data.updatedUserId
 * @param  {KouteiInfo} data.koutei           - order processing status
 * @return {MNPTennyuSchema|null}                 - if successful return updatedObject else return null
 */
const updateKouteiInfo = async (context, { so_id, updatedUserId, koutei }) => {
    if (!so_id) throw new Error('so_id is required');
    if (!updatedUserId) throw new Error('updatedUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');

    koutei.userId = updatedUserId;
    koutei.timestamp ||= dayjs().unix();

    const lastKoutei = removeNullValue(koutei);

    return await KaisenHaishiSoModel.findOneAndUpdate(
        { so_id },
        {
            $set: { lastKoutei },
            $push: { kouteis: lastKoutei },
        },
        { runValidators: true, context: 'query', new: true }
    ).exec();
};

/**
 * SO取得(SO_ID複数 or 条件)
 * @param {object} context
 * @param {string[]} soIds
 * @returns {Promise<[kaisenHaishiSoSchema]|[]>}
 */
const findSOBySOIDs = async (context, soIds) => {
    context.log('KaisenHaishiSoModel findSOBySOIDs START');
    if (isNone(soIds)) {
        return [];
    } else {
        return await KaisenHaishiSoModel.find({ so_id: { $in: soIds } })
            .sort({ _id: -1 })
            .exec();
    }
};

const setAladinIsOK = async (so_id, aladinIsOK) => {
    await KaisenHaishiSoModel.updateOne(
        { so_id },
        { $set: { aladinIsOK: aladinIsOK } },
        { runValidators: true }
    ).exec();
};

/**
 * 旧メソッド: updateKouteiInfo
 * @param {object} context
 * @param {string} soId
 * @param {number} oldKouteiId
 * @param {number} kouteiId
 * @param {string} userId
 * @param {string} [ngReason]
 * @param {boolean} isNeedUpdateLast
 */
const updateKouteiInfoKaisenHaishi = async (
    context,
    soId,
    oldKouteiId,
    kouteiId,
    userId,
    ngReason,
    isNeedUpdateLast = false
) => {
    const oldSo = await KaisenHaishiSoModel.findOne({ so_id: soId }).exec();
    const kouteiListFromDB = oldSo.kouteis;
    const kouteiList = [];

    const nowSecs = dayjs().unix();

    kouteiListFromDB.forEach((koutei) => {
        const kouteiObj = {
            kouteiId: koutei.kouteiId,
            timestamp: koutei.timestamp,
            userId: koutei.userId,
            ngReason: koutei.ngReason,
        };
        delete kouteiObj._id;
        if (koutei.kouteiId === oldKouteiId) {
            kouteiObj.kouteiId = kouteiId;
            kouteiObj.timestamp = nowSecs;
            if (ngReason !== undefined || ngReason !== null) {
                kouteiObj.ngReason = ngReason;
            }
        }
        kouteiList.push(kouteiObj);
    });

    const updateData = {
        kouteis: kouteiList,
    };

    if (isNeedUpdateLast) {
        const lastKoutei = {
            kouteiId,
            timestamp: nowSecs,
            userId,
            ngReason,
        };
        updateData.kouteis.push(lastKoutei);
        updateData.lastKoutei = lastKoutei;
    }
    await KaisenHaishiSoModel.updateOne({ so_id: soId }, { $set: updateData }, { runValidators: true }).exec();
};

const updateKaishiSOForResending = async (so_id) => {
    const now = dayjs().unix();
    const oldSo = await KaisenHaishiSoModel.findOne({ so_id }).exec();
    const kouteiListFromDB = oldSo.kouteis;
    const kouteiListDBList = [];
    let lastKouteiDBObj = {};

    kouteiListFromDB.forEach((koutei) => {
        if (koutei.kouteiId !== KOUTEI_IDS.ALADIN_KOUJI_TMP) {
            const kouteiObj = {};
            if (koutei.kouteiId === KOUTEI_IDS.ALADIN_KOUJI_NG) {
                kouteiObj.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_TMP;
                kouteiObj.timestamp = now;
                kouteiObj.userId = '';
            } else {
                if (koutei.kouteiId === KOUTEI_IDS.KANRYOU) {
                    lastKouteiDBObj.kouteiId = koutei.kouteiId;
                    lastKouteiDBObj.timestamp = koutei.timestamp;
                    lastKouteiDBObj.userId = koutei.userId;
                }
                kouteiObj.kouteiId = koutei.kouteiId;
                kouteiObj.timestamp = koutei.timestamp;
                kouteiObj.userId = koutei.userId;
            }
            kouteiListDBList.push(kouteiObj);
        }
    });
    const updateData = { isQueued: false, aladinIsOK: true, kouteis: kouteiListDBList };
    if (oldSo.koutei === KOUTEI_IDS.ALADIN_KOUJI_NG && !isNone(lastKouteiDBObj)) {
        updateData.lastKoutei = lastKouteiDBObj;
    }
    await KaisenHaishiSoModel.updateOne({ so_id }, updateData, { runValidators: true }).exec();
};

/**
 * ALADIN-API連携抑制のCSVデータ更新
 * @param  {object} context - context
 * @param  {Map<string, any>} result - Map<string, any>
 */
const updateResultBySOIDs = async (context, result) => {
    context.log('KaisenHaishiSoModel updateResultBySOIDs START');
    result.forEach(async (v, k) => {
        const lastKouteiObj = {
            kouteiId: KOUTEI_IDS.ALADIN_KOUJI_NG,
            timestamp: v.hosei_date,
            userId: '',
            ngReason: 'その他',
        };
        await KaisenHaishiSoModel.updateOne(
            { so_id: { $eq: k } },
            {
                $set: { lastKoutei: lastKouteiObj, aladinIsOK: false },
                $push: { kouteis: lastKouteiObj },
            },
            { runValidators: true }
        ).exec();
    });
};

/**
 *
 * @param {object} context
 * @param {number} processId
 * @param {number} reservedDate
 * @param {string[]} tenantIds
 * @returns {Promise<[kaisenHaishiSoSchema]|[]>} [kaisenHaishiSoSchema] or []
 */
const searchKaishiSOForReservedDate = async (context, processId, reservedDate, tenantIds) => {
    context.log('KaisenHaishiSoModel searchKaishiSOForReservedDate START');

    let query = {};
    query.tenantId = { $in: tenantIds };
    query['lastKoutei.kouteiId'] = processId;
    query.reservedDate = reservedDate;
    query.isSwimmyVLMCancelCreated = { $ne: true };
    const sort = { _id: 1 };
    return await KaisenHaishiSoModel.find(query).sort(sort).exec();
};

/**
 *
 * @param {object} context
 * @param {string} aladinSOId
 */
const markDoneSwimmyVLMRequest = async (context, aladinSOId) => {
    context.log('KaisenHaishiSoModel markDoneSwimmyVLMRequest START ', aladinSOId);
    if (isNone(aladinSOId)) throw new Error('aladinSOId is required');

    await KaisenHaishiSoModel.updateOne(
        { so_id: aladinSOId },
        { $set: { isSwimmyVLMCancelCreated: true } },
        { runValidators: true }
    ).exec();
};

const getAllSOCanExecuteAladinKouji = async () => {
    // MVNO_M-1336 毎時00分の処理で回線廃止のAladinApiLogを作るが二重に作られないようにする
    const result = await KaisenHaishiSoModel.find({ aladinIsOK: true, isQueued: { $ne: true } });

    const currentTime = dayjs();
    const kaisenHaishiAladinIsOkAfterXday = portalConfig.KAISEN_HAISHI_ALADIN_IS_OK_AFTER_X_DAY;

    return result.filter((row) =>
        currentTime.isAfter(getTimeAfterXDay(dayjs.unix(row.reservedDate), kaisenHaishiAladinIsOkAfterXday))
    );
};

const setIsQueuedToTrue = async (soId) => {
    await KaisenHaishiSoModel.updateOne({ so_id: soId }, { $set: { isQueued: true } });
};

/**
 *
 * @param {object} context
 * @param {Array<string>} soIds
 * @returns {Promise<number>} modifiedCount
 */
const addCompleteKouteiInfoToSOsSpecifiedSOIDs = async (context, soIds) => {
    if (isNone(soIds) || !Array.isArray(soIds)) return 0;
    const now = dayjs().unix();
    const kouteis = [
        {
            kouteiId: KOUTEI_IDS.ALADIN_KOUJI_TMP,
            timestamp: now,
            userId: '',
        },
        {
            kouteiId: KOUTEI_IDS.KANRYOU,
            timestamp: now,
            userId: '',
        },
    ];
    const rawResult = await KaisenHaishiSoModel.updateMany(
        { so_id: { $in: soIds } },
        {
            $push: { kouteis: { $each: kouteis } },
            $set: {
                lastKoutei: kouteis.at(-1),
                aladinIsOK: true,
            },
        },
        { rawResult: true }
    );
    return rawResult.modifiedCount;
};

/**
 * Update `immediateSuspensionSuccessful` flag for the specified SO
 * @param {object} context
 * @param {string} soId
 * @param {boolean} flag
 */
const updateImmediateSuspensionFlag = async (context, soId, flag) => {
    context.log('KaisenHaishiSoModel.immediateSuspensionSuccessful:', soId, flag);
    await KaisenHaishiSoModel.updateOne({ so_id: soId }, { $set: { immediateSuspensionSuccessful: flag } });
};

//statics
kaisenHaishiSoSchema.statics = {
    searchSO,
    searchSO2,
    addKouteiInfo,
    createKaisenHaishi,
    createKaisenHaishiNormalMvno,
    updateApiProcessID,
    findBySOIdAndTenantId,
    findBySOID,
    updateKouteiInfo,
    findSOBySOIDs,
    setAladinIsOK,
    updateKouteiInfoKaisenHaishi,
    updateKaishiSOForResending,
    updateResultBySOIDs,
    searchKaishiSOForReservedDate,
    markDoneSwimmyVLMRequest,
    getAllSOCanExecuteAladinKouji,
    setIsQueuedToTrue,
    addCompleteKouteiInfoToSOsSpecifiedSOIDs,
    updateImmediateSuspensionFlag,
};

const KaisenHaishiSoModel = mongoose.model('kaisen_haishi_so', kaisenHaishiSoSchema, 'kaisen_haishi_so');

module.exports = KaisenHaishiSoModel;
