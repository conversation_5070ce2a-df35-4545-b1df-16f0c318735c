const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { SO_KIND_TYPE } = require('../constants/orderConstants');
const { SWIMMY_SO_SEARCH_TYPES, SWIMMY_SO_SORT_BY_FIELDS } = require('../constants/swimmySoConstants');
const { removeNullValue, isNone } = require('../helpers/baseHelper');

const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const Schema = mongoose.Schema;

const swimmySoSchema = new Schema({
    so_id: {
        type: String,
        trim: true,
        required: true,
        unique: true,
    },
    denwaBango: {
        // kaisenNo
        type: String,
        trim: true,
        required: true,
    },
    tenantId: {
        type: String,
        trim: true,
        required: true,
    },
    soKind: {
        type: String,
        trim: true,
        required: true,
    },

    contractType: { type: String, trim: true },
    daihyouBango: { type: String, trim: true },
    IMSI: { type: String, trim: true },

    uketsukeDateTime: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    lastUpdateAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    lastUpdateUserId: { type: String, trim: true, required: true },

    kouteis: [
        {
            _id: false,
            kouteiId: { type: Number, required: true },
            timestamp: { type: Number },
            userId: { type: String, trim: true },
            ngReason: { type: String, trim: true },
        },
    ],
    lastKoutei: {
        kouteiId: { type: Number, required: true },
        timestamp: { type: Number },
        userId: { type: String, trim: true },
        ngReason: { type: String, trim: true },
    },

    yuubinBango: { type: String, trim: true },
    jigyoushoCode: { type: String, trim: true },
    kigyouCode: { type: String, trim: true },
    kigyouDaihyou: { type: String, trim: true },
    kigyouJuushoCode: { type: String, trim: true },
    kigyouJuushoHosoku: { type: String, trim: true },
    kigyouJuushoKataGaki: { type: String, trim: true },
    kigyouMei: { type: String, trim: true },
    kigyouMeiKana: { type: String, trim: true },

    renrakuShamei: { type: String, trim: true },
    renrakuDenwa: { type: String, trim: true },
    tokuteiHojinDenwa: { type: String, trim: true },

    seikyuuSakiKana: { type: String, trim: true },
    seikyuuSakiMei: { type: String, trim: true },
    seikyuuSakiDenwa: { type: String, trim: true },
    seikyuuSakiYuubinBango: { type: String, trim: true },
    seikyuuSakiJuushoCode: { type: String, trim: true },
    seikyuuSakiJuushoHosoku: { type: String, trim: true },
    seikyuuSakiJuushoKatagaki: { type: String, trim: true },

    ryoukinPlan: { type: String, trim: true },
    sousaService: { type: String, trim: true },

    //TODO: define more fields here
});

swimmySoSchema.virtual('soKindTitleStr').get(function () {
    switch (this.soKind) {
        case SO_KIND_TYPE.SWIMMY_DAIDAIHYOU_KAISEN_SERVICE_ORDER:
        case SO_KIND_TYPE.SWIMMY_DAIHYOU_KAISEN_SERVICE_ORDER:
        case SO_KIND_TYPE.SWIMMY_KO_KAISEN_TSUUJOU_SERVICE_ORDER:
        case SO_KIND_TYPE.SWIMMY_KO_KAISEN_YUUSEN_SERVICE_ORDER:
        case SO_KIND_TYPE.SWIMMY_KAISEN_HAISHI_SOKUJI_SERVICE_ORDER:
            return this.soKind;
        default:
            return SO_KIND_TYPE.SWIMMY_FUMEI_SERVICE_ORDER;
    }
});

const createDaidaihyoKaisenSO = async ({
    createUserId,
    tenantId,
    koutei,
    contractType,
    denwabango,
    imsi,
    kigyocode,
    jigyoshocode,
    kigyomeikana,
    kigyoumei,
    kigyodaihyo,
    yubimbango,
    kigyojushocode,
    // tokuteihojindenwa,
    kigyohosokujusho,
    kigyojushokatagaki,
    renrakushamei,
    renrakudenwa,
    seikyusakikana,
    seikyusakimei,
    seikyusakidenwa,
    seikyusakiyubimbango,
    seikyusakijushocode,
    seikyusakihosokujusho,
    seikyusakijushokatagaki,
    ryokinplan,
    sousaService,
}) => {
    const nowSec = dayjs().unix();

    const lastKouteiObj = {
        kouteiId: koutei,
        userId: createUserId,
        timestamp: nowSec,
    };

    const kouteiObj = {
        kouteiId: koutei,
        userId: createUserId,
        timestamp: nowSec,
    };

    const params = {
        tenantId: tenantId,
        lastUpdateAt: nowSec,
        lastUpdateUserId: createUserId,
        uketsukeDateTime: nowSec,
        soKind: SO_KIND_TYPE.SWIMMY_DAIDAIHYOU_KAISEN_SERVICE_ORDER,
        lastKoutei: lastKouteiObj,
        kouteis: [kouteiObj],
        contractType: contractType,
        denwaBango: denwabango,
        IMSI: imsi,
        kigyouCode: kigyocode,
        jigyoushoCode: jigyoshocode,
        kigyouMeiKana: kigyomeikana,
        kigyouMei: kigyoumei,
        kigyouDaihyou: kigyodaihyo,
        yuubinBango: yubimbango,
        kigyouJuushoCode: kigyojushocode,
        kigyouJuushoHosoku: kigyohosokujusho,
        // tokuteiHojinDenwa: tokuteihojindenwa,
        kigyouJuushoKataGaki: kigyojushokatagaki,
        renrakuShamei: renrakushamei,
        renrakuDenwa: renrakudenwa,
        seikyuuSakiKana: seikyusakikana,
        seikyuuSakiMei: seikyusakimei,
        seikyuuSakiDenwa: seikyusakidenwa,
        seikyuuSakiYuubinBango: seikyusakiyubimbango,
        seikyuuSakiJuushoCode: seikyusakijushocode,
        seikyuuSakiJuushoHosoku: seikyusakihosokujusho,
        seikyuuSakiJuushoKatagaki: seikyusakijushokatagaki,
        ryoukinPlan: ryokinplan,
        sousaService: sousaService,
    };

    let so = new SwimmySoModel(params);
    so.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + so._id;
    const savedSO = await so.save();
    return savedSO.so_id;
};

const createDaihyoKaisenSO = async ({
    createUserId,
    tenantId,
    koutei,
    contractType,
    denwabango,
    imsi,
    tokuteihojinDenwa,
    ryokinplan,
}) => {
    const nowSec = dayjs().unix();

    const lastKouteiObj = {
        kouteiId: koutei,
        userId: createUserId,
        timestamp: nowSec,
    };

    const kouteiObj = {
        kouteiId: koutei,
        userId: createUserId,
        timestamp: nowSec,
    };

    const params = {
        tenantId: tenantId,
        soKind: SO_KIND_TYPE.SWIMMY_DAIHYOU_KAISEN_SERVICE_ORDER,
        lastUpdateAt: nowSec,
        lastUpdateUserId: createUserId,
        uketsukeDateTime: nowSec,
        lastKoutei: lastKouteiObj,
        kouteis: [kouteiObj],
        contractType: contractType,
        denwaBango: denwabango,
        IMSI: imsi,
        tokuteiHojinDenwa: tokuteihojinDenwa,
        ryoukinPlan: ryokinplan,
    };

    let so = new SwimmySoModel(params);
    so.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + so._id;
    const savedSO = await so.save();
    return savedSO.so_id;
};

const createKoKaisenTsujyouSO = async ({
    createUserId,
    tenantId,
    koutei,
    contractType,
    daihyoubango,
    denwabango,
    imsi,
    ryokinplan,
}) => {
    const nowSec = dayjs().unix();

    const lastKouteiObj = {
        kouteiId: koutei,
        userId: createUserId,
        timestamp: nowSec,
    };

    const kouteiObj = {
        kouteiId: koutei,
        userId: createUserId,
        timestamp: nowSec,
    };

    const params = {
        tenantId: tenantId,
        soKind: SO_KIND_TYPE.SWIMMY_KO_KAISEN_TSUUJOU_SERVICE_ORDER,
        lastUpdateAt: nowSec,
        lastUpdateUserId: createUserId,
        uketsukeDateTime: nowSec,
        lastKoutei: lastKouteiObj,
        kouteis: [kouteiObj],
        contractType: contractType,
        daihyouBango: daihyoubango,
        denwaBango: denwabango,
        IMSI: imsi,
        ryoukinPlan: ryokinplan,
    };

    let so = new SwimmySoModel(params);
    so.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + so._id;
    const savedSO = await so.save();
    return savedSO.so_id;
};

const createKoKaisenYuusenServiceOrder = async ({
    createUserId,
    tenantId,
    koutei,
    contractType,
    daihyoubango,
    denwabango,
    imsi,
    ryokinplan,
}) => {
    const nowSec = dayjs().unix();

    const lastKouteiObj = {
        kouteiId: koutei,
        userId: createUserId,
        timestamp: nowSec,
    };

    const kouteiObj = {
        kouteiId: koutei,
        userId: createUserId,
        timestamp: nowSec,
    };

    const params = {
        tenantId: tenantId,
        soKind: SO_KIND_TYPE.SWIMMY_KO_KAISEN_YUUSEN_SERVICE_ORDER,
        contractType: contractType,
        lastUpdateAt: nowSec,
        lastUpdateUserId: createUserId,
        uketsukeDateTime: nowSec,
        lastKoutei: lastKouteiObj,
        kouteis: [kouteiObj],
        daihyouBango: daihyoubango,
        denwaBango: denwabango,
        IMSI: imsi,
        ryoukinPlan: ryokinplan,
    };

    let so = new SwimmySoModel(params);
    so.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + so._id;
    const savedSO = await so.save();
    return savedSO.so_id;
};

/**
 * 回線廃止即時SO
 * @param {object} context
 * @param {object} data
 * @param {string} data.createUserId
 * @param {string} data.tenantId
 * @param {number} data.kouteiId
 * @param {string} data.contractType
 * @param {string} data.denwaBango
 * @param {string} [data.ngReason]
 * @returns {Promise<string>} SO-ID
 */
const createKaisenHaishiSokujiSO = async (
    context,
    { createUserId, tenantId, kouteiId, contractType, denwaBango, ngReason }
) => {
    context.log.info('SwimmySo.createKaisenHaishiSokujiSO start');
    const soKind = SO_KIND_TYPE.SWIMMY_KAISEN_HAISHI_SOKUJI_SERVICE_ORDER;

    const now = dayjs().unix();
    const koutei = {
        kouteiId,
        timestamp: now,
        userId: createUserId,
        ngReason,
    };
    const data = {
        tenantId,
        soKind,
        contractType,
        lastUpdateAt: now,
        lastUpdateUserId: createUserId,
        uketsukeDateTime: now,
        denwaBango,
        kouteis: [koutei],
        lastKoutei: koutei,
    };

    const newSO = new SwimmySoModel(removeNullValue(data));
    newSO.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + newSO._id;
    const savedDoc = await newSO.save();
    return savedDoc.so_id;
};

/**
 * Update koutei and lastKoutei of Swimmy service order
 * @param {object} context
 * @param {object} data
 * @param {string} data.so_id
 * @param {string} data.updatedUserId
 * @param {object} data.koutei
 * @param {number} data.koutei.kouteiId
 * @param {string} [data.koutei.ngReason]
 */
const updateKouteiInfo = async (context, { so_id, updatedUserId, koutei }) => {
    if (!so_id) throw new Error('so_id is required');
    if (!updatedUserId) throw new Error('updatedUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');

    const nowSecs = dayjs().unix();
    koutei.userId = updatedUserId;
    koutei.timestamp ||= nowSecs;

    const lastKoutei = removeNullValue(koutei);
    return await SwimmySoModel.findOneAndUpdate(
        { so_id },
        {
            $set: { lastKoutei, lastUpdateAt: nowSecs, lastUpdateUserId: updatedUserId },
            $push: { kouteis: lastKoutei },
        },
        { runValidators: true, context: 'query', new: true }
    ).exec();
};

const findBySOId = async (context, soId) => {
    context.log('SwimmySoModel findBySOId START');
    if (!soId) throw new Error('soId is required');

    const query = { so_id: soId };
    return await SwimmySoModel.findOne(query).exec();
};

/**
 *
 * @param {object} context
 * @param {number} searchType
 * @param {string} searchValue
 * @param {string} tenantId
 * @param {number[]} processIds
 * @param {number} fromDate
 * @param {number} toDate
 * @param {string[]} soKind
 * @param {string[]} soIds
 * @param {boolean} isAllSelected
 * @returns {Promise<[swimmySoSchema]|[]>} [swimmySoSchema] or []
 */
const findBySoIds = async (
    context,
    { searchType, searchValue, tenantId, processIds, fromDate, toDate, soKind, soIds, isAllSelected }
) => {
    context.log('SwimmySoModel findBySoIds START');
    let ands = [];
    switch (searchType) {
        case SWIMMY_SO_SEARCH_TYPES.SO_ID: // so_idで検索する
            ands.push({ so_id: { $regex: searchValue, $options: 'i' } });
            break;
        case SWIMMY_SO_SEARCH_TYPES.PHONE_NUMBER: // 電話番号で検索する
            ands.push({ denwaBango: { $regex: searchValue, $options: 'i' } });
            break;
        case SWIMMY_SO_SEARCH_TYPES.IMSI: // IMSIで検索する
            ands.push({ IMSI: { $regex: searchValue, $options: 'i' } });
            break;
        default: // 何もしない
            break;
    }
    if (!isNone(tenantId)) {
        ands.push({ tenantId: tenantId });
    }
    if (!isNone(processIds)) {
        ands.push({ 'lastKoutei.kouteiId': { $in: processIds } });
    }
    if (!isNone(soKind)) {
        ands.push({ soKind: { $in: soKind } });
    }
    if (!isNone(fromDate)) {
        ands.push({ uketsukeDateTime: { $gte: fromDate } });
    }
    if (!isNone(toDate)) {
        ands.push({ uketsukeDateTime: { $lt: toDate } });
    }
    if (!isNone(soIds)) {
        if (isAllSelected === true) {
            ands.push({ so_id: { $nin: soIds } });
        } else {
            ands.push({ so_id: { $in: soIds } });
        }
    }
    let query = {};
    if (ands.length > 0) {
        query = { $and: ands };
    }
    const sort = { _id: 1 };
    return await SwimmySoModel.find(query).sort(sort).exec();
};

/**
 * Search Swimmy SO.
 * @param context - The context object.
 * @param {object} params - The search parameters.
 * @param {number} params.searchType - The search type.
 * @param {string} params.searchValue - The search value.
 * @param {string} params.tenantId - The tenant ID.
 * @param {number[]} params.processIds - The process IDs.
 * @param {number} params.fromDate - The from date.
 * @param {number} params.toDate - The to date.
 * @param {string[]} params.soKind - The SO kind.
 * @param {number} params.skip - The number of documents to skip.
 * @param {number} params.limit - The maximum number of documents to return.
 * @param {string} params.sortBy - The field to sort by.
 * @param {boolean} params.isSortOrderAscending - The sort order.
 * @returns {Promise<{result: [swimmySoSchema], resultCount: number}>} An object containing the search results and
 * the total count of matching documents. The result is an array of swimmySoSchema.
 */
const searchSwimmySO = async (
    context,
    {
        searchType,
        searchValue,
        tenantId,
        processIds,
        fromDate,
        toDate,
        soKind,
        skip,
        limit,
        sortBy,
        isSortOrderAscending,
    }
) => {
    context.log('SwimmySoModel searchSwimmySO START');

    const ands = [];
    switch (searchType) {
        case SWIMMY_SO_SEARCH_TYPES.SO_ID: // so_idで検索する
            ands.push({ so_id: { $regex: searchValue, $options: 'i' } });
            break;
        case SWIMMY_SO_SEARCH_TYPES.PHONE_NUMBER: // 電話番号で検索する
            ands.push({ denwaBango: { $regex: searchValue, $options: 'i' } });
            break;
        case SWIMMY_SO_SEARCH_TYPES.IMSI: // IMSIで検索する
            ands.push({ IMSI: { $regex: searchValue, $options: 'i' } });
            break;
        default: // 何もしない
            break;
    }
    if (!isNone(tenantId)) {
        ands.push({ tenantId: tenantId });
    }
    if (!isNone(processIds)) {
        ands.push({ 'lastKoutei.kouteiId': { $in: processIds } });
    }
    if (!isNone(soKind)) {
        ands.push({ soKind: { $in: soKind } });
    }
    if (!isNone(fromDate)) {
        ands.push({ uketsukeDateTime: { $gte: fromDate } });
    }
    if (!isNone(toDate)) {
        ands.push({ uketsukeDateTime: { $lt: toDate } });
    }

    let filter = {};
    if (ands.length > 0) {
        filter = { $and: ands };
    }

    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = { _id: sortAscendingInt };
    if (Object.hasOwn(SWIMMY_SO_SORT_BY_FIELDS, sortBy)) {
        sort = { [SWIMMY_SO_SORT_BY_FIELDS[sortBy]]: sortAscendingInt };
    }

    const query = SwimmySoModel.find(filter).sort(sort);
    const resultCountQuery = query.clone().countDocuments();
    const resultQuery = query.skip(skip).limit(limit);
    const [resultCount, result] = await Promise.all([resultCountQuery, resultQuery]);
    return {
        result,
        resultCount,
    };
};

//statics
swimmySoSchema.statics = {
    createDaidaihyoKaisenSO,
    createDaihyoKaisenSO,
    createKoKaisenTsujyouSO,
    createKoKaisenYuusenServiceOrder,
    createKaisenHaishiSokujiSO,
    updateKouteiInfo,
    findBySOId,
    findBySoIds,
    searchSwimmySO,
};

const SwimmySoModel = mongoose.model('swimmy_so', swimmySoSchema, 'swimmy_so');

module.exports = SwimmySoModel;
