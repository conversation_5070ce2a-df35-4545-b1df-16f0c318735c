const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { isNone } = require('../helpers/baseHelper');
const Schema = mongoose.Schema;
const ServiceOrderCountModel = require('./serviceOrderCount.model');
const baseHelper = require('../../__shared__/helpers/baseHelper');

const customerInfoSchema = new Schema({
    unique_id: { type: String, trim: true },
    lastUpdateUserId: { type: String, trim: true, required: true },
    tenantId: { type: String, trim: true, required: true },
    nNumber: { type: String, trim: true, default: '' },
    seqStr: { type: String, trim: true },

    updatedAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    removed: { type: Boolean, default: false },

    addresseePostcode: { type: String, trim: true, required: true },
    addresseePrefectures: { type: String, trim: true, required: true },
    addresseeCities: { type: String, trim: true, required: true },
    addresseeOoaza: { type: String, trim: true, default: '' },
    addresseeAza: { type: String, trim: true, default: '' },
    addresseeBlock: { type: String, trim: true, default: '' },
    addresseeMansion: { type: String, trim: true, default: '' },
    addresseeName: { type: String, trim: true, required: true },
    addresseeSectionName: { type: String, trim: true, default: '' },
    addresseeRepresentativeName: { type: String, trim: true, default: '' },
    addresseePhoneNumber: { type: String, trim: true, required: true },
});

/**
 * find CustomerInfo by deliveryAddressId(seqStr)
 * @param {string} seqStr
 * @param {string} [nNumber]
 * @param {boolean} [exceptDeleted]
 * @returns {customerInfoSchema|null} customerInfo object or null
 */
const findBySeqStr = async (seqStr, nNumber = null, exceptDeleted = false) => {
    if (!seqStr) throw new Error('seqStr is required');

    let conditions = { seqStr };
    if (nNumber) conditions.nNumber = nNumber;
    if (exceptDeleted) conditions.removed = { $ne: true };

    return await CustomerInfoModel.findOne(conditions).exec();
};

/**
 * @param {string?} tenantId
 * @param {string?} nNumber
 */
const findCustomerInfo = async (tenantId, nNumber) => {
    const query = {
        removed: { $ne: true },
    };
    if (!isNone(tenantId)) query.tenantId = tenantId;
    if (!isNone(nNumber)) query.nNumber = nNumber;
    return await CustomerInfoModel.find(query).sort({ _id: 1 }).lean().exec();
};

/**
 *
 */
const createCustomerInfo = async ({
    createUserId,
    tenantId,
    nNumber,
    addresseePrefectures,
    addresseePostcode,
    addresseeCities,
    addresseeOoaza,
    addresseeAza,
    addresseeBlock,
    addresseeMansion,
    addresseeName,
    addresseeSectionName,
    addresseeRepresentativeName,
    addresseePhoneNumber,
}) => {
    let nextSeq = 0;
    // 万が一、連番取得に失敗したら正常な連番が取れるまで実施する
    do {
        nextSeq = await ServiceOrderCountModel.getNextSeq('配送先', tenantId, nNumber);
    } while (nextSeq === 0);
    const seqStr = baseHelper.formatSeqStr(tenantId, nextSeq);

    const info = new CustomerInfoModel({
        seqStr,
        lastUpdateUserId: createUserId,
        tenantId: tenantId,
        nNumber: nNumber,
        addresseePrefectures: addresseePrefectures,
        addresseePostcode: addresseePostcode,
        addresseeCities: addresseeCities,
        addresseeOoaza: addresseeOoaza,
        addresseeAza: addresseeAza,
        addresseeBlock: addresseeBlock,
        addresseeMansion: addresseeMansion,
        addresseeName: addresseeName,
        addresseeSectionName: addresseeSectionName,
        addresseeRepresentativeName: addresseeRepresentativeName,
        addresseePhoneNumber: addresseePhoneNumber,
    });
    await info.save();
    return seqStr;
};

/**
 *
 */
const updateCustomerInfo = async ({
    createUserId,
    tenantId,
    nNumber,
    seqStr,
    addresseePrefectures,
    addresseePostcode,
    addresseeCities,
    addresseeOoaza,
    addresseeAza,
    addresseeBlock,
    addresseeMansion,
    addresseeName,
    addresseeSectionName,
    addresseeRepresentativeName,
    addresseePhoneNumber,
}) => {
    const query = {
        nNumber: nNumber,
        seqStr: seqStr,
    };

    await CustomerInfoModel.updateOne(query, {
        $set: {
            lastUpdateUserId: createUserId,
            tenantId: tenantId,
            nNumber: nNumber,
            addresseePrefectures: addresseePrefectures,
            addresseePostcode: addresseePostcode,
            addresseeCities: addresseeCities,
            addresseeOoaza: addresseeOoaza,
            addresseeAza: addresseeAza,
            addresseeBlock: addresseeBlock,
            addresseeMansion: addresseeMansion,
            addresseeName: addresseeName,
            addresseeSectionName: addresseeSectionName,
            addresseeRepresentativeName: addresseeRepresentativeName,
            addresseePhoneNumber: addresseePhoneNumber,
        },
    }).exec();

    return seqStr;
};

/**
 * @param {string} nNumber
 * @param {string} seqStr
 */
const deleteCustomerInfo = async (nNumber, seqStr) => {
    const query = {
        nNumber: nNumber,
        seqStr: seqStr,
    };
    await CustomerInfoModel.updateOne(query, { $set: { removed: true } }).exec();
};

//statics
customerInfoSchema.statics = {
    findBySeqStr,
    findCustomerInfo,
    createCustomerInfo,
    updateCustomerInfo,
    deleteCustomerInfo,
};

const CustomerInfoModel = mongoose.model('customer_info', customerInfoSchema, 'customer_info');

module.exports = CustomerInfoModel;
