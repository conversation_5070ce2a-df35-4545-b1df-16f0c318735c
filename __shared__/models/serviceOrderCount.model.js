const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const serviceOrderCountSchema = new Schema({
    orderType: { type: String, trim: true, required: true },
    tenantId: { type: String, trim: true, required: true },
    nNumber: { type: String, trim: true, required: true },
    seq: { type: Number, required: true },
    createdDateTime: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
});

/**
 * get coresponded seq value
 * (if doc not found increase seq by 1 then insert/update)
 * @param {string} orderType
 * @param {string} tenantId
 * @param {string} nNumber
 * @param {string} dateTimeStr - date format YYYYMMDD
 * @returns {number|0} seq value or 0
 */
const getNextSeq = async (orderType, tenantId, nNumber) => {
    if (!orderType) throw new Error('orderType is required');
    if (!tenantId) throw new Error('tenantId is required');
    if (!nNumber) throw new Error('nNumber is required');

    const res = await ServiceOrderCountModel.findOneAndUpdate(
        { orderType, tenantId, nNumber },
        { $inc: { seq: 1 } },
        { runValidators: true, context: 'query', upsert: true, new: true }
    ).exec();

    return res?.seq || 0;
};

//statics
serviceOrderCountSchema.statics = {
    getNextSeq,
};

const ServiceOrderCountModel = mongoose.model('service_order_count', serviceOrderCountSchema, 'service_order_count');

module.exports = ServiceOrderCountModel;
