const _ = require('lodash');
const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const TransactionLogCountModel = require('./transactionLogCount.model');
const {
    removeNullValue,
    formatSeqString,
    // getPriority,
    isNone,
    getAladinTransactionPriority,
} = require('../../helpers/baseHelper');
const {
    getAladinAPIRequestFukajikanInfo,
    canRequestOrderTypeBeExecutedWithinUketsukeFukaJikan,
} = require('../../helpers/receptionTimeHelper');

const {
    TRANSACTION_STATUS,
    ORDER_TYPE,
    SUCCESS_SHORI_KEKKA_CODES,
    FM_ORDER_TYPE_LIST,
    LOWEST_API_PRIORITY,
} = require('../../constants/aladinTransactions');
const { MVNO_SERVICES } = require('../../constants/mvnoServicesConstants');
const { encodeShiftJIS } = require('../../utils/stringUtils');
const { decryptTextToEncodeShiftJIS, decryptText } = require('../../utils/symmetricCipher');

/** @typedef {mongoose.HydratedDocumentFromSchema<aladinApiLogSchema} AladinApiLogDocument */

/**

 * AladinApiLog schema

 * @constructor AladinApiLog

 */
const aladinApiLogSchema = new Schema({
    lastUpdateUserId: { type: String, trim: true, required: true, default: 'API' },
    tenantId: { type: String, trim: true, required: true },
    tempoId: { type: String, trim: true },
    daihyouBango: { type: String, trim: true },
    denwabango: { type: String, trim: true },
    transactionType: { type: String, trim: true },
    createdDateTime: { type: Number, default: dayjs().unix() },
    transactionId: { type: String, trim: true, required: true, unique: true },
    requestOrderId: { type: String, trim: true, required: true },
    requestOrderType: { type: String, trim: true },
    requestParam: { type: Object },
    responseParam: { type: Object },
    requestRegisterParam: { type: Object },
    requestRegisterList: [{ type: Object }],
    /**
     * 0 : 未登録依頼, 1 : 登録依頼OK, 2 : 登録依頼NG, 3 : 登録要求OK, 4 : 登録要求NG, 90 : 不明なエラー
     */
    status: { type: Number, default: 0 },
    priority: { type: Number },

    basePriority: { type: Number }, // priority from ALADIN_API.PRIORITY_LIST (not multiplied by 1.4)
    isLowPriority: { type: Boolean, index: true }, // tenant priority type of this order before being processed

    jikkouchuFlg: { type: Boolean, default: false },
    isFM: { type: Boolean },
    canExecWithinFukaJikan: { type: Boolean },
    getTourokuIraiResponseAt: {
        type: Number,
        set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()),
    },
    getTourokuYoukyuuResponseAt: {
        type: Number,
        set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()),
    },
    // for 502 error
    isRetried: { type: Boolean, default: false },
    retryAt: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    //===new property step 22: Azure====
    sbMessageId: { type: String, trim: true },
    sbSequenceNumber: { type: String },
    // step 25: 再送
    resendAt: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()), index: true },
});

/**
 * Create aladin transaction
 * @param {object} context - azure context
 * @param {object} transaction - transaction object
 * @param {string} transaction.tenantId
 * @param {string} transaction.tempoId
 * @param {string} transaction.daihyouBango
 * @param {string} transaction.transactionType
 * @param {string} transaction.requestOrderType
 * @param {string} transaction.requestOrderId
 * @param {object} transaction.requestParam
 * @param {number} transaction.status
 * @param {string} transaction.createdUserId
 * @param {number} [transaction.retryAt] set to delay processing the transaction (unix timestamp)
 * @param {string} sbMessageId
 * @param {string} sbSequenceNumber
 * @param {string} jigyoshacode - default is "04" get from config
 * @returns {Promise<string>} transaction ID
 */
const registerTransaction = async (
    context,
    {
        tenantId,
        tempoId,
        daihyouBango,
        transactionType,
        requestOrderType,
        requestOrderId,
        requestParam,
        status,
        createdUserId,
        retryAt,
    },
    sbMessageId,
    sbSequenceNumber,
    jigyoshacode = portalConfig.ALADIN_API.JIGYOSHACODE
) => {
    if (!tenantId) throw new Error('tenantId is required');
    // if (!tempoId) throw new Error('tempoId is required'); // optional tempoId
    if (!requestOrderType) throw new Error('requestOrderType is required');
    if (!requestParam) throw new Error('requestParam is required');

    let aladinApiLog = new AladinApiLogModel({
        tenantId,
        tempoId,
        daihyouBango,
        transactionType,
        requestOrderType,
        requestOrderId,
        requestParam: removeNullValue(requestParam),
        sbMessageId,
        sbSequenceNumber,
        status,
    });

    if (requestParam.denwabango) {
        aladinApiLog.denwabango = requestParam.denwabango;
    }

    if (createdUserId) {
        aladinApiLog.lastUpdateUserId = createdUserId;
    }
    if (retryAt) {
        aladinApiLog.retryAt = retryAt;
    }

    const dateStr = dayjs().format('YYYYMMDD');
    let nextSeq = 0;
    // 万が一、連番取得に失敗したら正常な連番が取れるまで実施する
    do {
        nextSeq = await TransactionLogCountModel.getNextSeq(jigyoshacode, dateStr);
    } while (nextSeq === 0);
    const transactionId = formatSeqString(jigyoshacode, nextSeq);
    aladinApiLog.transactionId = transactionId;
    aladinApiLog.requestParam.transactionID = transactionId;
    const apiPriority = await getAladinTransactionPriority(requestOrderType, tenantId);
    aladinApiLog.priority = apiPriority.priority;
    aladinApiLog.basePriority = apiPriority.basePriority;
    aladinApiLog.isLowPriority = apiPriority.isLowPriority;
    // MVNO_M-1336
    // オーダー種別に応じて「ALADIN API 不可時間の間にALADINAPIが実行できるフラグ」を設定する
    aladinApiLog.canExecWithinFukaJikan = canRequestOrderTypeBeExecutedWithinUketsukeFukaJikan(requestOrderType);
    aladinApiLog.jikkouchuFlg = false;
    aladinApiLog.createdDateTime = dayjs().unix();

    // add flag isFM based on requestOrderType (from `create` function of AladinApiLog.scala)
    if (FM_ORDER_TYPE_LIST.includes(requestOrderType)) {
        aladinApiLog.isFM = true;
    }

    const savedDoc = await aladinApiLog.save();
    context.log(
        `saved aladinApiLog: transactionId: ${transactionId}, requestOrderId: ${requestOrderId}, priority: ${aladinApiLog.priority}`
    );
    return savedDoc?.transactionId;
};

/**
 * ALADINAPIログ作成 [FULL MVNO]
 *
 * @param {object} context - azure context
 * @param {object} transaction - transaction object
 * @param {string} transaction.tenantId
 * @param {string} transaction.tempoId
 * @param {string} transaction.daihyouBango
 * @param {string} transaction.transactionType
 * @param {string} transaction.requestOrderType
 * @param {string} transaction.requestOrderId
 * @param {object} transaction.requestParam
 * @param {number} transaction.status
 * @param {string} transaction.createdUserId
 * @param {number} [transaction.retryAt] set to delay processing the transaction (unix timestamp)
 * @param {string} sbMessageId
 * @param {string} sbSequenceNumber
 * @param {string} jigyoshacode - default is "04" get from config
 * @returns {Promise<string>} transaction ID
 */
const registerTransactionHLR_HSS = async (
    context,
    {
        tenantId,
        tempoId,
        daihyouBango,
        transactionType,
        requestOrderType,
        requestOrderId,
        requestParam,
        status,
        createdUserId,
        retryAt,
    },
    sbMessageId,
    sbSequenceNumber,
    jigyoshacode = portalConfig.ALADIN_API.JIGYOSHACODE_HLRHSS
) => {
    if (!tenantId) throw new Error('tenantId is required');
    // if (!tempoId) throw new Error('tempoId is required'); // optional tempoId
    if (!requestOrderType) throw new Error('requestOrderType is required');
    if (!requestParam) throw new Error('requestParam is required');

    let aladinApiLog = new AladinApiLogModel({
        tenantId,
        tempoId,
        daihyouBango,
        transactionType,
        requestOrderType,
        requestOrderId,
        requestParam: removeNullValue(requestParam),
        sbMessageId,
        sbSequenceNumber,
        status,
    });

    if (requestParam.denwabango) {
        aladinApiLog.denwabango = requestParam.denwabango;
    }

    if (createdUserId) {
        aladinApiLog.lastUpdateUserId = createdUserId;
    }
    if (retryAt) {
        aladinApiLog.retryAt = retryAt;
    }

    const dateStr = dayjs().format('YYYYMMDD');
    let nextSeq = 0;
    // 万が一、連番取得に失敗したら正常な連番が取れるまで実施する
    do {
        nextSeq = await TransactionLogCountModel.getNextSeq(jigyoshacode, dateStr);
    } while (nextSeq === 0);
    const transactionId = formatSeqString(jigyoshacode, nextSeq);
    aladinApiLog.transactionId = transactionId;
    aladinApiLog.requestParam.transactionID = transactionId;
    const apiPriority = await getAladinTransactionPriority(requestOrderType, tenantId);
    aladinApiLog.priority = apiPriority.priority;
    aladinApiLog.basePriority = apiPriority.basePriority;
    aladinApiLog.isLowPriority = apiPriority.isLowPriority;

    aladinApiLog.canExecWithinFukaJikan = true;
    aladinApiLog.jikkouchuFlg = false;
    aladinApiLog.createdDateTime = dayjs().unix();
    aladinApiLog.isFM = true;

    const savedDoc = await aladinApiLog.save();
    context.log(
        `saved registerTransactionHLR_HSS aladinApiLog: transactionId: ${transactionId}, requestOrderId: ${requestOrderId}, priority: ${aladinApiLog.priority}`
    );
    return savedDoc?.transactionId;
};

/**
 * Update old pending transaction data which don't have basePriority field
 * TODO remove it later?
 * @param {object} context
 * @param {string} tenantId
 * @param {boolean} isLowPriority
 */
const _preUpdateTenantPriority = async (context, tenantId, isLowPriority) => {
    context.log('_preUpdateTenantPriority start');

    // query must be same as get1RequestAladinAPIForPolling + tenantId
    const { TOUROKU_IRAI_NOT_YET_SENT, HENKOU_IRAI_NOT_YET_SENT, KAIYAKU_IRAI_NOT_YET_SENT } = TRANSACTION_STATUS;
    let query = {
        tenantId: tenantId,
        status: {
            $in: [TOUROKU_IRAI_NOT_YET_SENT, HENKOU_IRAI_NOT_YET_SENT, KAIYAKU_IRAI_NOT_YET_SENT],
        },
        jikkouchuFlg: {
            $ne: true, // only needs to update pending transactions
        },
        // STEP21: リトライ時のトランザクションの場合一定時間空いていることを確認する
        $or: [
            {
                retryAt: { $exists: false },
            },
            {
                retryAt: { $lte: dayjs().unix() },
            },
        ],

        basePriority: { $exists: false }, // old data won't have this field
    };

    if (getAladinAPIRequestFukajikanInfo(null)) {
        context.log('_preUpdateTenantPriority canExecWithinFukaJikan = true');
        query.canExecWithinFukaJikan = true;
    }

    const updateQuery = [
        {
            $set: {
                basePriority: LOWEST_API_PRIORITY,
                isLowPriority: !isLowPriority, // old tenant flag
            },
        },
    ];

    if (isLowPriority) {
        // current data is normal priority
        updateQuery[0].$set.basePriority = '$priority';
    } else {
        // current data is low priority, get original priority ( priority / 1.4 )
        updateQuery[0].$set.basePriority = { $ceil: { $multiply: ['$priority', 1 / 1.4] } };
    }

    const rawResult = await AladinApiLogModel.updateMany(query, updateQuery, { rawResult: true, runValidators: true });
    context.log('_preUpdateTenantPriority result', rawResult);
};

/**
 * Update priority for all pending transactions of a tenant
 * @param {object} context
 * @param {string} tenantId
 * @param {boolean} isLowPriority
 */
const updateTenantPriority = async (context, tenantId, isLowPriority) => {
    if (isNone(tenantId) || isNone(isLowPriority)) {
        context.log.error('updateTenantPriority missing tenantId or isLowPriority param', { tenantId, isLowPriority });
        return;
    }
    if (typeof tenantId !== 'string' || typeof isLowPriority !== 'boolean') {
        context.log.error('updateTenantPriority invalid parameter type', { tenantId, isLowPriority });
        return;
    }

    await _preUpdateTenantPriority(context, tenantId, isLowPriority);

    context.log('updateTenantPriority start', { tenantId, isLowPriority });

    // query must be same as get1RequestAladinAPIForPolling + tenantId
    const { TOUROKU_IRAI_NOT_YET_SENT, HENKOU_IRAI_NOT_YET_SENT, KAIYAKU_IRAI_NOT_YET_SENT } = TRANSACTION_STATUS;
    let query = {
        tenantId: tenantId,
        status: {
            $in: [TOUROKU_IRAI_NOT_YET_SENT, HENKOU_IRAI_NOT_YET_SENT, KAIYAKU_IRAI_NOT_YET_SENT],
        },
        jikkouchuFlg: {
            $ne: true, // only needs to update pending transactions
        },
        // STEP21: リトライ時のトランザクションの場合一定時間空いていることを確認する
        $or: [
            {
                retryAt: { $exists: false },
            },
            {
                retryAt: { $lte: dayjs().unix() },
            },
        ],

        isLowPriority: !isLowPriority,
    };

    if (getAladinAPIRequestFukajikanInfo(null)) {
        context.log('updateTenantPriority canExecWithinFukaJikan = true');
        query.canExecWithinFukaJikan = true;
    }

    const updateQuery = [
        {
            $set: {
                isLowPriority: isLowPriority,
            },
        },
    ];

    if (isLowPriority) {
        updateQuery[0].$set.priority = { $multiply: ['$basePriority', 1.4] };
    } else {
        updateQuery[0].$set.priority = '$basePriority';
    }

    context.log('updateTenantPriority query', query, updateQuery);

    const rawResult = await AladinApiLogModel.updateMany(query, updateQuery, { runValidators: true, rawResult: true });
    context.log('updateTenantPriority end', rawResult);
};

/**
 * get aladin transaction
 * @param {object} context - azure context
 * @param {boolean} withinUketsukeFukaJikan - withinUketsukeFukaJikan flag
 * @returns {object} aladin transaction
 */
const get1RequestAladinAPIForPolling = async (context, withinUketsukeFukaJikan) => {
    const { TOUROKU_IRAI_NOT_YET_SENT, HENKOU_IRAI_NOT_YET_SENT, KAIYAKU_IRAI_NOT_YET_SENT } = TRANSACTION_STATUS;
    let query = {
        status: {
            $in: [TOUROKU_IRAI_NOT_YET_SENT, HENKOU_IRAI_NOT_YET_SENT, KAIYAKU_IRAI_NOT_YET_SENT],
        },
        jikkouchuFlg: {
            $ne: true,
        },
        // STEP21: リトライ時のトランザクションの場合一定時間空いていることを確認する
        $or: [
            {
                retryAt: { $exists: false },
            },
            {
                retryAt: { $lte: dayjs().unix() },
            },
        ],
    };
    let orderBy = {
        priority: 1,
        createdDateTime: 1,
    };

    let update = {
        $set: {
            jikkouchuFlg: true,
        },
    };

    if (withinUketsukeFukaJikan) {
        query.canExecWithinFukaJikan = true;
    }

    const result = await AladinApiLogModel.findOneAndUpdate(query, update, { sort: orderBy }).lean();
    return result;
};

/**
 * Create aladin transaction
 * @param {object} context - azure context
 * @param {string} transactionId
 * @param {boolean} newJikkouchuFlg - newJikkouchuFlg
 */
const updateJikkouchuFlg = async (context, transactionId, newJikkouchuFlg) => {
    return await AladinApiLogModel.updateOne({ transactionId }, { $set: { jikkouchuFlg: newJikkouchuFlg } });
};

/**
 * It updates the status of a transaction in the database.
 * @param context - The context object that is passed to the function.
 * @param {string} transactionId - The transaction ID of the transaction you want to update.
 * @param {number} newStatus - The new status of the transaction.
 */
const updateStatus = async (context, transactionId, newStatus) => {
    return await AladinApiLogModel.updateOne({ transactionId }, { $set: { status: newStatus } });
};

/**
 * 再送するためのステータス修正
 * @param {object} context
 * @param {string} id AladinApiLog._id
 */
const updateOneForResend = async (context, id) => {
    const { TOUROKU_IRAI_NOT_YET_SENT } = TRANSACTION_STATUS;
    const result = await AladinApiLogModel.updateOne(
        {
            _id: mongoose.Types.ObjectId(id),
            // status:「0: 未登録依頼」も再送できるようにする
        },
        {
            $set: {
                status: TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: false,
                resendAt: dayjs().unix(),
            },
        }
    );
    return result.modifiedCount;
};

/**
 * It takes an Aladin API log and returns a Touroku Irai request parameter object
 * @param context - The context object that is passed to the function.
 * @param {object} aladinApiLog - The Aladin API log object.
 */
const toTourokuIraiRequestParams = (context, aladinApiLog) => {
    const { requestParam } = aladinApiLog;
    if (!requestParam) return null;

    let torokuIraiRequestParams = {
        transactionID: requestParam.transactionID || '',
        hanbaitencode: requestParam.hanbaitencode || '',
        fukajigyoshacode: null,
        keiyakusyubetu: requestParam.keiyakusyubetu || '',
        transactionTYPE: requestParam.transactionTYPE || '',
        daihyobango: requestParam.daihyobango,
        denwabango: requestParam.denwabango,
        seizobango: requestParam.seizobango,
        cardkeijo: requestParam.cardkeijo,
        MNPyoyakubango: requestParam.MNPyoyakubango,
        MNPzokusei: requestParam.MNPzokusei,
        MNPyoyakusyakana: decryptTextToEncodeShiftJIS(requestParam.MNPyoyakusyakana),
        MNPyoyakusyakanji: decryptTextToEncodeShiftJIS(requestParam.MNPyoyakusyakanji),
        MNPseinengappi: decryptText(requestParam.MNPseinengappi),
        ansyobango: requestParam.ansyobango,
        ryokinplan: requestParam.ryokinplan,
        sousaservice: requestParam.sousaservice,
        WWtukehenhaiFLG: requestParam.WWtukehenhaiFLG,
        WWriyouteisimeyasugaku: requestParam.WWriyouteisimeyasugaku,
        WWdaisankokuhassinkisei: requestParam.WWdaisankokuhassinkisei,
        WCtukehenhaiFLG: requestParam.WCtukehenhaiFLG,
        WCriyouteisimeyasugaku: requestParam.WCriyouteisimeyasugaku,
        WCtuwateisi: requestParam.WCtuwateisi,
        eid: requestParam.eid,
    };

    return removeNullValue(torokuIraiRequestParams);
};

/**
 * It takes the request parameters from the Aladin API log and returns a new object
 * @param context - The context object
 * @param aladinApiLog - The log of the Aladin API call.
 * @returns the object toHenkouIraiRequestParams.
 */
const toHenkouIraiRequestParams = (context, aladinApiLog) => {
    const { requestParam } = aladinApiLog;
    if (!requestParam) return null;

    let henkouIraiRequestParams = {
        transactionID: requestParam.transactionID || '',
        hanbaitencode: requestParam.hanbaitencode || '',
        fukajigyoshacode: null,
        keiyakusyubetu: requestParam.keiyakusyubetu || '',
        transactionTYPE: requestParam.transactionTYPE || '',
        denwabango: requestParam.denwabango,
        seizobango: requestParam.seizobango,
        cardsaihakoFLG: requestParam.cardsaihakoFLG,
        NWmikaituFLG: requestParam.NWmikaituFLG,
        cardkeijo: requestParam.cardkeijo,
        PINlockkaijoreset: requestParam.PINlockkaijoreset,
        MNPyoyakusyakana: decryptTextToEncodeShiftJIS(requestParam.MNPyoyakusyakana),
        MNPyoyakusyakanji: decryptTextToEncodeShiftJIS(requestParam.MNPyoyakusyakanji),
        MNPseinengappi: decryptText(requestParam.MNPseinengappi),
        MNPzokusei: requestParam.MNPzokusei,
        ansyobango: requestParam.ansyobango,
        ryokinplan: requestParam.ryokinplan,
        sousaservice: requestParam.sousaservice,
        WWtukehenhaiFLG: requestParam.WWtukehenhaiFLG,
        WWriyouteisimeyasugaku: requestParam.WWriyouteisimeyasugaku,
        WWdaisankokuhassinkisei: requestParam.WWdaisankokuhassinkisei,
        WCtukehenhaiFLG: requestParam.WCtukehenhaiFLG,
        WCriyouteisimeyasugaku: requestParam.WCriyouteisimeyasugaku,
        WCtuwateisi: requestParam.WCtuwateisi,
        eid: requestParam.eid,
    };

    return removeNullValue(henkouIraiRequestParams);
};

const toKaiyakuIraiRequestParams = (context, aladinApiLog) => {
    const { requestParam } = aladinApiLog;
    if (!requestParam) return null;
    let kaiyakuIraiRequestParams = {
        transactionID: requestParam.transactionID || '',
        hanbaitencode: requestParam.hanbaitencode || '',
        fukajigyoshacode: null,
        keiyakusyubetu: requestParam.keiyakusyubetu || '',
        transactionTYPE: requestParam.transactionTYPE || '',
        denwabango: requestParam.denwabango,
    };

    return removeNullValue(kaiyakuIraiRequestParams);
};

const toKurokaIraiRequestParams = (context, aladinApiLog) => {
    const { requestParam } = aladinApiLog;
    if (!requestParam) return null;
    let kurokaIraiRequestParams = {
        transactionID: requestParam.transactionID || '',
        hanbaitencode: requestParam.hanbaitencode || '',
        fukajigyoshacode: null,
        keiyakusyubetu: requestParam.keiyakusyubetu || '',
        transactionTYPE: requestParam.transactionTYPE || '',
        denwabango: requestParam.denwabango,
    };

    return removeNullValue(kurokaIraiRequestParams);
};

const toFullMVNOTourokuIraiRequestParams = (context, aladinApiLog) => {
    const { requestParam } = aladinApiLog;
    if (!requestParam) return null;

    let fullMVNOTourokuIraiRequestParams = {
        transactionID: requestParam.transactionID || '',
        hanbaitencode: requestParam.hanbaitencode || '',
        fukajigyoshacode: null,
        keiyakusyubetu: requestParam.keiyakusyubetu || '',
        transactionTYPE: requestParam.transactionTYPE || '',
        daihyobango: requestParam.daihyobango,
        denwabango: requestParam.denwabango || '',
        imsi: requestParam.imsi || '',
        kigyocode: requestParam.kigyocode,
        jigyoshocode: requestParam.jigyoshocode,
        kigyomeikana: encodeShiftJIS(requestParam.kigyomeikana),
        kigyomei: encodeShiftJIS(requestParam.kigyomei),
        kigyodaihyo: requestParam.kigyodaihyo,
        yubimbango: requestParam.yubimbango,
        kigyojushocode: requestParam.kigyojushocode,
        kigyohosokujusho: encodeShiftJIS(requestParam.kigyohosokujusho),
        kigyojushokatagaki: encodeShiftJIS(requestParam.kigyojushokatagaki),
        renrakushamei: encodeShiftJIS(requestParam.renrakushamei),
        renrakudenwa: requestParam.renrakudenwa,
        tokuteihojindenwa: requestParam.tokuteihojindenwa,
        seikyusakikana: encodeShiftJIS(requestParam.seikyusakikana),
        seikyusakimei: encodeShiftJIS(requestParam.seikyusakimei),
        seikyusakidenwa: requestParam.seikyusakidenwa,
        seikyusakiyubimbango: requestParam.seikyusakiyubimbango,
        seikyusakijushocode: requestParam.seikyusakijushocode,
        seikyusakihosokujusho: encodeShiftJIS(requestParam.seikyusakihosokujusho),
        seikyusakijushokatagaki: encodeShiftJIS(requestParam.seikyusakijushokatagaki),
        ryokinplan: requestParam.ryokinplan,
        sousaservice: requestParam.sousaservice,
    };

    return removeNullValue(fullMVNOTourokuIraiRequestParams);
};

const toFullMVNOSokujiKaiyakuIraiRequestParams = (context, aladinApiLog) => {
    const { requestParam } = aladinApiLog;
    if (!requestParam) return null;

    let fullMVNOSokujiKaiyakuIraiRequestParams = {
        transactionID: requestParam.transactionID || '',
        hanbaitencode: requestParam.hanbaitencode || '',
        fukajigyoshacode: null,
        keiyakusyubetu: requestParam.keiyakusyubetu || '',
        transactionTYPE: requestParam.transactionTYPE || '',
        denwabango: requestParam.denwabango || '',
    };

    return removeNullValue(fullMVNOSokujiKaiyakuIraiRequestParams);
};

const toKensakuIraiRequestParams = (context, aladinApiLog) => {
    const { requestParam } = aladinApiLog;
    if (!requestParam) return null;

    let kensakuIraiRequestParams = {
        transactionID: requestParam.transactionID || '',
        hanbaitencode: requestParam.hanbaitencode || '',
        fukajigyoshacode: null,
        keiyakusyubetu: requestParam.keiyakusyubetu || '',
        transactionTYPE: requestParam.transactionTYPE || '',

        daihyobango: requestParam.daihyobango,
        kensakukoumoku: requestParam.kensakukoumoku,
        denwabango: requestParam.denwabango,
        MNPyoyakubango: requestParam.MNPyoyakubango,
        MNPzokusei: requestParam.MNPzokusei,
        MNPyoyakusyakana: decryptTextToEncodeShiftJIS(requestParam.MNPyoyakusyakana),
        MNPyoyakusyakanji: decryptTextToEncodeShiftJIS(requestParam.MNPyoyakusyakanji),
        MNPseinengappi: decryptText(requestParam.MNPseinengappi),
        MNPjigyoshacode: requestParam.MNPjigyoshacode,
    };

    return removeNullValue(kensakuIraiRequestParams);
};

/**
 *
 * @param {object} context
 * @param {AladinApiLogDocument} [aladinApiLog]
 */
const toTourokuIraiRequestResponseParams = (context, aladinApiLog) => {
    const responseParam = aladinApiLog?.responseParam;
    if (isNone(responseParam)) {
        return null;
    }

    const tourokuIraiRequestResponse = {
        syorikekkaKbn: responseParam.syorikekkaKbn ?? '',
        transactionID: responseParam.transactionID ?? '',
        hanbaitencode: responseParam.hanbaitencode ?? '',
        fukajigyoshacode: responseParam.fukajigyoshacode,
        keiyakusyubetu: responseParam.keiyakusyubetu ?? '',
        transactionTYPE: responseParam.transactionTYPE ?? '',
        kaihainengappi: responseParam.kaihainengappi,
        denwabango: responseParam.denwabango,
        seizobango: responseParam.seizobango,
        MNPyoyakubango: responseParam.MNPyoyakubango,
        MNPyukokigen: responseParam.MNPyukokigen,
        riyoukaisibi: responseParam.riyoukaisibi,
        ansyobango: responseParam.ansyobango ?? '',
        PINlockkaijo: responseParam.PINlockkaijo,
        ryokinplanServiceCd: responseParam.ryokinplanServiceCd ?? '',
        ryokinplanServiceKaishibi: responseParam.ryokinplanServiceKaishibi ?? '',
        keiyakuservice: responseParam.keiyakuservice,
        WWriyouteisimeyasugaku: responseParam.WWriyouteisimeyasugaku,
        WWdaisankokuhassinkisei: responseParam.WWdaisankokuhassinkisei,
        WCriyouteisimeyasugaku: responseParam.WCriyouteisimeyasugaku,
        WCtuwateisi: responseParam.WCtuwateisi,
        kaikeijoho: responseParam.kaikeijoho,
        goukeikingaku: responseParam.goukeikingaku,
        MNPkahishoukaikekka: responseParam.MNPkahishoukaikekka,
    };

    return tourokuIraiRequestResponse;
};

/**
 * It updates the status and response of a transaction.
 * @param context - The context object that is passed to the function.
 * @param transactionId - The transaction ID of the request.
 * @param newStatus - The status of the transaction.
 * @param responseParam - The response parameter of the API call.
 * @returns The return value is a promise.
 */
const updateStatusAndResponse = async (context, transactionId, newStatus, responseParam) => {
    return await AladinApiLogModel.updateOne(
        { transactionId },
        { $set: { status: newStatus, responseParam, getTourokuIraiResponseAt: dayjs().unix() } }
    ).exec();
};

/**
 * reset status for 502 error
 * @param {object} context
 * @param {string} transactionId
 */
const retryRequestOneTime = async (context, transactionId) => {
    await AladinApiLogModel.updateOne(
        { transactionId },
        {
            $set: {
                status: TRANSACTION_STATUS.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: false,
                isRetried: true,
                retryAt: dayjs().unix() + portalConfig.ALADIN_API.POLLING.RETRY_INTERVAL,
            },
        }
    ).exec();
};

const findByTransactionId = async (context, transactionId) => {
    return await AladinApiLogModel.findOne({ transactionId: transactionId }).exec();
};

const findOneByRequestOrderId = async (context, requestOrderId) => {
    return await AladinApiLogModel.findOne({ requestOrderId }).exec();
};

/**
 * Rewrite of searchAladinApiLog
 * include result from getFullSummaryInfoByRequestOrderId and getRequestOrderDescriptionById,
 * @param {object} context
 * @param {object} param
 * @param {number} [param.searchType]
 * @param {string} [param.searchValue]
 * @param {Array<string>} param.tenantIds
 * @param {Array<string>} param.tempoIds
 * @param {Array<string>} param.transactionTypes
 * @param {Array<string>} param.requestOrderTypes
 * @param {Array<string>} param.transactionStatus
 * @param {number} [param.requestedDateTimeFrom]
 * @param {number} [param.requestedDateTimeTo]
 * @param {number} [param.resendDateTimeFrom]
 * @param {number} [param.resendDateTimeTo]
 * @param {string} [param.sortBy]
 * @param {number} [param.skip]
 * @param {number} param.limit
 * @param {boolean} param.isSortOrderAscending
 * @param {number} [param.service]
 */
const searchAladinApiLogNew = async (
    context,
    {
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        transactionTypes, // eslint-disable-line no-unused-vars
        requestOrderTypes,
        transactionStatus,
        requestedDateTimeFrom,
        requestedDateTimeTo,
        resendDateTimeFrom,
        resendDateTimeTo,
        sortBy,
        skip,
        limit,
        isSortOrderAscending,
        service,
    }
) => {
    context.log('searchAladinApiLogNew start');
    const query = [];
    switch (searchType) {
        case 0:
            query.push({ requestOrderId: searchValue });
            break;
        case 1:
            query.push({ daihyouBango: searchValue });
            break;
        case 2:
            query.push({
                $or: [{ 'requestParam.denwabango': searchValue }, { 'responseParam.denwabango': searchValue }],
            });
            break;
        case 3:
            query.push({ transactionId: searchValue });
            break;
    }

    switch (service ?? MVNO_SERVICES.ALL) {
        case MVNO_SERVICES.LITE:
            query.push({ isFM: { $ne: true } });
            break;
        case MVNO_SERVICES.FULL:
            query.push({ isFM: true });
            break;
    }

    if (!isNone(requestOrderTypes)) {
        query.push({ requestOrderType: { $in: requestOrderTypes } });
    }

    if (!isNone(tenantIds)) {
        query.push({ tenantId: { $in: tenantIds } });
    }

    if (!isNone(tempoIds)) {
        query.push({ tempoId: { $in: tempoIds } });
    }

    if (!isNone(transactionStatus)) {
        query.push({ status: { $in: transactionStatus.map((s) => +s) } });
    }

    if (!isNone(requestedDateTimeFrom) || !isNone(requestedDateTimeTo)) {
        const requestTime = [];
        if (!isNone(requestedDateTimeFrom)) {
            requestTime.push({
                createdDateTime: { $gte: requestedDateTimeFrom },
            });
        }
        if (!isNone(requestedDateTimeTo)) {
            requestTime.push({
                createdDateTime: { $lt: requestedDateTimeTo },
            });
        }
        query.push({ $and: requestTime });
    }
    if (!isNone(resendDateTimeFrom) || !isNone(resendDateTimeTo)) {
        const resendTime = [];
        if (!isNone(resendDateTimeFrom)) {
            resendTime.push({
                resendAt: { $gte: resendDateTimeFrom },
            });
        }
        if (!isNone(resendDateTimeTo)) {
            resendTime.push({
                resendAt: { $lt: resendDateTimeTo },
            });
        }
        query.push({ $and: resendTime });
    }

    let sortKey = 'createdDateTime';
    const sortOrder = isSortOrderAscending ? 1 : -1;
    const sortMap = {
        tenant_id: 'tenantId',
        start_date: 'createdDateTime',
        order_type: 'requestOrderType',
        request_oder_id: 'requestOrderId', // intended typo
        status: 'status',
        daihyou_bango: 'daihyouBango',
    };
    if (!isNone(sortBy)) {
        const key = sortMap[sortBy];
        if (!isNone(key)) {
            sortKey = key;
        }
    }

    const filter = query.length > 0 ? { $and: query } : {};
    let queryObj = AladinApiLogModel.find(filter).sort({ [sortKey]: sortOrder });
    if (!isNone(skip)) {
        queryObj = queryObj.skip(skip);
    }
    const limitValue = isNone(limit) || limit === 0 ? 20 : limit;
    queryObj.limit(limitValue);
    const requestOrderList = await queryObj.exec();
    const totalFound = await AladinApiLogModel.countDocuments(filter).exec();

    const notEmpty = (obj) => !isNone(obj) && typeof obj === 'object';

    return {
        requestOrderList: requestOrderList.map((row) => {
            // オーダ種別によって判断条件が異なる
            /*
             * 新規申込（通常開通）、新規申込（半黒ROM作成）、MNP転入受付（通常開通）、MNP転入受付（半黒ROM作成）、MP転入受付（OTA-ROM開通）
             * カード再発行（白ROM＜PINロック解除なし＞）、カード再発行（OTA-ROM開通＜PINロック解除なし＞）、MNP転入受付（eSIM開通）の場合は登録要求のレスポンスを判断する必要がある。
             * それ以外は登録依頼のレスポンスをチェックするだけでいいと思います。
             */

            const totalRequestCnt = 1;
            let totalSuccessCnt = 0;
            let totalFailedCnt = 0;
            let totalTourokuIraiSuccessCnt = 0;
            if (notEmpty(row.responseParam)) {
                switch (row.requestOrderType) {
                    case ORDER_TYPE.SHINKI_MOUSHIKOMI_TSUJOU_KAITSU:
                    case ORDER_TYPE.SHINKI_MOUSHIKOMI_HANKURO_ROM_SAKUSEI:
                    case ORDER_TYPE.MNP_TENNYU_UKETSUKE_TSUJOU_KAITSU:
                    case ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_SAKUSEI:
                    case ORDER_TYPE.MNP_TENNYU_UKETSUKE_OTA_ROM_KAITSU:
                    case ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM:
                    case ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_OTA_ROM_KAITSU:
                    case ORDER_TYPE.MNP_TENNYU_UKETSUKE_ESIM_KAITSU:
                        totalSuccessCnt = !isNone(row.requestRegisterParam) || !isNone(row.requestRegisterList) ? 1 : 0;
                        break;
                    default:
                        totalSuccessCnt = SUCCESS_SHORI_KEKKA_CODES.includes(row.responseParam?.syorikekkaKbn) ? 1 : 0;
                }
                if (!SUCCESS_SHORI_KEKKA_CODES.includes(row.responseParam.syorikekkaKbn)) {
                    totalFailedCnt = 1;
                }
                if (SUCCESS_SHORI_KEKKA_CODES.includes(row.responseParam.syorikekkaKbn)) {
                    totalTourokuIraiSuccessCnt = 1;
                }
            }
            const touRokuYoukyu = row.getTourokuIraiResponseAt > 0 || !isNone(row.requestRegisterList) ? true : false;

            return {
                id: row.id, // string version of _id
                requestOrderId: row.requestOrderId,
                requestOrderType: row.requestOrderType,
                tenantId: row.tenantId,
                tempoId: row.tempoId,
                daihyouBango: row.daihyouBango,
                transactionType: row.transactionType,
                createdDateTime: row.createdDateTime,
                denwabango: extractFromParameterObjects(
                    'denwabango',
                    row.requestParam,
                    row.responseParam,
                    row.requestRegisterParam
                ),
                status: row.status,
                transactionId: row.transactionId,
                summary: {
                    totalRequestCnt,
                    totalSuccessCnt,
                    totalFailedCnt,
                    totalTourokuIraiSuccessCnt,
                    touRokuYoukyu,
                },
            };
        }),
        totalFound,
    };
};

/**
 * 旧メソッド：search22
 *
 * Return list of requestOrderId which matches search parameters
 * @deprecated use searchAladinApiLogNew
 * @param {object} context
 * @param {object} param
 * @param {number} [param.searchType]
 * @param {string} [param.searchValue]
 * @param {Array<string>} param.tenantIds
 * @param {Array<string>} param.tempoIds
 * @param {Array<string>} param.transactionTypes
 * @param {Array<string>} param.requestOrderTypes
 * @param {Array<string>} param.transactionStatus
 * @param {number} [param.requestedDateTimeFrom]
 * @param {number} [param.requestedDateTimeTo]
 * @param {string} [param.sortBy]
 * @param {number} [param.skip]
 * @param {number} param.limit
 * @param {boolean} param.isSortOrderAscending
 * @param {number} [param.service]
 */
const searchAladinApiLog = async (
    context,
    {
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        transactionTypes, // eslint-disable-line no-unused-vars
        requestOrderTypes,
        transactionStatus,
        requestedDateTimeFrom,
        requestedDateTimeTo,
        sortBy,
        skip,
        limit,
        isSortOrderAscending,
        service,
    }
) => {
    context.log('searchAladinApiLog start');
    const query = {};
    switch (searchType) {
        case 0:
            query.requestOrderId = searchValue;
            break;
        case 1:
            query.daihyouBango = searchValue;
            break;
        case 2:
            query['$or'] = [{ 'requestParam.denwabango': searchValue }, { 'responseParam.denwabango': searchValue }];
            break;
        case 3:
            query.transactionId = searchValue;
            break;
    }

    switch (service ?? MVNO_SERVICES.ALL) {
        case MVNO_SERVICES.LITE:
            query.isFM = { $ne: true };
            break;
        case MVNO_SERVICES.FULL:
            query.isFM = true;
            break;
    }

    if (!isNone(requestOrderTypes)) {
        query.requestOrderType = { $in: requestOrderTypes };
    }

    if (!isNone(tenantIds)) {
        query.tenantId = { $in: tenantIds };
    }

    if (!isNone(tempoIds)) {
        query.tempoId = { $in: tempoIds };
    }

    if (!isNone(transactionStatus)) {
        query.status = { $in: transactionStatus.map((s) => +s) };
    }

    if (!isNone(requestedDateTimeFrom) || !isNone(requestedDateTimeTo)) {
        const requestTime = [];
        if (!isNone(requestedDateTimeFrom)) {
            requestTime.push({
                createdDateTime: { $gte: requestedDateTimeFrom },
            });
        }
        if (!isNone(requestedDateTimeTo)) {
            requestTime.push({
                createdDateTime: { $lt: requestedDateTimeTo },
            });
        }
        query['$and'] = requestTime;
    }

    const filterBlock = { $match: query };

    let sortKey = 'createdDateTime';
    const sortOrder = isSortOrderAscending ? 1 : -1;
    const sortMap = {
        tenant_id: 'tenantId',
        start_date: 'createdDateTime',
        order_type: 'requestOrderType',
        request_oder_id: 'requestOrderId', // intended typo
        status: 'status',
        daihyou_bango: 'daihyouBango',
    };
    if (!isNone(sortBy)) {
        const key = sortMap[sortBy];
        if (!isNone(key)) {
            sortKey = key;
        }
    }
    const sortBlock = { $sort: { [sortKey]: sortOrder } };

    const groupBlock = {
        $group: {
            _id: '$requestOrderId',
            [sortKey]: { $first: `$${sortKey}` },
        },
    };

    const aggregationList = [filterBlock, groupBlock, sortBlock];
    if (!isNone(skip)) {
        aggregationList.push({ $skip: skip });
    }
    const limitValue = isNone(limit) || limit === 0 ? 20 : limit;
    aggregationList.push({ $limit: limitValue });

    const requestOrderIds = await AladinApiLogModel.aggregate(aggregationList).exec();
    /** @type {Array<string>} */
    const requestOrderIdList = requestOrderIds.map((row) => row._id);

    const groupTotalFoundBlock = { $group: { _id: '$requestOrderId' } };
    const totalFound = (await AladinApiLogModel.aggregate([filterBlock, groupTotalFoundBlock]).exec()).length;
    return {
        requestOrderIdList,
        totalFound,
    };
};

/**
 * @deprecated use searchAladinApiLogNew
 * @param {object} context
 * @param {Array<string>} requestOrderIds
 */
const getFullSummaryInfoByRequestOrderId = async (context, requestOrderIds) => {
    const requests = await AladinApiLogModel.find({ requestOrderId: { $in: requestOrderIds } }).exec();
    return {
        descriptionMap: getRequestOrderDescriptionById(requests),
        summaryMap: getSummaryInfoByRequestOrderId(requests),
    };
};

/**
 * @deprecated use searchAladinApiLogNew
 * @param {Array<AladinApiLogDocument>} aladinAPIRequestSeq
 */
const getRequestOrderDescriptionById = (aladinAPIRequestSeq) => {
    const grouped = _.groupBy(aladinAPIRequestSeq, _.property('requestOrderId'));
    const mapValuesResult = _.mapValues(grouped, (obj) => {
        const first = obj[0];
        const denwabango = extractFromParameterObjects(
            'denwabango',
            first.requestParam,
            first.responseParam,
            first.requestRegisterParam
        );

        return {
            requestOrderId: first.requestOrderId,
            requestOrderType: first.requestOrderType,
            tenantId: first.tenantId,
            tempoId: first.tempoId,
            daihyouBango: first.daihyouBango,
            transactionType: first.transactionType,
            createdDateTime: first.createdDateTime,
            denwabango,
        };
    });
    return mapValuesResult;
};

/**
 * @deprecated use searchAladinApiLogNew
 * @param {Array<AladinApiLogDocument>} aladinAPIRequestSeq
 */
const getSummaryInfoByRequestOrderId = (aladinAPIRequestSeq) => {
    const orderIdMap = _.groupBy(aladinAPIRequestSeq, _.property('requestOrderId'));
    const totalRequestCntMap = _.mapValues(orderIdMap, (seq) => seq.length);

    const notEmpty = (obj) => !isNone(obj) && typeof obj === 'object';

    // オーダ種別によって判断条件が異なる
    /*
     * 新規申込（通常開通）、新規申込（半黒ROM作成）、MNP転入受付（通常開通）、MNP転入受付（半黒ROM作成）、MP転入受付（OTA-ROM開通）
     * カード再発行（白ROM＜PINロック解除なし＞）、カード再発行（OTA-ROM開通＜PINロック解除なし＞）、MNP転入受付（eSIM開通）の場合は登録要求のレスポンスを判断する必要がある。
     * それ以外は登録依頼のレスポンスをチェックするだけでいいと思います。
     */

    const totalSuccessCntMap = _.mapValues(orderIdMap, (seq) => {
        return seq.filter((row) => {
            if (notEmpty(row.responseParam)) {
                const firstRow = seq.at(0);
                const responseParam = row.responseParam;
                switch (firstRow.requestOrderType) {
                    case ORDER_TYPE.SHINKI_MOUSHIKOMI_TSUJOU_KAITSU:
                    case ORDER_TYPE.SHINKI_MOUSHIKOMI_HANKURO_ROM_SAKUSEI:
                    case ORDER_TYPE.MNP_TENNYU_UKETSUKE_TSUJOU_KAITSU:
                    case ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_SAKUSEI:
                    case ORDER_TYPE.MNP_TENNYU_UKETSUKE_OTA_ROM_KAITSU:
                    case ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM:
                    case ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_OTA_ROM_KAITSU:
                    case ORDER_TYPE.MNP_TENNYU_UKETSUKE_ESIM_KAITSU:
                        return !isNone(row.requestRegisterParam) || !isNone(row.requestRegisterList);
                    default:
                        return SUCCESS_SHORI_KEKKA_CODES.includes(responseParam.syorikekkaKbn);
                }
            } else {
                return false;
            }
        }).length;
    });

    const totalFailedCntMap = _.mapValues(orderIdMap, (seq) => {
        return seq.filter((row) => {
            if (notEmpty(row.responseParam)) {
                return !SUCCESS_SHORI_KEKKA_CODES.includes(row.responseParam.syorikekkaKbn);
            } else {
                return false;
            }
        }).length;
    });

    const totalTourokuIraiSuccessCntMap = _.mapValues(orderIdMap, (seq) => {
        return seq.filter((row) => {
            if (notEmpty(row.responseParam)) {
                return SUCCESS_SHORI_KEKKA_CODES.includes(row.responseParam.syorikekkaKbn);
            } else {
                return false;
            }
        }).length;
    });

    const touRokuYoukyu = _.mapValues(orderIdMap, (seq) => {
        const youkyuSize = seq.filter((row) =>
            row.getTourokuYoukyuuResponseAt > 0 || !isNone(row.requestRegisterList) ? true : false
        ).length;
        return youkyuSize > 0;
    });

    return {
        totalRequestCntMap,
        totalSuccessCntMap,
        totalTourokuIraiSuccessCntMap,
        totalFailedCntMap,
        touRokuYoukyu,
    };
};

const findAllByRequestOrderId = async (context, requestOrderId) => {
    return await AladinApiLogModel.find({ requestOrderId }).exec();
};

/**
 * get field value from parameter objects (e.g. denwabango from requestParam, responseParam)
 * @param {string} fieldName
 * @param  {...object} paramOpts
 */
const extractFromParameterObjects = (fieldName, ...paramOpts) => {
    /** @type {string|undefined} */
    let value;
    for (const param of paramOpts) {
        if (isNone(value) && !isNone(param) && typeof param === 'object') {
            value = param[fieldName];
            if (!isNone(value)) break; // stop after first found non empty value
        }
    }
    return value;
};

/**
 * This function updates a request register parameter in a MongoDB database with a response parameter
 * and returns the current time in seconds.
 * @param context
 * @param transactionId - The transaction ID is a unique identifier for a specific transaction or
 * request.
 * @param responseParam - RequestParamFromDocomo
 * @returns the current Unix timestamp in seconds after updating the `requestRegisterList` array in the
 * `AladinApiLogModel` document with the provided `responseParam` and the current timestamp.
 */
const updateRequestRegisterParam = async (context, transactionId, responseParam) => {
    const nowSecs = dayjs().unix();
    const query = { transactionId };
    const update = {
        $push: {
            requestRegisterList: {
                ...removeNullValue(responseParam),
                getTourokuYoukyuuResponseAt: nowSecs,
            },
        },
    };

    await AladinApiLogModel.updateOne(query, update).exec();
    return nowSecs;
};

/**
 * Get AladinApiLog by its id (multiple)
 * @param {object} context
 * @param {string[]} ids
 */
const getAladinApiLogsByIds = async (context, ids) => {
    context.log('getAladinApiLogsByIds start');
    if (!Array.isArray(ids) || ids.length === 0) return [];
    return await AladinApiLogModel.find({
        _id: { $in: ids.map((r) => mongoose.Types.ObjectId(r)) },
    });
};

aladinApiLogSchema.statics = {
    registerTransaction,
    registerTransactionHLR_HSS,
    updateTenantPriority,
    get1RequestAladinAPIForPolling,
    updateJikkouchuFlg,
    updateOneForResend,
    toTourokuIraiRequestParams,
    toHenkouIraiRequestParams,
    toKaiyakuIraiRequestParams,
    toKurokaIraiRequestParams,
    toFullMVNOTourokuIraiRequestParams,
    toFullMVNOSokujiKaiyakuIraiRequestParams,
    toKensakuIraiRequestParams,
    toTourokuIraiRequestResponseParams,
    updateStatus,
    updateStatusAndResponse,
    retryRequestOneTime,
    findByTransactionId,
    findOneByRequestOrderId,
    searchAladinApiLogNew,
    searchAladinApiLog,
    getFullSummaryInfoByRequestOrderId,
    getSummaryInfoByRequestOrderId,
    findAllByRequestOrderId,
    updateRequestRegisterParam,
    getAladinApiLogsByIds,
};

const AladinApiLogModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.ALADIN_SERVICE)
    .model('aladin_api_log', aladinApiLogSchema, 'aladin_api_log');

module.exports = AladinApiLogModel;
