const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const transactionLogCountSchema = new Schema({
    jigyoshacode: {
        type: String,
        required: true,
    },
    dateTimeStr: {
        type: String,
        required: true,
    },
    seq: {
        type: Number,
        required: true,
    },
    createdDateTime: {
        type: Number,
        default: dayjs().unix(),
        set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()),
    },
});

transactionLogCountSchema.index({ jigyoshacode: 1, dateTimeStr: 1 }, { unique: true });

/**
 * find seq by jigyoshacode and dateTime, then auto increase seq by 1 and return
 * @param {string} jigyoshacode
 * @param {string} dateTimeStr - date format YYYYMMDD
 * @returns {number|0} seq value or 0
 */
const getNextSeq = async (jigyoshacode, dateTimeStr) => {
    try {
        const res = await TransactionLogCountModel.findOneAndUpdate(
            { jigyoshacode, dateTimeStr },
            { $inc: { seq: 1 } },
            { runValidators: true, context: 'query', upsert: true, new: true }
        ).exec();
    
        return res?.seq || 0;
    } catch (error) {
        // Handle duplicate key error
        if (error.code === 11000) {
            // Duplicate key error, another process already created the record
            return 0;
        }
        throw error;
    }
};


//statics
transactionLogCountSchema.statics = {
    getNextSeq,
};

const TransactionLogCountModel = mongoose.connection.useDb(portalConfig.DB_NAME.ALADIN_SERVICE).model(
    'transaction_log_count',
    transactionLogCountSchema,
    'transaction_log_count'
);

module.exports = TransactionLogCountModel;
