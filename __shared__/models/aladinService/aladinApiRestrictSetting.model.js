const _ = require('lodash');
const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

/**
 * Check whether a value is null, undefined, NaN, empty string, empty array or empty object
 * (copied from baseHelper.js)
 * @param {any} v value to test
 */
const isNone = (v) => {
    return (
        v === null ||
        v === undefined ||
        v === '' ||
        (typeof v === 'number' && isNaN(v)) ||
        (Array.isArray(v) && v.length == 0) ||
        (typeof v === 'object' && v !== null && Object.keys(v).length == 0)
    );
};

/**
 * remove null value in object (copied from baseHelper.js)
 * @param  {object} object
 * @return {object}        - removed-null-value Object
 */
const removeNullValue = (object) => {
    let obj = _.cloneDeep(object);
    Object.keys(obj).forEach((key) => {
        if (obj[key] === undefined || obj[key] === null || obj[key] === '' || obj[key].length === 0) {
            delete obj[key];
        }
    });

    return obj;
};

const aladinApiRestrictSettingSchema = new Schema({
    kohaiEnableTenants: { type: [String] },
    lowPrioTenants: { type: [String] },
    maintenanceMode: { type: Boolean, default: false },
    lastUpdateUserId: { type: String, trim: true, required: true },
    updatedAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    createdDateTime: {
        type: Number,
        default: dayjs().unix(),
        set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()),
    },
});

/**
 * check tenant is Kohai enabled or not
 * @param {string} tenantId
 * @returns {boolean}
 */
const isKohaiEnabled = async (context, tenantId) => {
    if (!tenantId) throw new Error('tenantId is required');

    context.log('isKohaiEnabled START ' + tenantId);
    const result = await AladinApiRestrictSettingModel.findOne({ kohaiEnableTenants: tenantId }).exec();
    return result ? true : false;
};

/**
 *
 * @param {object} context
 * @returns {object|null} aladinApiRestrictSettingSchema or null
 */
const getAladinApiRestrictSetting = async (context) => {
    context.log('AladinApiRestrictSettingModel get START');
    return await AladinApiRestrictSettingModel.findOne({}).exec();
};

/**
 *
 * @param {object} context
 * @param {string} createdUserId
 * @param {Array<string>} kohaiEnableTenantsOpt
 * @param {Array<string>} lowPrioTenantsOpt
 * @returns {string} _id
 */
const createOrUpdate = async (context, createdUserId, kohaiEnableTenantsOpt, lowPrioTenantsOpt) => {
    context.log('AladinApiRestrictSettingModel createOrUpdate START');

    const nowSecs = dayjs().unix();

    const oldDataOpt = await getAladinApiRestrictSetting(context);

    let obj = {};
    obj.lastUpdateUserId = createdUserId;
    obj.updatedAt = nowSecs;
    obj.kohaiEnableTenants = kohaiEnableTenantsOpt;
    obj.lowPrioTenants = lowPrioTenantsOpt;

    if (!isNone(oldDataOpt)) {
        await AladinApiRestrictSettingModel.updateOne(
            { _id: oldDataOpt._id },
            { $set: obj },
            { runValidators: true }
        ).exec();
        return oldDataOpt._id.toString();
    } else {
        const aladinApiRestrictSetting = new AladinApiRestrictSettingModel(removeNullValue(obj));
        await aladinApiRestrictSetting.save();
        return aladinApiRestrictSetting._id.toString();
    }
};

/**
 * @param {object} context
 * @param {boolean} maintenanceMode
 */
const setMaintenanceMode = async (context, maintenanceMode) => {
    if (typeof maintenanceMode !== 'boolean') throw new Error('maintenanceMode must be a boolean');
    context.log('AladinApiRestrictSetting setMaintenance:', maintenanceMode);
    await AladinApiRestrictSettingModel.updateOne({}, { $set: { maintenanceMode } });
};

/**
 * A static method that is used to check if a tenant is low priority.
 * @param {string} tenantId
 * @returns {boolean} true if tenantId is found in the database
 */
const isLowPrio = async (tenantId) => {
    const result = await AladinApiRestrictSettingModel.findOne({
        lowPrioTenants: tenantId,
    }).exec();
    return result ? true : false;
};

//statics
aladinApiRestrictSettingSchema.statics = {
    isKohaiEnabled,
    getAladinApiRestrictSetting,
    createOrUpdate,
    setMaintenanceMode,
    isLowPrio,
};

const AladinApiRestrictSettingModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.ALADIN_SERVICE)
    .model('aladin_api_restrict_setting', aladinApiRestrictSettingSchema, 'aladin_api_restrict_setting');

module.exports = AladinApiRestrictSettingModel;
