const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const AppConfig = require('../../config/appConfig');

const portalConfig = AppConfig.getPortalConfig();

const AladinRequestOrderSchema = new Schema({
    requestOrderId: { type: String, trim: true, required: true },
    transactionType: { type: String, trim: true },
    tenantId: { type: String, trim: true },
    tempoId: { type: String, trim: true },
    daihyouBango: { type: String, trim: true },
    simShape: { type: String, trim: true },
    quantity: { type: Number, default: 0 },
    discountPlanOptions: [{ type: String, trim: true }],
    zaikoMakuhariComment: { type: String, trim: true },
    lastUpdateUserId: { type: String, trim: true },
    updatedAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    createdDateTime: { type: Number, default: dayjs().unix() },
});

/**
 * @param {object} context
 * @param {object} param
 * @param {string} param.createdUserId
 * @param {string} param.transactionType
 * @param {string} param.tenantId
 * @param {string} param.tempoId
 * @param {string} param.daihyouBango
 * @param {string} param.simShape
 * @param {number} param.quantity
 * @param {Array<string>} [param.discountPlanOptions]
 * @param {string} param.zaikoMakuhariComment
 */
const createRequestOrder = async (
    context,
    {
        createdUserId,
        transactionType,
        tenantId,
        tempoId,
        daihyouBango,
        simShape,
        quantity,
        discountPlanOptions,
        zaikoMakuhariComment,
    }
) => {
    const nowSecs = dayjs().unix();
    const newOrder = new AladinRequestOrderModel({
        lastUpdateUserId: createdUserId,
        updatedAt: nowSecs,
        transactionType,
        tenantId,
        tempoId,
        daihyouBango,
        simShape,
        quantity,
        zaikoMakuhariComment,
    });
    if (Array.isArray(discountPlanOptions)) {
        newOrder.discountPlanOptions = discountPlanOptions;
    }

    newOrder.requestOrderId = portalConfig.ALADIN_REQUEST_ORDER_PREFIX + newOrder._id;
    const saved = await newOrder.save();
    return saved.requestOrderId;
};

/**
 * This function finds and returns a document from the AladinRequestOrderModel collection that matches
 * the given requestOrderId.
 * @param context
 * @param requestOrderId 
 * @returns return a plain JavaScript object instead of a Mongoose document.
 */
const findByRequestOrderId = async (context, requestOrderId) => {
    return await AladinRequestOrderModel.findOne({ requestOrderId }).lean();
};

AladinRequestOrderSchema.statics = {
    createRequestOrder,
    findByRequestOrderId
};

const AladinRequestOrderModel = mongoose.model(
    'aladin_request_order',
    AladinRequestOrderSchema,
    'aladin_request_order'
);

module.exports = AladinRequestOrderModel;
