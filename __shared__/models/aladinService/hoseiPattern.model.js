const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/** @typedef {import('mongoose').HydratedDocumentFromSchema<HoseiPatternSchema>} HoseiPatternDocument */

const HoseiPatternSchema = new Schema({
    name: { type: String, trim: true, required: true },
    kouteiProcess: [{ type: Number }],
    beginKouteiId: { type: Number, required: true },
    isSwimmyPattern: { type: Boolean },
});

/**
 * @param {object} context
 * @param {number} lastKouteiId
 * @param {boolean} isSwimmyPattern
 */
const findPattern = async (context, lastKouteiId, isSwimmyPattern = false) => {
    const q = {
        beginKouteiId: { $gt: lastKouteiId },
    };
    if (isSwimmyPattern) {
        q.isSwimmyPattern = isSwimmyPattern;
    } else {
        q.isSwimmyPattern = { $ne: true };
    }

    return await HoseiPatternModel.find(q).sort({ _id: 1 }).exec();
};

HoseiPatternSchema.statics = {
    findPattern,
};

const HoseiPatternModel = mongoose.model('hosei_patern', HoseiPatternSchema, 'hosei_patern');
module.exports = HoseiPatternModel;
