const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { isNone } = require('../../helpers/baseHelper');

const cronMvnoTrafficsPlanSchema = new Schema({
    year: { type: Number, trim: true, required: true },
    month: { type: Number, trim: true, required: true },
    createdAt: { type: String, trim: true, required: true },
    timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    data: [
        {
            _id: false,
            month: { type: Number, trim: true },
            tenantId: { type: String, trim: true, required: true },
            planId: { type: Number },
            servicePlanId: { type: String },
            resalePlanId: { type: String },
            planName: { type: String },
            avgTrafficGB: { type: Number },
        },
    ],
    // for using in output of getPlanTrafficsCsvData
    tenantId: { type: String },
    servicePlanId: { type: String },
    planId: { type: Number },
    resalePlanId: { type: String },
    planName: { type: String },
});

/**
 * 
 * @param {object} context
 * @param {string} tenantId
 * @param {number} year
 * @param {number|null} month
 * @param {number|null} limit
 * @returns {<[cronMvnoTrafficsPlanSchema]|[]>}
 */
const getCronMvnoTrafficsPlan = async (context, tenantId, year, month, limit) => {
    context.log('CronMvnoTrafficsPlanModel getCronMvnoTrafficsPlan START ', tenantId, year, month, limit);
    if (!tenantId) throw new Error('tenantId is required');
    if (!year) throw new Error('year is required');

    const unwindObj = { $unwind: '$data' };
    let matchDBObj = {};
    matchDBObj.year = year;
    matchDBObj['data.tenantId'] = tenantId;
    const matchObj = { $match: matchDBObj };
    const sortObj = { $sort: { 'data.avgTrafficGB': -1 } };
    const defaultLimit = 20;
    const limitObj = !isNone(limit) ? { $limit: limit } : { $limit: defaultLimit };
    let pipeline = [];
    if (!isNone(month)) {
        matchDBObj.month = month;
        let groupDBObj = {};
        groupDBObj._id = '$_id';
        groupDBObj.year = { $first: '$year' };
        groupDBObj.month = { $first: '$month' };
        groupDBObj.timestamp = { $first: '$timestamp' };
        groupDBObj.data = { $push: '$data' };
        const groupObj = { $group: groupDBObj };
        pipeline.push(unwindObj, matchObj, sortObj, limitObj, groupObj);
    } else {
        let dataObj = {};
        dataObj.month = '$month';
        dataObj.tenantId = '$data.tenantId';
        dataObj.planId = '$data.planId';
        dataObj.servicePlanId = '$data.servicePlanId';
        dataObj.planName = '$data.planName';
        dataObj.avgTrafficGB = '$data.avgTrafficGB';
        let groupDBObj = {};
        groupDBObj._id = '$year';
        groupDBObj.year = { $first: '$year' };
        groupDBObj.data = { $push: dataObj };
        const groupObj = { $group: groupDBObj };
        pipeline.push(unwindObj, matchObj, sortObj, limitObj, groupObj);
    }
    const aggregateOptions = { allowDiskUse: true };
    return await CronMvnoTrafficsPlanModel.aggregate(pipeline, aggregateOptions).exec();
};

/**
* 
* @param {object} context
* @param {number} year
* @param {number} month
* @returns {<[cronMvnoTrafficsPlanSchema]|[]>}
*/
const getPlanTrafficsCsvData = async (context, year, month) => {
    context.log('CronMvnoTrafficsPlanModel getPlanTrafficsCsvData START ', year, month);
    if (!year) throw new Error('year is required');
    if (!month) throw new Error('month is required');

    // date with first day of month
    const dateStr1 = year + '/' + month + '/' + 1;
    // convert date to unix time format
    const paramMonthTimestamp = Math.floor(new Date(dateStr1).getTime() / 1000);
    // get timestamp of last 3 months of param month
    let dateStr2 = new Date(dateStr1);
    dateStr2.setMonth(dateStr2.getMonth() - 3);
    const lastThreeMonthTimestamp = Math.floor(dateStr2.getTime() / 1000);
    
    const unwindObj = { $unwind: '$data' };
    const tStamp = {
        $gte: lastThreeMonthTimestamp,
        $lt: paramMonthTimestamp,
    };
    
    const matchObj = { $match: { timestamp: tStamp } };
    const groupObj1 = {
        $group: {
            _id: {
                tenantId: '$data.tenantId',
                servicePlanId: '$data.servicePlanId',
                resalePlanId: '$data.resalePlanId',
                planId: '$data.planId',
                planName: '$data.planName',
                month: '$month',
            },
            totalAvgTrafficGB: { $sum: '$data.avgTrafficGB' }
        }
    };
    let data = {};
    data.month = '$_id.month';
    data.avgTrafficGB = '$totalAvgTrafficGB';
    const groupObj2 = {
        $group: {
            _id: {
                tenantId: '$_id.tenantId',
                servicePlanId: '$_id.servicePlanId',
                resalePlanId: '$_id.resalePlanId',
                planId: '$_id.planId',
                planName: '$_id.planName',
            },
            tenantId: { $first: '$_id.tenantId' },
            servicePlanId: { $first: '$_id.servicePlanId' },
            resalePlanId: { $first: '$_id.resalePlanId' },
            planId: { $first: '$_id.planId' },
            planName: { $first: '$_id.planName' },
            data: { $push: data },
        }
    };
    const sortObj = { $sort: { tenantId: 1 } };
    const pipeline = [unwindObj, matchObj, groupObj1, groupObj2, sortObj];
    const aggregateOptions = { allowDiskUse: true };
    return await CronMvnoTrafficsPlanModel.aggregate(pipeline, aggregateOptions).exec();
};

/**
 * This function checks if data exists in a MongoDB collection based on specified parameters.
 * @param context - It is a variable that represents the execution context of the function. It is not
 * used in the function and can be ignored.
 * @returns The function `checkExistsData` is returning the result of the `findOne`
 */
const checkExistsData = async (context, { year, month, day }) => {
    let conObj = {};
    if (year) conObj.year = year;
    if (month) conObj.month = month;
    if (day) conObj.day = day;

    return await CronMvnoTrafficsPlanModel.findOne(conObj).exec();
};

//statics
cronMvnoTrafficsPlanSchema.statics = {
    getCronMvnoTrafficsPlan,
    getPlanTrafficsCsvData,
    checkExistsData
};

const CronMvnoTrafficsPlanModel = mongoose.model('cron_mvno_traffics_plan', cronMvnoTrafficsPlanSchema, 'cron_mvno_traffics_plan');

module.exports = CronMvnoTrafficsPlanModel;
