const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { MAWP_ROLE_ID } = require('../../constants/userRole');
const Schema = mongoose.Schema;
const baseHelper = require('../../helpers/baseHelper');

const tanaoroshiNoticeSchema = new Schema({
    msgtitle: { type: String, trim: true, required: true },
    msgbody: { type: String, trim: true, required: true },
    tenantID: { type: [String] },
    tempoID: { type: [String] },
    daihyo_user: { type: Boolean },
    daihyo_users: { type: [String] },
    zentenant: { type: Boolean },
    zentempo: { type: Boolean },
    enabled: { type: Boolean, required: true },
    removed: { type: Boolean },
    timestamp: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
});

const allNotices = async () => {
    return await TanaoroshiNoticeModel.find({ removed: { $ne: true } }).sort({ _id: -1 });
};

const createNotice = async ({
    timestamp,
    msgtitle,
    msgbody,
    tenantId,
    tempoId,
    enabled,
    daihyoUser,
    zentenant,
    zentempo,
}) => {
    const doc = new TanaoroshiNoticeModel({
        timestamp: timestamp,
        msgtitle: msgtitle,
        msgbody: msgbody,
        enabled: enabled,
        daihyo_user: daihyoUser,
        zentenant: zentenant,
        zentempo: zentempo,
        tenantID: tenantId,
        tempoID: tempoId,
        daihyo_users: [],
    });

    await doc.save();

    return doc._id;
};

const allNoticeByTempo = async (context, noticeDateFrom, noticeDateTo, tenantId, tempoId, roleId) => {
    let query = {
        removed: { $ne: true },
        enabled: { $eq: true },
    };
    const sort = { _id: -1 };

    const tenantCondition = { tenantID: { $elemMatch: { $eq: tenantId } } };
    const zentenantCondition = { zentenant: true };
    const zentempoCondition = { zentempo: true };

    if (roleId !== MAWP_ROLE_ID.TENANT_DAIHYO_USER) {
        const tempoCondition = { tempoID: { $elemMatch: { $eq: tempoId.substring(0, 6) } } };
        query = {
            ...query,
            $and: [{ $or: [tenantCondition, zentenantCondition] }, { $or: [tempoCondition, zentempoCondition] }],
        };
    } else {
        query = { ...query, daihyo_user: true, $or: [tenantCondition, zentenantCondition] };
    }

    const endCal = dayjs().startOf('day').add(1, 'day').unix();

    // The first form loading
    let timestampCondition = { timestamp: { $lt: endCal } };

    if (!baseHelper.isNone(noticeDateFrom) && !baseHelper.isNone(noticeDateTo)) {
        if (noticeDateTo > endCal) {
            timestampCondition = { timestamp: { $gte: noticeDateFrom, $lte: endCal } };
        } else {
            timestampCondition = { timestamp: { $gte: noticeDateFrom, $lte: noticeDateTo } };
        }
    } else if (!baseHelper.isNone(noticeDateFrom) && baseHelper.isNone(noticeDateTo)) {
        timestampCondition = { timestamp: { $gte: noticeDateFrom, $lte: endCal } };
    } else if (baseHelper.isNone(noticeDateFrom) && !baseHelper.isNone(noticeDateTo)) {
        timestampCondition = { timestamp: { $lte: endCal } };
    }
    query = { ...query, ...timestampCondition };

    context.log('allNoticeByTempo query:', JSON.stringify(query));

    return await TanaoroshiNoticeModel.find(query).sort(sort);
};

const findSameJSTDateEnabledNotices = async (context, { timestamp, tenantId, tempoId, userId, roleId }) => {
    const startOfTheDay = dayjs(timestamp).startOf('day').unix();
    const endOfTheDay = dayjs.unix(startOfTheDay).add(1, 'day').unix();

    const tenantCondition = { tenantID: { $elemMatch: { $eq: tenantId } } };
    const tempoCondition = { tempoID: { $elemMatch: { $eq: tempoId.substring(0, 6) } } };
    const zentenantCondition = { zentenant: true };
    const zentempoCondition = { zentempo: true };

    const sort = { _id: -1 };

    let query = {
        enabled: true,
        removed: { $ne: true },
    };

    if (roleId === MAWP_ROLE_ID.TEMPO_USER) {
        query = {
            ...query,
            daihyo_user: { $ne: true },
            timestamp: { $gte: startOfTheDay, $lt: endOfTheDay },
            $and: [{ $or: [tenantCondition, zentenantCondition] }, { $or: [tempoCondition, zentempoCondition] }],
        };
    } else {
        query = {
            ...query,
            daihyo_user: { $eq: true },
            daihyo_users: {
                $nin: [`${tenantId}-${userId}`],
                $exists: true,
            },
            // STEP21: 代表ユーザが未読であれば取得する
            // リファクタリングSTEP4.0: お知らせのポップアップが通知日より前に出て来ないように
            // https://mobilus.backlog.jp/view/MVNO_N_M-2761
            timestamp: { $lt: endOfTheDay },
            $or: [tenantCondition, zentenantCondition],
        };
    }

    context.log('findSameJSTDateEnabledNotices query:', JSON.stringify(query));

    return await TanaoroshiNoticeModel.find(query).sort(sort);
};

/**
 * original: enableOrDisableNotice
 */
const switchNoticeState = async (noticeId, enabled) => {
    await TanaoroshiNoticeModel.updateOne({ _id: noticeId }, { enabled: enabled });
};

const deleteNotice = async (noticeId) => {
    await TanaoroshiNoticeModel.updateOne({ _id: noticeId }, { removed: true });
};

/**
 * Add daihyo user info to notice
 * Old: daihyoCheckRead. modified for update many notices once
 * @param {object} context
 * @param {Array<string>} noticeIds
 * @param {string} tenantId
 * @param {string} userId
 * @returns {Promise<*>}
 */
const addDaihyoUser = async (context, { noticeIds, tenantId, userId }) => {
    context.log('TanaoroshiNoticeModel addDaihyoUser START ', noticeIds, tenantId, userId);

    return await TanaoroshiNoticeModel.updateMany(
        {
            _id: { $in: noticeIds },
        },
        {
            // NOTE: Change using $push to $addToSet to avoid duplicate in daihyo_users array
            $addToSet: {
                daihyo_users: `${tenantId}-${userId}`,
            },
        }
    );
};

tanaoroshiNoticeSchema.statics = {
    allNotices,
    allNoticeByTempo,
    findSameJSTDateEnabledNotices,
    createNotice,
    switchNoticeState,
    deleteNotice,
    addDaihyoUser,
};

const TanaoroshiNoticeModel = mongoose.model('tanaoroshi_notice', tanaoroshiNoticeSchema, 'tanaoroshi_notice');

module.exports = TanaoroshiNoticeModel;
