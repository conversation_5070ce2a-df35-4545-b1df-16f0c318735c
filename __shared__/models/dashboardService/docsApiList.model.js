const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const docsApiListSchema = new Schema({
    no: { type: String, trim: true, required: true },
    name: { type: String },
    method: { type: String, trim: true, required: true },
    url: { type: String, trim: true, required: true },
    description: { type: String },
    requestParams: { type: Array, default: [] },
    response: { type: Array, default: [] },
    process: { type: Array, default: [] },
    index: { type: Number },
    status: { type: Boolean, required: true, default: false },
    createdAt: { type: Number, default: dayjs(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    updatedAt: { type: Number, default: dayjs(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
});

/**
 * It returns all the documents from the collection.
 * @returns An array of objects
 */
const getAllDocsApi = async () => {
    const query = {
        status: true,
    };
    return await DocsApiListModel.find(query).sort({ no: 1 }).lean();
};

const createDocsApi = async ({
    no,
    name,
    method,
    url,
    description,
    requestParams,
    response,
    process,
    index,
    status,
}) => {
    const doc = new DocsApiListModel({
        no: no,
        name: name,
        method: method,
        url: url,
        description: description,
        requestParams: requestParams,
        response: response,
        process: process,
        index: index,
        status: status,
    });
    await doc.save();
};

const editDocsApi = async ({
    no,
    oldNo,
    name,
    method,
    url,
    description,
    requestParams,
    response,
    process,
    index,
    status,
}) => {
    const query = {
        no: oldNo,
        status: true,
    };

    await DocsApiListModel.updateOne(query, {
        $set: {
            no: no,
            name: name,
            method: method,
            url: url,
            description: description,
            requestParams: requestParams,
            response: response,
            process: process,
            index: index,
            status: status,
        },
    });
};

const deleteDocsApi = async (no) => {
    const query = {
        no: no,
        status: true,
    };

    const doc = await DocsApiListModel.findOneAndUpdate(query, { $set: { status: false } });

    if (doc !== null && doc.no !== undefined) {
        return doc.no;
    } else {
        return null;
    }
};

const searchNoByStatusTrue = async (no, status) => {
    const query = {
        no: no,
        status: status,
    };
    const doc = await DocsApiListModel.findOne(query);
    if (doc !== null && doc.no !== undefined) {
        return doc.no;
    } else {
        return null;
    }
};

//statics
docsApiListSchema.statics = {
    getAllDocsApi,
    createDocsApi,
    deleteDocsApi,
    editDocsApi,
    searchNoByStatusTrue,
};

const DocsApiListModel = mongoose.model('docs_api_list', docsApiListSchema, 'docs_api_list');

module.exports = DocsApiListModel;
