const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const tanaoroshiMsgSchema = new Schema({
    msg_1: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_2: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_3: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_4: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_5: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_6: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_7: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_8: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_9: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
    msg_10: { title: { type: String, trim: true, required: true }, body: { type: String, trim: true, required: true } },
});

const getAllMessages = async () => {
    return await TanaoroshiMsgModel.findOne({});
};

const updateMsgs = async (msgs) => {
    return await TanaoroshiMsgModel.updateOne({}, { $set: msgs });
};

tanaoroshiMsgSchema.statics = {
    getAllMessages,
    updateMsgs,
};

const TanaoroshiMsgModel = mongoose.model('tanaoroshi_msg', tanaoroshiMsgSchema, 'tanaoroshi_msg');

module.exports = TanaoroshiMsgModel;
