const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { MVNO_SERVICES } = require('../../constants/mvnoServicesConstants');
const { isNone, removeNullValue } = require('../../helpers/baseHelper');

const searchConditionSchema = new Schema({
    patternName: { type: String, trim: true, required: true },
    searchType: { type: Number },
    searchValue: { type: String },
    fromDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    toDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    simStatus: { type: String },
    simType: { type: String },
    simYouto: { type: String },
    simContractType: { type: String },
    kaisenOptionFlag: { type: String },
    tenantId: { type: Array },
    tempoId: { type: Array },
    statusId: { type: Array },
    processId: { type: Array },
    hankuro: { type: String },
    soKind: { type: String },
    deliveryFlag: { type: String },
    sameMvneInFlag: { type: String },
    patternType: { type: String },
    service: { type: Number },
    lastUpdatedDateTime: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    lastUpdatedUserName: { type: String, trim: true, required: true },
    // STEP18.0 5G契約種別
    fiveGContractType: { type: String },
    // STEP20.0 在庫ステータス変更日時
    fromZaikoStatusChangeDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    toZaikoStatusChangeDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
});

/**
 * 検索パターン一覧
 * @param {object} context
 * @returns {<[searchConditionSchema]|[]>}
 */
const getSearchPattern = async (context) => {
    context.log('SearchConditionModel getSearchPattern START');
    return await SearchConditionModel.find().sort({ _id: 1 }).exec();
};

/**
 * 検索条件保存
 * @param {object} context
 * @param {object} params
 */
const saveSearchCondition = async (context, params) => {
    context.log('SearchConditionModel saveSearchCondition START ', params);
    if (!params) throw new Error('params is required');

    let obj = {};
    obj.patternName = params.newPatternName;
    obj.patternType = params.patternType;
    obj.lastUpdatedUserName = params.lastUpdatedUserName;
    obj.lastUpdatedDateTime = dayjs().unix();
    obj.service = !isNone(params.service) ? params.service : MVNO_SERVICES.ALL;
    if (!isNone(params.searchValue)) {
        obj.searchValue = params.searchValue;
    }
    if (!isNone(params.fromDate)) {
        obj.fromDate = params.fromDate;
    }
    if (!isNone(params.toDate)) {
        obj.toDate = params.toDate;
    }
    if (!isNone(params.tenantId)) {
        obj.tenantId = params.tenantId;
    }
    if (!isNone(params.tempoId)) {
        obj.tempoId = params.tempoId;
    }
    if (params.patternType === 'SO') {
        obj.searchType = !isNone(params.searchType) ? params.searchType : 4;
        if (!isNone(params.kohaiFlag)) {
            obj.deliveryFlag = params.kohaiFlag;
        }
        if (!isNone(params.sameMvneInFlag)) {
            obj.sameMvneInFlag = params.sameMvneInFlag === true ? '1' : '0';
        }
        if (!isNone(params.hankuro)) {
            obj.hankuro = params.hankuro;
        }
        if (!isNone(params.soKind)) {
            obj.soKind = params.soKind;
        }
        if (!isNone(params.processId)) {
            obj.processId = params.processId;
        }
    } else {
        obj.searchType = !isNone(params.searchType) ? params.searchType : 3;
        if (!isNone(params.kaisenOptionFlag)) {
            obj.kaisenOptionFlag = params.kaisenOptionFlag === true ? '1' : '0';
        }
        if (!isNone(params.simStatus)) {
            obj.simStatus = params.simStatus;
        }
        if (!isNone(params.simType)) {
            obj.simType = params.simType;
        }
        if (!isNone(params.simContractType)) {
            obj.simContractType = params.simContractType;
        }
        if (!isNone(params.simYouto)) {
            obj.simYouto = params.simYouto;
        }
        if (!isNone(params.statusId)) {
            obj.statusId = params.statusId;
        }
        // STEP18.0 5G契約種別
        if (!isNone(params.fiveGContractType)) {
            obj.fiveGContractType = params.fiveGContractType;
        }
        // STEP20.0 在庫ステータス変更日時
        if (!isNone(params.zaikoFromDate)) {
            obj.fromZaikoStatusChangeDate = params.zaikoFromDate;
        }
        if (!isNone(params.zaikoToDate)) {
            obj.toZaikoStatusChangeDate = params.zaikoToDate;
        }
    }
    let query = { patternName: params.oldPatternName };
    query.patternType = params.patternType;
    const doc = await SearchConditionModel.findOne(query).exec();
    if (!isNone(doc)) {
        obj._id = doc._id;
    }
    await SearchConditionModel.remove(query).exec();
    const conObj = new SearchConditionModel(removeNullValue(obj));
    await conObj.save();
};

//statics
searchConditionSchema.statics = {
    getSearchPattern,
    saveSearchCondition,
};

const SearchConditionModel = mongoose.model('search_condition', searchConditionSchema, 'search_condition');

module.exports = SearchConditionModel;
