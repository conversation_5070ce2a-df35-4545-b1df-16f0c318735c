const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const docsInformationSchema = new Schema({
    code: { type: String, trim: true, required: true },
    title: { type: String, trim: true, required: true },
    content: { type: String, trim: false, required: true },
    status: { type: Boolean, required: true, default: false },
    updatedBy: { type: String, trim: true, required: true },
    updatedAt: { type: Number, default: dayjs(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
});

const getOneInformation = async (code, status) => {
    const query = {
        code: code,
        status: status,
    };

    const doc = await DocsInformationModel.findOne(query);

    if (doc !== null) {
        return doc;
    } else {
        return null;
    }
};

const updateContent = async (code, content, status) => {
    const query = {
        code: code,
    };

    const updated = {
        content: content,
        status: status,
        updatedAt: dayjs(),
    };

    const doc = await DocsInformationModel.findOneAndUpdate(query, updated, { new: true });

    if (doc !== null && doc.content !== undefined) {
        return doc.content;
    } else {
        return null;
    }
};

docsInformationSchema.statics = {
    getOneInformation,
    updateContent,
};

const DocsInformationModel = mongoose.model('docs_information', docsInformationSchema, 'docs_information');

module.exports = DocsInformationModel;
