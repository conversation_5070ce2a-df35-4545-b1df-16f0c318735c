const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const dayjs = require('dayjs');

// add this so the empty user.plusId can be acceptable
const allowEmptyString = function () {
    return typeof this.createdBy === 'string' ? false : true;
};

const faqSchema = new Schema({
    faq_id: { type: String, trim: true },
    question: { type: String, trim: true, required: true },
    answer: { type: String, trim: true, required: true },
    tags: { type: [String], trim: true, required: true },
    createdBy: { type: String, trim: true, required: allowEmptyString },
    createdDateTime: { type: Number, default: dayjs(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    updatedBy: { type: String, trim: true },
    updatedDateTime: { type: Number, default: dayjs(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    deleted: { type: String, trim: true },
    deletedBy: { type: String, trim: true },
    deletedDateTime: { type: Number },
});

/**
 * original: search
 */
const getAll = async () => {
    return await FAQModel.find({ deleted: { $ne: true } });
};

const updateQuestion = async ({ faqId, userId, question, answer, tags }) => {
    const query = {
        faq_id: faqId,
        deleted: { $ne: true },
    };

    const updated = {
        question: question,
        answer: answer,
        tags: [...new Set(tags)],
        updatedBy: userId,
        updatedDateTime: dayjs(),
    };

    return await FAQModel.findOneAndUpdate(query, updated, { new: true });
};

const create = async (question, answer, userId, tags) => {
    const faq = new FAQModel({
        question: question,
        answer: answer,
        tags: tags,
        createdBy: userId,
    });

    const doc = await faq.save();
    const faqId = `FAQ${doc._id}`;

    // in scala, it returns id here but it is meaningless so just let it return an updated one
    const updated = await FAQModel.findByIdAndUpdate(doc._id, { faq_id: faqId }, { new: true });
    return updated;
};

const deleteQuestion = async (faqId, userId) => {
    const query = {
        faq_id: faqId,
    };

    const updated = {
        deleted: true,
        deletedBy: userId,
        deletedDateTime: dayjs(),
    };

    const doc = await FAQModel.updateOne(query, { $set: updated });

    return doc.modifiedCount === 1;
};

faqSchema.statics = {
    getAll,
    updateQuestion,
    create,
    deleteQuestion,
};

const FAQModel = mongoose.model('faq', faqSchema, 'faq');

module.exports = FAQModel;
