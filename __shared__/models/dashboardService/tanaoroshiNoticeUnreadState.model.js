const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const baseHelper = require('../../helpers/baseHelper');

const tenaoroshiNoticeUnreadStateSchema = new Schema({
    notice_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    unread: { type: Boolean, default: false },
    timestamp: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    mawp_user_flag: { type: Boolean, default: false },
    tenantId: { type: String, trim: true, required: true },
    tempoId: { type: String, trim: true },
    userId: { type: String, trim: true, required: true },
});

const createEntryForNoticeCreate = async ({ noticeId, tenantId, tempoId, mawpUserFlag, userId }) => {
    const doc = new TanaoroshiNoticeUnreadStateModel({
        notice_id: mongoose.Types.ObjectId(noticeId),
        unread: true,
        mawp_user_flag: mawpUserFlag,
        tenantId: tenantId,
        tempoId: tempoId,
        userId: userId,
    });
    await doc.save();
};

/**
 *
 * @param {Array<{ noticeId:string, tenantId:string, tempoId:string, mawpUserFlag:boolean, userId:string }>} docs
 */
const createEntryForNoticeCreateMultiple = (docs) => {
    const data = docs.map((d) => ({
        notice_id: mongoose.Types.ObjectId(d.noticeId),
        unread: true,
        mawp_user_flag: d.mawpUserFlag,
        tenantId: d.tenantId,
        tempoId: d.tempoId,
        userId: d.userId,
    }));
    return new Promise((resolve, reject) => {
        TanaoroshiNoticeUnreadStateModel.insertMany(data, { rawResult: true })
            .then((result) => resolve(result))
            .catch((err) => reject(err));
    });
};

const clearUnread = async ({ noticeId, tenantId, tempoId, mawpUserFlag, userId }) => {
    const now = dayjs().unix();
    const objId = mongoose.Types.ObjectId(noticeId);

    const query = {
        notice_id: objId,
        tenantId: tenantId,
        userId: userId,
    };

    const updated = {
        unread: false,
        timestamp: now,
    };

    const updatedDoc = await TanaoroshiNoticeUnreadStateModel.findOneAndUpdate(query, updated);

    if (baseHelper.isNone(updatedDoc)) {
        const doc = new TanaoroshiNoticeUnreadStateModel({
            notice_id: objId,
            unread: false,
            timestamp: now,
            mawp_user_flag: mawpUserFlag,
            tenantId: tenantId,
            tempoId: tempoId || undefined,
            userId: userId,
        });

        await doc.save();
    }
};

tenaoroshiNoticeUnreadStateSchema.statics = {
    createEntryForNoticeCreate,
    createEntryForNoticeCreateMultiple,
    clearUnread,
};

const TanaoroshiNoticeUnreadStateModel = mongoose.model(
    'tanaoroshi_notice_unread_state',
    tenaoroshiNoticeUnreadStateSchema,
    'tanaoroshi_notice_unread_state'
);

module.exports = TanaoroshiNoticeUnreadStateModel;
