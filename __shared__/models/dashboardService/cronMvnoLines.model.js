const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { isNone } = require('../../helpers/baseHelper');

const cronMvnoLinesSchema = new Schema({
    year: { type: Number, trim: true, required: true },
    month: { type: Number, trim: true, required: true },
    day: { type: Number, trim: true, required: true },
    date: { type: String },
    createdAt: { type: String },
    timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    data: { type: Object },
});

/**
 *
 * @param {object} context
 * @param {string} tenantId
 * @param {number} year
 * @param {number | null} month
 * @returns {<[cronMvnoLinesSchema]|[]>}
 */
const getCronMvnoLines = async (context, tenantId, year, month) => {
    context.log('CronMvnoLinesModel getCronMvnoLines START ', tenantId, year, month);
    if (!tenantId) throw new Error('tenantId is required');
    if (!year) throw new Error('year is required');

    let query1 = {};
    query1.year = year;
    if (!isNone(month)) {
        query1.month = month;
    }
    query1[`data.${tenantId}`] = { $exists: true };
    let query2 = {};
    query2._id = 0;
    query2[`data.${tenantId}`] = 1;
    query2.timestamp = 1;
    query2.year = 1;
    query2.month = 1;
    query2.day = 1;
    const sort = { day: 1 };
    return await CronMvnoLinesModel.find(query1, query2).sort(sort).exec();
};

/**
 * This function checks if data exists in a MongoDB collection based on specified parameters.
 * @param context - It is a variable that represents the execution context of the function. It is not
 * used in the function and can be ignored.
 * @returns The function `checkExistsData` is returning the result of the `findOne`
 */
const checkExistsData = async (context, { year, month, day }) => {
    let conObj = {};
    if (year) conObj.year = year;
    if (month) conObj.month = month;
    if (day) conObj.day = day;

    return await CronMvnoLinesModel.findOne(conObj).exec();
};

//statics
cronMvnoLinesSchema.statics = {
    getCronMvnoLines,
    checkExistsData,
};

const CronMvnoLinesModel = mongoose.model('cron_mvno_lines', cronMvnoLinesSchema, 'cron_mvno_lines');

module.exports = CronMvnoLinesModel;
