const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { isNone } = require('../../helpers/baseHelper');

const cronMvnoTrafficsUserSchema = new Schema({
    year: { type: Number, trim: true, required: true },
    month: { type: Number, trim: true, required: true },
    createdAt: { type: String, trim: true, required: true },
    timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    data: [
        {
            _id: false,
            month: { type: Number, trim: true },
            tenantId: { type: String, trim: true, required: true },
            lineId: { type: String },
            trafficGB: { type: Number },
        },
    ],
});

/**
 * 
 * @param {object} context
 * @param {string} tenantId
 * @param {number} year
 * @param {number|null} month
 * @param {number|null} limit
 * @returns {<[cronMvnoTrafficsUserSchema]|[]>}
 */
const getCronMvnoTrafficsUser = async (context, tenantId, year, month, limit) => {
    context.log('CronMvnoTrafficsUserModel getCronMvnoTrafficsUser START ', tenantId, year, month, limit);
    if (!tenantId) throw new Error('tenantId is required');
    if (!year) throw new Error('year is required');

    const unwindObj = { $unwind: '$data' };
    let matchDBObj = {};
    matchDBObj.year = year;
    if (tenantId !== "ALL") {
        matchDBObj['data.tenantId'] = tenantId;
    }
    const matchObj = { $match: matchDBObj };
    const sortObj = { $sort: { 'data.trafficGB': -1 } };
    const defaultLimit = 20;
    const limitObj = { $limit: !isNone(limit) ? limit : defaultLimit };
    let groupDBObj = {};
    groupDBObj._id = '$_id';
    groupDBObj.year = { $first: '$year' };
    groupDBObj.month = { $first: '$month' };
    groupDBObj.timestamp = { $first: '$timestamp' };
    groupDBObj.data = { $push: '$data' };
    const groupObj = { $group: groupDBObj };
    let pipeline = [];
    if (!isNone(month)) {
        matchDBObj.month = month;
        pipeline.push(unwindObj, matchObj, sortObj, limitObj, groupObj);
    } else {
        let dataObj = {};
        dataObj.month = '$month';
        dataObj.tenantId = '$data.tenantId';
        dataObj.lineId = '$data.lineId';
        dataObj.trafficGB = '$data.trafficGB';
        let groupDBObj = {};
        groupDBObj._id = '$year';
        groupDBObj.year = { $first: '$year' };
        groupDBObj.data = { $push: dataObj };
        const groupObj = { $group: groupDBObj };
        pipeline.push(unwindObj, matchObj, sortObj, limitObj, groupObj);
    }
    const aggregateOptions = { allowDiskUse: true };
    return await CronMvnoTrafficsUserModel.aggregate(pipeline, aggregateOptions).exec();
};

/**
 * This function checks if data exists in a MongoDB collection based on specified parameters.
 * @param context - It is a variable that represents the execution context of the function. It is not
 * used in the function and can be ignored.
 * @returns The function `checkExistsData` is returning the result of the `findOne`
 */
const checkExistsData = async (context, { year, month, day }) => {
    let conObj = {};
    if (year) conObj.year = year;
    if (month) conObj.month = month;
    if (day) conObj.day = day;

    return await CronMvnoTrafficsUserModel.findOne(conObj).exec();
};

//statics
cronMvnoTrafficsUserSchema.statics = {
    getCronMvnoTrafficsUser,
    checkExistsData
};

const CronMvnoTrafficsUserModel = mongoose.model('cron_mvno_traffics_user', cronMvnoTrafficsUserSchema, 'cron_mvno_traffics_user');

module.exports = CronMvnoTrafficsUserModel;
