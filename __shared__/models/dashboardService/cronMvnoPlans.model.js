const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { isNone } = require('../../helpers/baseHelper');

const cronMvnoPlansSchema = new Schema({
    year: { type: Number, trim: true, required: true },
    month: { type: Number, trim: true, required: true },
    day: { type: Number, trim: true, required: true },
    createdAt: { type: String, trim: true, required: true },
    timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    data: [
        {
            _id: false,
            tenantId: { type: String, trim: true, required: true },
            planId: { type: Number },
            servicePlanId: { type: String },
            resalePlanId: { type: String },
            planName: { type: String },
            count: { type: Number, required: true },
        },
    ],
});

/**
 * 
 * @param {object} context
 * @param {object} mvnoPlans
 * @returns {<[cronMvnoPlansSchema]|[]>}
 */
const getCronMvnoPlans = async (context, mvnoPlans) => {
    context.log('CronMvnoPlansModel getCronMvnoPlans START ', mvnoPlans);
    if (!mvnoPlans) throw new Error('mvnoPlans is required');

    const unwindObj = { $unwind: '$data' };
    let matchDBObj = {};
    if (mvnoPlans.tenantId !== "ALL") {
        matchDBObj['data.tenantId'] = mvnoPlans.tenantId;
    }
    matchDBObj.year = mvnoPlans.year;
    matchDBObj.month = mvnoPlans.month;
    if (!isNone(mvnoPlans.day)) {
        matchDBObj.day = mvnoPlans.day;
    }
    const matchObj = { $match: matchDBObj };
    const sortObj1 = { $sort: { 'data.count': -1 } };
    const limit = !isNone(mvnoPlans.limit) ? mvnoPlans.limit : 20;
    const limitObj = { $limit: limit };
    let groupDBObj = {};
    groupDBObj._id = '$_id';
    groupDBObj.year = { $first: '$year' };
    groupDBObj.month = { $first: '$month' };
    groupDBObj.day = { $first: '$day' };
    groupDBObj.timestamp = { $first: '$timestamp' };
    groupDBObj.data = { $push: '$data' };
    const groupObj = { $group: groupDBObj };
    const sortObj2 = { $sort: { day: 1 } };
    const pipeline = !isNone(mvnoPlans.day)
        ? [unwindObj, matchObj, sortObj1, limitObj, groupObj, sortObj2]
        : [unwindObj, matchObj, sortObj1, groupObj, sortObj2];
    const aggregateOptions = {
        allowDiskUse: true
    };
    return await CronMvnoPlansModel.aggregate(pipeline, aggregateOptions).exec();
};

/**
* 
* @param {object} context
* @param {number} year
* @param {number} month
* @param {number|null} day
* @returns {<[cronMvnoPlansSchema]|[]>}
*/
const getPlanCountCsvData = async (context, year, month, day) => {
    context.log('CronMvnoPlansModel getPlanCountCsvData START ', year, month, day);
    if (!year) throw new Error('year is required');
    if (!month) throw new Error('month is required');

    const unwindObj = { $unwind: '$data' };
    let matchDBObj = {};
    matchDBObj.year = year;
    matchDBObj.month = month;
    if (!isNone(day)) {
        matchDBObj.day = day;
    }
    const matchObj = { $match: matchDBObj };
    const sortObj1 = { $sort: { 'data.tenantId': 1 } };
    let groupDBObj = {};
    groupDBObj._id = '$_id';
    groupDBObj.year = { $first: '$year' };
    groupDBObj.month = { $first: '$month' };
    groupDBObj.day = { $first: '$day' };
    groupDBObj.timestamp = { $first: '$timestamp' };
    groupDBObj.data = { $push: '$data' };
    const groupObj = { $group: groupDBObj };
    const sortObj2 = { $sort: { day: 1 } };
    const pipeline = [unwindObj, matchObj, sortObj1, groupObj, sortObj2];
    const aggregateOptions = { allowDiskUse: true };
    return await CronMvnoPlansModel.aggregate(pipeline, aggregateOptions).exec();
};

/**
 * This function checks if data exists in a MongoDB collection based on specified parameters.
 * @param context - It is a variable that represents the execution context of the function. It is not
 * used in the function and can be ignored.
 * @returns The function `checkExistsData` is returning the result of the `findOne`
 */
const checkExistsData = async (context, { year, month, day }) => {
    let conObj = {};
    if (year) conObj.year = year;
    if (month) conObj.month = month;
    if (day) conObj.day = day;

    return await CronMvnoPlansModel.findOne(conObj).exec();
};

//statics
cronMvnoPlansSchema.statics = {
    getCronMvnoPlans,
    getPlanCountCsvData,
    checkExistsData
};

const CronMvnoPlansModel = mongoose.model('cron_mvno_plans', cronMvnoPlansSchema, 'cron_mvno_plans');

module.exports = CronMvnoPlansModel;
