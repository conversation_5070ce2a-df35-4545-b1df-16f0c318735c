const dayjs = require('dayjs');
const mongoose = require('mongoose');

const Schema = mongoose.Schema;

const calcMvnoLoginOkSchema = new Schema({
    randomId: {
        type: String,
        trim: true,
    },
    tenantId: {
        type: String,
        trim: true,
    },
    userId: {
        type: String,
        trim: true,
    },
    roleId: {
        type: Number,
    },
    modifyDate: {
        type: Number,
    },
});

/**
 * @param {object} context
 * @param {object} param
 * @param {string} param.randomId
 * @param {string} param.tenantId
 * @param {string} param.userId
 * @param {number} param.roleId
 * @returns {Promise<CalcMvnoLoginOkModel>}
 */
const createOrUpdate = async (context, { randomId, tenantId, userId, roleId }) => {
    context.log('CalcMvnoLoginOkModel createOrUpdate START');

    const filter = {
        tenantId,
        userId,
    };
    const update = {
        randomId,
        roleId,
        modifyDate: dayjs().unix(),
    };

    return await CalcMvnoLoginOkModel.updateOne(filter, update, { upsert: true });
};

//statics
calcMvnoLoginOkSchema.statics = { createOrUpdate };

const CalcMvnoLoginOkModel = mongoose.model('calc_mvnologin_ok', calcMvnoLoginOkSchema, 'calc_mvnologin_ok');

module.exports = CalcMvnoLoginOkModel;
