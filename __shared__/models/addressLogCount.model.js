const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const addressLogCountSchema = new Schema({
    tenantId: {
        type: String,
        trim: true,
        required: true,
    },
    dateTimeStr: {
        type: String,
        trim: true,
        required: true,
    },
    seq: {
        type: Number,
        required: true,
    },
    createdDateTime: {
        type: Number,
        default: dayjs().unix(),
        set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()),
    },
});

/**
 * get coresponded seq value
 * (if doc not found increase seq by 1 then insert/update)
 * @param {string} tenantId
 * @param {string} dateTimeStr - date format YYYYMMDD
 * @returns {number|0} seq value or 0
 */
const getNextSeq = async (tenantId, dateTimeStr) => {
    if (!tenantId) throw new Error('tenantId is required');
    if (!dateTimeStr) throw new Error('dateTimeStr is required');

    const res = await AddressLogCountModel.findOneAndUpdate(
        { tenantId, dateTimeStr },
        { $inc: { seq: 1 } },
        { runValidators: true, context: 'query', upsert: true, new: true }
    ).exec();

    return res?.seq || 0;
};

//statics
addressLogCountSchema.statics = {
    getNextSeq,
};

const AddressLogCountModel = mongoose.model('address_log_count', addressLogCountSchema, 'address_log_count');

module.exports = AddressLogCountModel;
