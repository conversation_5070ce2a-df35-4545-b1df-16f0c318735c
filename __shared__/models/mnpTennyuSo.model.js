const _ = require('lodash');
const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// DB
const CustomerInfoPgModel = require('../pgModels/customerInfo');

const TenantPlanLineOptionSettingModel = require('./adminService/tenantPlanLineOptionSetting.model');

// utils
const { createPrefixTempoFind } = require('../utils/stringUtils');

// Helpers & Constants
const { removeNullValue, createAddressNo, isNone } = require('../helpers/baseHelper');
const { SIM_ORDER_TYPES, SIM_TYPES_ESIM } = require('../constants/simConstants');
const { SO_KIND_TYPE, KOUTEI_IDS } = require('../constants/orderConstants');
const { MVNO_SERVICES } = require('../constants/mvnoServicesConstants');
const { SWIMMY_REQUEST_TYPES } = require('../constants/swimmyTransactions');
const { callAPIRiyouChuudanSaikaiSuspend } = require('../services/core.service');
// const { doOTAKekkaTsuchiProcess } = require('./swimmyService/otaKekkaTsuchi');
// const {
//     doESIMKekkaTsuChiSwimmyV2Process,
//     doESIMKekkaTsuChiSwimmyV3Process,
// } = require('./swimmyService/esimKekkaTsuchi');

const { encryptText } = require('../utils/symmetricCipher');

const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const mnpTennyuSoSchema = new Schema({
    aladinSOId: { type: String, trim: true },
    //===Begin of 4.0====
    //新規受付のSOID
    pSoId: { type: String, trim: true, unique: true },

    //納入希望日
    desiredDeliveryDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },

    //===Begin of 5.0====
    deliveryAddress: { type: String, trim: true },
    uketsukeNo: { type: String, trim: true },

    so_id: { type: String, trim: true, unique: true },
    simBlackingSOId: { type: String, trim: true },
    zm_id: { type: String, trim: true },

    lastUpdateUserId: { type: String, trim: true, required: true },
    updatedAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },

    tenantId: { type: String, trim: true, required: true },
    tempoId: {
        type: String,
        trim: true,
        required: function () {
            return ![SO_KIND_TYPE.MNP_CHECK].includes(this.soKind);
        },
    },
    planId: { type: Number },
    contractType: { type: String, trim: true },

    //STEP7.0で個配申込受付番号と伝票番号を追加
    addressNo: { type: String, trim: true },
    denpyoNo: { type: String, trim: true },
    deliveryFlag: { type: String, enum: ['0', '1'] },
    postalCode: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    shippingAddress1: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    shippingAddress2: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    shippingAddress3: { type: String, trim: true },
    shippingAddress4: { type: String, trim: true },
    shippingAddress5: { type: String, trim: true },
    shippingAddress6: { type: String, trim: true },
    shippingAddress7: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    shippingAddress8: { type: String, trim: true },
    shippingAddress9: { type: String, trim: true },
    contactPhoneNumber: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    deliveryPattern: { type: String, trim: true },
    exception: { type: String, trim: true },
    //STEP14 isFM
    isFM: { type: Boolean },

    //STEP7.1でとネットワーク契約を追加
    beforeContractType: { type: String, trim: true },
    afterContractType: { type: String, trim: true },

    //STEP7.1でとデフォルト配送申込番号を追加
    defaultAddressNo: { type: String, trim: true },

    //STEP 8.0 020対応
    is020Support: { type: Boolean },

    //STEP8.0でと同一MVNEを追加
    sameMvneInFlag: { type: Boolean },
    lineDelTenantId: { type: String, trim: true },
    lastKoutei: {
        kouteiId: { type: Number, required: true },
        timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
        userId: { type: String, trim: true },
        ngReason: { type: String, trim: true },
    },
    kouteis: [
        {
            _id: false,
            kouteiId: { type: Number, required: true },
            timestamp: { type: Number },
            userId: { type: String, trim: true },
            //===Begin of 4.0====
            //NG理由
            ngReason: { type: String, trim: true },
        },
    ],

    uketsukeDateTime: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    kaisenOptions: { type: [String], default: undefined },
    kaisenUpdateOptions: [
        {
            _id: false,
            kaisenOptionType: { type: Number, required: true },
            currentKaisenOptionId: { type: String },
            updateKaisenOptionId: { type: String },
        },
    ],
    kaisenNo: { type: String, trim: true },

    imsi: { type: String, trim: true },
    puk1: { type: String, trim: true },
    puk2: { type: String, trim: true },

    mnpNo: { type: String, trim: true },
    mnpExpired: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    contractName: { type: String, trim: true },
    contractNameKana: { type: String, trim: true },
    zokusei: { type: String, trim: true },
    birthday: { type: String, trim: true },
    shopMemo: { type: String, trim: true },
    simType: { type: String, trim: true },
    hankuro: { type: String, trim: true },
    Nno: { type: String, trim: true },
    simInfo: { type: String, trim: true },
    networkPassword: { type: String, trim: true },
    // STEP 16.0 0035denwa
    voicePlanId: { type: String, trim: true },
    isSwimmyVLMActiveCreated: { type: Boolean },
    // STEP 17 Swimmy向けMNP転出済通知 flag
    needSwimmyNoti: { type: Boolean },
    yakitsukeSIMSaisentakuHitsuyoNichiji: { type: Number },

    // STEP18
    isFrom9a1z: { type: Boolean },
    simBlackingSOIdList: { type: [String], default: undefined },

    soKind: { type: String, trim: true, required: true },
    additional_so: { type: String, trim: true },

    // STEP 23 eSIM対応
    eid: { type: String, trim: true },
    notifyHost: { type: String, trim: true },
    dummyLineNo: { type: String, trim: true },
    notifyPattern: { type: String, trim: true },

    // MNP関連照会
    pattern: { type: String, trim: true }, // mnpCheckConstants.js:MNP_CHECK_PATTERN
    carrierCode: { type: String, trim: true }, // MNP事業者コード

    // STEP26: 0035でんわプラン変更対応
    // oldVoicePlanId: use existing voicePlanId field // 変更前0035でんわプラン
    newVoicePlanId: { type: String, trim: true }, // 変更後0035でんわプラン

    // STEP29: 定額プラン基本料の月初指定
    fixedFeeNextMonth: { type: String, enum: ["0", "1"] },
});

mnpTennyuSoSchema.virtual('isComeFromShinMVNO').get(function () {
    const regex = /^S(?![FG])[A-J]/; // STEP 23: + H,J for 5G
    return regex.test(this.contractType ?? '');
});
mnpTennyuSoSchema.virtual('koutei').get(function () {
    if (isNone(removeNullValue(this.lastKoutei))) {
        return KOUTEI_IDS.UKETSUKE_CHECK_NG;
    } else {
        return this.lastKoutei.kouteiId;
    }
});
mnpTennyuSoSchema.virtual('is5GNSA').get(function () {
    // STEP18
    const regex = /^S[HJ]$/;
    return regex.test(this.contractType ?? '');
});
mnpTennyuSoSchema.virtual('ngReason').get(function () {
    if (this.lastKoutei) {
        return this.lastKoutei.ngReason ?? '';
    } else {
        return '';
    }
});

const MNPTennyuSoConstants = {
    // for MVNO 3.5
    HANKURO_ADDITIONAL_SO: '黒化受付',
    MNP_TENSYUTSU_YOYAKU_CANCEL_ADDITIONAL_SO: 'MNP転出予約キャンセル受付',

    //APIの場合にUserIdのフィールドに記入するためのId
    API_ACTION_ID: '-1',

    //STEP14 fullMvno 利用状態
    RIYOU_CHUU: '0',
    RIYOU_CHUUDAN_CHUU: '1',
};

/**
 * An object containing delivery addressInfo
 * @typedef {object} ShippingInfo
 * @property {string} [deliveryFlag] - 個別配送フラグ
 * @property {string} [postalCode] - 送付先住所_郵便番号
 * @property {string} [shippingAddress1] - 送付先住所_都道府県
 * @property {string} [shippingAddress2] - 送付先住所_市区郡町村
 * @property {string} [shippingAddress3] - 送付先住所_大字通称
 * @property {string} [shippingAddress4] - 送付先住所_字
 * @property {string} [shippingAddress5] - 送付先住所_丁目番地
 * @property {string} [shippingAddress6] - 送付先住所_ビル名等
 * @property {string} [shippingAddress7] - 送付先_宛先会社名/氏名
 * @property {string} [shippingAddress8] - 送付先_宛先部課名
 * @property {string} [shippingAddress9] - 送付先_宛先担当者
 * @property {string} [contactPhoneNumber] - 連絡先電話番号
 * @property {string} [deliveryPattern] - 個別配送パターン
 * @property {string} [exception] - Com使用欄
 */

/**
 * An object containing koutei Info
 * @typedef {object} KouteiInfo
 * @property {number} kouteiId
 * @property {string} [ngReason]
 */

/**
 * MVNO 3.5 additional_soを追加する
 * hankuro が 半黒MNPで、so_kindがMNP転入で、processIDが交換器設定OKか完了の時 => 「黒化受付」リンクがでる
 * so_kind が MNP転出予約で、processIDが交換器設定OKか完了の時 => 「MNP転出予約キャンセル受付」リンクがでる
 * @param  {mnpTennyuSoSchema} doc
 * @return {string|null} additional_so
 */
const getAdditionalSo = (doc) => {
    if (!doc) throw new Error('doc is required');

    const additionSOKouteiIds = [KOUTEI_IDS.KOUKANKI_SETTEI_OK, KOUTEI_IDS.KANRYOU];
    if (!additionSOKouteiIds.includes(doc.lastKoutei.kouteiId)) return null;

    if (doc.hankuro === SIM_ORDER_TYPES.HANKURO_MNP && doc.soKind === SO_KIND_TYPE.MNP_TENNYU)
        return MNPTennyuSoConstants.HANKURO_ADDITIONAL_SO;

    if (doc.soKind === SO_KIND_TYPE.MNP_TENSHUTSU)
        return MNPTennyuSoConstants.MNP_TENSYUTSU_YOYAKU_CANCEL_ADDITIONAL_SO;
};

/**
 * STEP21: CON000テナントのMNP転入(OTA)と新規受付(OTA)オーダについて、Swimmyに結果通知を行う。
 * 「3:焼付けSIM選択NG、11:ALADIN工事NG、21:交換機設定NG、22:交換機設定OK」工程になった際に通知を行う。
 * @param  {object} context
 * @param  {mnpTennyuSoSchema} doc
 * @return {Promise}
 */
const notifyResultToSwimmy = async (context, doc) => {
    if (!doc) throw new Error('doc is required');

    const swimmyKouteiIds = [
        KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_NG,
        KOUTEI_IDS.ALADIN_KOUJI_NG,
        KOUTEI_IDS.KOUKANKI_SETTEI_NG,
        KOUTEI_IDS.KOUKANKI_SETTEI_OK,
    ];
    if (!swimmyKouteiIds.includes(doc.lastKoutei.kouteiId)) return;

    // STEP25 don't create OTA結果通知 and eSIM結果通知
    // if (
    //     portalConfig.OTA_KEKKA_NOTIFY_TENANT_LIST.includes(doc.tenantId) &&
    //     (doc.soKind == SO_KIND_TYPE.MNP_TENNYU || doc.soKind == SO_KIND_TYPE.NEW_OTA_SERVICE_ORDER) &&
    //     doc.hankuro == SIM_ORDER_TYPES.OTA
    // ) {
    //     doOTAKekkaTsuchiProcess(context, doc);
    // }
    // //STEP23.0 SwimmyにeSIM結果通知を行う。
    // //MNP転入 →「SIMオーダ種別：eSIM」かつ「テナントID：CON000」かつ「通知パターン：1 or 2」
    // //新規受付(eSIM) →「テナントID：CON000」かつ「通知パターン：1 or 2」
    // //Not include CON and simType(eSIM) in condition bcz 通知パターン(notifyPattern) param will be defined when uketsuke with only CON & eSIM specify
    // else if (
    //     doc.notifyPattern &&
    //     [SO_KIND_TYPE.MNP_TENNYU, SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER].includes(doc.soKind)
    // ) {
    //     //通知パターン：1ならSwimmy-V2(DotaNotice)、通知パターン：2ならSwimmy-V3(EIDNotice)に通知
    //     if (doc.notifyPattern === NOTIFY_PATTERN.SWIMMY_V2) {
    //         await doESIMKekkaTsuChiSwimmyV2Process(context, { obj: doc });
    //     } else {
    //         await doESIMKekkaTsuChiSwimmyV3Process(context, { obj: doc });
    //     }
    // }
};

/**
 * @param {object} context
 * @param {object} apiLog SwimmyApiLog document
 * @param {object} selfObj MNPTennyuSo document
 * @returns {Promise<string>} processCode
 */
const handleSwimmyNotifyOK = async (context, apiLog, selfObj) => {
    let suspendFlag = '0';
    if (apiLog.swimmyType === SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND) {
        suspendFlag = '2';
    } else if (apiLog.swimmyType === SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_CHUUDAN) {
        suspendFlag = '1';
    }
    return await callAPIRiyouChuudanSaikaiSuspend(context, {
        targetTenantId: apiLog.tenantId,
        lineNo: apiLog.kaisenNo,
        suspendFlag,
        targetSoId: selfObj.so_id,
    });
};

/**
 * Create MNPTennyuu soId
 * @param  {object} context - API context
 * @param  {object} data
 * @param  {string} data.createdUserId        - created userId
 * @param  {KouteiInfo} data.koutei           - order processing status
 * @param  {object} data.mnpTennyuData                 - required data for import
 * @param  {ShippingInfo} [data.shippingInfo] - shipping address info (with deliveryFlag)
 * @return {string|null} soId                 - if successful return order id (soId)
 */
const createMNPTennyuSO = async (context, { createdUserId, koutei, mnpTennyuData, shippingInfo }) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');
    if (!mnpTennyuData || Object.keys(mnpTennyuData).length === 0) throw new Error('mnpTennyuData is required');

    const nowSec = dayjs().unix();
    // in case uketsukeDateTime is not set
    if (isNone(mnpTennyuData.uketsukeDateTime)) {
        mnpTennyuData.uketsukeDateTime = nowSec;
    }
    let mnpTennyuSo = new MNPTennyuSoModel(removeNullValue(mnpTennyuData));

    mnpTennyuSo.soKind = SO_KIND_TYPE.MNP_TENNYU;
    mnpTennyuSo.lastUpdateUserId = createdUserId;
    mnpTennyuSo.updatedAt = nowSec;

    mnpTennyuSo.lastKoutei = removeNullValue(koutei);
    mnpTennyuSo.lastKoutei.userId = createdUserId;
    mnpTennyuSo.lastKoutei.timestamp = nowSec;
    mnpTennyuSo.kouteis = [mnpTennyuSo.lastKoutei];

    if (shippingInfo?.deliveryFlag === '1') {
        Object.assign(mnpTennyuSo, removeNullValue(shippingInfo));

        mnpTennyuSo.addressNo = await createAddressNo(mnpTennyuSo.tenantId);
    }

    const additionSO = getAdditionalSo(mnpTennyuSo);
    if (additionSO) mnpTennyuSo.additional_so = additionSO;

    // STEP23.0 eSIM対応
    if (!isNone(mnpTennyuData.eid)) {
        mnpTennyuSo.simInfo = mnpTennyuData.eid;
    }

    mnpTennyuSo.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + mnpTennyuSo._id;

    const savedDoc = await mnpTennyuSo.save();
    return savedDoc?.so_id;
};

/**
 * bulk insert a quantity of MNPTennyuu soId
 * @param  {object} context - API context
 * @param  {object} data
 * @param  {number} data.quantity - number of SO to be created
 * @param  {string} data.createdUserId
 * @param  {MNPTennyuSchema} data.mnpTennyuData - required mnpTennyuData for import
 * @param  {ShippingInfo} [data.shippingInfo] - shipping address info (with deliveryFlag)
 * @return {[type]}
 */
const createMultipleSO = async (context, { quantity, createdUserId, mnpTennyuData, shippingInfo }) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!mnpTennyuData || Object.keys(mnpTennyuData).length === 0) throw new Error('mnpTennyuData is required');
    if (!quantity) throw new Error('quantity is required');

    const nowSec = dayjs().unix();
    const { addressNo, ...dataWithoutAddressNo } = mnpTennyuData;
    let mnpTennyuSo = new MNPTennyuSoModel(removeNullValue(dataWithoutAddressNo));

    mnpTennyuSo.soKind = SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER;
    mnpTennyuSo.lastUpdateUserId = createdUserId;
    mnpTennyuSo.updatedAt = nowSec;

    // Update koutei
    let lastKoutei = {
        kouteiId: KOUTEI_IDS.UKETSUKE_CHECK_OK,
        userId: createdUserId,
        timestamp: nowSec,
        ngReason: mnpTennyuData.ngReason,
    };
    mnpTennyuSo.kouteis = [removeNullValue(lastKoutei)];

    if (mnpTennyuSo.hankuro === SIM_ORDER_TYPES.KURO || mnpTennyuSo.hankuro === SIM_ORDER_TYPES.HANKURO) {
        lastKoutei = {
            kouteiId: KOUTEI_IDS.ALADIN_KOUJI_OK,
            userId: createdUserId,
            timestamp: nowSec,
        };
        mnpTennyuSo.kouteis.push(lastKoutei);
    }
    mnpTennyuSo.lastKoutei = lastKoutei;

    // Get kaisenOptions
    const [customerInfo, lineOptions] = await Promise.all([
        CustomerInfoPgModel.getVoicemailCallWaitingOptionByNNo(context, mnpTennyuSo.Nno),
        TenantPlanLineOptionSettingModel.getDefaultLineOptionByTypeId(
            context,
            mnpTennyuSo.tenantId,
            mnpTennyuSo.planId,
            true,
            false
        ),
    ]);

    const kaisenOptions = [
        lineOptions[1],
        lineOptions[2] && customerInfo?.voicemail_option_id ? customerInfo.voicemail_option_id : lineOptions[2],
        lineOptions[3] && customerInfo?.callwaiting_option_id ? customerInfo.callwaiting_option_id : lineOptions[3],
        lineOptions[4],
        lineOptions[5],
        lineOptions[6],
    ];

    mnpTennyuSo.kaisenOptions = kaisenOptions.filter((t) => !isNone(t)); //remove any empty value

    if (shippingInfo?.deliveryFlag === '1') {
        Object.assign(mnpTennyuSo, removeNullValue(shippingInfo));
        mnpTennyuSo.addressNo = addressNo;
    } else {
        mnpTennyuSo.defaultAddressNo = addressNo;
    }

    // bulk insert
    // (Mongoose always validates each document before sending to insertMany to MongoDB)
    let soList = new Array(quantity).fill(0).map(() => {
        // clone object
        // create a new copy
        let newDoc = { ...mnpTennyuSo }._doc;

        // delete property from new copy (remove _id).
        delete newDoc._id;

        // insert copy into db
        newDoc = new MNPTennyuSoModel(newDoc);
        if (newDoc.isFM === true) {
            newDoc.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + newDoc._id;
        } else {
            newDoc.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newDoc._id;
        }
        return newDoc;
    });

    if (!soList) throw new Error('createMultipleSO error: empty data is passed');

    return await MNPTennyuSoModel.insertMany(soList);
};

/**
 * MNP転出予約, or 予約キャンセルSOデータ作成
 * @param  {object} context - API context
 * @param  {object} data
 * @param  {string} data.createdUserId        - created userId
 * @param  {KouteiInfo} data.koutei           - order processing status
 * @param  {object} data.mnpTenshutsuData        - required data for import
 * @return {string|null} soId                 - if successful return order id (soId)
 */
const createMNPTenshutsuYoyakuSO = async (context, { createdUserId, koutei, mnpTenshutsuData }) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');
    if (!mnpTenshutsuData || Object.keys(mnpTenshutsuData).length === 0)
        throw new Error('mnpTenshutsuData is required');

    const nowSec = dayjs().unix();
    let mnpTenshutsuSo = new MNPTennyuSoModel(removeNullValue(mnpTenshutsuData));

    if (mnpTenshutsuData.yoyakuCancelFlag) {
        mnpTenshutsuSo.soKind = SO_KIND_TYPE.MNP_TENSHUTSU_CANCEL;
    } else {
        mnpTenshutsuSo.soKind = SO_KIND_TYPE.MNP_TENSHUTSU;

        // for MVNO 3.5, additional_soを追加する
        // so_kindが MNP転出予約で、processIDが交換器設定OKか完了の時 => 「MNP転出予約キャンセル受付」リンクがでる
        const additionSO = getAdditionalSo(mnpTenshutsuSo);
        if (additionSO) mnpTenshutsuSo.additional_so = additionSO;
    }

    mnpTenshutsuSo.lastUpdateUserId = createdUserId;
    mnpTenshutsuSo.updatedAt = nowSec;

    mnpTenshutsuSo.lastKoutei = removeNullValue(koutei);
    mnpTenshutsuSo.lastKoutei.userId = createdUserId;
    mnpTenshutsuSo.lastKoutei.timestamp = nowSec;
    mnpTenshutsuSo.kouteis = [mnpTenshutsuSo.lastKoutei];

    mnpTenshutsuSo.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + mnpTenshutsuSo._id;

    const savedDoc = await mnpTenshutsuSo.save();
    return savedDoc?.so_id;
};

/**
 * find documents by so_id and update lastKoutei fields
 * 旧メソッド: addKouteiInfo
 * @param  {object} context - API context
 * @param  {object} data
 * @param  {string} data.so_id                - for find query
 * @param  {string} data.updatedUserId
 * @param  {KouteiInfo} data.koutei           - order processing status
 * @return {MNPTennyuSchema|null}                 - if successful return updatedObject else return null
 */
const updateKouteiInfo = async (context, { so_id, updatedUserId, koutei }) => {
    if (!so_id) throw new Error('so_id is required');
    if (!updatedUserId) throw new Error('updatedUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');

    koutei.userId = updatedUserId;
    koutei.timestamp ||= dayjs().unix();
    const lastKoutei = removeNullValue(koutei);

    const updatedDoc = await MNPTennyuSoModel.findOneAndUpdate(
        { so_id },
        {
            $set: { lastKoutei },
            $push: { kouteis: lastKoutei },
        },
        { runValidators: true, context: 'query', new: true }
    ).exec();

    await notifyResultToSwimmy(context, updatedDoc);

    const additionSO = getAdditionalSo(updatedDoc);
    if (additionSO) {
        updatedDoc.additional_so = additionSO;
        return updatedDoc.save();
    }
    return updatedDoc;
};

/**
 * find mnpTennnyuInfo by so_id
 * @param {object} context - API context
 * @param {string} so_id
 * @returns {mnpTennyuSoSchema|null} mnpTennnyu object or null
 */
const getMnpTennyuInfoBySoId = async (context, so_id) => {
    if (!so_id) throw new Error('so_id is required');

    return await MNPTennyuSoModel.findOne({ so_id }).exec();
};

/**
 * find mnpTennnyuInfo by kaisenNo & simInfo
 * @param {object} context - API context
 * @param {object} data
 * @param {string} data.kaisenNo
 * @param {string} data.simInfo
 * @returns {mnpTennyuSoSchema|null} mnpTennnyu object or null
 */
const getMnpTennyuInfoByKaisenNoAndSimInfo = async (context, { kaisenNo, simInfo }) => {
    if (!kaisenNo) throw new Error('kaisenNo is required');
    if (!simInfo) throw new Error('simInfo is required');

    return await MNPTennyuSoModel.findOne({ kaisenNo, simInfo }).exec();
};

const setMNPNoForMNPCheck = async (context, so_id, mnpNo) => {
    return await MNPTennyuSoModel.updateOne({ so_id }, { $set: { mnpNo: mnpNo } }, { runValidators: true }).exec();
};

/**
 * set value to simBlackingSOId
 * 旧メソッド: setKurokaSOID
 * @param {string} aladinSoId
 * @param {string} kurokaSoId
 * @returns {mnpTennyuSoSchema|null} daihyouBango object or null
 */
const setKurokaSoId = async (context, { aladinSoId, kurokaSoId }) => {
    if (!aladinSoId) throw new Error('aladinSoId is required');
    if (!kurokaSoId) throw new Error('kurokaSoId is required');

    return await MNPTennyuSoModel.findOneAndUpdate(
        { so_id: aladinSoId },
        {
            $set: { simBlackingSOId: kurokaSoId },
            $push: { simBlackingSOIdList: kurokaSoId },
        },
        { runValidators: true, context: 'query', new: true }
    ).exec();
};

/**
 *
 * @param {object} context - context object
 * @param {number} searchTypeo - searchType
 * @param {string} searchValueo - searchValue
 * @param {string[]} tenantIdo - tenantId
 * @param {string[]} tempoIdo - tempoId
 * @param {number[]} processIdo - processId
 * @param {number} fromDateo - receptDateBeginKey
 * @param {number} toDateo - receptDateEndKey
 * @param {string[]} hankuroo - simOrderType
 * @param {string[]} soKindo - soKindType
 * @param {number} limito - limit
 * @param {boolean} isExcludeNewSO - isExcludeNewSO
 * @param {string[]} prefixTempoIdo - prefixTempoId
 * @returns {array.<mnpTennyuSO>} - MNPTennyuSO list
 */
const searchSO = async (
    context,
    {
        searchTypeo,
        searchValueo,
        tenantIdo,
        tempoIdo,
        processIdo,
        fromDateo,
        toDateo,
        hankuroo,
        soKindo,
        limito,
        isExcludeNewSO,
        prefixTempoIdo,
    }
) => {
    const soList = await searchSO2(
        context,
        searchTypeo,
        searchValueo,
        tenantIdo,
        tempoIdo,
        processIdo,
        fromDateo,
        toDateo,
        hankuroo,
        null,
        null,
        soKindo,
        null,
        limito,
        null,
        false,
        null,
        isExcludeNewSO,
        null,
        false,
        MVNO_SERVICES.ALL,
        prefixTempoIdo
    );

    return soList?.cursor;
};

/**
 *
 * @param {object} context - context object
 * @param {number} searchTypeo - searchType
 * @param {string} searchValueo - searchValue
 * @param {string[]} tenantIdo - tenantId
 * @param {string[]} tempoIdo - tempoId
 * @param {number[]} processIdo - processId
 * @param {number} fromDateo - receptDateBeginKey
 * @param {number} toDateo - receptDateEndKey
 * @param {string[]} hankuroo - simOrderType
 * @param {string} kohaiFlag - deliveryFlag
 * @param {boolean} sameMvneInFlag - sameMvneInFlag
 * @param {string[]} soKindo - soKindType
 * @param {number} skipo - skip
 * @param {number} limito - limit
 * @param {string} sortByo - sortBy
 * @param {boolean} isSortOrderAscending - sorting order up or down
 * @param {number} roleId - roleId
 * @param {boolean} isExcludeNewSO - isExcludeNewSO
 * @param {string[]} soIds - soId list
 * @param {boolean} isAllSelected - isAllSelected
 * @param {number} service - liteMVNO or fullMVNO or both
 * @param {string[]} prefixTempoIdo - prefixTempoId
 * @returns {array.<mnpTennyuSO>, number} - MNPTennyuSO list and result count
 */
const searchSO2 = async (
    context,
    searchTypeo,
    searchValueo,
    tenantIdo,
    tempoIdo,
    processIdo,
    fromDateo,
    toDateo,
    hankuroo,
    kohaiFlag,
    sameMvneInFlag,
    soKindo,
    skipo,
    limito,
    sortByo,
    isSortOrderAscending,
    roleId,
    isExcludeNewSO,
    soIds,
    isAllSelected,
    service,
    prefixTempoIdo
) => {
    const fullMVNOFlag = service ? service : MVNO_SERVICES.ALL;
    const query = await createQuery(
        context,
        searchTypeo,
        searchValueo,
        tenantIdo,
        tempoIdo,
        processIdo,
        fromDateo,
        toDateo,
        hankuroo,
        kohaiFlag,
        sameMvneInFlag,
        soKindo,
        roleId,
        isExcludeNewSO,
        soIds,
        isAllSelected,
        fullMVNOFlag,
        prefixTempoIdo
    );
    const sort = await createSortCondition(context, sortByo, isSortOrderAscending);
    const count = await MNPTennyuSoModel.find(query).count();
    let doc = {};
    switch (true) {
        case count === 0:
        case count === 1:
            doc.cursor = await MNPTennyuSoModel.find(query);
            doc.count = count;
            break;
        case count <= limito:
            doc.cursor = await MNPTennyuSoModel.find(query).sort(sort);
            doc.count = count;
            break;
        default:
            if (limito && !skipo) {
                doc.cursor = await MNPTennyuSoModel.find(query).sort(sort).limit(limito);
            } else if (!limito && skipo) {
                doc.cursor = await MNPTennyuSoModel.find(query).sort(sort).skip(skipo);
            } else {
                doc.cursor = await MNPTennyuSoModel.find(query).sort(sort).limit(limito).skip(skipo);
            }
            doc.count = count;
            break;
    }
    return doc;
};

/**
 *
 * @param {object} context
 * @param {number} [searchType]
 * @param {string} [searchValue]
 * @param {Array<string>} tenantId
 * @param {Array<string>} tempoId
 * @param {Array<number>} processId
 * @param {number} [fromDate]
 * @param {number} [toDate]
 * @param {Array<string>} hankuro
 * @param {Array<string>} pSoId
 * @param {number} [skip]
 * @param {number} [limit]
 * @param {string} [sortBy]
 * @param {boolean} [isSortOrderAscending]
 * @param {number} service
 */
const searchNewSO = async (
    context,
    searchType,
    searchValue,
    tenantId,
    tempoId,
    processId,
    fromDate,
    toDate,
    hankuro,
    pSoId,
    skip,
    limit,
    sortBy,
    isSortOrderAscending,
    service = MVNO_SERVICES.ALL
) => {
    context.log('searchNewSO start');
    const query = {};
    switch (searchType) {
        case 0:
            query.pSoId = searchValue;
            break;
        case 1:
            query.so_id = searchValue;
            break;
        case 2:
            query.kaisenNo = searchValue;
            break;
    }
    if (Array.isArray(tenantId) && tenantId.length > 0) {
        query.tenantId = { $in: tenantId };
    }
    if (Array.isArray(tempoId) && tempoId.length > 0) {
        query.tempoId = { $in: tempoId };
    }
    if (Array.isArray(processId) && processId.length > 0) {
        query['lastKoutei.kouteiId'] = { $in: processId };
    }
    if (!isNone(fromDate) && !isNone(toDate)) {
        query.uketsukeDateTime = { $gte: fromDate, $lt: toDate };
    } else if (!isNone(fromDate)) {
        query.uketsukeDateTime = { $gte: fromDate };
    } else if (!isNone(toDate)) {
        query.uketsukeDateTime = { $lt: toDate };
    }
    if (Array.isArray(hankuro) && hankuro.length > 0) {
        query.hankuro = { $in: hankuro };
    }
    if (Array.isArray(pSoId) && pSoId.length > 0) {
        query.pSoId = { $in: pSoId };
    }
    //新規SOだけを検索
    query.soKind = SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER;

    if (service == MVNO_SERVICES.LITE) {
        query.isFM = { $ne: true };
    } else if (service == MVNO_SERVICES.FULL) {
        query.isFM = true;
    }

    const sort = {};
    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    const sortMap = {
        p_so_id: 'pSoId',
        so_id: 'so_id',
        tenant_id: 'tenantId',
        tempo_id: 'tempoId',
        uketsuke_date: 'uketsukeDateTime',
        mnp_no: 'mnpNo',
        process_id: 'lastKoutei.kouteiId',
        kaisen_no: 'kaisenNo',
        hankuro: 'hankuro',
        additional_so: 'additional_so',
        imsi: 'imsi',
        puk1: 'puk1',
        puk2: 'puk2',
    };
    if (isNone(sortBy)) {
        sort._id = sortAscendingInt;
    } else {
        const key = sortMap[sortBy];
        if (isNone(key)) {
            sort._id = sortAscendingInt;
        } else {
            sort[key] = sortAscendingInt;
        }
    }

    const count = await MNPTennyuSoModel.find(query).count();
    const doc = {};
    switch (true) {
        case count === 0:
        case count === 1:
            doc.cursor = await MNPTennyuSoModel.find(query);
            doc.count = count;
            break;
        case count <= limit:
            doc.cursor = await MNPTennyuSoModel.find(query).sort(sort);
            doc.count = count;
            break;
        default:
            if (limit && !skip) {
                doc.cursor = await MNPTennyuSoModel.find(query).sort(sort).limit(limit);
            } else if (!limit && skip) {
                doc.cursor = await MNPTennyuSoModel.find(query).sort(sort).skip(skip);
            } else {
                doc.cursor = await MNPTennyuSoModel.find(query).sort(sort).limit(limit).skip(skip);
            }
            doc.count = count;
    }
    return doc;
};

/**
 *
 * @param {object} context - context object
 * @param {number} searchTypeo - searchType
 * @param {string} searchValueo - searchValue
 * @param {string[]} tenantIdo - tenantId
 * @param {string[]} tempoIdo - tempoId
 * @param {number[]} processIdo - processId
 * @param {number} fromDateo - receptDateBeginKey
 * @param {number} toDateo - receptDateEndKey
 * @param {string[]} hankuroo - simOrderType
 * @param {string} kohaiFlag - deliveryFlag
 * @param {boolean} sameMvneInFlag - sameMvneInFlag
 * @param {string[]} soKindo - soKindType
 * @param {number} roleId - roleId
 * @param {boolean} isExcludeNewSO - isExcludeNewSO
 * @param {string[]} soIds - soId list
 * @param {boolean} isAllSelected - isAllSelected
 * @param {number} fullMVNOFlag - fullMVNOFlag
 * @param {string[]} prefixTempoIdo - prefixTempoId
 * @returns {object} - query object
 */
const createQuery = async (
    context,
    searchTypeo,
    searchValueo,
    tenantIdo,
    tempoIdo,
    processIdo,
    fromDateo,
    toDateo,
    hankuroo,
    kohaiFlag,
    sameMvneInFlag,
    soKindo,
    roleId,
    isExcludeNewSO,
    soIds,
    isAllSelected,
    fullMVNOFlag,
    prefixTempoIdo
) => {
    context.log('MNPTennyuSoModel createQuery START ' + roleId);
    let params = {};
    switch (searchTypeo) {
        case 0:
            params.so_id = searchValueo;
            break;
        case 1:
            params.kaisenNo = searchValueo;
            break;
        case 2:
            params.mnpNo = searchValueo;
            break;
        case 3:
            params.simInfo = searchValueo;
            break;
        default:
            break;
    }

    switch (fullMVNOFlag) {
        case MVNO_SERVICES.LITE:
            params.isFM = { $ne: true };
            break;
        case MVNO_SERVICES.FULL:
            params.isFM = true;
            break;
        default:
            break;
    }

    if (Array.isArray(tenantIdo) && tenantIdo.length > 0) {
        params.tenantId = { $in: tenantIdo };
    }

    if (Array.isArray(tempoIdo) && tempoIdo.length > 0) {
        params.tempoId = { $in: tempoIdo };
    }

    if (Array.isArray(prefixTempoIdo) && prefixTempoIdo.length > 0) {
        const prefixRegex = createPrefixTempoFind(prefixTempoIdo);
        params.tempoId = { $regex: prefixRegex };
    }

    if (Array.isArray(processIdo) && processIdo.length > 0) {
        params['lastKoutei.kouteiId'] = { $in: processIdo };
    }

    if (fromDateo && !toDateo) {
        params.uketsukeDateTime = { $gte: fromDateo };
    } else if (!fromDateo && toDateo) {
        params.uketsukeDateTime = { $lt: toDateo };
    } else if (fromDateo && toDateo) {
        params.uketsukeDateTime = { $gte: fromDateo, $lt: toDateo };
    }

    if (Array.isArray(hankuroo) && hankuroo.length > 0) {
        params.hankuro = { $in: hankuroo };
    }

    if (kohaiFlag) {
        params.deliveryFlag = kohaiFlag === '1' ? kohaiFlag : { $ne: '1' };
    }

    if (!isNone(sameMvneInFlag)) {
        params.sameMvneInFlag = sameMvneInFlag === true ? true : { $ne: true };
    }

    if (Array.isArray(soKindo) && soKindo.length > 0) {
        let soKindList = [];
        soKindo.map((soKind) => {
            switch (soKind) {
                case SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER:
                    if (!isExcludeNewSO) {
                        soKindList.push(soKind);
                    }
                    break;
                default:
                    soKindList.push(soKind);
                    break;
            }
        });
        params.soKind = { $in: soKindList };
    }

    //ALADIN-SOから検索すると新規SOを外し
    if (isExcludeNewSO && (!Array.isArray(soKindo) || soKindo.length === 0)) {
        //STEP 9.0 とりあえず、コムオペレータユーザ以上検索できるようにする
        //STEP 12.1 とりあえず、全部のユーザーが検索できるようにする「SwitchStatusServiceOrder」
        //STEP14 remove condition sokind MNPTennyuVer20
        //STEP26 add 0035でんわプラン変更
        const soTypeExceptNewSO = [
            //SO_KIND_TYPE.MNP_TENNYU_VER20,
            SO_KIND_TYPE.MNP_TENNYU,
            SO_KIND_TYPE.MNP_TENSHUTSU,
            SO_KIND_TYPE.MNP_TENSHUTSU_CANCEL,
            SO_KIND_TYPE.MNP_SIM_SAIHAKKOU,
            SO_KIND_TYPE.MNP_KAISEN_OPTION_HENKOU,
            SO_KIND_TYPE.MNP_NWPASSWORD_HENKOU,
            SO_KIND_TYPE.STOP_SERVICE_ORDER,
            SO_KIND_TYPE.RESUME_SERVICE_ORDER,
            SO_KIND_TYPE.SWITCH_STATUS_SERVICE_ORDER,
            SO_KIND_TYPE.NEW_OTA_SERVICE_ORDER,
            SO_KIND_TYPE.CONTRACT_TYPE_CHANGE_ORDER,
            SO_KIND_TYPE.CHANGE_PROCESS,
            SO_KIND_TYPE.KAISEN_SUPPEND,
            SO_KIND_TYPE.KAISEN_SUPPEND_KAIJO,
            SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER,
            SO_KIND_TYPE.VOICE_PLAN_CHANGE,
        ];
        params.soKind = { $in: soTypeExceptNewSO };
    }

    if (Array.isArray(soIds) && soIds.length > 0) {
        params.so_id = isAllSelected === true ? { $nin: soIds } : { $in: soIds };
    }

    return params;
};

/**
 *
 * @param {object} context - context object
 * @param {string} sortBy - sortBy
 * @param {boolean} isSortOrderAscending - sorting order up or down
 * @returns {object} - sorted condition object
 */
const createSortCondition = async (context, sortBy, isSortOrderAscending) => {
    context.log('MNPTennyuSoModel createSortCondition START');
    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = {};
    if (sortBy) {
        switch (sortBy) {
            case 'so_id':
                sort.so_id = sortAscendingInt;
                break;
            case 'tenant_id':
                sort.tenantId = sortAscendingInt;
                break;
            case 'tempo_id':
                sort.tempoId = sortAscendingInt;
                break;
            case 'uketsuke_date':
                sort.uketsukeDateTime = sortAscendingInt;
                break;
            case 'mnp_no':
                sort.mnpNo = sortAscendingInt;
                break;
            case 'so_kind':
                sort.soKind = sortAscendingInt;
                break;
            case 'process_id':
                sort['lastKoutei.kouteiId'] = sortAscendingInt;
                break;
            case 'kaisen_no':
                sort.kaisenNo = sortAscendingInt;
                break;
            case 'sim_info':
                sort.simInfo = sortAscendingInt;
                break;
            case 'hankuro':
                sort.hankuro = sortAscendingInt;
                break;
            case 'additional_so':
                sort.additional_so = sortAscendingInt;
                break;
            case 'shop_memo':
                sort.shopMemo = sortAscendingInt;
                break;
            case 'denpyo_no':
                sort.denpyoNo = sortAscendingInt;
                break;
            case 'contract_type':
                sort.contractType = sortAscendingInt;
                break;
            default:
                sort.uketsukeDateTime = sortAscendingInt;
                break;
        }
    } else {
        sort.uketsukeDateTime = sortAscendingInt;
    }

    return sort;
};

// 回線オプション変更
const createKaisenOptionHenkouSO = async (context, { createdUserId, koutei, kaisenOptionChangeData }) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');
    if (!kaisenOptionChangeData || Object.keys(kaisenOptionChangeData).length === 0)
        throw new Error('kaisenOptionChangeData is required');

    const nowSec = dayjs().unix();

    let optionChangeSo = new MNPTennyuSoModel(removeNullValue(kaisenOptionChangeData));

    optionChangeSo.soKind = SO_KIND_TYPE.MNP_KAISEN_OPTION_HENKOU;

    optionChangeSo.lastUpdateUserId = createdUserId;
    optionChangeSo.updatedAt = nowSec;

    optionChangeSo.lastKoutei = removeNullValue(koutei);
    optionChangeSo.lastKoutei.userId = createdUserId;
    optionChangeSo.lastKoutei.timestamp = nowSec;
    optionChangeSo.kouteis = [optionChangeSo.lastKoutei];

    optionChangeSo.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + optionChangeSo._id;

    const savedDoc = await optionChangeSo.save();
    return savedDoc?.so_id;
};

/**
 * ネットワーク暗証番号変更
 * @param {object} context
 * @param {string} createdUserId
 * @param {string} tenantId
 * @param {string} tempoId
 * @param {number} koutei Koutei ID
 * @param {number} uketsukeDateTime
 * @param {string} kaisenNo
 * @param {string} networkPassword
 * @returns {Promise<string>} SO-ID
 */
const createNWPasswordHenkouSO = async (
    context,
    createdUserId,
    tenantId,
    tempoId,
    koutei,
    uketsukeDateTime,
    kaisenNo,
    networkPassword
) => {
    context.log('MNPTennyuSoModel createNWPasswordHenkouSO START');
    const nowSecs = dayjs().unix();
    const kouteiObj = {
        kouteiId: koutei,
        timestamp: nowSecs,
        userId: createdUserId,
    };
    const mnpTennyuObj = {
        lastUpdateUserId: createdUserId,
        updatedAt: nowSecs,
        tenantId: tenantId,
        tempoId: tempoId,
        kouteis: [kouteiObj],
        lastKoutei: kouteiObj,
        uketsukeDateTime,
        kaisenNo,
        networkPassword,
        soKind: SO_KIND_TYPE.MNP_NWPASSWORD_HENKOU,
    };
    let newSO = new MNPTennyuSoModel(removeNullValue(mnpTennyuObj));
    newSO.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSO._id;
    newSO = await newSO.save();
    return newSO.so_id;
};

/**
 * 0035でんわプラン変更SO作成
 * @param {object} context
 * @param {object} param1
 * @param {string} param1.createdUserId
 * @param {string} param1.tenantId
 * @param {string} param1.tempoId
 * @param {Array<number>} param1.kouteiIdList 工程
 * @param {number} param1.uketsukeDateTime
 * @param {string} param1.kaisenNo
 * @param {string} param1.beforeVoicePlanId 変更前の0035でんわプラン
 * @param {string} param1.afterVoicePlanId 変更後の0035でんわプラン
 * @param {string} param1.fixedFeeNextMonth 翌月課金オプション
 * @returns {Promise<string>} new SOID
 */
const createVoicePlanChangeSO = async (
    context,
    { createdUserId, tenantId, tempoId, kouteiIdList, uketsukeDateTime, kaisenNo, beforeVoicePlanId, afterVoicePlanId, fixedFeeNextMonth }
) => {
    if (isNone(createdUserId)) throw new Error('createdUserId is required');
    if (isNone(tenantId)) throw new Error('tenantId is required');
    if (isNone(tempoId)) throw new Error('tempoId is required');
    if (isNone(kouteiIdList) || !Array.isArray(kouteiIdList))
        throw new Error('kouteiIdList is required or type not match');
    if (isNone(kaisenNo)) throw new Error('kaisenNo is required');
    if (isNone(beforeVoicePlanId)) throw new Error('beforeVoicePlanId is required');
    if (isNone(afterVoicePlanId)) throw new Error('afterVoicePlanId is required');
    context.log('MNPTennyuSoModel createVoicePlanChangeSO start');

    const now = dayjs().unix();
    const kouteiList = kouteiIdList.map((koutei) => ({
        kouteiId: koutei,
        timestamp: now,
        userId: createdUserId,
    }));
    let newSO = new MNPTennyuSoModel(
        removeNullValue({
            lastUpdateUserId: createdUserId,
            tenantId,
            tempoId,
            kouteis: kouteiList,
            lastKoutei: kouteiList.at(-1),
            uketsukeDateTime,
            kaisenNo,
            voicePlanId: beforeVoicePlanId,
            newVoicePlanId: afterVoicePlanId,
            soKind: SO_KIND_TYPE.VOICE_PLAN_CHANGE,
            fixedFeeNextMonth
        })
    );
    newSO.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSO._id;
    newSO = await newSO.save();
    return newSO.so_id;
};

/**
 * SIM在庫引当
 * @param {string} so_id
 * @param {string} zm_id
 * @param {string} simInfo
 */
const updateSimZaikoInfo = async (so_id, zm_id, simInfo) => {
    if (!so_id) throw new Error('soId is required');
    // zm_id can be empty string
    // if (!zm_id) throw new Error('zm_id is required');
    if (!simInfo) throw new Error('simInfo is required');

    await MNPTennyuSoModel.updateOne(
        { so_id },
        {
            $set: {
                zm_id,
                simInfo,
            },
        },
        { runValidators: true }
    ).exec();
};

/**
 * @param {string} so_id
 * @param {string} zm_id
 * @param {string} simInfo
 * @param {string} imsi
 * @param {string} puk1
 * @param {string} puk2
 */
const updateSimZaikoInfoFullMvno = async (so_id, zm_id, simInfo, imsi, puk1, puk2) => {
    if (!so_id) throw new Error('soId is required');
    if (!zm_id) throw new Error('zm_id is required');
    if (!simInfo) throw new Error('simInfo is required');

    await MNPTennyuSoModel.updateOne(
        { so_id },
        {
            $set: {
                zm_id,
                simInfo,
                imsi,
                puk1,
                puk2,
            },
        },
        { runValidators: true }
    ).exec();
};

/**
 * 回線番号設定（回線新設の交換機設定OKだったとき用）
 * @param {string} so_id
 * @param {string} kaisenNo
 */
const setKaisenNo = async (so_id, kaisenNo) => {
    await MNPTennyuSoModel.updateOne({ so_id }, { $set: { kaisenNo } }, { runValidators: true }).exec();
};

/**
 * similar to `setKaisenNo` but returns updated SO
 * @param {string} so_id
 * @param {string} kaisenNo
 */
const setKaisenNoAndReturnUpdated = async (so_id, kaisenNo) => {
    return await MNPTennyuSoModel.findOneAndUpdate(
        { so_id },
        { $set: { kaisenNo } },
        { runValidators: true, new: true }
    ).exec();
};

/**
 * SIM在庫割当解除
 * @param {string} so_id
 */
const unsetSimZaikoInfo = async (so_id) => {
    await MNPTennyuSoModel.updateOne({ so_id }, { $unset: { zm_id: 1, simInfo: 1 } }, { runValidators: true }).exec();
};

/**
 * 焼付けSIM際選択必要日時を設定する
 * @param {string} so_id
 * @param {number} nichiji
 */
const setYakitsukeSIMSaisentakuHitsuyoNichiji = async (so_id, nichiji) => {
    await MNPTennyuSoModel.updateOne(
        { so_id },
        { $set: { yakitsukeSIMSaisentakuHitsuyoNichiji: nichiji } },
        { runValidators: true }
    ).exec();
};

/**
 * create SO for MNP関連照会
 * - encrypt kojin info fields if has value (契約者名義, 契約者名義(半角カナ), 生年月日)
 * @param {object} context
 * @param {object} data
 * @param {string} data.craetedUserId
 * @param {string} data.tenantId
 * @param {string} data.pattern
 * @param {string} data.lineNo
 * @param {string} data.mnpNo
 * @param {string} data.contractName
 * @param {string} data.contractNameKana
 * @param {string} data.property
 * @param {string} [data.birthday]
 * @param {string} data.contractType
 * @param {string} [data.carrierCode]
 * @param {number} data.kouteiId
 * @param {string} [data.ngReason]
 * @param {number} data.uketsukeDateTime
 *
 */
const createMNPCheckSO = async (
    context,
    {
        createdUserId,
        tenantId,
        pattern,
        lineNo,
        mnpNo,
        contractName,
        contractNameKana,
        property,
        birthday,
        contractType,
        carrierCode,
        kouteiId,
        ngReason,
        uketsukeDateTime,
    }
) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!tenantId) throw new Error('tenantId is required');
    if (!kouteiId) throw new Error('kouteiId is required');
    const nowSecs = dayjs().unix();
    let kouteiObj = {
        kouteiId,
        timestamp: nowSecs,
        userId: createdUserId,
    };
    if (ngReason) {
        kouteiObj.ngReason = ngReason;
    }
    const newData = {
        lastUpdateUserId: createdUserId,
        updateAt: nowSecs,
        uketsukeDateTime,
        tenantId,
        pattern,
        kaisenNo: lineNo,
        mnpNo,
        contractName: contractName ? encryptText(contractName) : null,
        contractNameKana: contractNameKana ? encryptText(contractNameKana) : null,
        zokusei: property,
        birthday: birthday ? encryptText(birthday) : null,
        contractType,
        carrierCode,
        soKind: SO_KIND_TYPE.MNP_CHECK,
        kouteis: [kouteiObj],
        lastKoutei: kouteiObj,
    };
    let newSo = new MNPTennyuSoModel(removeNullValue(newData));
    newSo.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSo._id;
    const savedDoc = await newSo.save();
    return savedDoc.so_id;
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {string} data.createdUserId
 * @param {string} data.tenantId
 * @param {string} data.tempoId
 * @param {number} data.planId
 * @param {number} data.kouteiId
 * @param {number} data.uketsukeDateTime,
 * @param {Array.<string>} [data.kaisenOptions]
 * @param {string} [data.simType]
 * @param {string} [data.contractType]
 * @param {string} [data.Nno]
 * @param {string} [data.shopMemo]
 * @param {string} [data.ngReason]
 * @param {string} [data.voicePlanId]
 * @param {boolean}
 */
const createShinkiOTASO = async (
    context,
    {
        createdUserId,
        tenantId,
        tempoId,
        planId,
        kouteiId,
        uketsukeDateTime,
        kaisenOptions,
        simType,
        contractType,
        Nno,
        shopMemo,
        ngReason,
        voicePlanId,
    }
) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!kouteiId) throw new Error('kouteiId is required');

    const is020Support = await TenantPlanLineOptionSettingModel.check020Support(tenantId, planId);

    const nowSecs = dayjs().unix();
    let newData = {
        lastUpdateUserId: createdUserId,
        updatedAt: nowSecs,
        tenantId,
        tempoId,
        planId,
        uketsukeDateTime,
        is020Support,
        hankuro: SIM_ORDER_TYPES.OTA,
        soKind: SO_KIND_TYPE.NEW_OTA_SERVICE_ORDER, // for MVNO 7.0
        kaisenOptions,
        simType,
        Nno,
        shopMemo,
        voicePlanId, // STEP 16.0 0035でんわ
        contractType, // STEP 18: 契約種別
    };

    let kouteiObj = {
        kouteiId,
        timestamp: nowSecs,
        userId: createdUserId,
    };
    if (ngReason) {
        kouteiObj.ngReason = ngReason;
    }
    newData.lastKoutei = kouteiObj;
    newData.kouteis = [kouteiObj];

    let newSo = new MNPTennyuSoModel(removeNullValue(newData));
    newSo.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSo._id;
    const savedDoc = await newSo.save();
    return savedDoc.so_id;
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {string} data.createdUserId
 * @param {string} data.tenantId
 * @param {string} data.tempoId
 * @param {number} data.planId
 * @param {string} data.eid
 * @param {number} data.kouteiId
 * @param {number} data.uketsukeDateTime,
 * @param {Array.<string>} [data.kaisenOptions]
 * @param {string} [data.contractType]
 * @param {string} [data.Nno]
 * @param {string} [data.shopMemo]
 * @param {string} [data.notifyPattern]
 * @param {string} [data.dummyLineNo]
 * @param {string} [data.ngReason]
 * @param {string} [data.voicePlanId]
 * @param {boolean}
 */
const createShinkiESIMSO = async (
    context,
    {
        createdUserId,
        tenantId,
        tempoId,
        planId,
        eid,
        kouteiId,
        uketsukeDateTime,
        kaisenOptions,
        contractType,
        Nno,
        shopMemo,
        notifyPattern,
        dummyLineNo,
        ngReason,
        voicePlanId,
    }
) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!kouteiId) throw new Error('kouteiId is required');

    const is020Support = await TenantPlanLineOptionSettingModel.check020Support(tenantId, planId);

    const nowSecs = dayjs().unix();
    let newData = {
        lastUpdateUserId: createdUserId,
        updatedAt: nowSecs,
        tenantId,
        tempoId,
        planId,
        eid,
        simInfo: eid, // save `eid` to `simInfo` too
        uketsukeDateTime,
        hankuro: SIM_ORDER_TYPES.ESIM,
        soKind: SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER,
        kaisenOptions,
        simType: SIM_TYPES_ESIM,
        Nno,
        shopMemo,
        notifyPattern,
        dummyLineNo,
        voicePlanId, // STEP 16.0 0035でんわ
        contractType, // STEP 18: 契約種別
        is020Support,
    };

    let kouteiObj = {
        kouteiId,
        timestamp: nowSecs,
        userId: createdUserId,
    };
    if (ngReason) {
        kouteiObj.ngReason = ngReason;
    }
    newData.lastKoutei = kouteiObj;
    newData.kouteis = [kouteiObj];

    let newSo = new MNPTennyuSoModel(removeNullValue(newData));
    newSo.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSo._id;
    const savedDoc = await newSo.save();
    return savedDoc.so_id;
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {string} data.createdUserId
 * @param {string} data.tenantId
 * @param {string} data.tempoId
 * @param {number} data.kouteiId
 * @param {number} data.uketsukeDateTime
 * @param {string} [data.kaisenNo]
 * @param {string} [data.simType]
 * @param {string} [data.hankuro]
 * @param {string} [data.ngReason]
 * @param {string} [data.contractType]
 * @param {ShippingInfo} [data.shippingInfo]
 * @param {boolean} [data.needSwimmyNoti]
 * @param {string} [data.eid]
 * @returns {Promise<string>} SO-ID
 */
const createSIMSaihakkouSO = async (
    context,
    {
        createdUserId,
        tenantId,
        tempoId,
        kouteiId,
        uketsukeDateTime,
        kaisenNo,
        simType,
        hankuro,
        ngReason,
        contractType, // STEP 7.0で配送情報を追加
        shippingInfo,
        needSwimmyNoti, // STEP 22: SwimmyへSIM再発行通知
        eid, // STEP23.0: EIDを追加
    }
) => {
    context.log('createSIMSaihakkouSO START');
    const nowSecs = dayjs().unix();

    let kouteiObj = {
        kouteiId,
        timestamp: nowSecs,
        userId: createdUserId,
    };
    if (ngReason) {
        kouteiObj.ngReason = ngReason;
    }

    let data = {
        lastUpdateUserId: createdUserId,
        updatedAt: nowSecs,
        tenantId,
        tempoId,
        kouteis: [kouteiObj],
        lastKoutei: kouteiObj,
        uketsukeDateTime,
        kaisenNo,
        simType,
        hankuro,
        soKind: SO_KIND_TYPE.MNP_SIM_SAIHAKKOU,
        contractType, // STEP 20: SIM再発行も契約種別を追加
        needSwimmyNoti, // STEP 22: SwimmyへSIM再発行通知対応
        simInfo: eid, // STEP23.0: EIDを追加
        eid: eid, // STEP23.0: EIDを追加
    };
    const cleanAddress = removeNullValue(shippingInfo);
    if (cleanAddress) {
        data.deliveryFlag = cleanAddress.deliveryFlag;
        if ((cleanAddress.deliveryFlag || '0') === '1') {
            Object.assign(data, cleanAddress);
            data.addressNo = await createAddressNo(data.tenantId);
        }
    }

    let newSO = new MNPTennyuSoModel(removeNullValue(data));
    newSO.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSO._id;
    const savedSO = await newSO.save();
    return savedSO?.so_id;
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {string} data.createdUserId
 * @param {string} data.tenantId
 * @param {string} data.tempoId
 * @param {number} data.kouteiId
 * @param {number} data.uketsukeDateTime,
 * @param {string} data.kaisenNo
 * @param {string} data.soKind
 * @param {string} [data.ngReason]
 * @param {boolean} data.isFM
 * @returns {Promise<string>} SOID
 */
const createServiceStopResumeSO = async (
    context,
    { createdUserId, tenantId, tempoId, kouteiId, uketsukeDateTime, kaisenNo, soKind, ngReason, isFM }
) => {
    context.log('createServiceStopResumeSO START');
    const nowSecs = dayjs().unix();

    let kouteiObj = {
        kouteiId,
        timestamp: nowSecs,
        userId: createdUserId,
    };
    if (ngReason) {
        kouteiObj.ngReason = ngReason;
    }

    let data = {
        lastUpdateUserId: createdUserId,
        updatedAt: nowSecs,
        tenantId,
        tempoId,
        kouteis: [kouteiObj],
        lastKoutei: kouteiObj,
        uketsukeDateTime,
        kaisenNo,
        soKind,
    };

    let newSO = new MNPTennyuSoModel(removeNullValue(data));
    if (isFM) {
        newSO.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + newSO._id;
        newSO.isFM = true;
    } else {
        newSO.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSO._id;
    }
    const savedSO = await newSO.save();
    return savedSO?.so_id;
};

/**
 * SO取得(SO_ID複数 or 条件)
 * @param {object} context - context object
 * @param {string[]} pSoIds - pSoId
 * @returns {array.<mnpTennyuSoSchema>|null} - [mnpTennyuSoSchema] or []
 */
const findSOByParentSOIDs = async (context, pSoIds) => {
    context.log('MNPTennyuSoModel findSOByParentSOIDs START: ' + pSoIds);
    if (Array.isArray(pSoIds) && pSoIds.length === 0) {
        return [];
    } else {
        const query = { pSoId: { $in: pSoIds } };
        const sort = { _id: -1 };
        return await MNPTennyuSoModel.find(query).sort(sort).exec();
    }
};

/**
 * @param {object} context - context object
 * @param {string} soId - soId
 * @param {string} tenantId - tenantId
 * @returns {mnpTennyuSoSchema|null} - mnpTennyuSO object or null
 */
const findSOBySOIDAndTenantId = async (context, { soId, tenantId }) => {
    context.log('MNPTennyuSoModel findSOBySOIDAndTenantId START');
    if (!soId) throw new Error('soId is required');
    if (!tenantId) throw new Error('tenantId is required');

    const query = {
        so_id: soId,
        tenantId,
    };
    return await MNPTennyuSoModel.findOne(query).exec();
};

/**
 * @param {object} context - context object
 * @param {string} soId - soId
 * @returns {mnpTennyuSoSchema|null} - mnpTennyuSO object or null
 */
const findSOBySOID = async (context, soId) => {
    context.log('MNPTennyuSoModel findSOBySOID START');
    // if (!soId) throw new Error('soId is required');
    if (isNone(soId)) return null; // allow undefined or empty soId (like aladinSOID of kurokaSO)

    const query = {
        so_id: soId,
    };
    return await MNPTennyuSoModel.findOne(query).exec();
};

/**
 * Check `isFM` flag of service order
 * @param {string} tenantId
 * @param {string} soId
 * @param {boolean=false} notFoundValue
 * @returns {Promise<boolean|null>}
 */
const checkFMByTenantIdAndSoId = async (tenantId, soId, notFoundValue = false) => {
    const found = await MNPTennyuSoModel.findOne({ tenantId, so_id: soId });
    return found ? found.isFM : notFoundValue;
};

/* ネットワーク契約変更
 * @param {object} context
 * @param {object} data
 * @param {string} data.createdUserId
 * @param {string} data.tenantId
 * @param {string} data.tempoId
 * @param {number} data.kouteiId
 * @param {number} data.uketsukeDateTime
 * @param {string} data.kaisenNo
 * @param {string} data.beforeContractType
 * @param {string} data.afterContractType
 * @param {string} [data.ngReason]
 * @param {boolean} data.isFrom9a1z
 * @returns {Promise<string>} new SO-ID
 */
const createNWContractChangeSO = async (
    context,
    {
        createdUserId,
        tenantId,
        tempoId,
        kouteiId,
        uketsukeDateTime,
        kaisenNo,
        beforeContractType,
        afterContractType,
        ngReason,
        isFrom9a1z,
    }
) => {
    const nowSecs = dayjs().unix();

    const kouteiObj = removeNullValue({
        kouteiId,
        timestamp: nowSecs,
        userId: createdUserId,
        ngReason,
    });

    let newData = {
        lastUpdateUserId: createdUserId,
        updatedAt: nowSecs,
        tenantId,
        tempoId,
        kouteis: [kouteiObj],
        lastKoutei: kouteiObj,
        uketsukeDateTime,
        kaisenNo,
        beforeContractType,
        afterContractType,
        isFrom9a1z,
        soKind: SO_KIND_TYPE.CONTRACT_TYPE_CHANGE_ORDER,
    };

    let newSo = new MNPTennyuSoModel(removeNullValue(newData));
    newSo.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + newSo._id;
    const savedDoc = await newSo.save();
    return savedDoc.so_id;
};

/**
 * SOID新規で工程が完了になった新規SOを取得
 * @param {object} context - context object
 * @param {string} pSoId - pSoId
 * @returns {number} - length of array
 */
const getAllCompletedSO = async (context, pSoId) => {
    //SO-ID新規受付に属している新規SOを検索
    context.log('MNPTennyuSoModel getAllCompletedSO START ' + pSoId);
    if (!pSoId) throw new Error('pSoId is required');

    const docs = await MNPTennyuSoModel.find({ pSoId: pSoId }).exec();
    return docs.filter((doc) => doc.koutei === KOUTEI_IDS.KOUKANKI_SETTEI_OK || doc.koutei === KOUTEI_IDS.KANRYOU)
        .length;
};

/**
 * 最後の工程を更新
 * @param  {object} context - API context
 * @param  {string} soId
 * @param  {number} kouteiId
 * @param  {string} updateUserId
 * @param  {string} ngReason
 */
const updateLastKouteiInfo = async (context, { soId, kouteiId, updateUserId, ngReason }) => {
    context.log('MNPTennyuSoModel updateLastKouteiInfo START');
    if (!soId) throw new Error('soId is required');
    if (!kouteiId) throw new Error('kouteiId is required');
    if (!updateUserId) throw new Error('updateUserId is required');
    if (!ngReason) throw new Error('ngReason is required');

    const oldSo = await findSOBySOID(context, soId);
    const kouteiList = oldSo.kouteis ?? [];
    const removedLastKouteiList = _.dropRight(kouteiList, 1);
    let kouteisList = [];
    removedLastKouteiList &&
        removedLastKouteiList.length > 0 &&
        removedLastKouteiList.map((koutei) => {
            const kouteisObj = {
                kouteiId: koutei.kouteiId ?? KOUTEI_IDS.UKETSUKE_CHECK_NG,
                timestamp: koutei.timestamp ?? 0,
                userId: koutei.userId ?? '',
                ngReason: koutei.ngReason,
            };
            kouteisList.push(removeNullValue(kouteisObj));
        });

    const lastKouteiObj = {
        kouteiId: kouteiId,
        timestamp: dayjs().unix(),
        userId: updateUserId,
        ngReason: ngReason,
    };
    kouteisList.push(lastKouteiObj);

    await MNPTennyuSoModel.updateOne(
        { so_id: soId },
        { $set: { kouteis: kouteisList, lastKoutei: lastKouteiObj } },
        { runValidators: true }
    ).exec();
};

/**
 * SO取得(SO_ID複数 or 条件)
 * @param {object} context
 * @param {string[]} soIds
 * @returns {Promise<[mnpTennyuSoSchema]|[]>}
 */
const findSOBySOIDs = async (context, soIds) => {
    context.log('MNPTennyuSoModel findSOBySOIDs START');
    if (isNone(soIds)) {
        return [];
    } else {
        return await MNPTennyuSoModel.find({ so_id: { $in: soIds } })
            .sort({ _id: 1 })
            .exec();
    }
};

/**
 * It updates the additional_so field of the mnp_tennyu_so collection.
 * @param context - The context object that is passed to the resolver function.
 * @param soIds - an array of SO IDs
 */
const addAdditionalSOToSpecifiedSOIDs = async (context, soIds) => {
    // for MVNO 3.5 additional_soを追加する
    // hankuro が 半黒MNPで、so_kindがMNP転入で、processIDが交換器設定OKか完了の時 => 「黒化受付」リンクがでる
    // so_kind が MNP転出予約で、processIDが交換器設定OKか完了の時 => 「MNP転出予約キャンセル受付」リンクがでる

    // for MVNO 4.0 additional_soを追加する
    // hankuro が 半黒で、so_kindがMNP転入で、processIDが交換器設定OKか完了の時 => 「黒化受付」リンクがでる
    const queryHankuro = {
        'lastKoutei.kouteiId': {
            $in: [KOUTEI_IDS.KOUKANKI_SETTEI_OK, KOUTEI_IDS.KANRYOU],
        },
        so_id: { $in: soIds },
        $or: [
            {
                hankuro: SIM_ORDER_TYPES.HANKURO_MNP,
                soKind: SO_KIND_TYPE.MNP_TENNYU,
            },
            {
                hankuro: SIM_ORDER_TYPES.HANKURO,
                soKind: SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER,
            },
        ],
    };
    const updateHankuroAdditionalSO = {
        $set: {
            additional_so: MNPTennyuSoConstants.HANKURO_ADDITIONAL_SO,
        },
    };

    const queryMNPTenshutsu = {
        'lastKoutei.kouteiId': {
            $in: [KOUTEI_IDS.KOUKANKI_SETTEI_OK, KOUTEI_IDS.KANRYOU],
        },
        so_id: { $in: soIds },
        soKind: SO_KIND_TYPE.MNP_TENSHUTSU,
    };

    const updateMNPTenshutsuAdditionalSO = {
        $set: {
            additional_so: MNPTennyuSoConstants.MNP_TENSYUTSU_YOYAKU_CANCEL_ADDITIONAL_SO,
        },
    };

    return Promise.all([
        MNPTennyuSoModel.updateMany(queryHankuro, updateHankuroAdditionalSO).exec(),
        MNPTennyuSoModel.updateMany(queryMNPTenshutsu, updateMNPTenshutsuAdditionalSO).exec(),
    ]);
};

const addCompleteKouteiInfoToSOsSpecifiedSOIDs = async (context, soIds) => {
    const query = {
        'lastKoutei.kouteiId': KOUTEI_IDS.KOUKANKI_SETTEI_OK,
        so_id: { $in: soIds },
    };
    const kouteiObj = {
        kouteiId: KOUTEI_IDS.KANRYOU,
        timestamp: dayjs().unix(),
        userId: '',
    };
    const update = {
        $push: {
            kouteis: kouteiObj,
        },
        $set: {
            lastKoutei: kouteiObj,
        },
    };

    const wr = await MNPTennyuSoModel.updateMany(query, update).exec();
    //additional_soを追加する
    await addAdditionalSOToSpecifiedSOIDs(context, soIds);

    return wr.modifiedCount;
};

/**
 *
 * @param {object} context
 * @param {number} searchType
 * @param {string} searchValue
 * @param {string[]} tenantIds
 * @param {string[]} tempoIds
 * @param {number[]} processIds
 * @param {number} fromDate
 * @param {number} toDate
 * @param {string[]} hankuros
 * @param {string} kohaiFlag
 * @param {boolean} sameMvneInFlag
 * @param {number} toDate
 * @param {number} roleId
 * @param {boolean} isExcludeNewSO
 * @param {string[]} soIds
 * @param {boolean} isAllSelected
 * @param {number} service
 * @returns {number} - count
 */
const countTotalSO = async (
    context,
    searchType,
    searchValue,
    tenantIds,
    tempoIds,
    processIds,
    fromDate,
    toDate,
    hankuros,
    kohaiFlag,
    sameMvneInFlag,
    soKind,
    roleId,
    isExcludeNewSO,
    soIds,
    isAllSelected,
    service
) => {
    context.log('MNPTennyuSoModel countTotalSO START');
    const isFullMVNO = service ? service : MVNO_SERVICES.ALL;
    // create query string
    const query = await createQuery(
        context,
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        processIds,
        fromDate,
        toDate,
        hankuros,
        kohaiFlag,
        sameMvneInFlag,
        soKind,
        roleId,
        isExcludeNewSO,
        soIds,
        isAllSelected,
        isFullMVNO
    );
    return await MNPTennyuSoModel.find(query).count();
};

/**
 *
 * @param {object} context
 * @param {number} searchType
 * @param {string} searchValue
 * @param {string[]} tenantIds
 * @param {string[]} tempoIds
 * @param {number[]} processIds
 * @param {number} fromDate
 * @param {number} toDate
 * @param {string[]} hankuros
 * @param {string} kohaiFlag
 * @param {boolean} sameMvneInFlag
 * @param {number} toDate
 * @param {number} roleId
 * @param {boolean} isExcludeNewSO
 * @param {string[]} soIds
 * @param {boolean} isAllSelected
 * @param {number} pageNumber
 * @param {number} limit
 * @param {number} service
 * @returns {Promise<[mnpTennyuSoSchema]|[]>} [mnpTennyuSoSchema] or []
 */
const findBySoIdsWithPageNumber = async (
    context,
    searchType,
    searchValue,
    tenantIds,
    tempoIds,
    processIds,
    fromDate,
    toDate,
    hankuros,
    kohaiFlag,
    sameMvneInFlag,
    soKind,
    roleId,
    isExcludeNewSO,
    soIds,
    isAllSelected,
    pageNumber = 1,
    limit = 100,
    service
) => {
    context.log('MNPTennyuSoModel findBySoIdsWithPageNumber START');
    const isFullMVNO = service ? service : MVNO_SERVICES.ALL;
    const query = await createQuery(
        context,
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        processIds,
        fromDate,
        toDate,
        hankuros,
        kohaiFlag,
        sameMvneInFlag,
        soKind,
        roleId,
        isExcludeNewSO,
        soIds,
        isAllSelected,
        isFullMVNO
    );
    const sort = { _id: 1 };
    const skip = pageNumber > 0 ? (pageNumber - 1) * limit : 0;
    return await MNPTennyuSoModel.find(query).sort(sort).limit(limit).skip(skip);
};

/**
 * Update MNP number and/or expiration date
 * @param {object} context
 * @param {string} so_id
 * @param {string} [mnpNo]
 * @param {number} [mnpExpired]
 */
const setMNPNoAndExpiration = async (context, so_id, mnpNo, mnpExpired) => {
    if (isNone(so_id)) throw new Error('so_id is required');
    if ((mnpNo === undefined || mnpNo === null) && (mnpExpired === undefined || mnpExpired === null)) {
        throw new Error('mnpNo or mnpExpired should be defined');
    }
    const update = {};
    if (!isNone(mnpNo)) update.mnpNo = mnpNo;
    if (!isNone(mnpExpired)) update.mnpExpired = mnpExpired;

    return await MNPTennyuSoModel.updateOne(
        { so_id, soKind: SO_KIND_TYPE.MNP_TENSHUTSU },
        { $set: update },
        { runValidators: true }
    ).exec();
};

const searchSOForHankuroSIMSaihakkou = async (context, kaisenNo, soKind, hankuro) => {
    return await MNPTennyuSoModel.findOne({ kaisenNo, soKind, hankuro }).sort({ uketsukeDateTime: -1 });
};

/**
 *
 * @param {object} context - context object
 * @param {string} addressNo - addressNo
 * @returns {mnpTennyuSoSchema|null} - mnpTennyuSoSchema or null
 */
const findSOByAddressNo = async (context, addressNo) => {
    context.log('MNPTennyuSoModel findSOByAddressNo START ', addressNo);
    if (!addressNo) throw new Error('addressNo is required');

    return await MNPTennyuSoModel.findOne({
        $and: [{ addressNo: addressNo }, { soKind: { $ne: SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER } }],
    }).exec();
};

/**
 *
 * @param {object} context - context object
 * @param {string} addressNo - addressNo
 * @param {string} denpyoNo - denpyoNo
 * @returns {boolean}
 */
const setDenpyoNo = async (context, addressNo, denpyoNo) => {
    context.log('MNPTennyuSoModel setDenpyoNo START ', addressNo, ', ', denpyoNo);
    if (!addressNo) throw new Error('addressNo is required');
    if (!denpyoNo) throw new Error('denpyoNo is required');

    const soByAddressNo = await findSOByAddressNo(context, addressNo);
    const isNew = isNone(soByAddressNo.denpyoNo);
    await MNPTennyuSoModel.updateOne({ addressNo: addressNo }, { $set: { denpyoNo: denpyoNo } }).exec();
    return isNew;
};

/**
 *
 * @param {object} context
 * @param {number} processId
 * @param {string} hankuro
 * @param {string} soKind
 * @param {number} desiredDeliveryDate
 * @param {string[]} tenantIds
 * @returns {Promise<[mnpTennyuSoSchema]|[]>} [mnpTennyuSoSchema] or []
 */
const searchSOForDesiredDeliveryDate = async (context, processId, hankuro, soKind, desiredDeliveryDate, tenantIds) => {
    context.log('MNPTennyuSoModel searchSOForDesiredDeliveryDate START');

    let query = {};
    query.tenantId = { $in: tenantIds };
    query['lastKoutei.kouteiId'] = processId;
    query.hankuro = hankuro;
    query.soKind = soKind;
    query.desiredDeliveryDate = desiredDeliveryDate;
    query.isSwimmyVLMActiveCreated = { $ne: true };
    const sort = { _id: 1 };
    return await MNPTennyuSoModel.find(query).sort(sort).exec();
};

/**
 *
 * @param {object} context
 * @param {string} aladinSOId
 */
const markDoneSwimmyVLMRequest = async (context, aladinSOId) => {
    context.log('MNPTennyuSoModel markDoneSwimmyVLMRequest START ', aladinSOId);
    if (isNone(aladinSOId)) throw new Error('aladinSOId is required');

    await MNPTennyuSoModel.updateOne(
        { so_id: aladinSOId },
        { $set: { isSwimmyVLMActiveCreated: true } },
        { runValidators: true }
    ).exec();
};

const execStatisticsLinesMnpTennyu = async (context, year, month, day) => {
    //date start of month
    let _dateStart = dayjs(`${year}-${month}-${day}`).startOf('day').unix();
    //date end of month
    let _dateEnd = dayjs(`${year}-${month}-${day}`).endOf('day').unix();

    const result = await MNPTennyuSoModel.aggregate([
        {
            $match: {
                soKind: 'MNP転入',
                kouteis: {
                    $elemMatch: {
                        kouteiId: 22,
                        timestamp: { $gte: _dateStart, $lte: _dateEnd },
                    },
                },
            },
        },
        {
            $group: {
                _id: {
                    tenantId: '$tenantId',
                    mnpNoHead: { $substr: ['$mnpNo', 0, 2] },
                },
                count: { $sum: 1 },
            },
        },
    ]);

    return result;
};

const execStatisticsLinesMnpTenShutsu = async (context, year, month, day) => {
    //date start of month
    let _dateStart = dayjs(`${year}-${month}-${day}`).startOf('day').unix();
    //date end of month
    let _dateEnd = dayjs(`${year}-${month}-${day}`).endOf('day').unix();

    const result = await MNPTennyuSoModel.aggregate([
        {
            $match: {
                soKind: 'MNP転出予約',
                kouteis: {
                    $elemMatch: {
                        kouteiId: 40,
                        timestamp: { $gte: _dateStart, $lte: _dateEnd },
                    },
                },
            },
        },
        {
            $group: {
                _id: {
                    tenantId: '$tenantId',
                },
                count: { $sum: 1 },
            },
        },
    ]);

    return result;
};

/**
 * Search latest MNPTennyuSO to be used for kuroka
 * @param {object} context
 * @param {string} lineNo
 * @param {string} [tenantId] (optional) tenant for filter
 * @param {string} [tempoId] (optional) tempo for filter
 */
const getServiceOrderForKuroka = async (context, lineNo, tenantId, tempoId) => {
    const query = {
        kaisenNo: lineNo,
        soKind: {
            $in: [
                SO_KIND_TYPE.MNP_TENNYU,
                SO_KIND_TYPE.MNP_TENNYU_VER20,
                SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER,
                SO_KIND_TYPE.MNP_SIM_SAIHAKKOU,
            ],
        },
        hankuro: { $in: [SIM_ORDER_TYPES.HANKURO, SIM_ORDER_TYPES.HANKURO_MNP] },
        'lastKoutei.kouteiId': { $in: [KOUTEI_IDS.KOUKANKI_SETTEI_OK, KOUTEI_IDS.KANRYOU] },
        'kouteis.kouteiId': KOUTEI_IDS.KOUKANKI_SETTEI_OK,
    };
    if (!isNone(tenantId) && typeof tenantId === 'string') {
        query.tenantId = tenantId;
    }
    if (!isNone(tempoId) && typeof tempoId === 'string') {
        query.tempoId = tempoId;
    }

    // get the latest created SO
    return await MNPTennyuSoModel.findOne(query).sort({ uketsukeDateTime: -1 });
};

mnpTennyuSoSchema.statics = {
    handleSwimmyNotifyOK,
    createMNPTennyuSO,
    createMultipleSO,
    createMNPTenshutsuYoyakuSO,
    createKaisenOptionHenkouSO,
    createVoicePlanChangeSO,
    updateKouteiInfo,
    getMnpTennyuInfoBySoId,
    getMnpTennyuInfoByKaisenNoAndSimInfo,
    setMNPNoForMNPCheck,
    setKurokaSoId,
    searchSO,
    searchSO2,
    searchNewSO,
    createNWPasswordHenkouSO,
    updateSimZaikoInfo,
    updateSimZaikoInfoFullMvno,
    setKaisenNo,
    setKaisenNoAndReturnUpdated,
    unsetSimZaikoInfo,
    setYakitsukeSIMSaisentakuHitsuyoNichiji,
    createMNPCheckSO,
    createShinkiOTASO,
    createShinkiESIMSO,
    createSIMSaihakkouSO,
    createServiceStopResumeSO,
    findSOByParentSOIDs,
    findSOBySOIDAndTenantId,
    findSOBySOID,
    checkFMByTenantIdAndSoId,
    createNWContractChangeSO,
    getAllCompletedSO,
    updateLastKouteiInfo,
    findSOBySOIDs,
    addCompleteKouteiInfoToSOsSpecifiedSOIDs,
    countTotalSO,
    findBySoIdsWithPageNumber,
    setMNPNoAndExpiration,
    searchSOForHankuroSIMSaihakkou,
    findSOByAddressNo,
    setDenpyoNo,
    searchSOForDesiredDeliveryDate,
    markDoneSwimmyVLMRequest,
    execStatisticsLinesMnpTennyu,
    execStatisticsLinesMnpTenShutsu,
    getServiceOrderForKuroka,
};

const MNPTennyuSoModel = mongoose.model('mnp_tennyu_so', mnpTennyuSoSchema, 'mnp_tennyu_so');

const MNPTennyuSoModule = (module.exports = MNPTennyuSoModel);
MNPTennyuSoModule.MNPTennyuSoConstants = MNPTennyuSoConstants;
