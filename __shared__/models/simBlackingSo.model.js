const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// utils
const { createPrefixTempoFind } = require('../utils/stringUtils');

// Helpers & Constants
const { removeNullValue, isNone } = require('../helpers/baseHelper');
const { SIM_ORDER_TYPES } = require('../constants/simConstants');
const { SO_KIND_TYPE, KOUTEI_IDS } = require('../constants/orderConstants');
const { MVNO_SERVICES } = require('../constants/mvnoServicesConstants');
const { callAPILineEnable } = require('../services/core.service');

const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

/** @typedef {import('mongoose').HydratedDocumentFromSchema<simBlackingSoSchema>} SimBlackingSoDocument */

const simBlackingSoSchema = new Schema({
    so_id: { type: String, trim: true, unique: true },
    aladinSOId: { type: String, unique: true },
    createUserId: { type: String, trim: true },
    tempoId: { type: String, required: true },

    // 以下、検索ソート用フィールド
    tenantId: { type: String, required: true },
    kaisenNo: { type: String, trim: true, required: true },
    mnpNo: { type: String, trim: true },
    uketsukeDateTime: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    simInfo: { type: String, trim: true },
    hankuro: { type: String, trim: true },
    //STEP7.0で新MVNO顧客に向け対応のため追加
    blackFlag: { type: String, trim: true },
    contractType: { type: String, trim: true },
    //STEP8.0でと同一MVNEを追加
    sameMvneInFlag: { type: Boolean },
    lineDelTenantId: { type: String, trim: true },
    //STEP14 FULL MVNO
    isFM: { type: Boolean },
    //STEP 16.0 0035denwa
    voicePlanId: { type: String, trim: true },
    //STEP20 Swimmyへ黒化済通知
    notifyHost: { type: String, trim: true },

    lastKoutei: {
        kouteiId: { type: Number, required: true },
        timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
        userId: { type: String, trim: true },
        ngReason: { type: String, trim: true },
    },
    kouteis: [
        {
            _id: false,
            kouteiId: { type: Number, required: true },
            timestamp: { type: Number },
            userId: { type: String, trim: true },
            //===Begin of 4.0====
            //NG理由
            ngReason: { type: String, trim: true },
        },
    ],
});

simBlackingSoSchema.virtual('isComeFromShinMVNO').get(function () {
    const regex = /^S(?![FG])[A-J]/;
    return regex.test(this.contractType ?? '');
});
simBlackingSoSchema.virtual('koutei').get(function () {
    if (this.lastKoutei) {
        return this.lastKoutei.kouteiId;
    } else {
        return KOUTEI_IDS.UKETSUKE_CHECK_NG;
    }
});
simBlackingSoSchema.virtual('is5GNSA').get(function () {
    // add is5GNSA virtual to sim blacking model too
    const regex = /^S[HJ]$/;
    return regex.test(this.contractType ?? '');
});

const SimBlackingSoConstants = {
    // for MVNO 3.5
    HANKURO_ADDITIONAL_SO: '黒化受付',
    MNP_TENSYUTSU_YOYAKU_CANCEL_ADDITIONAL_SO: 'MNP転出予約キャンセル受付',

    //APIの場合にUserIdのフィールドに記入するためのId
    API_ACTION_ID: '-1',

    //STEP14 fullMvno 利用状態
    RIYOU_CHUU: '0',
    RIYOU_CHUUDAN_CHUU: '1',
};

/**
 * @param {object} context
 * @param {object} apiLog SwimmyApiLog document
 * @param {object} selfObj SimBlackingSo document
 * @returns {Promise<string>} processCode
 */
const handleSwimmyNotifyOK = async (context, apiLog, selfObj) => {
    const csvUnnecessary = '1';
    const sameMvne = selfObj.sameMvneInFlag ? '1' : '0';
    return await callAPILineEnable(context, {
        targetSoId: selfObj?.so_id,
        targetTenantId: apiLog.tenantId,
        lineNo: apiLog.kaisenNo,
        csvUnnecessaryFlag: csvUnnecessary,
        sameMvneInFlag: sameMvne,
    });
};

/**
 * Create SimBlackingSO
 * @param  {object} context - API context
 * @param  {object} data
 * @param  {string} data.createdUserId        - created userId
 * @param  {KouteiInfo} data.koutei           - order processing status
 * @param  {object} data.simBlackingData      - required data for import
 * @return {string|null} soId            - if successful return order id (soId)
 */
const createSO = async (context, { createdUserId, koutei, simBlackingData }) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');
    if (!simBlackingData || Object.keys(simBlackingData).length === 0) throw new Error('simBlackingData is required');

    const nowSec = dayjs().unix();
    let simBlackingSo = new SimBlackingSoModel(removeNullValue(simBlackingData));

    simBlackingSo.soKind = SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER;
    simBlackingSo.createUserId = createdUserId;
    simBlackingSo.uketsukeDateTime = nowSec;

    simBlackingSo.lastKoutei = removeNullValue(koutei);
    simBlackingSo.lastKoutei.userId = createdUserId;
    simBlackingSo.lastKoutei.timestamp = nowSec;
    simBlackingSo.kouteis = [simBlackingSo.lastKoutei];

    if (simBlackingSo.isFM === true) {
        simBlackingSo.so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + simBlackingSo._id;
    } else {
        simBlackingSo.so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + simBlackingSo._id;
    }

    const savedDoc = await simBlackingSo.save();
    return savedDoc?.so_id;
};

/**
 * find documents by so_id and update kouteis info
 * 旧メソッド: addKouteiInfo
 * @param  {object} context - API context
 * @param  {object} data
 * @param  {string} data.so_id                - for find query
 * @param  {string} data.updatedUserId
 * @param  {KouteiInfo} data.koutei           - order processing status
 * @return {MNPTennyuSchema|null}                 - if successful return updatedObject else return null
 */
const updateKouteiInfo = async (context, { so_id, updatedUserId, koutei }) => {
    if (!so_id) throw new Error('so_id is required');
    if (!updatedUserId) throw new Error('updatedUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');

    koutei.userId = updatedUserId;
    koutei.timestamp ||= dayjs().unix();

    const lastKoutei = removeNullValue(koutei);

    return await SimBlackingSoModel.findOneAndUpdate(
        { so_id },
        {
            $set: { lastKoutei },
            $push: { kouteis: lastKoutei },
        },
        { runValidators: true, context: 'query', new: true }
    ).exec();
};

/**
 *
 * @param {object} context - context object
 * @param {number} searchTypeo - searchType
 * @param {string} searchValueo - searchValue
 * @param {string[]} tenantIdo - tenantId
 * @param {string[]} tempoIdo - tempoId
 * @param {number[]} processIdo - processId
 * @param {number} fromDateo - receptDateBeginKey
 * @param {number} toDateo - receptDateEndKey
 * @param {string} hankuroo - simOrderType
 * @param {number} limito - limit
 * @param {string[]} prefixTempoIdo - prefixTempoId
 * @returns {array.<kurokaSO>} - kurokaSO list
 */
const searchSO = async (
    context,
    { searchTypeo, searchValueo, tenantIdo, tempoIdo, processIdo, fromDateo, toDateo, hankuroo, limito, prefixTempoIdo }
) => {
    const soList = await searchSO2(
        context,
        searchTypeo,
        searchValueo,
        tenantIdo,
        tempoIdo,
        processIdo,
        fromDateo,
        toDateo,
        hankuroo,
        null,
        limito,
        null,
        false,
        MVNO_SERVICES.ALL,
        prefixTempoIdo
    );

    return soList?.cursor;
};

/**
 *
 * @param {object} context - context object
 * @param {number} searchTypeo - searchType
 * @param {string} searchValueo - searchValue
 * @param {string[]} tenantIdo - tenantId
 * @param {string[]} tempoIdo - tempoId
 * @param {number[]} processIdo - processId
 * @param {number} fromDateo - receptDateBeginKey
 * @param {number} toDateo - receptDateEndKey
 * @param {string} hankuroo - simOrderType
 * @param {number} skipo - skip
 * @param {number} limito - limit
 * @param {string} sortByo - sortBy
 * @param {boolean} isSortOrderAscending - sorting order up or down
 * @param {number} service - liteMVNO or fullMVNO or both
 * @param {string[]} prefixTempoIdo - prefixTempoId
 * @returns {array.<kurokaSO>, number} - kurokaSO list and result count
 */
const searchSO2 = async (
    context,
    searchTypeo,
    searchValueo,
    tenantIdo,
    tempoIdo,
    processIdo,
    fromDateo,
    toDateo,
    hankuroo,
    skipo,
    limito,
    sortByo,
    isSortOrderAscending,
    service,
    prefixTempoIdo
) => {
    context.log('SimBlackingSoModel searchSO2 START');
    var searchBySoIdFlg = false;
    var searchByTenantId = false;
    var searchBySimInfo = false;
    var searchByKaisenNo = false;
    let query = {};
    switch (searchTypeo) {
        case 0:
            query.so_id = { $regex: searchValueo, $options: 'i' };
            searchBySoIdFlg = true;
            break;
        case 1:
            query.kaisenNo = { $regex: searchValueo, $options: 'i' };
            searchByKaisenNo = true;
            break;
        case 2:
            query.simInfo = { $regex: searchValueo, $options: 'i' };
            searchBySimInfo = true;
            break;
        default:
            break;
    }

    if (Array.isArray(tenantIdo) && tenantIdo.length > 0) {
        query.tenantId = { $in: tenantIdo };
        searchByTenantId = true;
    }

    if (Array.isArray(tempoIdo) && tempoIdo.length > 0) {
        query.tempoId = { $in: tempoIdo };
    }

    //部分一致かつ複数検索: https://mobilus.backlog.jp/view/MVNO_N_M-2123#comment-131332889
    if (Array.isArray(prefixTempoIdo) && prefixTempoIdo.length > 0) {
        const prefixRegex = createPrefixTempoFind(prefixTempoIdo);
        query.tempoId = { $regex: prefixRegex };
    }

    if (Array.isArray(processIdo) && processIdo.length > 0) {
        query['lastKoutei.kouteiId'] = { $in: processIdo };
    }

    if (fromDateo && !toDateo) {
        query.uketsukeDateTime = { $gte: fromDateo };
    } else if (!fromDateo && toDateo) {
        query.uketsukeDateTime = { $lt: toDateo };
    } else if (fromDateo && toDateo) {
        query.uketsukeDateTime = { $gte: fromDateo, $lt: toDateo };
    }

    // TOBI COMMENT
    // hankuroso が None : "hankuro" フィールドの検索条件がなしになるので、全部検索対象になる
    // hankuroso が Some("半黒") : "hankuro"フィールドが存在しているもの(=ver.4.0 の"新規黒化")だけが検索対象
    // hankuroso が Some("aaaaaaaa") : "hankuro"フィールドが存在しないもの(<ver.4.0の"黒化", =ver4.0の"半黒MNP黒化")が検索対象
    if (hankuroo) {
        switch (hankuroo) {
            case SIM_ORDER_TYPES.HANKURO:
                query.hankuro = { $in: [SIM_ORDER_TYPES.HANKURO] };
                break;
            case SIM_ORDER_TYPES.HANKURO_MNP:
                query.hankuro = { $in: [SIM_ORDER_TYPES.HANKURO_MNP] };
                break;
            default:
                query.hankuro = { $in: [hankuroo] };
                break;
        }
    }

    //STEP 14 FULL MVNO
    switch (service) {
        case MVNO_SERVICES.LITE:
            query.isFM = { $ne: true };
            break;
        case MVNO_SERVICES.FULL:
            query.isFM = true;
            break;
        default:
            break;
    }

    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = {};
    if (sortByo) {
        switch (sortByo) {
            case 'so_id':
                sort.so_id = sortAscendingInt;
                break;
            case 'tenant_id':
                sort.tenantId = sortAscendingInt;
                break;
            case 'tempo_id':
                sort.tempoId = sortAscendingInt;
                break;
            case 'uketsuke_date':
                sort.uketsukeDateTime = sortAscendingInt;
                break;
            case 'sim_info':
                sort.simInfo = sortAscendingInt;
                break;
            case 'process_id':
                sort['lastKoutei.kouteiId'] = sortAscendingInt;
                break;
            case 'kaisen_no':
                sort.kaisenNo = sortAscendingInt;
                break;
            case 'hankuro':
                sort.hankuro = sortAscendingInt;
                break;
            default:
                sort.uketsukeDateTime = sortAscendingInt;
                break;
        }
    } else {
        sort.uketsukeDateTime = sortAscendingInt;
    }

    //Step 14 search by so_id use hint and remove sort condition
    let hintObj = {},
        doc = {};
    if (searchBySoIdFlg && !searchByTenantId) {
        hintObj.so_id = 1;
    } else if (searchBySoIdFlg && searchByTenantId) {
        hintObj.tenantId = 1;
        hintObj.so_id = 1;
    } else if (searchByKaisenNo && !searchByTenantId) {
        hintObj.kaisenNo = 1;
    } else if (searchByKaisenNo && searchByTenantId) {
        hintObj.tenantId = 1;
        hintObj.kaisenNo = 1;
    } else if (searchBySimInfo && !searchByTenantId) {
        hintObj.simInfo = 1;
    } else if (searchBySimInfo && searchByTenantId) {
        hintObj.tenantId = 1;
        hintObj.simInfo = 1;
    } else {
        hintObj;
    }

    const count = await SimBlackingSoModel.find(query).count();

    switch (true) {
        case count === 0:
        case count === 1:
            if (Object.keys(hintObj).length === 0) {
                doc.cursor = await SimBlackingSoModel.find(query);
            } else {
                doc.cursor = await SimBlackingSoModel.find(query).hint(hintObj);
            }
            doc.count = count;
            break;
        case count <= limito:
            if (Object.keys(hintObj).length === 0) {
                doc.cursor = await SimBlackingSoModel.find(query).sort(sort);
            } else {
                doc.cursor = await SimBlackingSoModel.find(query).hint(hintObj).sort(sort);
            }
            doc.count = count;
            break;
        default:
            if (limito && !skipo) {
                if (Object.keys(hintObj).length === 0) {
                    doc.cursor = await SimBlackingSoModel.find(query).sort(sort).limit(limito);
                } else {
                    doc.cursor = await SimBlackingSoModel.find(query).hint(hintObj).sort(sort).limit(limito);
                }
            } else if (!limito && skipo) {
                if (Object.keys(hintObj).length === 0) {
                    doc.cursor = await SimBlackingSoModel.find(query).sort(sort).skip(skipo);
                } else {
                    doc.cursor = await SimBlackingSoModel.find(query).hint(hintObj).sort(sort).skip(skipo);
                }
            } else {
                if (Object.keys(hintObj).length === 0) {
                    doc.cursor = await SimBlackingSoModel.find(query).sort(sort).limit(limito).skip(skipo);
                } else {
                    doc.cursor = await SimBlackingSoModel.find(query)
                        .hint(hintObj)
                        .sort(sort)
                        .limit(limito)
                        .skip(skipo);
                }
            }
            doc.count = count;
            break;
    }
    return doc;
};

/**
 *
 * @param {object} context
 * @param {string} soId
 * @returns {Promise<object|null>}
 */
const findSOByKurokaSOId = async (context, soId) => {
    context.log('SimBlackingSoModel findSOByKurokaSOId START');
    if (!soId) throw new Error('soId is required');
    return await SimBlackingSoModel.findOne({ so_id: soId }).exec();
};

/**
 *
 * @param {object} context - context object
 * @param {string} soId - soId
 * @param {string} tenantId - tenantId
 * @returns {object|null} - simBlackingSO object or null
 */
const findSOByKurokaSOIdAndTenantId = async (context, { soId, tenantId }) => {
    context.log('SimBlackingSoModel findSOByKurokaSOIdAndTenantId START');
    if (!soId) throw new Error('soId is required');
    if (!tenantId) throw new Error('tenantId is required');

    const query = {
        so_id: soId,
        tenantId,
    };
    return await SimBlackingSoModel.findOne(query).exec();
};

/**
 * SO取得(SO_ID複数 or 条件)
 * @param {object} context
 * @param {string[]} soIds
 * @returns {Promise<[simBlackingSoSchema]|[]>}
 */
const findSOByKurokaSOIDs = async (context, soIds) => {
    context.log('SimBlackingSoModel findSOByKurokaSOIDs START');
    if (isNone(soIds)) {
        return [];
    } else {
        return await SimBlackingSoModel.find({ so_id: { $in: soIds } }).sort({ _id: -1 }).exec();
    }
};

const findSOBySOID = findSOByKurokaSOId

//statics
simBlackingSoSchema.statics = {
    handleSwimmyNotifyOK,
    createSO,
    updateKouteiInfo,
    searchSO,
    searchSO2,
    findSOByKurokaSOId,
    findSOByKurokaSOIdAndTenantId,
    findSOByKurokaSOIDs,
    findSOBySOID,
};

const SimBlackingSoModel = mongoose.model('sim_blacking_so', simBlackingSoSchema, 'sim_blacking_so');

const SimBlackingSoModule = (module.exports = SimBlackingSoModel);
SimBlackingSoModule.SimBlackingSoConstants = SimBlackingSoConstants;
