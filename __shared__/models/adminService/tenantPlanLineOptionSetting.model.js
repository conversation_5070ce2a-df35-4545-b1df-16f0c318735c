const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
const baseHelper = require('../../helpers/baseHelper');

const tenantPlanLineOptionSettingSchema = new Schema({
    createUserId: { type: String, trim: true, required: true },
    tenantId: { type: String, trim: true, required: true },
    planId: { type: Number, required: true },
    is020Support: { type: Boolean, required: true, default: false },
    isShinki: { type: Boolean, required: true, default: false },
    isShinkiOTA: { type: Boolean, required: true, default: false },
    iseSIM: { type: Boolean, required: true, default: false },
    isMnp: { type: Boolean, required: true, default: false },
    mvnoKakinJyoho: { type: Boolean, set: (kakin) => (typeof kakin == 'boolean' ? kakin : null) },
    lineOptionType1: { type: String, trim: true },
    lineOptionType2: { type: String, trim: true },
    lineOptionType3: { type: String, trim: true },
    lineOptionType4: { type: String, trim: true },
    lineOptionType5: { type: String, trim: true },
    lineOptionType6: { type: String, trim: true },
    updateDateTime: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
});

/**
 * find all planIds by tenantId and ryoukinPlan
 * @param {string} tenantId
 * @param {string} ryoukinPlan
 * @returns {array.<number>|null}  - array of planIds
 */
const findAllPlansForShinki = async (tenantId) => {
    const docs = await TenantPlanLineOptionSettingModel.find(
        {
            tenantId,
            isShinki: true,
        },
        { planId: 1, _id: 0 }
    ).exec();

    return docs?.map((doc) => doc.planId);
};

/**
 * get correspond lineOptionType
 * @param {string} tenantId
 * @param {string|number} planId
 * @param {boolean} isShinki
 * @param {boolean} isMnp
 * @param {boolean} [is020Support]
 * @returns {{object}|null}  - array of planIds
 */
const getlineOptionType = async (tenantId, planId, isShinki, isMnp, is020Support = false) => {
    return await TenantPlanLineOptionSettingModel.findOne(
        {
            tenantId,
            planId: parseInt(planId),
            isShinki,
            isMnp,
            is020Support,
        },
        {
            _id: 0,
            lineOptionType1: 1,
            lineOptionType2: 1,
            lineOptionType3: 1,
            lineOptionType4: 1,
            lineOptionType5: 1,
            lineOptionType6: 1,
        }
    ).exec();
};

/**
 *
 * @param {string} tenantId
 * @param {string|number} planId
 * @param {boolean} isShinki - false/null: no filter
 * @param {boolean} isMnp - false/null: no filter
 * @param {boolean} iseSIM - false/null: no filter
 * @param {boolean} [is020Support] - false/null: no filter
 */
const getDefaultLineOption = async (tenantId, planId, isShinki, isMnp, iseSIM, is020Support = false) => {
    let query = { tenantId, planId: parseInt(planId) };

    if (isShinki) query.isShinki = isShinki;
    if (isMnp) query.isMnp = isMnp;
    if (iseSIM) query.iseSIM = iseSIM;
    if (is020Support) query.is020Support = true;

    return await TenantPlanLineOptionSettingModel.findOne(query).exec();
};

/**
 * get is020Support flag
 * @param {string} tenantId
 * @param {string} planId
 * @param {boolean} isShinki
 * @param {boolean} isMnp
 * @returns {Promise<boolean>}
 */
const check020Support = async (tenantId, planId) => {
    const lineOpt = await TenantPlanLineOptionSettingModel.getDefaultLineOption(
        tenantId,
        planId,
        false, // isShinki
        false, // isMNP
        false, // iseSIM
        false // is020Support
    );
    return lineOpt ? lineOpt.is020Support : false;
};

const getSelectPlanByShinki = async (tenantId) => {
    return (await TenantPlanLineOptionSettingModel.find({ tenantId, isShinki: true })).map((r) => r.planId);
};

const getSelectPlanByMnp = async (tenantId) => {
    return (await TenantPlanLineOptionSettingModel.find({ tenantId, isMnp: true })).map((r) => r.planId);
};

const getSelectPlanByShinkiOTA = async (tenantId) => {
    return (await TenantPlanLineOptionSettingModel.find({ tenantId, isShinkiOTA: true })).map((r) => r.planId);
};

const getSelectPlanByESIM = async (tenantId) => {
    return (await TenantPlanLineOptionSettingModel.find({ tenantId, iseSIM: true })).map((r) => r.planId);
};

const checkPlanExists = async (tenantId, planId, { isShinki, isShinkiOTA, iseSIM, isMnp } = {}) => {
    const q = { tenantId, planId };
    if (isShinki) {
        q.isShinki = true;
    } else if (isShinkiOTA) {
        q.isShinkiOTA = true;
    } else if (iseSIM) {
        q.iseSIM = true;
    } else if (isMnp) {
        q.isMnp = true;
    }
    return (await TenantPlanLineOptionSettingModel.exists(q)) ? true : false;
};


const getDefaultLineOptionByTypeId = async (context, tenantId, planId, isShinki, isMnp, isESIM = false) => {
    context.log('getDefaultLineOptionByTypeId start');
    const defaultLineOption = await getDefaultLineOption(tenantId, planId, isShinki, isMnp, isESIM);

    const result = {};

    if (!baseHelper.isNone(defaultLineOption)) {
        if (!baseHelper.isNone(defaultLineOption.lineOptionType1)) {
            result[1] = defaultLineOption.lineOptionType1;
        }
        if (!baseHelper.isNone(defaultLineOption.lineOptionType2)) {
            result[2] = defaultLineOption.lineOptionType2;
        }
        if (!baseHelper.isNone(defaultLineOption.lineOptionType3)) {
            result[3] = defaultLineOption.lineOptionType3;
        }
        if (!baseHelper.isNone(defaultLineOption.lineOptionType4)) {
            result[4] = defaultLineOption.lineOptionType4;
        }
        if (!baseHelper.isNone(defaultLineOption.lineOptionType5)) {
            result[5] = defaultLineOption.lineOptionType5;
        }
        if (!baseHelper.isNone(defaultLineOption.lineOptionType6)) {
            result[6] = defaultLineOption.lineOptionType6;
        }
    }
    return result;
};

/**
 *
 * @param {object} context
 * @param {string} plusUserId
 * @param {object} tenantPlanOptionInfo
 * @param {string} tenantPlanOptionInfo.tenantId
 * @param {number} tenantPlanOptionInfo.planId
 * @param {boolean} tenantPlanOptionInfo.is020Support
 * @param {boolean} tenantPlanOptionInfo.isShinki
 * @param {boolean} tenantPlanOptionInfo.isShinkiOTA
 * @param {boolean} tenantPlanOptionInfo.iseSIM
 * @param {boolean} tenantPlanOptionInfo.isMnp
 * @param {boolean|null} tenantPlanOptionInfo.mvnoKakinJyohoOpt
 * @param {any} tenantPlanOptionInfo.lineOptionType1
 * @param {any} tenantPlanOptionInfo.lineOptionType2
 * @param {any} tenantPlanOptionInfo.lineOptionType3
 * @param {any} tenantPlanOptionInfo.lineOptionType4
 * @param {any} tenantPlanOptionInfo.lineOptionType5
 * @param {any} tenantPlanOptionInfo.lineOptionType6
 * @returns {Promise<string>} _id
 */
const createOrUpdate = async (context, plusUserId, tenantPlanOptionInfo) => {
    context.log('TenantPlanLineOptionSettingModel createOrUpdate START');

    const nowSecs = dayjs().unix();

    let obj = {};
    obj.createUserId = plusUserId;
    obj.tenantId = tenantPlanOptionInfo.tenantId;
    obj.planId = tenantPlanOptionInfo.planId;
    obj.is020Support = tenantPlanOptionInfo.is020Support;
    obj.isShinki = tenantPlanOptionInfo.isShinki;
    obj.isShinkiOTA = tenantPlanOptionInfo.isShinkiOTA;
    obj.iseSIM = tenantPlanOptionInfo.iseSIM;
    obj.isMnp = tenantPlanOptionInfo.isMnp;
    context.log('tenantPlanOptionInfo.mvnoKakinJyohoOpt :', tenantPlanOptionInfo.mvnoKakinJyohoOpt);
    if (!baseHelper.isNone(tenantPlanOptionInfo.mvnoKakinJyohoOpt)) {
        obj.mvnoKakinJyoho = tenantPlanOptionInfo.mvnoKakinJyohoOpt;
    } else {
        obj.mvnoKakinJyoho = '';
    }
    obj.lineOptionType1 = tenantPlanOptionInfo.lineOptionType1;
    obj.lineOptionType2 = tenantPlanOptionInfo.lineOptionType2;
    obj.lineOptionType3 = tenantPlanOptionInfo.lineOptionType3;
    obj.lineOptionType4 = tenantPlanOptionInfo.lineOptionType4;
    obj.lineOptionType5 = tenantPlanOptionInfo.lineOptionType5;
    obj.lineOptionType6 = tenantPlanOptionInfo.lineOptionType6;
    obj.updateDateTime = nowSecs;

    const oldData = await findByTenantIdPlanInfo(context, tenantPlanOptionInfo);
    if (!baseHelper.isNone(oldData)) {
        await TenantPlanLineOptionSettingModel.updateOne(
            { _id: oldData._id },
            { $set: obj },
            { runValidators: true }
        ).exec();
        return oldData._id.toString();
    } else {
        const tenantPlanLineOptionSetting = new TenantPlanLineOptionSettingModel(obj);
        await tenantPlanLineOptionSetting.save();
        return tenantPlanLineOptionSetting._id.toString();
    }
};

/**
 *
 * @param {object} context
 * @param {object} tenantPlanOptionInfo
 * @returns {Promise<object|null>} tenantPlanLineOptionSettingSchema or null
 */
const findByTenantIdPlanInfo = async (context, tenantPlanOptionInfo) => {
    context.log('TenantPlanLineOptionSettingModel findByTenantIdPlanInfo START');

    let query = { tenantId: tenantPlanOptionInfo.tenantId };
    query.planId = tenantPlanOptionInfo.planId;
    return await TenantPlanLineOptionSettingModel.findOne(query).exec();
};

/**
 *
 * @param {object} context
 * @param {string|null} tenantId
 * @returns {Promise<Array<object>>} [tenantPlanLineOptionSettingSchema]
 */
const findByTenantId = async (context, tenantId) => {
    context.log('TenantPlanLineOptionSettingModel findByTenantId START', tenantId);

    let ands = [];
    if (!baseHelper.isNone(tenantId)) {
        ands.push({ tenantId: tenantId });
    }
    let ors = [];
    ors.push({ isShinki: { $ne: false } });
    // MVNO_M-1848 20180411
    ors.push({ isShinkiOTA: true });
    ors.push({ iseSIM: true });
    ors.push({ isMnp: { $ne: false } });
    ands.push({ $or: ors });

    const query = ands.length > 0 ? { $and: ands } : {};
    const sort = { _id: -1 };
    return await TenantPlanLineOptionSettingModel.find(query).sort(sort).exec();
};

//statics
tenantPlanLineOptionSettingSchema.statics = {
    findAllPlansForShinki,
    getlineOptionType,
    getDefaultLineOption,
    check020Support,
    getSelectPlanByShinki,
    getSelectPlanByMnp,
    getSelectPlanByShinkiOTA,
    getSelectPlanByESIM,
    getDefaultLineOptionByTypeId,
    checkPlanExists,
    createOrUpdate,
    findByTenantIdPlanInfo,
    findByTenantId,
};

const TenantPlanLineOptionSettingModel = mongoose.connection.useDb(portalConfig.DB_NAME.ADMIN_SERVICE).model(
    'tenant_plan_line_option_setting',
    tenantPlanLineOptionSettingSchema,
    'tenant_plan_line_option_setting'
);

module.exports = TenantPlanLineOptionSettingModel;
