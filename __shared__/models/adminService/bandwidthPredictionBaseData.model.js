const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const allowEmptyResalePlanId = function () {
    return typeof this.resalePlanId === 'string' ? false : true;
};

const allowEmptyServicePlanId = function () {
    return typeof this.servicePlanId === 'string' ? false : true;
};

const allowEmptyMemo = function () {
    return typeof this.memo === 'string' ? false : true;
};

const BandwidthPredictionBaseDataSchema = new Schema({
    tenantId: { type: String, trim: true, required: true },
    tenantName: { type: String, trim: true, required: true },
    planId: { type: String, trim: true, required: true },
    resalePlanId: { type: String, trim: true, required: allowEmptyResalePlanId },
    servicePlanId: { type: String, trim: true, required: allowEmptyServicePlanId },
    planName: { type: String, trim: true, required: true },
    baseNum: { type: Number, required: true },
    apn: { type: String, trim: true, required: true },
    apnDescription: { type: String, trim: true, required: true },
    memo: { type: String, trim: true, required: allowEmptyMemo },
});

const findDataByTenantIdAndPlanId = async (tenantId, planId) => {
    const query = {
        tenantId: tenantId,
        planId: planId,
    };
    return await BandwidthPredictionBaseDataModel.findOne(query);
};

const saveBandwidthPredictionBaseData = async (data) => {
    await BandwidthPredictionBaseDataModel.deleteMany({});

    const promises = [];
    data.forEach(async (item) => {
        const doc = new BandwidthPredictionBaseDataModel({
            tenantId: item.tenantId,
            tenantName: item.tenantName,
            planId: item.planId,
            planName: item.planName,
            baseNum: item.baseNum,
            apn: item.apn,
            apnDescription: item.apnDescription,
            memo: item.memo,
            resalePlanId: item.resalePlanId,
            servicePlanId: item.servicePlanId,
        });
        promises.push(doc.save());
    });
    await Promise.all(promises);
};

const getBandwidthPredictionBaseData = async () => {
    return await BandwidthPredictionBaseDataModel.find({});
};

//statics
BandwidthPredictionBaseDataSchema.statics = {
    findDataByTenantIdAndPlanId,
    saveBandwidthPredictionBaseData,
    getBandwidthPredictionBaseData,
};

const BandwidthPredictionBaseDataModel = mongoose.model(
    'bandwidth_prediction_base_data',
    BandwidthPredictionBaseDataSchema,
    'bandwidth_prediction_base_data'
);

module.exports = BandwidthPredictionBaseDataModel;
