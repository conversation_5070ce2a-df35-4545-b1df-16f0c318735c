const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const servicesRestrictSettingSchema = new Schema({
    lastUpdateUserId: { type: String, trim: true, required: true },
    updatedAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    createdDateTime: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    fiveGEnabledTenants: { type: [String] },
    eSIMEnabledTenants: { type: [String] },
    bbUnibaEnabledTenants: { type: [String], default: [] },
});

/**
 * check if 5G setting is turned on for tenantId
 * @param {string} tenantId
 * @returns {boolean} true if tenantId is found in the database
 */
const is5GEnabled = async (tenantId) => {
    const result = await ServicesRestrictSettingModel.findOne({ fiveGEnabledTenants: tenantId }).exec();
    return result ? true : false;
};

/**
 * check if delivery service is turned on for tenantId
 * @param {string} tenantId
 * @returns {boolean} true if tenantId is found in the database
 */
const isDeliveryServiceEnabled = async (tenantId) => {
    const result = await ServicesRestrictSettingModel.findOne({ kohaiEnableTenants: tenantId }).exec();
    return result ? true : false;
};

/**
 * check if eSIM setting is turned on for tenantId
 * @param {string} tenantId
 * @returns {boolean} true if tenantId is found in the database
 */
const isESIMEnabled = async (tenantId) => {
    const result = await ServicesRestrictSettingModel.findOne({ eSIMEnabledTenants: tenantId }).exec();
    return result ? true : false;
};

/**
 * check if BBユニット setting is turned on for tenantId
 * @param {string} tenantId
 * @returns {boolean} true if tenantId is found in the database
 */
const isBBUnibaEnabled = async (tenantId) => {
    const result = await ServicesRestrictSettingModel.findOne({ bbUnibaEnabledTenants: tenantId }).exec();
    return result ? true : false;
};

/**
 * @deprecated should not use this method! (check AladinApiRestrictSettings)
 * A static method that is used to check if a tenant is low priority.
 * @param {string} tenantId
 * @returns {boolean} true if tenantId is found in the database
 */
const isLowPrio = async (tenantId) => {
    const result = await ServicesRestrictSettingModel.findOne({
        lowPrioTenants: tenantId,
    }).exec();
    return result ? true : false;
};

/** get ServicesRestrictSetting singleton (lean) */
const getServicesRestrictSetting = async () => {
    return await ServicesRestrictSettingModel.findOne({}).lean().exec();
};

/**
 *
 * @param {object} context
 * @param {string} createdUserId
 * @param {Array<string>} fiveGEnabledTenantsOpt
 * @param {Array<string>} eSIMEnabledTenantsOpt
 * @param {Array<string>} bbUnibaEnabledTenantsOpt
 * @returns {string} _id
 */
const createOrUpdate = async (context, createdUserId, fiveGEnabledTenantsOpt, eSIMEnabledTenantsOpt, bbUnibaEnabledTenantsOpt) => {
    context.log('ServicesRestrictSettingModel createOrUpdate START');

    const nowSecs = dayjs().unix();

    const oldDataOpt = await getServicesRestrictSetting();

    let obj = {};
    obj.lastUpdateUserId = createdUserId;
    obj.updatedAt = nowSecs;
    obj.fiveGEnabledTenants = fiveGEnabledTenantsOpt;
    obj.eSIMEnabledTenants = eSIMEnabledTenantsOpt;
    obj.bbUnibaEnabledTenants = bbUnibaEnabledTenantsOpt;

    if (oldDataOpt) {
        await ServicesRestrictSettingModel.updateOne(
            { _id: oldDataOpt._id },
            { $set: obj },
            { runValidators: true }
        ).exec();
        return oldDataOpt._id.toString();
    } else {
        const servicesRestrictSetting = new ServicesRestrictSettingModel(obj);
        await servicesRestrictSetting.save();
        return servicesRestrictSetting._id.toString();
    }
};

//statics
servicesRestrictSettingSchema.statics = {
    is5GEnabled,
    isDeliveryServiceEnabled,
    isESIMEnabled,
    isBBUnibaEnabled,
    isLowPrio,
    getServicesRestrictSetting,
    createOrUpdate,
};

const ServicesRestrictSettingModel = mongoose.connection.useDb(portalConfig.DB_NAME.ADMIN_SERVICE).model(
    'services_restrict_setting',
    servicesRestrictSettingSchema,
    'services_restrict_setting'
);

module.exports = ServicesRestrictSettingModel;
