const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const dayjs = require('dayjs');

const BandwidthPredictionSettingsSchema = new Schema({
    isPredictionReady: { type: Boolean, default: false },
    nextExecutionTimestamp: { type: Number },
    executionTime: { type: Number },
    hankuroTenants: { type: [String] },
});

const getSettings = async () => {
    return await BandwidthPredictionSettings.findOne({});
};

const changeStatus = async (isPredictionReady = true) => {
    let updated = {
        isPredictionReady: isPredictionReady,
    };

    if (isPredictionReady) {
        const current = await BandwidthPredictionSettings.getSettings();
        const tomorrow = dayjs().add(1, 'day');
        const tomorrowAtZero = tomorrow.startOf('day');
        const nextExecutionTimestamp = tomorrowAtZero.add(current.executionTime, 'hour');

        updated = {
            ...updated,
            nextExecutionTimestamp: nextExecutionTimestamp,
        };
    }

    return await BandwidthPredictionSettings.findOneAndUpdate({}, { $set: updated }, { new: true });
};

/**
 *
 * @param {object} context
 * @param {Array<string>} hankuroTenantsOpt
 */
const updateHankuroTenants = async (context, hankuroTenantsOpt) => {
    context.log('BandwidthPredictionSettings updateHankuroTenants SART');

    const obj = { hankuroTenants: hankuroTenantsOpt };
    await BandwidthPredictionSettings.updateOne(
        {},
        { $set: obj },
        { runValidators: true }
    ).exec();
};

//statics
BandwidthPredictionSettingsSchema.statics = {
    getSettings,
    changeStatus,
    updateHankuroTenants,
};

const BandwidthPredictionSettings = mongoose.model(
    'bandwidth_prediction_settings',
    BandwidthPredictionSettingsSchema,
    'bandwidth_prediction_settings'
);

module.exports = BandwidthPredictionSettings;
