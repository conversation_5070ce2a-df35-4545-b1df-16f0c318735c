const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const generalSettingsSchema = new Schema({
    M2M_sousaservice: { type: String, trim: true, required: true },
    EID_prefix: { type: [String] },
    MNP_related_features: { type: [String] },
});

const getEIDPrefix = async () => {
    const result = await GeneralSettingsModel.findOne({}).exec();
    return result ? result.EID_prefix : [];
};

const getMNPRelatedFeatures = async () => {
    const result = await GeneralSettingsModel.findOne({}).exec();
    return result ? result.MNP_related_features : [];
};

const getM2Msousaservice = async () => {
    const result = await GeneralSettingsModel.findOne({}).exec();
    return result.M2M_sousaservice;
};

//statics
generalSettingsSchema.statics = {
    getEIDPrefix,
    getMNPRelatedFeatures,
    getM2Msousaservice,
};

const GeneralSettingsModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.ADMIN_SERVICE)
    .model('general_settings', generalSettingsSchema, 'general_settings');

module.exports = GeneralSettingsModel;
