const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { randomBytes } = require('crypto');

const MongoDBChecksSchema = new Schema({
    soId: {
        type: String,
        trim: true,
        unique: true,
        index: true,
    },
    stringId: {
        type: String,
        trim: true,
        index: true,
    },
    salt: {
        type: Buffer
    },
    enumStatus: {
        type: Number,
        default: 0,
        enum: [0, 1, 2],
        index: true,
    },
    quantity: {
        type: Number,
        default: 0
    },
    options: {
        type: [String], default: undefined
    },
    createdAt: { type: Number, index: true, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    boolTest: {
        type: Boolean, default: false
    },
    statusLog: { type: Object, default: {} },
    object: {
        _id: false,
        low: { type: Number },
        high: { type: Number },
        unsigned: { type: Boolean },
    }
});

/**
 * Create random salt
 * @returns {Buffer}
 */
const createNewSalt = () => {
    return randomBytes(8);
};

const randomString = () => {
    return randomBytes(16).toString('base64url')
}

const randInt = (min, max) => {
    return Math.floor(Math.random() * (max + 1 - min)) + min
}

const createElementObject = (soId) => {
    return {
        soId: soId,
        stringId: "test",
        salt: createNewSalt(),
        enumStatus: 1,
        quantity: randInt(0, 100),
        boolTest: true,
        object: {low: randInt(0, 30), high: randInt(60, 100), unsigned: true}
    };
}

var soId = "test_" + randomString();
var testId = null;

const aggregateTest = async () => {
    const pipeline = [
        {
            $match: {
                quantity: {$gte: 20}
            }
        }, {
            $group: {
                _id: "$enumStatus",
                count: {$sum: 1}
            }
        }
    ];
    await MongoDBCheckModel.aggregate(pipeline, { allowDiskUse: true });
    return await MongoDBCheckModel.aggregate(pipeline);
}

const bulkWriteTest = async () => {
    const mongo_db_checks = await MongoDBCheckModel.findOne({ stringId: "test" });
    return await MongoDBCheckModel.bulkWrite([
        {deleteOne: {
            filter: mongo_db_checks.toObject()
        }},
        {insertOne: {
            document: createElementObject("test22" + randomString()),
        }},
    ])
}

const countDocumentsTest = async () => {
    return await MongoDBCheckModel.countDocuments({stringId: "test"});
}

const createTest = async () => {
    var data = createElementObject(soId);

    return await MongoDBCheckModel.create(data);
}

const deleteOneTest = async () => {
    return await MongoDBCheckModel.deleteOne({stringId: "test"});
}

const deleteManyTest = async () => {
    return await MongoDBCheckModel.deleteMany({stringId: "test"});
}

const existsTest = async () => {
    return await MongoDBCheckModel.exists({stringId: "test"});
}

const findTest = async () => {
    return await MongoDBCheckModel.find({stringId: "test"});
};

const findWithSkipTest = async () => {
    return await MongoDBCheckModel.find().limit(1).skip(1);
}

const findWithLeanTest = async () => {
    return await MongoDBCheckModel.find().lean();
}
const findWithExecTest = async () => {
    return await MongoDBCheckModel.find().exec();
}
const findWithHintTest = async () => {
    return await MongoDBCheckModel.find().hint({stringId: 1});
}
const findWithDistinctTest = async () => {
    return await MongoDBCheckModel.find().distinct("stringId");
}

const findOneTest = async () => {
    var result = await MongoDBCheckModel.findOne({ stringId: "test"});
    return result;
}

const logicalFindTest = async () => {
    return await MongoDBCheckModel.find({$and: [{ stringId: "test"}, {$or: [{quantity: {$gte: 30}}, {soId: {$nin: [randomString()]}}]}]});
}

const findWithLimitTest = async () => {
    return await MongoDBCheckModel.find().limit(1);
}

const findWithSortTest = async () => {
    return await MongoDBCheckModel.find().sort({createdAt: -1});
}

const findOneAndUpdateTest = async () => {
    return await MongoDBCheckModel.findOneAndUpdate(
        { stringId: "test" },
        { $inc: {quantity: 100} },
        { runValidators: true, context: 'query', upsert: true, new: true }
    )
}

const findByIdTest = async () => {
    if (!testId) {
        var result = await MongoDBCheckModel.findOne();
        if (!result) {
            throw new Error("No ID for testing is available.")
        }
        return await MongoDBCheckModel.findById(result._id);
    } else {
        return await MongoDBCheckModel.findById(this.testId);
    }
}

const findByIdAndUpdate = async () => {
    if (!this.testId) {
        var result = await MongoDBCheckModel.findOne();
        if (!result) {
            throw new Error("No ID for testing is available.")
        }
        return await MongoDBCheckModel.findByIdAndUpdate(result._id, { $inc: {quantity: 2000} }, { new: true });
    } else {
        return await MongoDBCheckModel.findByIdAndUpdate(this.testId, { $inc: {quantity: 2000} }, { new: true });
    }
}

const findByNonExistId = async () => {
    return await MongoDBCheckModel.findById(mongoose.Types.ObjectId("aaaabbbbccccddddeeee0000"));
}

const insertManyTest = async () => {
    var objects = [];
    
    for (let index = 0; index < 20; index++) {
        const element = createElementObject("test" + index + randomString());
        objects.push(element);
    }
    return await MongoDBCheckModel.insertMany(objects);
}

const removeTest = async () => {
    return await MongoDBCheckModel.remove({ boolTest: false});
}

const saveTest = async () => {
    var data = new MongoDBCheckModel(createElementObject("test21" + randomString()));
    data.boolTest = false;
    await data.save();
}

const updateTest = async () => {
    return await MongoDBCheckModel.update({stringId: "test"}, {$inc: {"object.low": -100}});
}

const updateOneTest = async () => {
    return await MongoDBCheckModel.updateOne({stringId: "test"}, {$set: {boolTest: false}});
}

const updateManyTest = async() => {
    return await MongoDBCheckModel.updateOne({boolTest: true}, {$inc: {"object.high": 100}});
}


const failTest = async() => {
    var data = {
        soId: soId,
        stringId: "test",
        salt: createNewSalt(),
        enumStatus: 1,
        quantity: randInt(0, 100),
        boolTest: true,
        object: {low: randInt(0, 30), high: randInt(60, 100), unsigned: true}
    }
    soId = "test_" + randomString();
    return await MongoDBCheckModel.create(data);
}

const availableMethods = {
    aggregate: aggregateTest,
    bulkWrite: bulkWriteTest,
    create: createTest,
    countDocuments: countDocumentsTest,
    deleteOne: deleteOneTest,
    deleteMany: deleteManyTest,
    exists: existsTest,
    failTest: failTest,
    find: findTest,
    findOne: findOneTest,
    findOneAndUpdate: findOneAndUpdateTest,
    findByIdAndUpdate: findByIdAndUpdate,
    "find with limit": findWithLimitTest,
    "find with logic": logicalFindTest,
    "find with sort": findWithSortTest,
    "find with skip": findWithSkipTest,
    "find with lean": findWithLeanTest,
    "find with exec": findWithExecTest,
    "find with hint": findWithHintTest,
    "find with distinct": findWithDistinctTest,
    findById: findByIdTest,
    "find by nonexist id": findByNonExistId,
    insertMany: insertManyTest,
    remove: removeTest,
    save: saveTest,
    update: updateTest,
    updateOne: updateOneTest,
    updateMany: updateManyTest
};

MongoDBChecksSchema.statics = {
    aggregateTest,
    bulkWriteTest,
    createTest,
    deleteOneTest,
    deleteManyTest,
    existsTest,
    failTest,
    findTest,
    findOneTest,
    findOneAndUpdateTest,
    logicalFindTest,
    findWithSortTest
}

const runTest = async(methodName) => {
    try {
        await availableMethods[methodName]();
        return {
            "methodName": methodName,
            "status": true,
            //"results": results
        }
    } catch (error) {
        return {
            "methodName": methodName,
            "status": false,
            "error": error.message
        }
    }
}



const MongoDBCheckModel = mongoose.model('mongo_db_checks', MongoDBChecksSchema, 'mongo_db_checks');

module.exports = {MongoDBCheckModel: MongoDBCheckModel, availableMethods: availableMethods, runTest: runTest};
