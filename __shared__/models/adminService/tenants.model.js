const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const tenantsSchema = new Schema({
    _id: { type: String },
    db_hosts: { type: String, trim: true, required: true },
    db_port: { type: Number, required: true },
    apiKey: { type: String, trim: true, required: true },
});

const retrieveAllTenants = async () => {
    return await TenantsModel.find().sort({ _id: 1 });
};

//statics
tenantsSchema.statics = {
    retrieveAllTenants,
};

const TenantsModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.ADMIN_SERVICE)
    .model('tenants', tenantsSchema, 'tenants');

module.exports = TenantsModel;
