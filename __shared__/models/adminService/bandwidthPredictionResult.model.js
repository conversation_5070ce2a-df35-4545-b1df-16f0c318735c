const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const BandwidthPredictionResultSchema = new Schema({
    date: { type: String, trim: true, required: true },
    data: [
        {
            date: { type: String, trim: true, required: true },
            tenantId: { type: String, trim: true, required: true },
            tenantName: { type: String, trim: true, required: true },
            planId: { type: String, trim: true, required: true },
            planName: { type: String, trim: true, required: true },
            baseNum: { type: Number, required: true },
            apn: { type: String, trim: true, required: true },
            apnDescription: { type: String, trim: true, required: true },
            count: { type: Number, required: true },
            predictedCount: { type: Number, required: true },
            bandwidth: { type: Number, required: true },
            resalePlanId: { type: String, trim: true, required: true },
            servicePlanId: { type: String, trim: true, required: true },
        },
    ],
});

const getDateList = async () => {
    return await BandwidthPredictionResultModel.find().distinct('date');
};

const getLineCountPrediction = async (date) => {
    const query = {
        date: date,
    };

    const doc = await BandwidthPredictionResultModel.findOne(query);

    return doc.data.map((d) => {
        return {
            date: d.date,
            tenantId: d.tenantId,
            tenantName: d.tenantName,
            planId: d.planId,
            planName: d.planName,
            resalePlanId: d.resalePlanId,
            servicePlanId: d.servicePlanId,
            count: d.count,
            predictedCount: d.predictedCount,
            baseNum: d.baseNum,
            apn: d.apn,
        };
    });
};

const getBandwidthPrediction = async (date) => {
    const query = {
        date: date,
    };

    const doc = await BandwidthPredictionResultModel.findOne(query);

    return doc.data.map((d) => {
        // BandwidthPredictionResultCSVData bandwidth type is Int
        const bandwidth = parseInt(d.bandwidth);
        return {
            date: d.date,
            apn: d.apn,
            apnDescription: d.apnDescription,
            bandwidth: isNaN(bandwidth) ? 0 : bandwidth,
        };
    });
};

//statics
BandwidthPredictionResultSchema.statics = {
    getDateList,
    getLineCountPrediction,
    getBandwidthPrediction,
};

const BandwidthPredictionResultModel = mongoose.model(
    'bandwidth_prediction_result',
    BandwidthPredictionResultSchema,
    'bandwidth_prediction_result'
);

module.exports = BandwidthPredictionResultModel;
