/**
 * OTADaihyouBango class
 *
 * collection name: ota_daihyou_bango
 */

const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { isNone } = require('../../helpers/baseHelper');
const Schema = mongoose.Schema;

const SORT_BY_FIELDS = {
    daihyou_bango: 'daihyouBango',
    dai_daihyou_bango: 'daiDaihyouBango',
    tenant_id: 'tenantId',
    tenant_display_name: 'tenantDisplayName',
    ryoukin_plan_id: 'ryoukinPlan',
    ryoukin_plan_name: 'ryoukinPlan',
    quota_quantity: 'quotaQuantity',
};

const OTADaihyouBangoSchema = new Schema({
    daihyouId: { type: String, trim: true, required: true },
    tenantId: { type: String, trim: true, required: true },
    tenantDisplayName: { type: String, trim: true },
    daihyouBango: { type: String, trim: true }, // 代表回線 // seems to have unique constraint
    daiDaihyouBango: { type: String, trim: true }, // 大代表回線
    contractType: { type: String, trim: true }, // 契約種別
    ryoukinPlan: { type: String, trim: true }, // 料金プラン
    quotaQuantity: { type: Number }, // 枚数
    kaisenOptions: [{ type: String, trim: true }],
    updatedAt: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    lastUpdateUserId: { type: String, trim: true },
});

const OTADaihyouBangoConstants = {
    FOMA_TOKUTEI_SETSUZOKU_PLAN: 'A1089',
    FOMA_YUPIKITASU_PLAN: 'AC001',
    XI_TOKUSEI_SETSUZOKU_PLAN: 'AJ010',
    XI_YUPIKITASU: 'AJ055',
    TYPE_XI: 'AJ034',
};

OTADaihyouBangoConstants.RYOKIN_PLAN_NAMES = {
    [OTADaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN]: '卸FOMA特定接続プラン',
    [OTADaihyouBangoConstants.FOMA_YUPIKITASU_PLAN]: '卸FOMAユビキタスプラン',
    [OTADaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN]: '卸Xi特定接続プラン',
    [OTADaihyouBangoConstants.XI_YUPIKITASU]: '卸Xiユビキタス',
    [OTADaihyouBangoConstants.TYPE_XI]: 'タイプXi',
};

/**
 * @param {object} context
 * @param {string} daihyouBango
 */
const findDaihyouBango = async (context, daihyouBango) => {
    return await OTADaihyouBangoModel.findOne({ daihyouBango }).exec();
};

/**
 * 旧メソッド：findByDaihyouBangoByTenant
 * @param {object} context
 * @param {string} tenantId
 * @returns {Promise<OTADaihyouBangoSchema|null>} OTADaihyouBangoSchema or null
 */
const findDaihyouBangoByTenantId = async (context, tenantId) => {
    context.log('OTADaihyouBangoModel findDaihyouBangoByTenantId START ', tenantId);
    if (!tenantId) throw new Error('tenantId is required');

    const tenantVal = tenantId === 'CON000' ? tenantId : 'ZZZ000';
    return await OTADaihyouBangoModel.findOne({ tenantId: tenantVal }).exec();
};

/**
 *
 * @param {object} context
 * @param {string} tenantId
 * @param {number} newQuotaQuantity
 */
const updateQuantity = async (context, tenantId, newQuotaQuantity) => {
    context.log('OTADaihyouBangoModel updateQuantity START ', tenantId, newQuotaQuantity);
    if (!tenantId) throw new Error('tenantId is required');
    if (!newQuotaQuantity) throw new Error('newQuotaQuantity is required');

    const tenantVal = tenantId === 'CON000' ? tenantId : 'ZZZ000';
    return await OTADaihyouBangoModel.updateOne(
        { tenantId: tenantVal },
        { $inc: { quotaQuantity: newQuotaQuantity } },
        { runValidators: true }
    ).exec();
};

/**
 * @param {object} context
 * @param {number} skip
 * @param {number} limit
 * @param {string} sortBy
 * @param {boolean} isSortOrderAscending
 * @returns {Promise<{result: OTADaihyouBangoSchema[], resultCount: number}>}
 */
const searchExtended = async (context, { skip, limit, sortBy, isSortOrderAscending }) => {
    context.log('OTADaihyouBangoModel searchExtended START ', skip, limit, sortBy, isSortOrderAscending);

    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = { _id: sortAscendingInt };
    if (Object.hasOwn(SORT_BY_FIELDS, sortBy)) {
        sort = { [SORT_BY_FIELDS[sortBy]]: sortAscendingInt };
    }

    const query = OTADaihyouBangoModel.find().sort(sort);
    const resultCountQuery = query.clone().countDocuments();
    const resultQuery = query.skip(skip).limit(limit);
    const [resultCount, result] = await Promise.all([resultCountQuery, resultQuery]);
    return {
        result,
        resultCount,
    };
};

/**
 * Get all OTADaihyouBango
 * @param {object} context
 * @returns {Promise<[OTADaihyouBangoSchema]|[]>} [OTADaihyouBangoSchema] or []
 */
const getAllOTADaihyouBango = async (context) => {
    context.log('OTADaihyouBangoModel getAllOTADaihyouBango START ');
    return await OTADaihyouBangoModel.find({}).exec();
};

/**
 * Create OTADaihyouBango
 * @param {object} context
 * @param {string} createdUserId
 * @param {string} tenantId
 * @param {string} daiDaihyouBango
 * @param {string} daihyouBango
 * @param {string} ryoukinPlan
 * @param {number} quotaQuantity
 * @returns {Promise<OTADaihyouBangoSchema>} OTADaihyouBangoSchema
 */
const createOTADaihyouBango = async (
    context,
    { createdUserId, tenantId, daiDaihyouBango, daihyouBango, ryoukinPlan, quotaQuantity }
) => {
    context.log(
        'OTADaihyouBangoModel createOTADaihyouBango START ',
        tenantId,
        daiDaihyouBango,
        daihyouBango,
        ryoukinPlan,
        quotaQuantity
    );
    if (!tenantId) throw new Error('tenantId is required');
    if (!daiDaihyouBango) throw new Error('daiDaihyouBango is required');
    if (!daihyouBango) throw new Error('daihyouBango is required');
    if (!ryoukinPlan) throw new Error('ryoukinPlan is required');
    if (!quotaQuantity) throw new Error('quotaQuantity is required');

    await OTADaihyouBangoModel.deleteMany({ tenantId }).exec();

    const newOTADaihyouBango = new OTADaihyouBangoModel({
        lastUpdateUserId: createdUserId,
        updatedAt: dayjs().unix(),
        tenantId,
        daiDaihyouBango,
        daihyouBango,
        daihyouId: daiDaihyouBango,
        contractType: getContractType(ryoukinPlan),
        ryoukinPlan,
        quotaQuantity,
    });
    newOTADaihyouBango.daihyouId = 'DH' + newOTADaihyouBango._id.toString();

    return await newOTADaihyouBango.save();
};

/**
 * Update OTADaihyouBango
 * @param {object} context
 * @param {string} createdUserId
 * @param {string} tenantId
 * @param {string} daiDaihyouBango
 * @param {string} daihyouBango
 * @param {string} ryoukinPlan
 * @param {number} quotaQuantity
 * @returns {Promise<OTADaihyouBangoSchema>} OTADaihyouBangoSchema
 */
const updateOTADaihyouBango = async (
    context,
    { createdUserId, tenantId, daiDaihyouBango, daihyouBango, ryoukinPlan, quotaQuantity }
) => {
    context.log(
        'OTADaihyouBangoModel updateOTADaihyouBango START ',
        tenantId,
        daiDaihyouBango,
        daihyouBango,
        ryoukinPlan,
        quotaQuantity
    );
    if (!tenantId) throw new Error('tenantId is required');
    if (!daiDaihyouBango) throw new Error('daiDaihyouBango is required');
    if (!daihyouBango) throw new Error('daihyouBango is required');
    if (!ryoukinPlan) throw new Error('ryoukinPlan is required');
    if (isNone(quotaQuantity) || typeof quotaQuantity !== 'number') throw new Error('quotaQuantity is required');

    return await OTADaihyouBangoModel.updateOne(
        { daihyouBango },
        {
            lastUpdateUserId: createdUserId,
            updatedAt: dayjs().unix(),
            tenantId,
            daiDaihyouBango,
            contractType: getContractType(ryoukinPlan),
            ryoukinPlan,
            quotaQuantity,
        }
    );
};

/**
 * Get contract type
 * NOTE: This is a copy of the function in `helpers/otaDaihyouBangoHelper.js` to can be used in the model
 * @param {string} ryoukinPlan
 * @returns {string} contract type
 */
const getContractType = (ryoukinPlan) => {
    switch (ryoukinPlan) {
        case OTADaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN:
            return 'PC004';
        case OTADaihyouBangoConstants.FOMA_YUPIKITASU_PLAN:
        case OTADaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN:
        case OTADaihyouBangoConstants.XI_YUPIKITASU:
        case OTADaihyouBangoConstants.TYPE_XI:
            return 'PCJ04';
        default:
            return '';
    }
};

/**
 *
 * @param {object} context
 * @param {string} tenantId
 * @param {number} registerCount
 * @returns {boolean}
 */
const isRegisterable = async (context, tenantId, registerCount) => {
    context.log('OTADaihyouBangoModel isRegisterable START', tenantId, registerCount);

    let query = {};
    query.tenantId = tenantId;
    query.quotaQuantity = { $gte: registerCount };
    const doc = await OTADaihyouBangoModel.findOne(query).exec();
    return doc ? true : false;
};
OTADaihyouBangoSchema.statics = {
    findDaihyouBango,
    findDaihyouBangoByTenantId,
    updateQuantity,
    searchExtended,
    getAllOTADaihyouBango,
    createOTADaihyouBango,
    updateOTADaihyouBango,
    isRegisterable,
};

const OTADaihyouBangoModel = mongoose.model('ota_daihyou_bango', OTADaihyouBangoSchema, 'ota_daihyou_bango');

const OTADaihyouBangoModule = (module.exports = OTADaihyouBangoModel);
OTADaihyouBangoModule.OTADaihyouBangoConstants = OTADaihyouBangoConstants;
