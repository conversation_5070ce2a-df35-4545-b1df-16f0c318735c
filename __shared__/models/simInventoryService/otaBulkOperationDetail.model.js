/**
 * OTABulkServiceOrder class
 *
 * collection name: ota_details_log
 */
const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { isNone, removeNullValue } = require('../../helpers/baseHelper');
const Schema = mongoose.Schema;

const OTABulkOperationDetailSchema = new Schema({
    pSoId: { type: String, trim: true, required: true },
    createUserId: { type: String, trim: true },
    tenantId: { type: String, trim: true, required: true },
    uketsukeDateTime: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    haishiDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    kaisenNo: { type: String, trim: true },
    isQueued: { type: Boolean, default: false },
    isKaitsuOK: { type: Boolean, default: false },
    isHaishiOK: { type: Boolean, default: false },
});

const getOTAIkkatsuDetailsLog = async (context, id) => {
    context.log('OTABulkOperationDetailModel getOTAIkkatsuDetailsLog START ', id);
    return await OTABulkOperationDetailModel.findById(id).exec();
};

/**
 * OTA詳細更新する
 * @param {object} context
 * @param {string} otaId `_id` hex string
 * @param {boolean} [isQueued]
 * @param {boolean} [isKaitsuOK]
 * @param {boolean} [isHaishiOK]
 */
const updateOTADetailFlagBySOIDs = async (context, otaId, isQueued, isKaitsuOK, isHaishiOK) => {
    context.log('OTABulkOperationDetailModel updateOTADetailFlagBySOIDs START ', otaId, isQueued, isKaitsuOK, isHaishiOK);
    // update if exists
    const updateData = {};
    if (!isNone(isQueued)) updateData.isQueued = isQueued;
    if (!isNone(isKaitsuOK)) updateData.isKaitsuOK = isKaitsuOK;
    if (!isNone(isHaishiOK)) updateData.isHaishiOK = isHaishiOK;

    // if all updates are undefined, stop
    if (isNone(updateData)) return;

    return await OTABulkOperationDetailModel.updateOne(
        {
            _id: mongoose.Types.ObjectId.createFromHexString(otaId),
        },
        { $set: updateData },
        { runValidators: true }
    ).exec();
};

/**
 *
 * @param {object} context
 * @param {number} reservedDate
 * @returns {Promise<[OTABulkOperationDetailSchema]|[]>} [OTABulkOperationDetailSchema] or []
 */
const searchOTAIkkatsuKaitsuForReservedDate = async (context, reservedDate) => {
    context.log('OTABulkOperationDetailModel searchOTAIkkatsuKaitsuForReservedDate START ', reservedDate);
    if (!reservedDate) throw new Error('reservedDate is required');

    let query = {};
    query.haishiDate = reservedDate;
    query.isQueued = { $ne: true };
    const sort = { _id: 1 };
    return await OTABulkOperationDetailModel.find(query).sort(sort).exec();
};

/**
 * OTA詳細ログを作成する
 * @param {object} context
 * @param {string} createUserId
 * @param {string} tenantId
 * @param {string} kaisenNo
 * @param {number} haishiDate
 * @param {string} pSoId
 * @returns {Promise<string>} _id
 */
const createSO = async (
    context,
    {
        createUserId,
        tenantId,
        kaisenNo,
        haishiDate,
        pSoId,
    }
) => {
    context.log('OTABulkOperationDetailModel createSO START');
    const nowSecs = dayjs().unix();

    let obj = {};
    obj.createUserId = createUserId;
    obj.tenantId = tenantId;
    obj.kaisenNo = kaisenNo;
    obj.haishiDate = haishiDate ?? 0;
    obj.pSoId = pSoId;
    obj.isQueued = false;
    obj.isKaitsuOK = false;
    obj.isHaishiOK = false;
    obj.uketsukeDateTime = nowSecs;

    const newOTABulkOperationDetail = new OTABulkOperationDetailModel(removeNullValue(obj));
    await newOTABulkOperationDetail.save();
    return newOTABulkOperationDetail._id.toString();
};

OTABulkOperationDetailSchema.statics = {
    getOTAIkkatsuDetailsLog,
    updateOTADetailFlagBySOIDs,
    searchOTAIkkatsuKaitsuForReservedDate,
    createSO,
};

const OTABulkOperationDetailModel = mongoose.model('ota_details_log', OTABulkOperationDetailSchema, 'ota_details_log');
module.exports = OTABulkOperationDetailModel;
