const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { isNone } = require('../../helpers/baseHelper');
const { TANAOROSHI_DATA_SAVE_STATUS } = require('../../constants/tanaoroshiDataConstants');

const tanaoroshiDataSchema = new Schema({
    month_id: { type: String, trim: true, required: true },
    sim_info: { type: String, trim: true, required: true },
    zm_status: { type: Number, trim: true },
    last_update_timestamp: { type: Number },
    inventory_status: { type: Number },
    timestamp: { type: Number },
    save_status: { type: Number },
});

const TOCHU_HOZON = 0;
const KEKKA_HOUKOKU = 1;
// const MI_HOZON = -1;

/**
 * get TanaoroshiExecuteStatusData
 * @param {object} obj
 * @returns {object} tanaoroshiExecuteStatusData
 */
const getTanaoroshiExecuteStatusData = (obj) => {
    const count = obj.count;
    const total_executed_count = obj.total_executed_count;
    let execute_status = null;
    if (isNone(count) || isNone(total_executed_count)) {
        execute_status = null;
    } else {
        if (total_executed_count < count) {
            execute_status = TOCHU_HOZON;
        } else {
            execute_status = KEKKA_HOUKOKU;
        }
    }
    return {
        month_id: obj.month_id,
        count,
        total_executed_count,
        execute_status,
    };
};

const getExecuteStatusByMonthIds = async (tanaoroshiMonthDataArr) => {
    const tanaoroshiMonthDataMap = new Map();

    tanaoroshiMonthDataArr.forEach((data) => {
        const strId = data._id.toString();
        tanaoroshiMonthDataMap.set(strId, data);
    });
    const monthIdArr = Array.from(tanaoroshiMonthDataMap.keys());

    const matchObj = {
        $match: {
            month_id: {
                $in: monthIdArr,
            },
        },
    };

    const groupObj = {
        $group: {
            _id: { month_id: '$month_id' }, // TODO: Is this correct?
            count: { $sum: 1 },
            total_executed_count: { $sum: '$save_status' },
            month_id: { $first: '$month_id' },
        },
    };

    const pipeLines = [matchObj, groupObj];
    const aggregateOptions = {
        allowDiskUse: true,
    };

    const resultArr = await TanaoroshiDataModel.aggregate(pipeLines, aggregateOptions);
    const resultMap = new Map();

    const getExecuteStatus = (count, total_executed_count) => {
        if (isNone(count) || isNone(total_executed_count)) {
            return null;
        } else if (total_executed_count < count) {
            return TANAOROSHI_DATA_SAVE_STATUS.TOCHU_HOZON;
        } else {
            return TANAOROSHI_DATA_SAVE_STATUS.KEKKA_HOUKOKU;
        }
    };

    resultArr.forEach((data) => {
        const tanaoroshiMonthData = tanaoroshiMonthDataMap.get(data.month_id);
        if (tanaoroshiMonthData !== undefined) {
            resultMap.set(
                `${tanaoroshiMonthData.month_value},${tanaoroshiMonthData.tenant_id},${tanaoroshiMonthData.tempo_id}`,
                {
                    ...data,
                    execute_status: getExecuteStatus(data.count, data.total_executed_count),
                }
            );
        }
    });

    return resultMap;
};

/**
 * getTanaoroshiData
 * @param {object} context
 * @param {string} monthId
 * @param {number} inventoryStatus
 * @returns {string[],number} [tanaoroshiDataSchema] and count
 */
const findByMonthId = async (context, monthId, inventoryStatus = null) => {
    context.log('TanaoroshiDataModel findByMonthId START', monthId, inventoryStatus);

    let query = { month_id: monthId };
    if (!isNone(inventoryStatus)) {
        query.inventory_status = inventoryStatus;
    }
    const sort = { sim_info: 1 };
    const [result, resultCount] = await Promise.all([
        TanaoroshiDataModel.find(query).sort(sort),
        TanaoroshiDataModel.find(query).count(),
    ]);
    return {
        result,
        resultCount,
    };
};

/**
 *
 * @param {object} context
 * @param {tanaoroshiMonthData[]} tanaoroshiMonthDataSeq
 * @returns {Map<(object|null), ([obj],obj)>} Map{(TanaoroshiMonthDataObj|null), ([tanaoroshiDatas], tanaoroshiExecuteStatusDataObj)}
 */
const getTanaoroshiDataMapByMonthIds = async (context, tanaoroshiMonthDataSeq) => {
    context.log('TanaoroshiDataModel getTanaoroshiDataMapByMonthIds START');

    const tanaoroshiMonthDataMap = new Map();
    tanaoroshiMonthDataSeq.forEach((tanaoroshiMonthData) => {
        tanaoroshiMonthDataMap.set(tanaoroshiMonthData._id.toString(), tanaoroshiMonthData);
    });
    const monthIdSeq = [...tanaoroshiMonthDataMap.keys()];
    const matchObj = { $match: { month_id: { $in: monthIdSeq } } };

    let groupDBObj = { _id: { month_id: '$month_id' } };
    groupDBObj.data = { $push: '$$ROOT' };
    groupDBObj.count = { $sum: 1 };
    groupDBObj.total_executed_count = { $sum: '$save_status' };
    groupDBObj.month_id = { $first: '$month_id' };
    const groupObj = { $group: groupDBObj };

    const sortObj = { $sort: { sim_info: 1 } };
    const pipeline = [matchObj, sortObj, groupObj];
    const aggregateOptions = { allowDiskUse: true };

    const resultSeq = await TanaoroshiDataModel.aggregate(pipeline, aggregateOptions).exec();

    const resultMap = new Map();
    resultSeq.forEach((dbObj) => {
        const tanaoroshiExecuteStatusData = dbObj;
        const tanaoroshiMonthData = tanaoroshiMonthDataMap.get(tanaoroshiExecuteStatusData.month_id);
        const tanaoroshiDatas = dbObj.data;
        resultMap.set(tanaoroshiMonthData, { tanaoroshiDatas, tanaoroshiExecuteStatusData });
    });
    return resultMap;
};

/**
 * @param {object} context
 * @param {string} monthId
 */
const removeAllTempDataByMonthId = async (context, monthId) => {
    await TanaoroshiDataModel.deleteMany({ month_id: monthId, save_status: TANAOROSHI_DATA_SAVE_STATUS.TOCHU_HOZON });
};

/**
 *
 * @param {object} context
 * @param {string} monthId
 * @param {string} simNumber
 * @param {number} zmStatus
 * @param {number} inventoryStatus
 * @param {number} saveStatus
 * @param {number} [timestamp]
 */
const createTanaoroshi = async (context, monthId, simNumber, zmStatus, inventoryStatus, saveStatus, timestamp) => {
    const query = {
        month_id: monthId,
        sim_info: simNumber,
    };
    // 棚卸結果報告の場合、修正できない
    // invalid if save_status = KEKKA_HOUKOKU
    const isInsertValid = isNone(
        await TanaoroshiDataModel.exists({
            ...query,
            save_status: TANAOROSHI_DATA_SAVE_STATUS.KEKKA_HOUKOKU,
        })
    );

    if (!isInsertValid) return undefined;

    const updateData = {
        zm_status: zmStatus,
        timestamp: isNone(timestamp) ? 0 : timestamp,
        inventory_status: inventoryStatus,
        save_status: saveStatus,
    };
    if (saveStatus === TANAOROSHI_DATA_SAVE_STATUS.KEKKA_HOUKOKU) {
        updateData.last_update_timestamp = dayjs().unix();
    }

    const updated = await TanaoroshiDataModel.findOneAndUpdate(
        query,
        { $set: updateData },
        { new: true, upsert: true }
    );

    return updated._id.toString();
};

/**
 *
 * @param {object} context
 * @param {string} monthId
 * @param {number} save_status
 */
const updateSaveStatus = async (context, monthId, save_status) => {
    const update = {
        save_status,
    };
    if (save_status === TANAOROSHI_DATA_SAVE_STATUS.TOCHU_HOZON) {
        update.last_update_timestamp = 0;
    }
    await TanaoroshiDataModel.updateMany({ month_id: monthId }, { $set: update });
};

tanaoroshiDataSchema.statics = {
    getTanaoroshiExecuteStatusData,
    getExecuteStatusByMonthIds,
    findByMonthId,
    getTanaoroshiDataMapByMonthIds,
    removeAllTempDataByMonthId,
    createTanaoroshi,
    updateSaveStatus,
};

const TanaoroshiDataModel = mongoose.model('tanaoroshi_data', tanaoroshiDataSchema, 'tanaoroshi_data');
module.exports = TanaoroshiDataModel;
