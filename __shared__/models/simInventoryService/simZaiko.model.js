const _ = require('lodash');
const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { CONTRACT_TYPES, SIM_YOUTO, SIM_STATUS, SIM_ZAIKO_SEARCH_TYPES } = require('../../constants/simConstants');
const { MVNO_SERVICES } = require('../../constants/mvnoServicesConstants');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
const { isNone } = require('../../helpers/baseHelper');

const simZaikoSchema = new Schema({
    zm_id: { type: String, trim: true, required: true, unique: true },
    kaisenNo: { type: String, trim: true },
    tenantId: { type: String, trim: true, required: true },
    tempoId: { type: String, trim: true, required: true },
    kariKaisenNo: { type: String, trim: true },
    simContractType: { type: String, trim: true },
    simCreated: { type: Number, trim: true },
    simExpired: { type: Number, trim: true },
    simInfo: { type: String, trim: true },
    simStatus: { type: String, trim: true },
    simStatusChangeDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    simType: { type: String, trim: true },
    simYouto: { type: String, trim: true },

    lastZaikoStatus: {
        kouteiId: { type: Number, required: true },
        timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
        userId: { type: String, trim: true },
        ngReason: { type: String, trim: true },
    },
    zaikoStatuses: [
        {
            _id: false,
            kouteiId: { type: Number, required: true },
            timestamp: { type: Number },
            userId: { type: String, trim: true },
            //===Begin of 4.0====
            //NG理由
            ngReason: { type: String, trim: true },
        },
    ],
    makuhariZaikoKanriRan: { type: String, trim: true },

    // STEP 14.0、Com-SIM(フルMVNO)の場合は、「IMSI」、「PUK1」、「PUK2」を追加(必須)とする。
    imsi: { type: String, trim: true },
    puk1: { type: String, trim: true },
    puk2: { type: String, trim: true },

    // STEP18.0 5G契約種別
    fiveGContractType: { type: String, trim: true },

    removed: { type: Boolean },
    isNeedCheckOption: { type: Boolean },
    kaisenOptions: { type: [String] },
});

simZaikoSchema.virtual('is5GNSA').get(function () {
    // STEP18.0 5G契約種別
    const regex = /5G\(NSA\)/; // /5G\\(NSA\\)/
    return regex.test(this.fiveGContractType ?? '');
});

// TODO 新しいバーチャルフィールドを追加したら、ここも追加してください
// virtual fields for aggregate query
const _virtualFielsdMap = {
    is5GNSA: (row) => /5G\(NSA\)/.test(row.fiveGContractType ?? ''),
};
/** create a dictionary of populated virtual fields from a SimZaiko record */
const buildVirtualFields = (row) => {
    return Object.entries(_virtualFielsdMap).reduce((acc, [key, fn]) => {
        acc[key] = fn(row);
        return acc;
    }, {});
};

const SimZaikoConstants = {
    ZAIKO_STATUS_ID_ZAIKO_TOUROKU_ZUMI: 1001,
    ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI: 1002,

    ZAIKO_STATUS_ID_ERROR: 1501,
    ZAIKO_STATUS_ID_SHOKI_FURYO: 1502,
    // ZAIKO_STATUS_ID_OTA_SMS_KAITSU_ERROR: 1503,

    ZAIKO_STATUS_ID_MAKUHARI_HENKYAKU_ZUMI: 1601,
    ZAIKO_STATUS_ID_DOCOMO_HENKYAKU_ZUMI: 1602,
    ZAIKO_STATUS_ID_EXPIRED: 1701,

    ZAIKO_STATUS_ID_ALADIN_KAITSU_ZUMI: 2001,
    ZAIKO_STATUS_ID_ALADIN_SMS_KAITSU_ZUMI: 2002,
    ZAIKO_STATUS_ID_OTA_IKKATSU_KAITSU: 2003,

    // ZAIKO_STATUS_ID_ALADIN_KAITSU_ZUMI_SAIHAKKOU: 3001,
    ZAIKO_STATUS_ID_GENBUTSU_NASHI: 3002,

    // ZAIKO_STATUS_ID_FUMEI: 3003,
    // ZAIKO_STATUS_ID_OTA_ALADIN_KAITSU_ZUMI: 2002,
    // ZAIKO_STATUS_ID_OTA_SMS_KAITSU_ZUMI: 2003,
    // ZAIKO_STATUS_ID_HANKURO_ALADIN_KAITSU_ZUMI: 2004,
    ZAIKO_STATUS_ID_SHINKI_KOUKANKI_TOUROKU_ERROR: 2005, //新規SOが交換機設定NGになったら新規交換機登録エラーに更新
};

SimZaikoConstants.ZAIKO_TITLES = {
    [SimZaikoConstants.ZAIKO_STATUS_ID_ZAIKO_TOUROKU_ZUMI]: '在庫登録済み',
    [SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI]: '入庫処理済み',
    [SimZaikoConstants.ZAIKO_STATUS_ID_ERROR]: 'エラー',
    [SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO]: '初期不良',
    // [SimZaikoConstants.ZAIKO_STATUS_ID_OTA_SMS_KAITSU_ERROR]: "OTA-SMS開通エラー",

    [SimZaikoConstants.ZAIKO_STATUS_ID_MAKUHARI_HENKYAKU_ZUMI]: '幕張返却済み',
    [SimZaikoConstants.ZAIKO_STATUS_ID_DOCOMO_HENKYAKU_ZUMI]: 'docomo返却済み',
    [SimZaikoConstants.ZAIKO_STATUS_ID_EXPIRED]: '有効期限切れ',

    [SimZaikoConstants.ZAIKO_STATUS_ID_ALADIN_KAITSU_ZUMI]: 'ALADIN開通済み',
    [SimZaikoConstants.ZAIKO_STATUS_ID_ALADIN_SMS_KAITSU_ZUMI]: 'OTA-ALADIN開通済み',
    [SimZaikoConstants.ZAIKO_STATUS_ID_SHINKI_KOUKANKI_TOUROKU_ERROR]: '新規交換機登録エラー',
    // [SimZaikoConstants.ZAIKO_STATUS_ID_OTA_ALADIN_KAITSU_ZUMI]: "OTA-ALADIN開通済み",
    // [SimZaikoConstants.ZAIKO_STATUS_ID_OTA_SMS_KAITSU_ZUMI]: "OTA-SMS開通済み",
    // [SimZaikoConstants.ZAIKO_STATUS_ID_HANKURO_ALADIN_KAITSU_ZUMI]: "半黒-ALADIN開通済み"
    // [SimZaikoConstants.ZAIKO_STATUS_ID_ALADIN_KAITSU_ZUMI_SAIHAKKOU]: 'ALADIN開通済み(再発行)',
    // [SimZaikoConstants.ZAIKO_STATUS_ID_FUMEI]: "不明",
    [SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI]: '現物なし(事前報告必須)',

    [SimZaikoConstants.ZAIKO_STATUS_ID_OTA_IKKATSU_KAITSU]: 'OTA-ALADIN一括開通済',
};

// [currentZaikoStatus, newZaikoStatus]
SimZaikoConstants.zaikoStatusUpdateableForNormalUserList = [
    [SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI, SimZaikoConstants.ZAIKO_STATUS_ID_ERROR],
    [SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI, SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO],
    [SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI, SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI],
    [SimZaikoConstants.ZAIKO_STATUS_ID_ERROR, SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI],
    [SimZaikoConstants.ZAIKO_STATUS_ID_ERROR, SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO],
    [SimZaikoConstants.ZAIKO_STATUS_ID_ERROR, SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI],
    [SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO, SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI],
    [SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO, SimZaikoConstants.ZAIKO_STATUS_ID_ERROR],
    [SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO, SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI],
    [SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI, SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI],
    [SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI, SimZaikoConstants.ZAIKO_STATUS_ID_ERROR],
    [SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI, SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO],
];

const createQuery = ({
    searchType,
    searchValue,
    tenantIds,
    tempoIds,
    zaikoStatuses,
    simStatuses,
    simContractTypes,
    simTypes,
    simYoutos,
    fromSimStatusChangeDate,
    toSimStatusChangeDate,
    isNeedCheckOption,
    zmIds = [],
    isAllSelected = false,
    exceptSimInfos = [],
    service = MVNO_SERVICES.LITE,
    fiveGContractTypes,
    fromZaikoStatusChangeDate,
    toZaikoStatusChangeDate,
}) => {
    const ands = [];
    switch (searchType) {
        case SIM_ZAIKO_SEARCH_TYPES.SIM_NUMBER: // SIM番号 で検索する
            ands.push({ simInfo: { $regex: searchValue, $options: 'i' } });
            break;
        case SIM_ZAIKO_SEARCH_TYPES.KAISEN_NO: // 回線番号で検索する
            ands.push({ kaisenNo: { $regex: searchValue, $options: 'i' } });
            break;
        case SIM_ZAIKO_SEARCH_TYPES.KARI_KAISEN_NO: // 仮の電話番号で検索する
            ands.push({ kariKaisenNo: { $regex: searchValue, $options: 'i' } });
            break;
        default: // 何もしない
            break;
    }

    if (!isNone(tenantIds)) {
        ands.push({ tenantId: { $in: tenantIds } });
    }

    if (!isNone(tempoIds)) {
        ands.push({ tempoId: { $in: tempoIds } });
    }

    if (!isNone(zaikoStatuses)) {
        ands.push({ 'lastZaikoStatus.kouteiId': { $in: zaikoStatuses } });
    }

    if (!isNone(simStatuses)) {
        ands.push({ simStatus: { $in: simStatuses } });
    }

    if (!isNone(simContractTypes)) {
        ands.push({ simContractType: { $in: simContractTypes } });
    }

    if (!isNone(simTypes)) {
        ands.push({ simType: { $in: simTypes } });
    }

    if (!isNone(simYoutos)) {
        ands.push({ simYouto: { $in: simYoutos } });
    } else {
        switch (service) {
            case MVNO_SERVICES.LITE:
                ands.push({ simYouto: { $in: [SIM_YOUTO.WHITE_SIM, SIM_YOUTO.OTA_SIM] } });
                break;
            case MVNO_SERVICES.FULL:
                ands.push({ simYouto: SIM_YOUTO.COM_SIM });
                break;
            default:
                break;
        }
    }

    if (!isNone(fromSimStatusChangeDate)) {
        ands.push({ simStatusChangeDate: { $gte: fromSimStatusChangeDate } });
    }

    if (!isNone(toSimStatusChangeDate)) {
        ands.push({ simStatusChangeDate: { $lt: toSimStatusChangeDate } });
    }

    if (!isNone(isNeedCheckOption)) {
        if (isNeedCheckOption) {
            ands.push({ isNeedCheckOption: true });
        } else {
            ands.push({ isNeedCheckOption: { $ne: true } });
        }
    }

    ands.push({ removed: { $ne: true } });

    if (!isNone(exceptSimInfos)) {
        ands.push({ simInfo: { $nin: exceptSimInfos } });
    }

    if (!isNone(zmIds)) {
        if (isAllSelected) {
            ands.push({ zm_id: { $nin: zmIds } });
        } else {
            ands.push({ zm_id: { $in: zmIds } });
        }
    }

    if (!isNone(fiveGContractTypes)) {
        ands.push({ fiveGContractType: { $in: fiveGContractTypes } });
    }

    if (!isNone(fromZaikoStatusChangeDate)) {
        ands.push({ 'lastZaikoStatus.timestamp': { $gte: fromZaikoStatusChangeDate } });
    }

    if (!isNone(toZaikoStatusChangeDate)) {
        ands.push({ 'lastZaikoStatus.timestamp': { $lt: toZaikoStatusChangeDate } });
    }

    return ands.length > 0 ? { $and: ands } : {};
};

/**
 * find simZaikoInfo by simInfo
 * 旧メソッド: findBySimInfo
 * @param {object} context - API context
 * @param {object} data
 * @param {string} data.simInfo
 * @param {boolean} [data.onlyRemoved] - default: false
 * @param {boolean} [data.onlyDocomoHenkyakuZumi] - default: false
 * @returns {simZaikoSchema|null} simZaikoInfo object or null
 */
const getSimZaikoBySimInfo = async (context, { simInfo, onlyRemoved = false, onlyDocomoHenkyakuZumi = false }) => {
    if (!simInfo) throw new Error('simInfo is required');

    let conditions = { simInfo };
    if (onlyRemoved) conditions.removed = true;
    if (onlyDocomoHenkyakuZumi)
        conditions.zaikoStatuses = { kouteiId: SimZaikoConstants.ZAIKO_STATUS_ID_DOCOMO_HENKYAKU_ZUMI };

    return await SimZaikoModel.findOne(conditions).exec();
};

/**
 * find simZaikoInfo by simInfo (original function)
 * @param {string} simInfo
 * @param {boolean} includeRemoved
 * @param {boolean} includeDoCoMoHenkyakuZumi
 * @returns
 */
const findBySimInfo = async (simInfo, includeRemoved = false, includeDoCoMoHenkyakuZumi = false) => {
    if (!simInfo) throw new Error('simInfo is required');
    let conditions = { simInfo };
    if (!includeRemoved) {
        conditions.removed = { $ne: true };
    }
    if (!includeDoCoMoHenkyakuZumi) {
        conditions['zaikoStatuses.kouteiId'] = { $ne: SimZaikoConstants.ZAIKO_STATUS_ID_DOCOMO_HENKYAKU_ZUMI };
    }
    return await SimZaikoModel.findOne(conditions).exec();
};

/**
 * Get list of sim zaiko records by `simInfo`
 * @param {Array<string>} simInfoList
 * @param {boolean=} includeRemoved
 * @param {boolean=} includeDoCoMoHenkyakuZumi
 * @param {Array<string>=} fieldsForOutput `null|undefined` = all fields from Schema
 */
const findBySimInfoList = async (
    simInfoList,
    includeRemoved = false,
    includeDoCoMoHenkyakuZumi = false,
    fieldsForOutput = undefined
) => {
    if (!simInfoList || !Array.isArray(simInfoList)) throw new Error('simInfo is required');
    const conditions = { simInfo: { $in: simInfoList } };

    if (!includeRemoved) {
        conditions.removed = { $ne: true };
    }
    if (!includeDoCoMoHenkyakuZumi) {
        conditions['zaikoStatuses.kouteiId'] = { $ne: SimZaikoConstants.ZAIKO_STATUS_ID_DOCOMO_HENKYAKU_ZUMI };
    }

    const _fields = fieldsForOutput ?? Object.keys(SimZaikoModel.schema.tree);

    const ignoreFields = Object.keys(SimZaikoModel.schema.virtuals).concat('_id', '__v', 'simStatusChangeDate');
    const fields = Array.isArray(_fields) ? _fields.filter((f) => !ignoreFields.includes(f)) : [];

    const result = await SimZaikoModel.aggregate([
        { $match: conditions },
        {
            $sort: {
                simInfo: 1,
                simStatusChangeDate: -1, // take latest zaiko info by simStatusChangeDate
            },
        },
        {
            $group: {
                _id: '$simInfo',
                simStatusChangeDate: {
                    $first: '$simStatusChangeDate',
                },
                ...fields.reduce((acc, field) => {
                    acc[field] = { $first: `$${field}` };
                    return acc;
                }, {}),
            },
        },
    ]).exec();

    return result.map((row) => ({
        ...row,
        // populate virtual fields here:
        ...buildVirtualFields(row),
    }));
};

/**
 *
 * @param {string} context
 * @param {object} zaikoStatus
 * @param {string} zaikoStatus.zmId
 * @param {number} zaikoStatus.zaikoStatusId
 * @param {string} zaikoStatus.userId
 * @param {string} [zaikoStatus.contractType]
 */
const addZaikoStatusInfo = async (context, { zmId, zaikoStatusId, userId, contractType = '' }) => {
    const koutei = {
        kouteiId: zaikoStatusId,
        timestamp: dayjs().unix(),
        userId: userId,
    };
    let updateQuery = {
        $push: { zaikoStatuses: koutei },
        $set: { lastZaikoStatus: koutei },
    };
    if (contractType) {
        const updatedContractType = contractType === CONTRACT_TYPES.FIVE_G_NSA ? CONTRACT_TYPES.FIVE_G_NSA : '';
        updateQuery.$set.fiveGContractType = updatedContractType;
    }
    return await SimZaikoModel.findOneAndUpdate({ zm_id: zmId }, updateQuery, { runValidators: true });
};

const updateZaikoSimStatus = async (zm_id, simStatus) => {
    await SimZaikoModel.updateOne(
        { zm_id },
        { simStatus, simStatusChangeDate: dayjs().unix() },
        { runValidators: true }
    ).exec();
};

const findByZMId = async (zm_id) => {
    return await SimZaikoModel.findOne({ zm_id, removed: { $ne: true } }).exec();
};

const updateZaikoContractType = async (zm_id, contractType) => {
    return await SimZaikoModel.updateOne({ zm_id }, { fiveGContractType: contractType }, { runValidators: true });
};

const updateZaikoKaisenNo = async (zm_id, kaisenNo) => {
    return await SimZaikoModel.updateOne({ zm_id }, { kaisenNo: kaisenNo }, { runValidators: true });
};

const isZaikoStatusUpdateAvailableForNomalUser = (status) => {
    switch (parseInt(status)) {
        case SimZaikoModel.SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI:
        case SimZaikoModel.SimZaikoConstants.ZAIKO_STATUS_ID_ERROR:
        case SimZaikoModel.SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO:
        case SimZaikoModel.SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI:
            return true;
        default:
            return false;
    }
};

/**
 * STEP 17.0 OTA一括開通・廃止
 * @param {object} context
 * @param {string} tenantId
 * @param {string} kariKaisenNo
 * @param {string} simYouto
 */
const getSeizoBango = async (context, tenantId, kariKaisenNo, simYouto) => {
    context.log('SimZaikoModel getSeizoBango START ', tenantId, kariKaisenNo, simYouto);
    // STEP20: OTA一括開通/廃止には「有効期限切れ」あるいは「幕張返却済み」が利用されるのでフィルタする
    const statusArray = [
        SimZaikoConstants.ZAIKO_STATUS_ID_EXPIRED,
        SimZaikoConstants.ZAIKO_STATUS_ID_MAKUHARI_HENKYAKU_ZUMI,
        SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI,
    ];
    return await SimZaikoModel.findOne({
        tenantId,
        kariKaisenNo,
        simYouto,
        'lastZaikoStatus.kouteiId': { $in: statusArray },
        removed: { $ne: true },
    }).exec();
};


/**
 * STEP 17.0 OTA一括開通・廃止
 * @param {object} context
 * @param {string} tenantId
 * @param {string} kariKaisenNoList
 * @param {string} simYouto
 */
const getSeizoBangoMultiple = async (context, tenantIdList, kariKaisenNoList, simYouto) => {
    context.log('SimZaikoModel getSeizoBangoMultiple START ', tenantIdList, kariKaisenNoList, simYouto);
    // STEP20: OTA一括開通/廃止には「有効期限切れ」あるいは「幕張返却済み」が利用されるのでフィルタする
    const statusArray = [
        SimZaikoConstants.ZAIKO_STATUS_ID_EXPIRED,
        SimZaikoConstants.ZAIKO_STATUS_ID_MAKUHARI_HENKYAKU_ZUMI,
        SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI,
    ];
    return await SimZaikoModel.find({
        tenantId: { $in: tenantIdList },
        kariKaisenNo: { $in: kariKaisenNoList },
        simYouto,
        'lastZaikoStatus.kouteiId': { $in: statusArray },
        removed: { $ne: true },
    }).exec();
};


const getExiredOTASIM = async () => {
    const now = dayjs().unix();
    const query = {
        $and: [
            { simYouto: SIM_YOUTO.OTA_SIM },
            { simExpired: { $lt: now - 24 * 60 * 60 } },
            {
                'lastZaikoStatus.kouteiId': {
                    $in: [
                        SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI,
                        SimZaikoConstants.ZAIKO_STATUS_ID_ZAIKO_TOUROKU_ZUMI,
                    ],
                },
            },
        ],
    };

    return await SimZaikoModel.find(query);
};

const updateOTASimByZMIds = async (simzaikos) => {
    if (simzaikos.length > 0) {
        const query = {
            zm_id: { $in: simzaikos },
        };
        const zaikoStatusObj = {
            kouteiId: SimZaikoConstants.ZAIKO_STATUS_ID_EXPIRED,
            timestamp: dayjs().unix(),
            userId: '1000',
        };
        const updated = {
            $set: {
                lastZaikoStatus: zaikoStatusObj,
            },
            $push: {
                zaikoStatuses: zaikoStatusObj,
            },
        };
        await SimZaikoModel.updateMany(query, updated);
    }
};

/**
 * Change tenant and tempo
 * @param context - The context object.
 * @param {object} params - The search parameters.
 * @param {string} params.zmId - The zmId.
 * @param {string} params.userId - The userId of the user who is updating the SimZaikoModel.
 * @param {string} params.tenantId - The tenantId to update.
 * @param {string} params.tempoId - The tempoId to update.
 * @returns {Promise<object>} The result of the `updateOne` method call on the `SimZaikoModel`.
 */
const relocateTenantAndTempo = async (context, { zmId, userId, tenantId, tempoId }) => {
    context.log('SimZaikoModel relocateTenantAndTempo START ', zmId, tenantId, tempoId);

    const simZaiko = await SimZaikoModel.findOne({ zm_id: zmId });
    if (!simZaiko) {
        throw new Error(`SimZaikoModel not found for zmId: ${zmId}`);
    }

    const koutei = {
        kouteiId: SimZaikoConstants.ZAIKO_STATUS_ID_ZAIKO_TOUROKU_ZUMI, // 今の在庫ステータスを「在庫登録済み」に変更する
        timestamp: dayjs().unix(),
        userId: userId,
    };
    simZaiko.lastZaikoStatus = koutei;
    simZaiko.zaikoStatuses.push(koutei);
    simZaiko.tempoId = tempoId;

    if (simZaiko.tenantId === tenantId) {
        return await simZaiko.save();
    } else {
        simZaiko.tenantId = tenantId;
        return await SimZaikoModel.bulkWrite([
            {
                deleteOne: {
                    filter: { zm_id: zmId },
                },
            },
            {
                insertOne: {
                    document: simZaiko.toObject(),
                },
            },
        ]);
    }
};

/**
 * 作成
 * @param {object} context
 * @param {string} createdUserId
 * @param {string} simYouto
 * @param {string} tenantId
 * @param {string} tempoId
 * @param {string} simType
 * @param {string} simInfo
 * @param {string} kariKaisenNo
 * @param {number} simCreated
 * @param {number} simExpired
 * @param {string} makuhariZaikoKanriRan
 * @param {string} imsi
 * @param {string} pukOne
 * @param {string} pukTwo
 * @returns {Promise<string>} zmId or error
 */
const createZaiko = async (
    context,
    {
        createdUserId,
        simYouto,
        tenantId,
        tempoId,
        simType,
        simInfo,
        kariKaisenNo,
        simCreated,
        simExpired,
        makuhariZaikoKanriRan,
        imsi,
        pukOne,
        pukTwo,
    }
) => {
    context.log('SimZaikoModel createZaiko START');

    let query = {};
    query.removed = { $ne: true };
    query.simInfo = simInfo;
    query['zaikoStatuses.kouteiId'] = { $ne: SimZaikoConstants.ZAIKO_STATUS_ID_DOCOMO_HENKYAKU_ZUMI };

    let update = {};
    update.simYouto = simYouto;
    let simStatusVal;
    switch (simYouto) {
        // STEP4.0から、新規登録するSIM在庫のSIM用途を白-SIM/OTA-SIMだけにする
        // ALADIN-MNP, 半黒MNP => 白-SIM
        // OTA-MNP => OTA-SIM
        case SIM_YOUTO.OTA_SIM:
            simStatusVal = SIM_STATUS.OTA;
            break;
        default:
            simStatusVal = SIM_STATUS.SHIRO;
            break;
    }
    update.simStatus = simStatusVal;
    update.simStatusChangeDate = dayjs().unix();

    let zaikoRirekiObj = {};
    zaikoRirekiObj.kouteiId = SimZaikoConstants.ZAIKO_STATUS_ID_ZAIKO_TOUROKU_ZUMI;
    zaikoRirekiObj.timestamp = dayjs().unix();
    zaikoRirekiObj.userId = createdUserId;
    let zaikoRirekiDBList = [];
    zaikoRirekiDBList.push(zaikoRirekiObj);

    update.zaikoStatuses = zaikoRirekiDBList;
    update.lastZaikoStatus = zaikoRirekiObj;
    update.tenantId = tenantId;
    update.tempoId = tempoId;
    update.simType = simType;
    update.simInfo = simInfo;
    if (!isNone(kariKaisenNo)) {
        update.kariKaisenNo = kariKaisenNo;
    }
    if (!isNone(simCreated)) {
        update.simCreated = simCreated;
    }
    if (!isNone(simExpired)) {
        update.simExpired = simExpired;
    }
    if (!isNone(makuhariZaikoKanriRan)) {
        update.makuhariZaikoKanriRan = makuhariZaikoKanriRan;
    }
    // STEP14.0で追加する
    if (!isNone(imsi)) {
        update.imsi = imsi;
    }
    if (!isNone(pukOne)) {
        update.puk1 = pukOne;
    }
    if (!isNone(pukTwo)) {
        update.puk2 = pukTwo;
    }

    const update2 = { $setOnInsert: update };
    const doc = await SimZaikoModel.findOneAndUpdate(query, update2, {
        runValidators: true,
        context: 'query',
        upsert: true,
        new: true,
    }).exec();
    // 検索用に id から zm_id の値を作成してレコードに入れとく
    const _id_ObjectId = doc._id;
    const zmIdValue = portalConfig.ZAIKO_KANRI_ID_PREFIX + doc._id.toString();
    await SimZaikoModel.updateOne(
        { _id: _id_ObjectId },
        { $set: { zm_id: zmIdValue } },
        { runValidators: true }
    ).exec();
    return !isNone(zmIdValue) ? zmIdValue : 'エラー';
};

/**
 * 作成
 * @param {object} context
 * @param {string} createdUserId
 * @param {string} simYouto
 * @param {string} tenantId
 * @param {string} tempoId
 * @param {string} simType
 * @param {string} simInfo
 * @param {string} simStatus
 * @param {string} simContractType
 * @param {string} kaisenNo
 * @param {string} kariKaisenNo
 * @param {number} simCreated
 * @param {number} simExpired
 * @param {string} makuhariZaikoKanriRan
 * @param {[string]} kaisenOptions
 * @param {string} imsi
 * @param {string} pukOne
 * @param {string} pukTwo
 * @param {string} fiveGContractType
 * @returns {Promise<string>} zmId or error
 */
const createZaiko2 = async (
    context,
    {
        createdUserId,
        simYouto,
        tenantId,
        tempoId,
        simType,
        simInfo,
        simStatus,
        simContractType,
        kaisenNo,
        kariKaisenNo,
        simCreated,
        simExpired,
        makuhariZaikoKanriRan,
        kaisenOptions,
        imsi,
        pukOne,
        pukTwo,
        fiveGContractType,
    }
) => {
    context.log('SimZaikoModel createZaiko2 START');

    let query = {};
    query.removed = { $ne: true };
    query.simInfo = simInfo;
    query['zaikoStatuses.kouteiId'] = { $ne: SimZaikoConstants.ZAIKO_STATUS_ID_DOCOMO_HENKYAKU_ZUMI };

    let update = {};
    update.simYouto = simYouto;
    let simStatusVal;
    switch (simYouto) {
        // STEP4.0から、新規登録するSIM在庫のSIM用途を白-SIM/OTA-SIMだけにする
        // ALADIN-MNP, 半黒MNP => 白-SIM
        // OTA-MNP => OTA-SIM
        case SIM_YOUTO.OTA_SIM:
            simStatusVal = SIM_STATUS.OTA;
            break;
        default:
            simStatusVal = simStatus;
            break;
    }
    update.simStatus = simStatusVal;
    update.simContractType = simContractType;
    update.simStatusChangeDate = dayjs().unix();

    let isAllowUpdateKaisenNo = false;
    switch (simYouto) {
        case SIM_YOUTO.WHITE_SIM:
            isAllowUpdateKaisenNo = [SIM_STATUS.SHINKI_KURO, SIM_STATUS.SHINKI_HANKURO].includes(simStatus);
            break;
        case SIM_YOUTO.COM_SIM:
            isAllowUpdateKaisenNo = [SIM_STATUS.SHINKI_HANKURO].includes(simStatus);
            break;
        default:
            isAllowUpdateKaisenNo = false;
            break;
    }

    if (isAllowUpdateKaisenNo) {
        update.kaisenNo = kaisenNo;
    }

    let zaikoRirekiObj = {};
    zaikoRirekiObj.kouteiId = SimZaikoConstants.ZAIKO_STATUS_ID_ZAIKO_TOUROKU_ZUMI;
    zaikoRirekiObj.timestamp = dayjs().unix();
    zaikoRirekiObj.userId = createdUserId;
    let zaikoRirekiDBList = [];
    zaikoRirekiDBList.push(zaikoRirekiObj);

    update.zaikoStatuses = zaikoRirekiDBList;
    update.lastZaikoStatus = zaikoRirekiObj;
    update.tenantId = tenantId;
    update.tempoId = tempoId;
    update.simType = simType;
    update.simInfo = simInfo;

    if (!isNone(kariKaisenNo)) {
        update.kariKaisenNo = kariKaisenNo;
    }
    if (!isNone(simCreated)) {
        update.simCreated = simCreated;
    }
    if (!isNone(simExpired)) {
        update.simExpired = simExpired;
    }
    if (!isNone(makuhariZaikoKanriRan)) {
        update.makuhariZaikoKanriRan = makuhariZaikoKanriRan;
    }
    if (!isNone(kaisenOptions)) {
        update.kaisenOptions = kaisenOptions;
        update.isNeedCheckOption = true;
    }
    // STEP14.0で追加する
    if (!isNone(imsi)) {
        update.imsi = imsi;
    }
    if (!isNone(pukOne)) {
        update.puk1 = pukOne;
    }
    if (!isNone(pukTwo)) {
        update.puk2 = pukTwo;
    }
    if (!isNone(fiveGContractType)) {
        update.fiveGContractType = fiveGContractType;
    }
    const update2 = { $setOnInsert: update };
    const doc = await SimZaikoModel.findOneAndUpdate(query, update2, {
        runValidators: true,
        context: 'query',
        upsert: true,
        new: true,
    }).exec();
    // 検索用に id から zm_id の値を作成してレコードに入れとく
    const _id_ObjectId = doc._id;
    const zmIdValue = portalConfig.ZAIKO_KANRI_ID_PREFIX + doc._id.toString();
    await SimZaikoModel.updateOne(
        { _id: _id_ObjectId },
        { $set: { zm_id: zmIdValue } },
        { runValidators: true }
    ).exec();
    return !isNone(zmIdValue) ? zmIdValue : 'エラー';
};

const countTotalSimType = async (
    context,
    {
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds,
        isAllSelected,
        service,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate,
    }
) => {
    context.log('SimZaikoModel countTotalSimType START');

    const filter = createQuery({
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds,
        isAllSelected,
        service,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate,
    });
    context.log(
        'SimZaikoModel countTotalSimType aggregate: ',
        JSON.stringify([
            { $match: filter },
            { $group: { _id: '$simType', total: { $sum: 1 } } },
            { $project: { _id: 1, total: 1 } },
        ])
    );
    return await SimZaikoModel.aggregate([
        { $match: filter },
        { $group: { _id: '$simType', total: { $sum: 1 } } },
        { $project: { _id: 1, total: 1 } },
    ]);
};

/**
 *
 * @param {object} context
 * @param {number} searchType
 * @param {string} searchValue
 * @param {string[]} tenantIds
 * @param {string[]} tempoIds
 * @param {number[]} zaikoStatuses
 * @param {string[]} simStatuses
 * @param {string[]} simContractTypes
 * @param {string[]} simTypes
 * @param {string[]} simYoutos
 * @param {number} fromSimStatusChangeDate
 * @param {number} toSimStatusChangeDate
 * @param {boolean} isNeedCheckOption
 * @param {string[]} zmIds
 * @param {boolean} isAllSelected
 * @param {string[]} exceptSimInfos
 * @param {number} service
 * @param {string[]} fiveGContractTypes
 * @param {number} fromZaikoStatusChangeDate
 * @param {number} toZaikoStatusChangeDate
 * @returns {number} - count
 */
const countTotalSimZaiko = async (
    context,
    {
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds = null,
        isAllSelected = false,
        exceptSimInfos = null,
        service = MVNO_SERVICES.LITE,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate,
    }
) => {
    context.log('SimZaikoModel countTotalSimZaiko START');

    // create query string
    const query = createQuery({
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds,
        isAllSelected,
        exceptSimInfos,
        service,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate,
    });
    return await SimZaikoModel.find(query).count();
};

/**
 *
 * @param {object} context
 * @param {number} searchType
 * @param {string} searchValue
 * @param {string[]} tenantIds
 * @param {string[]} tempoIds
 * @param {number[]} zaikoStatuses
 * @param {string[]} simStatuses
 * @param {string[]} simContractTypes
 * @param {string[]} simTypes
 * @param {string[]} simYoutos
 * @param {number} fromSimStatusChangeDate
 * @param {number} toSimStatusChangeDate
 * @param {boolean} isNeedCheckOption
 * @param {string[]} zmIds
 * @param {boolean} isAllSelected
 * @param {string[]} exceptpageNumberSimInfos
 * @param {number} pageNumber
 * @param {number} limit
 * @param {number} service
 * @param {string[]} fiveGContractTypes
 * @param {number} fromZaikoStatusChangeDate
 * @param {number} toZaikoStatusChangeDate
 * @returns {Promise<[simZaikoSchema]|[]>} [simZaikoSchema] or []
 */
const findByZmIdsWithPageNumber = async (
    context,
    {
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds = null,
        isAllSelected = false,
        exceptSimInfos = null,
        pageNumber = 1,
        limit = 100,
        service = MVNO_SERVICES.LITE,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate,
    }
) => {
    context.log('SimZaikoModel findByZmIdsWithPageNumber START');

    const query = createQuery({
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds,
        isAllSelected,
        exceptSimInfos,
        service,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate,
    });
    //在庫管理のデータ抽出不具合（2020/6/11）https://mobilus.backlog.jp/view/MVNO_N_M-2001
    const sort = { simStatusChangeDate: -1 };
    const skip = pageNumber > 0 ? (pageNumber - 1) * limit : 0;
    return await SimZaikoModel.find(query).sort(sort).limit(limit).skip(skip);
};

const getSimYoutoList = () => {
    return [SIM_YOUTO.OTA_SIM, SIM_YOUTO.WHITE_SIM, SIM_YOUTO.COM_SIM];
};

/**
 *
 * @param {string} simYouto
 * @returns {number[]} zaiko status id list
 */
const getTanaoroshiZaikoStatuses = (simYouto) => {
    const statusIdList = Object.keys(SimZaikoConstants.ZAIKO_TITLES).filter((k) => {
        switch (parseInt(k)) {
            case SimZaikoConstants.ZAIKO_STATUS_ID_NYUKO_SHORI_ZUMI:
            case SimZaikoConstants.ZAIKO_STATUS_ID_ERROR:
            case SimZaikoConstants.ZAIKO_STATUS_ID_SHOKI_FURYO:
            case SimZaikoConstants.ZAIKO_STATUS_ID_EXPIRED:
                return true;
            case SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI:
                return simYouto === SIM_YOUTO.WHITE_SIM;
            default:
                return false;
        }
    });
    return statusIdList.map(Number);
};

/**
 *
 * @param {object} context
 * @param {string[]} tenantIds
 * @param {string[]} tempoIds
 * @param {string} sortBy
 * @param {boolean} isSortOrderAscending
 * @returns {simZaikoSchema[], number} [simZaikoSchema] and count
 */
const searchTanaoroshiZaikoSims = async (context, tenantIds, tempoIds, sortBy, isSortOrderAscending) => {
    context.log('SimZaikoModel searchTanaoroshiZaikoSims START');

    let ands = [];
    // Add tenantId condition
    if (!isNone(tenantIds)) {
        ands.push({ tenantId: { $in: tenantIds } });
    }
    // Add tempoId condition
    if (!isNone(tempoIds)) {
        ands.push({ tempoId: { $in: tempoIds } });
    }
    // Add simYouto conditions
    let simYoutoConditions = [];
    getSimYoutoList().forEach((simYouto) => {
        let simYoutoAnds = [];
        const tanaoroshiZaikoStatuses = getTanaoroshiZaikoStatuses(simYouto);
        simYoutoAnds.push({ simYouto: simYouto });

        if (!isNone(tanaoroshiZaikoStatuses)) {
            let zaikoStatuses = [];
            tanaoroshiZaikoStatuses.forEach((st) => zaikoStatuses.push(st));
            simYoutoAnds.push({ 'lastZaikoStatus.kouteiId': { $in: zaikoStatuses } });
        }

        simYoutoConditions.push({ $and: simYoutoAnds });
    });
    ands.push({ $or: simYoutoConditions });
    // Remove the removed sims
    ands.push({ removed: { $ne: true } });
    const query = ands.length > 0 ? { $and: ands } : {};
    // Add sort condition
    const sort = createSortCondition(sortBy, isSortOrderAscending);
    // Query to find sims
    const [zaikoSeq, zaikoCount] = await Promise.all([
        SimZaikoModel.find(query).sort(sort),
        SimZaikoModel.find(query).count(),
    ]);
    return { zaikoSeq, zaikoCount };
};

const createSortCondition = (sortBy, isSortOrderAscending) => {
    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = { simStatusChangeDate: sortAscendingInt };
    if (!isNone(sortBy)) {
        switch (sortBy) {
            case 'sim_youto':
                sort = { simYouto: sortAscendingInt };
                break;
            case 'tenant_id':
                sort = { tenantId: sortAscendingInt };
                break;
            case 'tempo_id':
                sort = { tempoId: sortAscendingInt };
                break;
            case 'sim_type':
                sort = { simType: sortAscendingInt };
                break;
            case 'sim_info':
                sort = { simInfo: sortAscendingInt };
                break;
            case 'sim_status':
                sort = { simStatus: sortAscendingInt };
                break;
            case 'kari_kaisen_no':
                sort = { kariKaisenNo: sortAscendingInt };
                break;
            case 'kaisen_no':
                sort = { kaisenNo: sortAscendingInt };
                break;
            case 'zaiko_status':
                sort = { 'lastZaikoStatus.kouteiId': sortAscendingInt };
                break;
            case 'zaiko_status_change_date':
                sort = { 'lastZaikoStatus.timestamp': sortAscendingInt };
                break;
            case 'sim_expired':
                sort = { simExpired: sortAscendingInt };
                break;
            case 'makuhari_zaiko_kanri_ran':
                sort = { makuhariZaikoKanriRan: sortAscendingInt };
                break;
            case 'imsi':
                sort = { imsi: sortAscendingInt };
                break;
            case 'puk1':
                sort = { puk1: sortAscendingInt };
                break;
            case 'puk2':
                sort = { puk2: sortAscendingInt };
                break;
            default:
                sort = { 'lastZaikoStatus.timestamp': sortAscendingInt };
                break;
        }
    }
    return sort;
};

/**
 *
 * @param {object} context
 * @param {string[]} tenantIds
 * @param {string[]} tempoIds
 * @returns {Map<(string,string), [simZaikoSchema]>} Map{(tenantId,tempoId), [simZaikoSchema]}
 */
const getZaikoSimSeq = async (context, tenantIds, tempoIds) => {
    context.log('SimZaikoModel getZaikoSimSeq START');
    // simYouto conditions
    let simYoutoConditions = [];
    getSimYoutoList().forEach((simYouto) => {
        let simYoutoAnds = [];
        const tanaoroshiZaikoStatuses = getTanaoroshiZaikoStatuses(simYouto);
        simYoutoAnds.push({ simYouto: simYouto });

        if (!isNone(tanaoroshiZaikoStatuses)) {
            let zaikoStatuses = [];
            tanaoroshiZaikoStatuses.forEach((st) => zaikoStatuses.push(st));
            simYoutoAnds.push({ 'lastZaikoStatus.kouteiId': { $in: zaikoStatuses } });
        }

        simYoutoConditions.push({ $and: simYoutoAnds });
    });

    const sort = { simInfo: 1 };

    let query = { $or: simYoutoConditions };
    query.tenantId = { $in: [...new Set(tenantIds)] };
    query.tempoId = { $in: [...new Set(tempoIds)] };
    query.removed = { $ne: true };

    const resultSeq = await SimZaikoModel.find(query).sort(sort).exec();
    const groupByResultSeq = _.groupBy(resultSeq, function (result) {
        return result.tenantId + ',' + result.tempoId;
    });
    return new Map(Object.entries(groupByResultSeq));
};

/**
 *
 * @param {object} context
 * @param {string} simInfo
 * @returns {number|null} koutieId|null
 */
const getLastZaikoStatus = async (context, simInfo) => {
    context.log('getLastZaikoStatus START', simInfo);
    if (!simInfo) throw new Error('simInfo is required');

    let query = {};
    query.simInfo = simInfo;
    query.removed = { $ne: true };
    const simZaikoSeq = (await SimZaikoModel.find(query).exec()).filter((simZaiko) => simZaiko.lastZaikoStatus);
    if (isNone(simZaikoSeq)) {
        return null;
    } else {
        const lastUpdateZaikoStatusSimzaiko = _.maxBy(simZaikoSeq, function (obj) {
            return obj.lastZaikoStatus.timestamp;
        });
        return lastUpdateZaikoStatusSimzaiko.lastZaikoStatus.kouteiId;
    }
};

/**
 * search sim zaiko
 * @param {object} context
 */
const search2 = async (
    context,
    searchType,
    searchValue,
    tenantIds,
    tempoIds,
    zaikoStatuses,
    simStatuses,
    simContractTypes,
    simTypes,
    simYoutos,
    fromSimStatusChangeDate,
    toSimStatusChangeDate,
    isNeedCheckOption,
    skip,
    limit,
    sortBy,
    isSortOrderAscending,
    exceptSimInfos,
    zmIds,
    isAllSelected,
    service,
    fiveGContractTypes,
    fromZaikoStatusChangeDate,
    toZaikoStatusChangeDate
) => {
    context.log('SimZaikoModel.search2 START');
    const { searchBySimInfo, searchByKaisenNo, searchByKariKaisenNo, searchByTenant, query } = createQuery2(
        context,
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds,
        isAllSelected,
        exceptSimInfos,
        service,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate
    );
    const sortCondition = createSortCondition(sortBy, isSortOrderAscending);
    // Step 14 improve performance
    const hintObj = {};
    if (searchBySimInfo && !searchByTenant) {
        hintObj.removed = 1;
        hintObj.simInfo = 1;
    } else if (searchBySimInfo && searchByTenant) {
        hintObj.tenantId = 1;
        hintObj.removed = 1;
        hintObj.simInfo = 1;
    } else if (searchByKaisenNo && !searchByTenant) {
        hintObj.removed = 1;
        hintObj.kaisenNo = 1;
    } else if (searchByKaisenNo && searchByTenant) {
        hintObj.tenantId = 1;
        hintObj.removed = 1;
        hintObj.kaisenNo = 1;
    } else if (searchByKariKaisenNo && !searchByTenant) {
        hintObj.removed = 1;
        hintObj.kariKaisenNo = 1;
    } else if (searchByKariKaisenNo && searchByTenant) {
        hintObj.tenantId = 1;
        hintObj.removed = 1;
        hintObj.kariKaisenNo = 1;
    }
    const countQuery = SimZaikoModel.find(query);
    const searchQuery = SimZaikoModel.find(query);

    if (!isNone(hintObj)) {
        countQuery.hint(hintObj);
        searchQuery.hint(hintObj);
    }

    const result = {
        count: await countQuery.count().exec(),
        cursor: [],
    };

    if (result.count <= 1) {
        result.cursor = await searchQuery.exec();
    } else if (result.count <= (limit ?? 0)) {
        result.cursor = await searchQuery.sort(sortCondition).exec();
    } else {
        const curSort = searchQuery.sort(sortCondition);
        const curSkip = isNone(skip) ? curSort : curSort.skip(skip);
        const curLimit = isNone(limit) ? curSkip : curSkip.limit(limit);
        result.cursor = await curLimit.exec();
    }

    return result;
};

/**
 *
 * @param {object} context
 * @param {number} [searchType]
 * @param {string} [searchValue]
 * @param {Array<string>} [tenantIds]
 * @param {Array<string>} [tempoIds]
 * @param {Array<number>} [zaikoStatuses]
 * @param {Array<string>} [simStatuses]
 * @param {Array<string>} [simContractTypes]
 * @param {Array<string>} [simTypes]
 * @param {Array<string>} [simYoutos]
 * @param {number} [fromSimStatusChangeDate]
 * @param {number} [toSimStatusChangeDate]
 * @param {boolean} [isNeedCheckOption]
 * @param {Array<string>} zmIds
 * @param {boolean} isAllSelected
 * @param {Array<string>} [exceptSimInfos]
 * @param {number} service
 * @param {Array<string>} [fiveGContractTypes]
 * @param {number} [fromZaikoStatusChangeDate]
 * @param {number} [toZaikoStatusChangeDate]
 */
const createQuery2 = (
    context,
    searchType,
    searchValue,
    tenantIds,
    tempoIds,
    zaikoStatuses,
    simStatuses,
    simContractTypes,
    simTypes,
    simYoutos,
    fromSimStatusChangeDate,
    toSimStatusChangeDate,
    isNeedCheckOption,
    zmIds,
    isAllSelected,
    exceptSimInfos,
    service,
    fiveGContractTypes,
    fromZaikoStatusChangeDate,
    toZaikoStatusChangeDate
) => {
    const andQuery = [];
    let searchBySimInfo = false,
        searchByTenant = false,
        searchByKaisenNo = false,
        searchByKariKaisenNo = false;

    switch (searchType) {
        case SIM_ZAIKO_SEARCH_TYPES.SIM_NUMBER:
            // SIM番号で検索する
            andQuery.push({ simInfo: new RegExp(searchValue, 'i') });
            searchBySimInfo = true;
            break;
        case SIM_ZAIKO_SEARCH_TYPES.KAISEN_NO:
            // 回線番号で検索する
            andQuery.push({ kaisenNo: new RegExp(searchValue, 'i') });
            searchByKaisenNo = true;
            break;
        case SIM_ZAIKO_SEARCH_TYPES.KARI_KAISEN_NO:
            // 仮回線で検索する
            andQuery.push({ kariKaisenNo: new RegExp(searchValue, 'i') });
            searchByKariKaisenNo = true;
            break;
    }

    if (!isNone(tenantIds) && Array.isArray(tenantIds)) {
        andQuery.push({ tenantId: { $in: tenantIds } });
        searchByTenant = true;
    }

    if (!isNone(tempoIds) && Array.isArray(tempoIds)) {
        andQuery.push({ tempoId: { $in: tempoIds } });
    }

    if (!isNone(zaikoStatuses) && Array.isArray(zaikoStatuses)) {
        andQuery.push({ 'lastZaikoStatus.kouteiId': { $in: zaikoStatuses } });
    }

    if (!isNone(simStatuses) && Array.isArray(simStatuses)) {
        andQuery.push({ simStatus: { $in: simStatuses } });
    }

    if (!isNone(simContractTypes) && Array.isArray(simContractTypes)) {
        andQuery.push({ simContractType: { $in: simContractTypes } });
    }

    if (!isNone(simTypes) && Array.isArray(simTypes)) {
        andQuery.push({ simType: { $in: simTypes } });
    }

    if (!isNone(simYoutos) && Array.isArray(simYoutos)) {
        andQuery.push({ simYouto: { $in: simYoutos } });
    } else {
        // STEP14: FULL MVNO
        switch (service) {
            case MVNO_SERVICES.LITE:
                andQuery.push({ simYouto: { $in: [SIM_YOUTO.WHITE_SIM, SIM_YOUTO.OTA_SIM] } });
                break;
            case MVNO_SERVICES.FULL:
                andQuery.push({ simYouto: SIM_YOUTO.COM_SIM });
                break;
        }
    }

    if (!isNone(fromSimStatusChangeDate)) {
        andQuery.push({ simStatusChangeDate: { $gte: fromSimStatusChangeDate } });
    }

    if (!isNone(toSimStatusChangeDate)) {
        andQuery.push({ simStatusChangeDate: { $lt: toSimStatusChangeDate } });
    }

    if (!isNone(isNeedCheckOption)) {
        if (isNeedCheckOption) {
            andQuery.push({ isNeedCheckOption: true });
        } else {
            andQuery.push({ isNeedCheckOption: { $ne: true } });
        }
    }

    andQuery.push({ removed: { $in: [false, null] } });

    if (!isNone(exceptSimInfos) && Array.isArray(exceptSimInfos)) {
        andQuery.push({ simInfo: { $nin: exceptSimInfos } });
    }

    if (!isNone(zmIds) && Array.isArray(zmIds)) {
        if (isAllSelected) {
            andQuery.push({ zm_id: { $nin: zmIds } });
        } else {
            andQuery.push({ zm_id: { $in: zmIds } });
        }
    }

    // STEP18.0 5G契約種別
    if (!isNone(fiveGContractTypes) && Array.isArray(fiveGContractTypes)) {
        andQuery.push({ fiveGContractType: { $in: fiveGContractTypes } });
    }

    // STEP20: 在庫ステータス変更日時の追加
    if (!isNone(fromZaikoStatusChangeDate)) {
        andQuery.push({ 'lastZaikoStatus.timestamp': { $gte: fromZaikoStatusChangeDate } });
    }

    if (!isNone(toZaikoStatusChangeDate)) {
        andQuery.push({ 'lastZaikoStatus.timestamp': { $lt: toZaikoStatusChangeDate } });
    }

    const query = andQuery.length > 0 ? { $and: andQuery } : {};
    return { searchBySimInfo, searchByKaisenNo, searchByKariKaisenNo, searchByTenant, query };
};

/**
 * STEP21: OTA一括開通・廃止
 * 該当の回線番号を持つSIMの枚数を返す、1枚以外ならエラー
 * @param {object} context
 * @param {string} tenantId
 * @param {string} kariKaisenNo
 * @param {string} simYouto
 * @returns {number} count
 */
const checkSeizoBango = async (context, tenantId, kariKaisenNo, simYouto) => {
    context.log('checkSeizoBango START', tenantId, kariKaisenNo, simYouto);

    let query = { tenantId: tenantId };
    query.kariKaisenNo = kariKaisenNo;
    query.simYouto = simYouto;
    // STEP20: OTA一括開通/廃止には「有効期限切れ」あるいは「幕張返却済み」が利用されるのでフィルタする
    const statusArr = [
        SimZaikoConstants.ZAIKO_STATUS_ID_EXPIRED,
        SimZaikoConstants.ZAIKO_STATUS_ID_MAKUHARI_HENKYAKU_ZUMI,
        SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI,
    ];
    query['lastZaikoStatus.kouteiId'] = { $in: statusArr };
    query.removed = { $ne: true };

    return await SimZaikoModel.find(query).count();
};

/**
 * STEP21: OTA一括開通・廃止
 * 該当の回線番号を持つSIMの枚数を返す、1枚以外ならエラー
 * @param {object} context
 * @param {string} tenantId
 * @param {Array<string>} kariKaisenNoList
 * @param {string} simYouto
 * @returns {Promise<{[kariKaisenNo: string]: number}>}
 */
const checkSeizoBangoMultiple = async (context, tenantId, kariKaisenNoList, simYouto) => {
    if (!Array.isArray(kariKaisenNoList) || kariKaisenNoList.length === 0) return {};
    context.log('checkSeizoBangoMultiple START', tenantId, simYouto, `| kariKaisen count: ${kariKaisenNoList.length}`);

    // STEP20: OTA一括開通/廃止には「有効期限切れ」あるいは「幕張返却済み」が利用されるのでフィルタする
    const statusArr = [
        SimZaikoConstants.ZAIKO_STATUS_ID_EXPIRED,
        SimZaikoConstants.ZAIKO_STATUS_ID_MAKUHARI_HENKYAKU_ZUMI,
        SimZaikoConstants.ZAIKO_STATUS_ID_GENBUTSU_NASHI,
    ];
    /** @type {Array<{_id: string, count: number}>} */
    const result = await SimZaikoModel.aggregate([
        {
            $match: {
                tenantId: tenantId,
                kariKaisenNo: { $in: kariKaisenNoList },
                simYouto: simYouto,
                'lastZaikoStatus.kouteiId': { $in: statusArr },
                removed: { $ne: true },
            },
        },
        {
            $group: {
                _id: '$kariKaisenNo',
                count: { $sum: 1 },
            },
        },
    ]);

    return result.reduce((acc, cur) => {
        acc[cur._id] = cur.count;
        return acc;
    }, {});
};

/**
 *
 * @param {object} context
 * @param {string} simInfo
 * @param {string} userId
 * @param {string} tenantId
 * @param {string} tempoId
 * @param {string|null} simStatus
 * @param {string|null} kaisenNo
 */
const zaikoUpdateBySimInfo = async (context, { simInfo, userId, tenantId, tempoId, simStatus, kaisenNo }) => {
    context.log('SimZaikoModel zaikoUpdateBySimInfo START');

    let query = { simInfo: simInfo };
    query.removed = { $ne: true };

    let updateObj = { tenantId: tenantId };
    updateObj.tempoId = tempoId;

    if (!isNone(simStatus)) {
        updateObj.simStatus = simStatus;
        updateObj.simStatusChangeDate = dayjs().unix();
    }

    if (!isNone(kaisenNo)) {
        updateObj.kaisenNo = kaisenNo;
    }

    // 今の在庫ステータスを「在庫登録済み」に変更する
    let zaikoRirekiObj = {};
    zaikoRirekiObj.kouteiId = SimZaikoConstants.ZAIKO_STATUS_ID_ZAIKO_TOUROKU_ZUMI;
    zaikoRirekiObj.timestamp = dayjs().unix();
    zaikoRirekiObj.userId = userId;
    updateObj.lastZaikoStatus = zaikoRirekiObj;

    let operation = { $set: updateObj };
    operation.$push = { zaikoStatuses: zaikoRirekiObj };

    // check if tenantId changes
    const oldZaiko = await SimZaikoModel.findOne(query).lean().exec();
    if (oldZaiko.tenantId !== tenantId) {
        context.log('zaikoUpdateBySimInfo change tenantId:', {
            zm_id: oldZaiko.zm_id,
            oldTenant: oldZaiko.tenantId,
            newTenant: tenantId,
            oldId: oldZaiko._id,
        });
        // reinsert zaiko since we can't update tenantId (shard key)
        const cloned = _.cloneDeep(oldZaiko);
        delete cloned._id;
        cloned.tenantId = tenantId;
        try {
            await SimZaikoModel.deleteOne(query);
            const newZaiko = new SimZaikoModel(cloned);
            await newZaiko.save();
            context.log('zaikoUpdateBySimInfo reinsert zaiko:', {
                zm_id: newZaiko.zm_id,
                oldId: oldZaiko._id,
                newId: newZaiko._id,
            });
            // do update as usual
            await SimZaikoModel.updateOne(query, operation, { runValidators: true }).exec();
        } catch (exception) {
            context.log.error('zaikoUpdateBySimInfo error:', exception);
        }
    } else {
        await SimZaikoModel.updateOne(query, operation, { runValidators: true }).exec();
    }
};

/**
 * search sim zaiko
 * @param {object} context
 */
const getSimZaikoListCur = async (
    context,
    searchType,
    searchValue,
    tenantIds,
    tempoIds,
    zaikoStatuses,
    simStatuses,
    simContractTypes,
    simTypes,
    simYoutos,
    fromSimStatusChangeDate,
    toSimStatusChangeDate,
    isNeedCheckOption,
    zmIds,
    isAllSelected,
    service,
    fiveGContractTypes,
    fromZaikoStatusChangeDate,
    toZaikoStatusChangeDate
) => {
    context.log('SimZaikoModel.getSimZaikoListCur START');
    const query = createQuery({
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds,
        isAllSelected,
        exceptSimInfos: null,
        service,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate,
    });
    return SimZaikoModel.find(query);
};

/**
 * delete sim zaiko
 * @param {object} context
 */
const deleteSimZaiko = async (
    context,
    searchType,
    searchValue,
    tenantIds,
    tempoIds,
    zaikoStatuses,
    simStatuses,
    simContractTypes,
    simTypes,
    simYoutos,
    fromSimStatusChangeDate,
    toSimStatusChangeDate,
    isNeedCheckOption,
    zmIds,
    isAllSelected,
    service,
    fiveGContractTypes,
    fromZaikoStatusChangeDate,
    toZaikoStatusChangeDate
) => {
    context.log('SimZaikoModel.deleteSimZaiko START');
    const query = createQuery({
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses,
        simContractTypes,
        simTypes,
        simYoutos,
        fromSimStatusChangeDate,
        toSimStatusChangeDate,
        isNeedCheckOption,
        zmIds,
        isAllSelected,
        exceptSimInfos: null,
        service,
        fiveGContractTypes,
        fromZaikoStatusChangeDate,
        toZaikoStatusChangeDate,
    });

    context.log('SimZaikoModel.deleteSimZaiko query:', JSON.stringify(query));
    const result = await SimZaikoModel.updateMany(
        query,
        { $set: { removed: true } },
        { upsert: false, rawResult: true }
    );
    context.log('SimZaikoModel.deleteSimZaiko deleted:', result.modifiedCount, 'records');
    return;
};

//statics
simZaikoSchema.statics = {
    getSimZaikoBySimInfo,
    findBySimInfo,
    findBySimInfoList,
    addZaikoStatusInfo,
    updateZaikoSimStatus,
    findByZMId,
    updateZaikoContractType,
    updateZaikoKaisenNo,
    isZaikoStatusUpdateAvailableForNomalUser,
    getSeizoBango,
    getExiredOTASIM,
    updateOTASimByZMIds,
    relocateTenantAndTempo,
    createZaiko,
    createZaiko2,
    countTotalSimType,
    countTotalSimZaiko,
    findByZmIdsWithPageNumber,
    searchTanaoroshiZaikoSims,
    getZaikoSimSeq,
    getLastZaikoStatus,
    search2,
    checkSeizoBango,
    checkSeizoBangoMultiple,
    zaikoUpdateBySimInfo,
    getSimZaikoListCur,
    deleteSimZaiko,
    getSeizoBangoMultiple
};

const SimZaikoModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.SIM_INVENTORY_SERVICE)
    .model('sim_zaiko', simZaikoSchema, 'sim_zaiko');

const SimZaikoModule = (module.exports = SimZaikoModel);
SimZaikoModule.SimZaikoConstants = SimZaikoConstants;
