const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const tanaoroshiMonthDataSchema = new Schema({
    month_value: { type: String, trim: true, required: true },
    tenant_id: { type: String, trim: true, required: true },
    tempo_id: { type: String, trim: true, required: true },
    user_id: { type: String, trim: true, required: true },
});

/**
 * @param {string} month
 * @param {Array<string>} tenantIds
 * @param {Array<string>} tempoIds
 */
const findByMonthValueAndTenantIdsAndTempoIds = async (month, tenantIds, tempoIds) => {
    const query = {
        month_value: month,
        tenant_id: {
            $in: tenantIds,
        },
        tempo_id: {
            $in: tempoIds,
        },
    };
    const sort = {
        tenant_id: 1,
        tempo_id: 1,
    };
    // Original function returns the count as well but here only returns the data for now
    return await TanaoroshiMonthDataModel.find(query).sort(sort);
};

/**
 * @param {string} monthValue
 * @param {string} tenantId
 * @param {string} tempoId
 * @returns {tanaoroshiMonthDataSchema|null} tanaoroshiMonthDataSchema or null
 */
const findByMonthValueAndTenantIdAndTempoId = async (context, monthValue, tenantId, tempoId) => {
    context.log('TanaoroshiMonthDataModel findByMonthValueAndTenantIdAndTempoId START', monthValue, tenantId, tempoId);

    let query = {};
    query.month_value = monthValue;
    query.tenant_id = tenantId;
    query.tempo_id = tempoId;
    return await TanaoroshiMonthDataModel.findOne(query).exec();
};

/**
 * @param {object} context
 * @param {string} monthValue
 * @param {string} tenantId
 * @param {string[]} tempoIds
 * @returns {[tanaoroshiMonthDataSchema],number} [tanaoroshiMonthDataSchema] & count
 */
const findByMonthValueAndTenantIdAndTempoIds = async (context, monthValue, tenantId, tempoIds) => {
    context.log(
        'TanaoroshiMonthDataModel findByMonthValueAndTenantIdAndTempoIds START',
        monthValue,
        tenantId,
        tempoIds
    );

    let query = {};
    query.month_value = monthValue;
    query.tenant_id = tenantId;
    query.tempo_id = { $in: tempoIds };
    const sort = { tempo_id: 1 };
    const [tanaoroshiMonthDataSeqResult, tanaoroshiMonthDataCount] = await Promise.all([
        TanaoroshiMonthDataModel.find(query).sort(sort),
        TanaoroshiMonthDataModel.find(query).count(),
    ]);
    return { tanaoroshiMonthDataSeqResult, tanaoroshiMonthDataCount };
};

/**
 *
 * @param {object} context
 * @param {string} monthValue
 * @param {string} tenantId
 * @param {string} tempoId
 * @param {string} userId
 */
const createTanaoroshiMonthData = async (context, monthValue, tenantId, tempoId, userId) => {
    const result = await TanaoroshiMonthDataModel.findOneAndUpdate(
        {
            month_value: monthValue,
            tenant_id: tenantId,
            tempo_id: tempoId,
        },
        { $set: { user_id: userId } },
        { new: true, upsert: true }
    ).exec();
    return result._id.toString();
};

tanaoroshiMonthDataSchema.statics = {
    findByMonthValueAndTenantIdsAndTempoIds,
    findByMonthValueAndTenantIdAndTempoId,
    findByMonthValueAndTenantIdAndTempoIds,
    createTanaoroshiMonthData,
};

const TanaoroshiMonthDataModel = mongoose.model(
    'tanaoroshi_month_data',
    tanaoroshiMonthDataSchema,
    'tanaoroshi_month_data'
);
module.exports = TanaoroshiMonthDataModel;
