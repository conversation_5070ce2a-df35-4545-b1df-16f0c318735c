/**
 * OTAIkkatuKaitsuuResultInfo class
 *
 * collection name: ota_bulk_operation
 */

const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { isNone, removeNullValue } = require('../../helpers/baseHelper');

const OTABulkOperationResultSchema = new Schema({
    tenantId: { type: String, trim: true, required: true },
    total: { type: Number },
    checkNG: { type: Number },
    checkOK: { type: Number },
    noProcess: { type: Number },
    endTime: { type: Number },
    createAt: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    detailsNG: [
        {
            _id: false,
            tenantId: { type: String, trim: true },
            kaisenNo: { type: String, trim: true },
            haishiDate: { type: Number },
            errorMsg: { type: String, trim: true },
        },
    ],
});

const searchOTAIkkatsuKaitsuuResult = async () => {
    return await OTABulkOperationResultModel.find({}).sort({ createAt: -1 }).limit(5);
};

const searchOTAIkkatsuKaitsuuResultDetailsById = async (id) => {
    const query = {
        _id: id,
    };

    const fields = {
        _id: 1,
        detailsNG: 1,
    };

    return await OTABulkOperationResultModel.findOne(query, fields);
};

/**
 * OTA_BULK＿OPERATORの更新する
 * @param {object} context
 * @param {string} id
 * @param {number} count
 */
const updateResultBySOIDs = async (context, id, count) => {
    context.log('OTABulkOperationResultModel updateResultBySOIDs START ', id, count);

    const query = { _id: mongoose.Types.ObjectId(id) };
    const update = { $inc: { noProcess: -count } };
    await OTABulkOperationResultModel.updateMany(query, update).exec();
};

/**
 * OTA一括開通結果追加（CSV一括登録）
 * @param {object} context
 * @param {object} infoInput
 * @returns {string} _id
 */
const otaIkkatsuKaitsuuInsert = async (context, infoInput) => {
    context.log('OTABulkOperationResultModel otaIkkatsuKaitsuuInsert START');

    let dbObj = {};
    dbObj.tenantId = infoInput.tenantId;
    dbObj.total = infoInput.total;
    if (!isNone(infoInput.checkNG)) {
        dbObj.checkNG = infoInput.checkNG;
    }

    if (!isNone(infoInput.checkOK)) {
        dbObj.checkOK = infoInput.checkOK;
    }

    if (!isNone(infoInput.noProcess)) {
        dbObj.noProcess = infoInput.noProcess;
    }
    dbObj.createAt = infoInput.createAt;

    // detailsNG
    let detailsNGList = [];
    if (!isNone(infoInput.detailsNG)) {
        infoInput.detailsNG.forEach((f) => {
            let detailNG = {};
            detailNG.tenantId = f.tenantId;
            detailNG.kaisenNo = f.kaisenNo;
            if (!isNone(f.errorMsg)) {
                detailNG.errorMsg = f.errorMsg;
            }
            detailsNGList.push(detailNG);
        });
    }
    dbObj.detailsNG = detailsNGList;

    const newOTABulkOperationResult = new OTABulkOperationResultModel(removeNullValue(dbObj));
    await newOTABulkOperationResult.save();
    return newOTABulkOperationResult._id.toString();
};

/**
 *
 * @param {object} context
 * @param {string} objectIdStr
 * @param {object} fields
 * @param {Array<object>} ngResultList
 */
const updateResult = async (context, objectIdStr, fields, ngResultList) => {
    context.log('OTABulkOperationResultModel updateResult START', objectIdStr);

    const query = { _id: mongoose.Types.ObjectId(objectIdStr) };
    
    let update1 = {};
    if (!isNone(fields)) {
        update1 = fields;
    }

    if (!isNone(ngResultList)) {
        let detailsNGList = [];
        ngResultList.forEach((f) => {
            let detailNG = {};
            detailNG.tenantId = f.tenantId;
            detailNG.kaisenNo = f.kaisenNo;
            detailNG.haishiDate = f.haishiDate;
            if (!isNone(f.errorMsg)) {
                detailNG.errorMsg = f.errorMsg;
            }
            detailsNGList.push(detailNG);
        });
        update1.detailsNG = detailsNGList;
    }

    const update = { $set: update1 };
    await OTABulkOperationResultModel.updateOne(query, update, { runValidators: true }).exec();
};

OTABulkOperationResultSchema.statics = {
    searchOTAIkkatsuKaitsuuResult,
    searchOTAIkkatsuKaitsuuResultDetailsById,
    updateResultBySOIDs,
    otaIkkatsuKaitsuuInsert,
    updateResult,
};

const OTABulkOperationResultModel = mongoose.model(
    'ota_bulk_operation',
    OTABulkOperationResultSchema,
    'ota_bulk_operation'
);
module.exports = OTABulkOperationResultModel;
