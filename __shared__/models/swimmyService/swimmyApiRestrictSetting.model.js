const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const appConfig = require('../../config/appConfig');
const { isNone, removeNullValue } = require('../../helpers/baseHelper');

const portalConfig = appConfig.getPortalConfig();

const swimmyApiRestrictSettingSchema = new Schema({
    maintenanceMode: { type: Boolean, default: false },
    lastUpdateUserId: { type: String, trim: true, required: true },
    updatedAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    createdDateTime: {
        type: Number,
        default: dayjs().unix(),
        set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()),
    },
});

const getSwimmyApiRestrictSetting = async (context) => {
    context.log('SwimmyApiRestrictSettingModel get START');
    return await SwimmyApiRestrictSettingModel.findOne({}).exec();
};

/**
 *
 * @param {object} context
 * @param {string} createdUserId
 * @returns {Promise<string>} _id
 */
const createOrUpdate = async (context, createdUserId) => {
    // NOTE: Add new parameters later when needed
    context.log('SwimmyApiRestrictSettingModel createOrUpdate START');

    const nowSecs = dayjs().unix();
    const oldData = await getSwimmyApiRestrictSetting(context);

    let obj = {};
    obj.lastUpdateUserId = createdUserId;
    obj.updatedAt = nowSecs;

    if (!isNone(oldData)) {
        await SwimmyApiRestrictSettingModel.updateOne(
            { _id: oldData._id },
            { $set: obj },
            { runValidators: true }
        ).exec();
        return oldData._id.toString();
    } else {
        const swimmyApiRestrictSetting = new SwimmyApiRestrictSettingModel(removeNullValue(obj));
        await swimmyApiRestrictSetting.save();
        return swimmyApiRestrictSetting._id.toString();
    }
};

const setMaintenanceMode = async (context, maintenanceMode) => {
    if (typeof maintenanceMode !== 'boolean') throw new Error('maintenanceMode must be a boolean');
    context.log('SwimmyApiRestrictSetting setMaintenance:', maintenanceMode);
    await SwimmyApiRestrictSettingModel.updateOne({}, { $set: { maintenanceMode } });
};

swimmyApiRestrictSettingSchema.statics = {
    getSwimmyApiRestrictSetting,
    createOrUpdate,
    setMaintenanceMode,
};

const SwimmyApiRestrictSettingModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.SWIMMY_SERVICE)
    .model('swimmy_api_restrict_setting', swimmyApiRestrictSettingSchema, 'swimmy_api_restrict_setting');

module.exports = SwimmyApiRestrictSettingModel;
