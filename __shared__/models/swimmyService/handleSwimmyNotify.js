const _ = require('lodash');
const { KOUTEI_IDS } = require('../../constants/orderConstants');
const { SIM_STATUS } = require('../../constants/simConstants');
const { SwimmyApiLogStatus } = require('../../constants/swimmyApiLogConstants');
const { SWIMMY_COMPLETE_RESULT, SWIMMY_REQUEST_TYPES } = require('../../constants/swimmyTransactions');

const { RESULT_CODE_OK } = require('../../helpers/responseHelper');

const MNPTennyuSoModel = require('../mnpTennyuSo.model');
const SimBlackingSoModel = require('../simBlackingSo.model');
const SimZaikoModel = require('../simInventoryService/simZaiko.model');
const SwimmyApiLogModel = require('./swimmyApiLog.model');

const { doKurokaKanryouTsuchiProcess } = require('./kurokaKanryouTsuchi');

/**
 *
 * @param {object} context
 * @param {object} apiLog - SwimmyApiLog document
 * @param {object} notification
 * @param {number} notification.notificationDateTime
 * @param {string} notification.resultCode
 * @param {string} [notification.errorCode]
 * @param {string} [notification.errorMessage]
 * @param {string} userId
 * @param {MNPTennyuSoModel|SimBlackingSoModel} provider - `MNPTennyuServiceOrder` or `SimBlackingServiceOrder`
 * - with fn `addKouteiInfo(soID:string, kouteiId:number, userId:string, ngReason?:string)`
 * - with fn `findSOBySOID(soID:string)` ->
 */
const handleSwimmyNotify = async (
    context,
    apiLog,
    { resultCode, errorCode, errorMessage }, // eslint-disable-line no-unused-vars
    userId,
    provider
) => {
    // https://code2flow.com/pB2njA
    /**
     * General rules:
     * - We ALWAYS add KOUTEI_ID_ALADIN_KOUJI_OK|KOUTEI_ID_ALADIN_KOUJI_NG to the SO depending on
     *   SwimmyOK|SwimmyNG
     * - If it's the first time we get a SwimmyOK , we send the request to CoreAPI
     *   We can check if we had received SwimmyOK before by checking for the presence of a
     *   KOUTEI_ID_ALADIN_KOUJI_OK in the list, besides the one we just added
     * - If it's not, we do nothing, and the SO remains in KOUTEI_ID_ALADIN_KOUJI_OK until it's
     *   fixed by hand on the Hosei screen.
     *   We log a severity ERROR message, since this should not happen
     * - If we get a SwimmyNG, and we had received a SwimmyOK before, we log a severity ERROR
     *   message, since that should not happen either
     */
    context.log('handleSwimmyNotify START');
    if (resultCode === SWIMMY_COMPLETE_RESULT.OK) {
        await SwimmyApiLogModel.updateStatus(
            apiLog.requestOrderId,
            SwimmyApiLogStatus.statuses.TOUROKU_YOUKYUU_RESPONSE_OK,
            apiLog.swimmyType
        );
    } else if (resultCode === SWIMMY_COMPLETE_RESULT.NG) {
        await SwimmyApiLogModel.updateStatus(
            apiLog.requestOrderId,
            SwimmyApiLogStatus.statuses.TOUROKU_YOUKYUU_RESPONSE_NG,
            apiLog.swimmyType
        );
    }

    const targetSO = await provider.findOne({ so_id: apiLog.requestOrderId }).exec();

    if (!targetSO) {
        context.log.error(
            `handleSwimmyNotify: service order not found. requestOrderId ${apiLog.requestOrderId} ; swimmyReceiptId ${apiLog.swimmyReceiptId}`
        );
    } else {
        if (resultCode === SWIMMY_COMPLETE_RESULT.OK) {
            // check first OK inside koutei array
            const isFirstOKReceived =
                _.findIndex(targetSO.kouteis || [], (k) => k.kouteiId === KOUTEI_IDS.ALADIN_KOUJI_OK) === -1;

            await provider.updateKouteiInfo(context, {
                so_id: targetSO.so_id,
                updatedUserId: userId,
                koutei: {
                    kouteiId: KOUTEI_IDS.ALADIN_KOUJI_OK,
                },
            });

            //STEP22.1: Swimmyへ黒化済通知（if ALADIN_KOUJI_OK, OK通知）
            if (apiLog.swimmyType === SWIMMY_REQUEST_TYPES.KUROKA) {
                const kuroSO = await SimBlackingSoModel.findSOByKurokaSOId(context, targetSO.so_id);
                if (kuroSO && kuroSO.notifyHost) {
                    context.log('handleSwimmyNotify: doKurokaKanryouTsuchiProcess - ALADIN_KOUJI_OK ', kuroSO.so_id)
                    await doKurokaKanryouTsuchiProcess(context, null, kuroSO);
                }
            }

            if (isFirstOKReceived) {
                // MVNO_N_M-2108 fix: フルMVNOの回線については、黒化してSIMステータスを黒になる
                if (!targetSO.aladinSOId) {
                    const aladinSO = await MNPTennyuSoModel.findOne({ so_id: targetSO.aladinSOId }).exec();
                    // 在庫データを更新
                    if (aladinSO && aladinSO.zm_id) {
                        await SimZaikoModel.updateZaikoSimStatus(aladinSO.zm_id, SIM_STATUS.KURO);
                    }
                }

                // Process with Core API
                // -> MNPTennyuSO & SimBlackingSo `handleSwimmyNotifyOK` function
                // error on this fn call will be caught from index.js
                const processCode = await provider.handleSwimmyNotifyOK(context, apiLog, targetSO);

                // update koutei if handleSwimmyNotifyOK succeed
                if (processCode === RESULT_CODE_OK) {
                    await provider.updateKouteiInfo(context, {
                        so_id: targetSO.so_id,
                        updatedUserId: userId,
                        koutei: {
                            kouteiId: KOUTEI_IDS.KOUKANKI_SETTEI_OK,
                        },
                    });
                    await provider.updateKouteiInfo(context, {
                        so_id: targetSO.so_id,
                        updatedUserId: userId,
                        koutei: {
                            kouteiId: KOUTEI_IDS.KANRYOU,
                        },
                    });
                } else {
                    await provider.updateKouteiInfo(context, {
                        so_id: targetSO.so_id,
                        updatedUserId: userId,
                        koutei: {
                            kouteiId: KOUTEI_IDS.KOUKANKI_SETTEI_NG,
                        },
                    });
                }
            } else {
                context.log.error(
                    `handleSwimmyNotify: ${apiLog.requestOrderId} had previously received OK and received OK again`
                );
            }
        } else if (resultCode === SWIMMY_COMPLETE_RESULT.NG) {
            const ngErrorCode = errorCode ?? '';
            const ngErrorMsg = errorMessage ?? 'その他';
            await provider.updateKouteiInfo(context, {
                so_id: targetSO.so_id,
                updatedUserId: userId,
                koutei: {
                    kouteiId: KOUTEI_IDS.ALADIN_KOUJI_NG,
                    ngReason: `${ngErrorCode}(${ngErrorMsg})`,
                },
            });

            //STEP22.1: Swimmyへ黒化済通知（if ALADIN_KOUJI_NG, NG通知）
            if (apiLog.swimmyType === SWIMMY_REQUEST_TYPES.KUROKA) {
                const kuroSO = await SimBlackingSoModel.findSOByKurokaSOId(context, targetSO.so_id);
                if (kuroSO && kuroSO.notifyHost) {
                    context.log('handleSwimmyNotify: doKurokaKanryouTsuchiProcess - ALADIN_KOUJI_NG ', kuroSO.so_id)
                    await doKurokaKanryouTsuchiProcess(context, null, kuroSO, ngErrorCode, ngErrorMsg);
                }
            }
        } else {
            context.log.error(`Unexpected parameters: ${apiLog.requestOrderId} | ${resultCode}`);
        }
    }
};

module.exports = {
    handleSwimmyNotify,
};
