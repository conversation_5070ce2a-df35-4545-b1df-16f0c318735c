const dayjs = require('dayjs');
const AppConfig = require('../../config/appConfig');
const { isNone } = require('../../helpers/baseHelper');
const { SO_KIND_TYPE, KOUTEI_IDS } = require('../../constants/orderConstants');
const { SWIMMY_REQUEST_TYPES } = require('../../constants/swimmyTransactions');
const { SwimmyApiLogStatus } = require('../../constants/swimmyApiLogConstants');
const { createRequestParamsForSwimmyApiLog } = require('../../helpers/swimmyHelper');
const { registerSwimmyTransaction } = require('../../services/swimmy.service');
const SwimmyApiLogModel = require('./swimmyApiLog.model');

const portalConfig = AppConfig.getPortalConfig();
const SWIMMY_VERSION = {
    V2: 'V2',
    V3: 'V3',
};

const checkError = async (context, { swimmyVersion, obj, kouteiId, forceNotify, now }) => {
    context.log(`doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: checkError: ${obj.so_id}`);

    if (!Object.keys(SWIMMY_VERSION).includes(swimmyVersion)) {
        context.log.error(`doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Invalid swimmyVersion: ${swimmyVersion}`);
        return { hasError: true };
    }

    if (![SO_KIND_TYPE.MNP_TENNYU, SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER].includes(obj.soKind)) {
        context.log.error(`doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Invalid soKind: ${obj.soKind}`);
        return { hasError: true };
    }

    if (
        ![
            KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_NG,
            KOUTEI_IDS.ALADIN_KOUJI_NG,
            KOUTEI_IDS.KOUKANKI_SETTEI_NG,
            KOUTEI_IDS.KOUKANKI_SETTEI_OK,
        ].includes(kouteiId)
    ) {
        context.log.error(`doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Invalid kouteiId: ${kouteiId}`);
        return { hasError: true };
    }

    //以下、OKと見做す場合は「回線番号」「SIM番号」必須とする。
    //・工程が「21:交換機設定NG」、「22:交換機設定OK」
    //https://mobilus.backlog.jp/view/MVNO_N_M-2566#comment-200747651
    //F1001エラー時のSwimmy通知改修ですが、まとめると以下のようになります。
    //・新規の場合→SIM番号：あり（リクエストから取得）、回線番号：なしでSwimmyに通知
    let kaisenNoAreNeeded = false;
    const isAladinKoujiNg =
        obj.soKind === SO_KIND_TYPE.MNP_TENNYU &&
        kouteiId === KOUTEI_IDS.ALADIN_KOUJI_NG &&
        obj.ngReason === portalConfig.ALADIN_KOUJI_NG_REASON;
    if ([KOUTEI_IDS.KOUKANKI_SETTEI_NG, KOUTEI_IDS.KOUKANKI_SETTEI_OK].includes(kouteiId) || isAladinKoujiNg) {
        kaisenNoAreNeeded = true;
    }

    let simNoAreNeeded = false;
    if (
        obj.soKind === SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER &&
        kouteiId === KOUTEI_IDS.ALADIN_KOUJI_NG &&
        obj.ngReason === portalConfig.ALADIN_KOUJI_NG_SPECIAL_REASON_FOR_NEW_ESIM
    ) {
        simNoAreNeeded = true;
    }
    context.log(`doESIMKekkaTsuChiSwimmy${swimmyVersion}Process:simNoAreNeeded: ${simNoAreNeeded}`);

    if (kaisenNoAreNeeded && isNone(obj.kaisenNo)) {
        context.log.error(
            `doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Not found param kaisenNo in aladin SO-ID: ${obj.so_id}`
        );
        return { hasError: true };
    }

    if (swimmyVersion === SWIMMY_VERSION.V2) {
        if ((kaisenNoAreNeeded || simNoAreNeeded) && isNone(obj.simInfo)) {
            context.log.error(
                `doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Not found param simNo in aladin SO-ID: ${obj.so_id}`
            );
            return { hasError: true };
        }
    } else {
        if ((kaisenNoAreNeeded || simNoAreNeeded) && isNone(obj.eid)) {
            context.log.error(
                `doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Not found param eId in aladin SO-ID: ${obj.so_id}`
            );
            return { hasError: true };
        }

        //「ダミー回線番号」はテナントIDがCON000の時のみ必須 so its value will be existed along with CON
        if (obj.soKind === SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER && isNone(obj.dummyLineNo)) {
            context.log.error(
                `doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Not found param dummyLineNo in aladin SO-ID: ${obj.so_id}`
            );
            return { hasError: true };
        }

        if (obj.soKind === SO_KIND_TYPE.MNP_TENNYU && isNone(obj.kaisenNo)) {
            context.log.error(
                `doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Not found param dummyLineNo(kaisenNo) in aladin SO-ID: ${obj.so_id}`
            );
            return { hasError: true };
        }
    }

    if (kouteiId === KOUTEI_IDS.ALADIN_KOUJI_NG && isNone(obj.ngReason)) {
        context.log.error(
            `doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: Not found param ngReason in aladin SO-ID: ${obj.so_id}`
        );
        return { hasError: true };
    }

    const swimmyApiLog = await SwimmyApiLogModel.findOneByRequestOrderId(obj.so_id);
    if (swimmyApiLog) {
        context.log(`doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: already sent notice to Swimmy: ${obj.so_id}`);
        //send swimmy notice even already sent
        if (forceNotify) {
            context.log(
                `Force send notice to Swimmy: ${obj.so_id}, old request: ${swimmyApiLog.requestParam}, old response: ${swimmyApiLog.responseParam}`
            );
            let requestParam;
            if (swimmyVersion === SWIMMY_VERSION.V2) {
                requestParam = createRequestParamsForSwimmyApiLog(
                    SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI,
                    now,
                    undefined,
                    undefined,
                    swimmyApiLog.requestOrderId,
                    obj.soKind,
                    obj.kaisenNo,
                    undefined,
                    [],
                    undefined,
                    undefined,
                    undefined,
                    undefined,
                    obj.simInfo,
                    `${kouteiId}`
                );
            } else {
                requestParam = createRequestParamsForSwimmyApiLog(
                    SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI,
                    now,
                    undefined,
                    undefined,
                    swimmyApiLog.requestOrderId,
                    obj.soKind,
                    obj.kaisenNo,
                    undefined,
                    [],
                    undefined,
                    undefined,
                    undefined,
                    undefined,
                    undefined,
                    `${kouteiId}`,
                    undefined,
                    undefined,
                    obj.dummyLineNo,
                    obj.simInfo
                );
            }

            await SwimmyApiLogModel.updateStatusAndRequestParamByRequestOrderIdAndSwimmyType(
                obj.so_id,
                swimmyApiLog.swimmyType,
                SwimmyApiLogStatus.statuses.TOUROKU_IRAI_NOT_YET_SENT,
                requestParam,
                obj.kaisenNo
            );
        }

        return { hasError: true };
    }

    return { hasError: false, kaisenNoAreNeeded, simNoAreNeeded };
};

const createApiLog = async (context, { swimmyVersion, obj, kouteiId, kaisenNoAreNeeded, simNoAreNeeded, now }) => {
    context.log(`doESIMKekkaTsuChiSwimmy${swimmyVersion}Process: createApiLog: ${obj.so_id}`);

    const params = {
        tenantId: obj.tenantId,
        swimmyType: SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI,
        requestOrderId: obj.so_id,
        lineNo: '',
        soKind: obj.soKind,
        processId: kouteiId,
        otherSystemSendDateTime: now,
        notifyPattern: obj.notifyPattern,
    };

    //卸ポータル上のALADIN工事NG理由（工程が「11:ALADIN工事NG」の場合、必須）
    if (kouteiId === KOUTEI_IDS.ALADIN_KOUJI_NG) {
        params.aladinSoNGReason = obj.ngReason;
    }

    if (swimmyVersion === SWIMMY_VERSION.V2) {
        //工程が「21:交換機設定NG」、「22:交換機設定OK」の場合 → 「回線番号」「SIM番号」は必須
        //工程が「11:ALADIN工事NG」かつALADIN工事NG理由が「（その他(F1001_1000)）」 → 「回線番号」「SIM番号」は必須
        if (kaisenNoAreNeeded || simNoAreNeeded) {
            params.lineNo = obj.kaisenNo;
            params.simNo = obj.simInfo;
        }
    } else {
        //ダミーとなる回線番号。通知されたデータをSwimmyで紐づける際に利用する。MNP/OTA時は任意とする。
        if (obj.soKind === SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER) {
            params.dummyLineNo = obj.dummyLineNo;
        }
        //工程が「21:交換機設定NG」、「22:交換機設定OK」の場合 → 「回線番号」「EID」は必須
        //工程が「11:ALADIN工事NG」かつALADIN工事NG理由が「（その他(F1001_1000)）」 → 「回線番号」「EID」は必須
        if (kaisenNoAreNeeded || simNoAreNeeded) {
            params.lineNo = obj.kaisenNo;
            params.eId = obj.eid;
        }
    }

    // if lineNo is undefined, change to empty string to avoid error
    // lineNo = kaisenNoo.getOrElse("")
    if (params.lineNo === undefined || params.lineNo === null) {
        params.lineNo = '';
    }

    await registerSwimmyTransaction(context, params);
};

const process = async (context, { swimmyVersion, obj, updateToKouteiId, forceNotify }) => {
    const now = dayjs().format('YYYYMMDDHHmmss');
    const kouteiId = updateToKouteiId ?? obj.koutei;

    const { hasError, kaisenNoAreNeeded, simNoAreNeeded } = await checkError(context, {
        swimmyVersion,
        obj,
        kouteiId,
        forceNotify,
        now,
    });

    if (!hasError) {
        await createApiLog(context, {
            swimmyVersion,
            obj,
            kouteiId,
            kaisenNoAreNeeded,
            simNoAreNeeded,
            now,
        });
    }
};

const doESIMKekkaTsuChiSwimmyV2Process = async (context, { obj, updateToKouteiId = null, forceNotify = false }) => {
    context.log.info('Start doESIMKekkaTsuChiSwimmyV2Process ', obj);

    await process(context, {
        swimmyVersion: SWIMMY_VERSION.V2,
        obj,
        updateToKouteiId,
        forceNotify,
    });

    context.log.info('End doESIMKekkaTsuChiSwimmyV2Process');
};

const doESIMKekkaTsuChiSwimmyV3Process = async (context, { obj, updateToKouteiId = null, forceNotify = false }) => {
    context.log.info('Start doESIMKekkaTsuChiSwimmyV3Process ', obj);

    await process(context, {
        swimmyVersion: SWIMMY_VERSION.V3,
        obj,
        updateToKouteiId,
        forceNotify,
    });

    context.log.info('End doESIMKekkaTsuChiSwimmyV3Process');
};

module.exports = {
    checkError,
    doESIMKekkaTsuChiSwimmyV2Process,
    doESIMKekkaTsuChiSwimmyV3Process,
};
