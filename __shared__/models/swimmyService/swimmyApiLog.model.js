const dayjs = require('dayjs');
const _ = require('lodash');
const mongoose = require('mongoose');
const {
    SWIMMY_API_LOG_COMPLETE_STATUS,
    SWIMMY_REQUEST_TYPES,
    SWIMMY_CONSTANTS,
    SWIMMY_APP_ATTRIBUTE_VALUE,
    SWIMMY_API_LOG_SEARCH_TYPE,
    SWIMMY_API_LOG_SORT_BY_FIELDS,
} = require('../../constants/swimmyTransactions');
const { SwimmyApiLogStatus } = require('../../constants/swimmyApiLogConstants');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const { removeNullValue, isNone } = require('../../helpers/baseHelper');
const SwimmyHelper = require('../../helpers/swimmyHelper');

const swimmyApiLogSchema = new Schema({
    // TODO check require flags
    tenantId: { type: String, trim: true },
    tempoId: { type: String, trim: true },
    daihyouBango: { type: String, trim: true },
    kaisenNo: { type: String, trim: true },
    swimmyType: { type: String, trim: true }, // swimmy request types
    requestOrderId: { type: String, trim: true },
    createdDateTime: {
        type: Number,
        default: dayjs().unix(),
        set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()),
    },
    getSwimmyResponseAt: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    priority: { type: Number, index: true },

    jikkouchuFlg: { type: Boolean, default: false, index: true },
    notifyHost: { type: String, trim: true },
    notifyPattern: { type: String, trim: true },

    // receipt ID returned by swimmy when we send a work order
    // For example, a 黒化 request
    swimmyReceiptId: { type: String, trim: true, index: true },

    status: { type: Number, index: true },
    completeStatus: {
        type: String,
        trim: true,
        get: (v) =>
            [
                SWIMMY_API_LOG_COMPLETE_STATUS.IN_PROCESS,
                SWIMMY_API_LOG_COMPLETE_STATUS.COMPLETE_NG,
                SWIMMY_API_LOG_COMPLETE_STATUS.COMPLETE_OK,
            ].includes(v)
                ? v
                : SWIMMY_API_LOG_COMPLETE_STATUS.UNKNOWN,
    },

    completedDateTime: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    completeNotifications: [
        {
            _id: false,
            notificationDateTime: {
                type: Number,
                default: dayjs().unix(),
                set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()),
            },
            resultCode: { type: String, trim: true },
            errorCode: { type: String, trim: true },
            errorMessage: { type: String, trim: true },
        },
    ],
    httpStatusCode: { type: Number },

    transactionId: { type: String, trim: true, index: true },
    aladinResultCode: { type: String, trim: true },
    swimmyResultCode: { type: String, trim: true },
    responseParam: {},
    requestParam: {},

    // step 25: 再送
    resendAt: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()), index: true },

    // TODO add more fields
});

swimmyApiLogSchema.index({ status: 1, jikkouchuFlg: 1 });
swimmyApiLogSchema.index({ status: 1, jikkouchuFlg: 1, priority: 1, createdDateTime: 1 });
swimmyApiLogSchema.index({ requestOrderId: 1, swimmyType: 1 });

/**
 * get swimmy transaction
 * @param {object} context
 */
// eslint-disable-next-line no-unused-vars
const get1RequestSwimmyAPIForPolling = async (context) => {
    const query = {
        status: { $in: [SwimmyApiLogStatus.statuses.TOUROKU_IRAI_NOT_YET_SENT] },
        jikkouchuFlg: { $ne: true },
    };
    const orderBy = {
        priority: 1,
        createdDateTime: 1,
    };
    const update = {
        $set: {
            jikkouchuFlg: true,
        },
    };
    const result = await SwimmyApiLogModel.findOneAndUpdate(query, update, { sort: orderBy }).lean();
    return result;
};

/**
 * @param {object} context - azure context
 * @param {string} transactionId
 * @param {boolean} newJikkouchuFlg - newJikkouchuFlg
 */
const updateJikkouchuFlg = async (context, transactionId, newJikkouchuFlg) => {
    return await SwimmyApiLogModel.updateOne({ transactionId }, { $set: { jikkouchuFlg: newJikkouchuFlg } });
};

const updateOneForResend = async (context, id) => {
    const result = await SwimmyApiLogModel.updateOne(
        {
            _id: mongoose.Types.ObjectId(id),
            // status:「0: 未登録依頼」も再送できるようにする
        },
        {
            $set: {
                status: SwimmyApiLogStatus.statuses.TOUROKU_IRAI_NOT_YET_SENT,
                completeStatus: SWIMMY_API_LOG_COMPLETE_STATUS.IN_PROCESS,
                jikkouchuFlg: false,
                resendAt: dayjs().unix(),
            },
        }
    );
    return result.modifiedCount;
};

/**
 * @param {string} swimmyReceiptId
 * @returns {Promise<object|null>}
 */
const findOneBySwimmyReceiptId = async (swimmyReceiptId) => {
    return await SwimmyApiLogModel.findOne({ swimmyReceiptId }).exec();
};

/**
 *
 * @param {string} requestOrderId
 * @param {string} swimmyType
 * @returns {Promise<object|null>}
 */
const findOneByRequestOrderIdAndSwimmyType = async (requestOrderId, swimmyType) => {
    return await SwimmyApiLogModel.findOne({ requestOrderId, swimmyType }).exec();
};

const findOneByRequestOrderId = async (requestOrderId) => {
    return await SwimmyApiLogModel.findOne({ requestOrderId }).exec();
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {string} data.swimmyReceiptId
 * @param {string} data.resultCode
 * @param {string} [data.errorCode]
 * @param {string} [data.errorMessage]
 * @returns {Promise<object|null>} SwimmyApiLog document
 */
const addCompleteNotification = async (context, { swimmyReceiptId, resultCode, errorCode, errorMessage }) => {
    context.log(`SwimmyApiLog.addCompleteNotification START swimmyReceiptId: ${swimmyReceiptId}`);
    const nowSecs = dayjs().unix();

    const notifObj = removeNullValue({
        notificationDateTime: nowSecs,
        resultCode,
        errorCode,
        errorMessage,
    });

    let updateData = {
        completedDateTime: nowSecs,
    };
    try {
        const doc = await SwimmyApiLogModel.findOneAndUpdate(
            { swimmyReceiptId },
            { $set: updateData, $push: { completeNotifications: notifObj } },
            { runValidators: true }
        );
        context.log('SwimmyApiLog.addCompleteNotification END');
        return doc;
    } catch (exception) {
        context.log.error('Error adding swimmy complete notification: ', exception);
        return;
    }
};

/**
 * @deprecated use swimmyService/handleSwimmyNotify.js
 */
// eslint-disable-next-line no-unused-vars
const handleSwimmyNotify = async (context, ...args) => {
    context.log.error('SwimmyApiLog.handleSwimmyNotify: use swimmyService/handleSwimmyNotify.js');
};

/**
 * Update `status` and `completeStatus` fields
 * @param {string} requestOrderId
 * @param {number} newStatus - typeof SWIMMY_API_LOG_STATUS
 * @param {string} [requestTypeOpt] - typeof SWIMMY_REQUEST_TYPES
 */
const updateStatus = async (requestOrderId, newStatus, requestTypeOpt) => {
    let requestType;
    if (requestTypeOpt) {
        requestType = requestTypeOpt;
    } else {
        const so = await findOneByRequestOrderId(requestOrderId);
        if (so) {
            requestType = so.swimmyType;
        }
    }
    if (!requestType) {
        requestType = SWIMMY_REQUEST_TYPES.fumei;
    }

    let updateData = {
        status: newStatus,
        completeStatus: SwimmyHelper.getCompleteStatus(requestType, newStatus),
    };
    await SwimmyApiLogModel.updateOne(
        {
            requestOrderId,
            //STEP22.1: As some orders have the same requestOrderIds, in order to avoid to update wrongly the order's status
            swimmyType: requestType,
        },
        updateData,
        {
            runValidators: true,
        }
    );
};

/**
 *
 * @param {object} context
 * @param {string} requestOrderId
 * @param {number} newStatus
 * @param {object} responseParam
 * @param {string} [requestTypeOpt]
 * @param {number} httpStatusCode
 */
const updateStatusAndResponse = async (
    context,
    requestOrderId,
    newStatus,
    responseParam,
    requestTypeOpt,
    httpStatusCode = 0
) => {
    const requestType = isNone(requestTypeOpt) ? SWIMMY_REQUEST_TYPES.fumei : requestTypeOpt;
    const updateData = {
        status: newStatus,
        responseParam,
        getSwimmyResponseAt: dayjs().unix(),
        httpStatusCode,
        completeStatus: SwimmyHelper.getCompleteStatus(requestType, newStatus),
    };

    await SwimmyApiLogModel.updateOne(
        {
            requestOrderId,
            //STEP22.1: As some orders have two requestOrderIds, in order to avoid to update wrongly the order's responseParam
            swimmyType: requestType,
        },
        { $set: updateData },
        { runValidators: true }
    );
};

/**
 * swimmy_api_log orders with status=10 and swimmytype=6 get processed and assigned a receipt ID
 * @param {object} context
 * @param {string} requestOrderId
 * @param {string} swimmyReceiptId
 */
const updateSwimmyReceiptId = async (context, requestOrderId, swimmyReceiptId) => {
    context.log('updateSwimmyReceiptId requestOrderId:', requestOrderId, ', swimmyReceiptId:', swimmyReceiptId);
    await SwimmyApiLogModel.updateOne({ requestOrderId }, { $set: { swimmyReceiptId } });
};

/**
 * create new Swimmy API Log (旧メソッド：create)
 * @param {object} context
 * @param {object} p
 * @param {number} p.status
 * @param {string} p.swimmyType
 * @param {number} p.priority
 * @param {string} p.requestOrderId
 * @param {boolean} [p.jikkouchuFlg]
 * @param {string} [p.transactionId]
 * @param {string} [p.tenantId]
 * @param {string} [p.daihyouBango]
 * @param {string} [p.responseParam]
 * @param {number} [p.getSwimmyResponseAt]
 * @param {string} [p.otherSystemSendDateTime]
 * @param {string} [p.userId]
 * @param {string} [p.useAuth]
 * @param {string} [p.soKind]
 * @param {string} p.lineNo // <-- required
 * @param {string} [p.resultCode]
 * @param {string} [p.errMessageId]
 * @param {string} [p.errMessage]
 * @param {string} [p.mnpOutDate]
 * @param {string} [p.appDate]
 * @param {string} [p.salesChannelCode]
 * @param {string} [p.simNo]
 * @param {string} [p.notifyHost]
 * @param {string} [p.processId]
 * @param {string} [p.aladinSoNGReason]
 * @param {string} [p.shippingNumber]
 * @param {string} [p.swimmyResultCode]
 * @param {string} [p.dummyLineNo]
 * @param {string} [p.eId]
 * @param {string} [p.notifyPattern]
 */
const createSwimmyApiLog = async (
    context,
    {
        status,
        swimmyType,
        priority,
        requestOrderId,
        jikkouchuFlg,
        transactionId,
        tenantId,
        daihyouBango,
        responseParam,
        getSwimmyResponseAt,
        // request header
        otherSystemSendDateTime,
        userId,
        useAuth,
        // case aladin kekka tsuchi
        soKind,
        lineNo,
        resultCode,
        errMessageId,
        errMessage,
        // case mnp tenshutu kanryou tsuchi
        mnpOutDate,
        // case kuroka
        appDate,
        salesChannelCode,
        // case kuroka kanryou tsuchi
        simNo,
        notifyHost,
        // case ota kekka tsuchi
        processId,
        aladinSoNGReason,
        // case sim saihakkou kekka tsuchi
        shippingNumber,
        swimmyResultCode, // STEP22.1 add for full mvno of case kurokaKanryouTsuchi
        dummyLineNo, // STEP23.0 add for eSIM kekka tsuchi
        eId, // STEP23.0 add for eSIM kekka tsuchi
        notifyPattern, // STEP23.0 add for eSIM kekka tsuchi
    }
) => {
    const nowSecs = dayjs().unix();
    const requestSwimmyParams = SwimmyHelper.createRequestParamsForSwimmyApiLog(
        swimmyType,
        otherSystemSendDateTime,
        userId,
        useAuth,
        requestOrderId,
        soKind ?? '',
        lineNo,
        (!isNone(resultCode) ? resultCode : swimmyResultCode) ?? '',
        !isNone(errMessageId) ? [{ errorCode: errMessageId, errorMessage: errMessage }] : [],
        appDate ?? '',
        salesChannelCode ?? '',
        [
            {
                appAttributeCode: SWIMMY_CONSTANTS.APP_ATTRIBUTE_CODE_1,
                appAttributeValue: SWIMMY_APP_ATTRIBUTE_VALUE[swimmyType],
            },
            { appAttributeCode: SWIMMY_CONSTANTS.APP_ATTRIBUTE_CODE_2, appAttributeValue: lineNo },
        ],
        mnpOutDate,
        simNo ?? '',
        processId ?? '',
        aladinSoNGReason,
        shippingNumber,
        dummyLineNo,
        eId,
        notifyPattern
    );
    const data = {
        transactionId,
        tenantId,
        daihyouBango,
        aladinResultCode: resultCode,
        swimmyResultCode: swimmyResultCode,
        responseParam,
        getSwimmyResponseAt,
        requestParam: requestSwimmyParams,
        status: status,
        kaisenNo: lineNo,
        swimmyType,
        priority,
        requestOrderId,
        jikkouchuFlg,
        createdDateTime: nowSecs,

        completeStatus: SwimmyHelper.getCompleteStatus(swimmyType, status),
        // STEP20: Swimmyへ黒化済通知
        notifyHost,
        // STEP23.0 need to save it in order to know which pattern is for calling eSIMKekkaTsuchi Swimmy api
        notifyPattern,
    };
    await SwimmyApiLogModel.create(removeNullValue(data));
    return requestOrderId;
};

const updateStatusAndRequestParam = async (requestOrderId, statusParam, requestParam, kaisenNo) => {
    return await SwimmyApiLogModel.updateOne(
        { requestOrderId },
        {
            $set: {
                status: statusParam,
                requestParam,
                jikkouchuFlg: false,
                kaisenNo: kaisenNo ?? '',
            },
        }
    ).exec();
};

const updateStatusAndRequestParamByRequestOrderIdAndSwimmyType = async (
    requestOrderId,
    swimmyType,
    newStatus,
    requestParam,
    kaisenNo
) => {
    return await SwimmyApiLogModel.updateOne(
        { requestOrderId, swimmyType },
        {
            $set: {
                status: newStatus,
                requestParam,
                jikkouchuFlg: false,
                kaisenNo: kaisenNo ?? '',
            },
        }
    ).exec();
};

/**
 * Search Swimmy API Log.
 * @param context - The context object.
 * @param {object} params - The search parameters.
 * @param {number} params.searchType - The search type.
 * @param {string} params.searchValue - The search value.
 * @param {string[]} params.tenantIds - The tenant ID list.
 * @param {string[]} params.requestOrderTypes - The request order type list.
 * @param {string[]} params.statuses - The status list.
 * @param {number} params.requestedDateTimeFrom - The requested date time from.
 * @param {number} params.requestedDateTimeTo - The requested date time to.
 * @param {number} [params.resendDateTimeFrom] - resendAt start
 * @param {number} [params.resendDateTimeTo] - resendAt until
 * @param {string} params.sortBy - The field to sort by.
 * @param {number} params.skip - The number of documents to skip.
 * @param {number} params.limit - The maximum number of documents to return.
 * @param {boolean} params.isSortOrderAscending - The sort order.
 * @returns {Promise<{result: [swimmyApiLogSchema], resultCount: number}>} An object containing the search results and
 * the total count of matching documents. The result is an array of swimmySoSchema.
 */
const searchSwimmyApiLog = async (
    context,
    {
        searchType,
        searchValue,
        tenantIds,
        requestOrderTypes,
        statuses,
        requestedDateTimeFrom,
        requestedDateTimeTo,
        resendDateTimeFrom,
        resendDateTimeTo,
        sortBy,
        skip,
        limit,
        isSortOrderAscending,
    }
) => {
    context.log('SwimmyApiLogModel searchSwimmyApiLog START');

    const ands = [];

    switch (searchType) {
        case SWIMMY_API_LOG_SEARCH_TYPE.REQUEST_ORDER_ID: // requestOrderId で検索する
            ands.push({ requestOrderId: { $eq: searchValue } });
            break;
        case SWIMMY_API_LOG_SEARCH_TYPE.KAISEN_NO: // 回線番号で検索する
            ands.push({ kaisenNo: { $eq: searchValue } });
            break;
        case SWIMMY_API_LOG_SEARCH_TYPE.SWIMMY_RECEIPT_ID: // swimmyReceiptId で検索する
            ands.push({ swimmyReceiptId: { $eq: searchValue } });
            break;
        default: // 何もしない
            break;
    }

    if (!isNone(requestOrderTypes)) {
        ands.push({ swimmyType: { $in: requestOrderTypes } });
    }
    if (!isNone(tenantIds)) {
        ands.push({ tenantId: { $in: tenantIds } });
    }
    if (!isNone(statuses)) {
        ands.push({ status: { $in: statuses } });
    }

    const requestedDateTime = [];
    if (!isNone(requestedDateTimeFrom)) {
        requestedDateTime.push({ createdDateTime: { $gte: requestedDateTimeFrom } });
    }
    if (!isNone(requestedDateTimeTo)) {
        requestedDateTime.push({ createdDateTime: { $lt: requestedDateTimeTo } });
    }
    if (requestedDateTime.length > 0) {
        ands.push({ $and: requestedDateTime });
    }

    const resendDateTime = [];
    if (!isNone(resendDateTimeFrom)) {
        resendDateTime.push({ resendAt: { $gte: resendDateTimeFrom } });
    }
    if (!isNone(resendDateTimeTo)) {
        resendDateTime.push({ resendAt: { $lt: resendDateTimeTo } });
    }
    if (resendDateTime.length > 0) {
        ands.push({ $and: resendDateTime });
    }

    const filter = ands.length > 0 ? { $and: ands } : {};

    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = { createdDateTime: sortAscendingInt };
    if (Object.hasOwn(SWIMMY_API_LOG_SORT_BY_FIELDS, sortBy)) {
        sort = { [SWIMMY_API_LOG_SORT_BY_FIELDS[sortBy]]: sortAscendingInt };
    }

    const query = SwimmyApiLogModel.find(filter).sort(sort);
    const resultCountQuery = query.clone().countDocuments();
    const resultQuery = query.skip(skip).limit(limit);
    const [resultCount, result] = await Promise.all([resultCountQuery, resultQuery]);
    return {
        result,
        resultCount,
    };
};

const findAllByRequestOrderIdAndOrderType = async (context, requestOrderId, requestOrderType) => {
    context.log('SwimmyApiLogModel findAllByRequestOrderIdAndOrderType START');
    const swimmyRequestTypeByAppAttributeValue = _.invert(SWIMMY_APP_ATTRIBUTE_VALUE);
    const swimmyType = swimmyRequestTypeByAppAttributeValue[requestOrderType] ?? '';
    const filter = { requestOrderId, swimmyType };
    return await SwimmyApiLogModel.find(filter).exec();
};

swimmyApiLogSchema.statics = {
    get1RequestSwimmyAPIForPolling,
    updateJikkouchuFlg,
    updateOneForResend,
    findOneBySwimmyReceiptId,
    findOneByRequestOrderId,
    findOneByRequestOrderIdAndSwimmyType,
    addCompleteNotification,
    handleSwimmyNotify,
    updateStatus,
    updateStatusAndResponse,
    updateSwimmyReceiptId,
    createSwimmyApiLog,
    updateStatusAndRequestParam,
    updateStatusAndRequestParamByRequestOrderIdAndSwimmyType,
    searchSwimmyApiLog,
    findAllByRequestOrderIdAndOrderType,
};

const SwimmyApiLogModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.SWIMMY_SERVICE)
    .model('swimmy_api_log', swimmyApiLogSchema, 'swimmy_api_log');

module.exports = SwimmyApiLogModel;
