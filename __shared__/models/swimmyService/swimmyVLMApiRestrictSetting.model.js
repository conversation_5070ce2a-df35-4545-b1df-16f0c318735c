const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const appConfig = require('../../config/appConfig');
const { isNone, removeNullValue } = require('../../helpers/baseHelper');
const portalConfig = appConfig.getPortalConfig();

const swimmyVLMApiRestrictSettingSchema = new Schema({
    maintenanceMode: { type: Boolean, default: false },
    lastUpdateUserId: { type: String, trim: true, required: true },
    updatedAt: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    apiCallableTenants: { type: [String] },

    createdDateTime: { type: Number, default: dayjs().unix(), set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
});

/** get ServicesRestrictSetting singleton (lean) */
const getSwimmyVLMApiRestrictSetting = async () => {
    return await SwimmyVLMApiRestrictSettingModel.findOne({}).lean().exec();
};

const isApiCallable = async (tenantId) => {
    return (await SwimmyVLMApiRestrictSettingModel.exists({ apiCallableTenants: tenantId }).exec()) !== null;
};

/**
 *
 * @param {object} context
 * @param {string} createdUserId
 * @param {Array<string>} apiCallableTenantsOpt
 * @returns {string} _id
 */
const createOrUpdate = async (context, createdUserId, apiCallableTenantsOpt) => {
    context.log('SwimmyVLMApiRestrictSettingModel createOrUpdate START');

    const nowSecs = dayjs().unix();

    const oldDataOpt = await getSwimmyVLMApiRestrictSetting();

    let obj = {};
    obj.lastUpdateUserId = createdUserId;
    obj.updatedAt = nowSecs;
    obj.apiCallableTenants = apiCallableTenantsOpt;

    if (!isNone(oldDataOpt)) {
        await SwimmyVLMApiRestrictSettingModel.updateOne(
            { _id: oldDataOpt._id },
            { $set: obj },
            { runValidators: true }
        ).exec();
        return oldDataOpt._id.toString();
    } else {
        const swimmyVLMApiRestrictSetting = new SwimmyVLMApiRestrictSettingModel(removeNullValue(obj));
        await swimmyVLMApiRestrictSetting.save();
        return swimmyVLMApiRestrictSetting._id.toString();
    }
};

/**
 * @param {object} context
 * @param {boolean} maintenanceMode
 */
const setMaintenanceMode = async (context, maintenanceMode) => {
    if (typeof maintenanceMode !== 'boolean') throw new Error('maintenanceMode must be a boolean');
    context.log('SwimmyVLMApiRestrictSetting setMaintenance:', maintenanceMode);
    await SwimmyVLMApiRestrictSettingModel.updateOne({}, { $set: { maintenanceMode } });
};


//statics
swimmyVLMApiRestrictSettingSchema.statics = {
    getSwimmyVLMApiRestrictSetting,
    isApiCallable,
    createOrUpdate,
    setMaintenanceMode,
};

const SwimmyVLMApiRestrictSettingModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.SWIMMY_VLM_0035_SERVICE)
    .model('swimmy_vlm_api_restrict_setting', swimmyVLMApiRestrictSettingSchema, 'swimmy_vlm_api_restrict_setting');

module.exports = SwimmyVLMApiRestrictSettingModel;
