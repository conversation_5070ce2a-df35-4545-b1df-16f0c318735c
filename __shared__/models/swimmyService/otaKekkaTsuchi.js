const dayjs = require('dayjs');
const AppConfig = require('../../config/appConfig');
const { SO_KIND_TYPE, KOUTEI_IDS } = require('../../constants/orderConstants');
const { SWIMMY_REQUEST_TYPES, SWIMMY_API_LOG_STATUS } = require('../../constants/swimmyTransactions');
const SwimmyHelper = require('../../helpers/swimmyHelper');
const { isNone } = require('../../helpers/baseHelper');
const SwimmyApiLogModel = require('./swimmyApiLog.model');
const SwimmyService = require('../../services/swimmy.service');

const portalConfig = AppConfig.getPortalConfig();

/**
 *
 * @param {object} context
 * @param {object} obj MNPTennyuServiceOrder
 * @param {boolean} [recoveryResultForKoutei]
 */
const doOTAKekkaTsuchiProcess = async (context, obj, recoveryResultForKoutei) => {
    context.log('Start doOTAKekkaTsuchiProcess');
    const priority = SwimmyHelper.getSwimmyApiPriority(context, SWIMMY_REQUEST_TYPES.OTA_KEKKA_TSUCHI);
    let hasErr = false;

    if (obj.soKind !== SO_KIND_TYPE.MNP_TENNYU && obj.soKind !== SO_KIND_TYPE.NEW_OTA_SERVICE_ORDER) {
        context.log.error('doOTAKekkaTsuchiProcess: Invalid soKind', obj.soKind, `(${obj.so_id})`);
        hasErr = true;
    }

    // take 22 because recoveryResultForKoutei can have to be updated till KouteiId=31
    const kouteiId = recoveryResultForKoutei ? KOUTEI_IDS.KOUKANKI_SETTEI_OK : obj.koutei;
    if (
        [
            KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_NG,
            KOUTEI_IDS.ALADIN_KOUJI_NG,
            KOUTEI_IDS.KOUKANKI_SETTEI_NG,
            KOUTEI_IDS.KOUKANKI_SETTEI_OK,
        ].every((koutei) => koutei !== kouteiId)
    ) {
        context.log.error('doOTAKekkaTsuchiProcess: Invalid kouteiId:', kouteiId, `${obj.so_id}`);
        hasErr = true;
    }

    // 以下、OKとみなす場合は「回線番号」「SIM番号」必須とする。
    // 工程が「21:交換機設定NG」、「22:交換機設定OK」
    // 工程が「11:ALADIN工事NG」かつALADIN工事NG理由が「（その他(F1001_1000))」
    let kaisenNosimNoAreNeeded = false;
    if (
        kouteiId === KOUTEI_IDS.KOUKANKI_SETTEI_NG ||
        kouteiId == KOUTEI_IDS.KOUKANKI_SETTEI_OK ||
        (KOUTEI_IDS.ALADIN_KOUJI_NG && obj.ngReason === portalConfig.ALADIN_KOUJI_NG_REASON)
    ) {
        kaisenNosimNoAreNeeded = true;
    }

    if (kaisenNosimNoAreNeeded && isNone(obj.kaisenNo)) {
        context.log.error('doOTAKekkaTsuchiProcess: Not found param kaisenNo in aladin SO-ID:', obj.so_id);
        hasErr = true;
    }
    if (kaisenNosimNoAreNeeded && isNone(obj.simInfo)) {
        context.log.error('doOTAKekkaTsuchiProcess: Not found param simNo in aladin SO-ID:', obj.so_id);
        hasErr = true;
    }

    if (!hasErr) {
        // TODO confirm timezone
        const now = dayjs().format('YYYYMMDDHHmmss');
        const statusParam = SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_NOT_YET_SENT;
        const swimmyTypeParam = SWIMMY_REQUEST_TYPES.OTA_KEKKA_TSUCHI;
        // 工程が「21:交換機設定NG」、「22:交換機設定OK」の場合 →「回線番号」「SIM番号」は必須
        // 工程が「11:ALADIN工事NG」かつALADIN工事NG理由が「（その他(F1001_1000)）」 →「回線番号」「SIM番号」は必須
        let kaisenNo, simInfo;
        if (kaisenNosimNoAreNeeded) {
            kaisenNo = obj.kaisenNo;
            simInfo = obj.simInfo;
        }
        //卸ポータル上のALADIN工事NG理由（工程が「11:ALADIN工事NG」の場合、必須）
        const aladinSONGReason = kouteiId === KOUTEI_IDS.ALADIN_KOUJI_NG ? obj.ngReason : null;

        const swimmyApiLog = await SwimmyApiLogModel.findOneByRequestOrderId(obj.so_id);

        if (!isNone(swimmyApiLog)) {
            // update
            context.log('doOTAKekkaTsuchiProcess: already sent notice to Swimmy:', obj.so_id);
            const requestParam = SwimmyHelper.createRequestParamsForSwimmyApiLog(
                swimmyTypeParam,
                now,
                undefined,
                undefined,
                obj.so_id,
                obj.soKind,
                kaisenNo,
                undefined,
                [],
                undefined,
                undefined,
                undefined,
                undefined,
                simInfo,
                `${kouteiId}`,
                aladinSONGReason
            );
            await SwimmyApiLogModel.updateStatusAndRequestParam(obj.so_id, statusParam, requestParam, kaisenNo);
        } else {
            // create
            await SwimmyService.registerSwimmyTransaction(context, {
                status: statusParam,
                swimmyType: swimmyTypeParam,
                priority,
                otherSystemSendDateTime: now,
                requestOrderId: obj.so_id,
                tenantId: obj.tenantId,
                processId: `${kouteiId}`,
                soKind: obj.soKind,
                lineNo: kaisenNo ?? '',
                simNo: simInfo,
                aladinSoNGReason: aladinSONGReason,
            });
        }
    }
    context.log('End doOTAKekkaTsuchiProcess');
};

module.exports = {
    doOTAKekkaTsuchiProcess,
};
