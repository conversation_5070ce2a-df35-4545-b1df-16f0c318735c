const dayjs = require('dayjs');

const { SWIMMY_REQUEST_TYPES } = require('../../constants/swimmyTransactions');
const { isNone } = require('../../helpers/baseHelper');
const { getSwimmyApiPriority } = require('../../helpers/swimmyHelper');

const SwimmyApiLogModel = require('./swimmyApiLog.model');
const SwimmyService = require('../../services/swimmy.service');

/**
 * Swimmy向けMNP転出済通知
 * @param {object} context
 * @param {import('../aladinService/aladinApiLog.model').AladinApiLogDocument} obj
 * @param {number} getTourokuYoukyuuResponseAt
 */
const doMNPTenshutsuKanryouTsuchiProcess = async (context, obj, getTourokuYoukyuuResponseAt = 0) => {
    context.log('Start doMNPTenshutsuKanryouTsuchiProcess');
    if (isNone(obj)) {
        throw new Error('doMNPTenshutsuKanryouTsuchiProcess error: aladin api log is required');
    }
    const priority = getSwimmyApiPriority(context, SWIMMY_REQUEST_TYPES.MNP_TENSHUTSU_KANRYOU_TSUCHI);

    let hasErr = false;

    const requestParam = obj.requestParam;
    const responseParam = obj.responseParam;

    let line = responseParam?.denwabango;
    if (isNone(line) || (typeof line === 'string' && line.length < 11)) {
        line = requestParam?.denwabango;
    }

    if (isNone(line)) {
        context.log.error(
            'doMNPTenshutsuKanryouTsuchiProcess: Not found param denwabango in response params aladin SO-ID:',
            obj.requestOrderId
        );
        hasErr = true;
    }

    const swimmyApiLog = await SwimmyApiLogModel.findOneByRequestOrderId(obj.requestOrderId);
    if (!isNone(swimmyApiLog)) {
        // this might not an actual error, so keep debug level log
        context.log('doMNPTenshutsuKanryouTsuchiProcess already sent notice to Swimmy:', obj.requestOrderId);
        hasErr = true;
    }

    if (!hasErr) {
        // TODO confirm timezone used
        const now = dayjs().format('YYYYMMDDHHmmss');
        const swimmyParams = {
            swimmyType: SWIMMY_REQUEST_TYPES.MNP_TENSHUTSU_KANRYOU_TSUCHI,
            priority,
            requestOrderId: obj.requestOrderId,
            transactionId: obj.transactionId,
            tenantId: obj.tenantId,
            otherSystemSendDateTime: now,
            lineNo: line ?? '',
            mnpOutDate: dayjs.unix(getTourokuYoukyuuResponseAt).format('YYYY/MM/DD HH:mm:ss'),
        };
        await SwimmyService.registerSwimmyTransaction(context, swimmyParams);
    }
    context.log('End doMNPTenshutsuKanryouTsuchiProcess');
};

module.exports = {
    doMNPTenshutsuKanryouTsuchiProcess,
};
