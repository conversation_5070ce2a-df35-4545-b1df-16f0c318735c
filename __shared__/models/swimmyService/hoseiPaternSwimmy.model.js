const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const HoseiPaternSwimmySchema = new Schema({
    name: { type: String, trim: true, required: true },
    kouteiProcess: [{ type: Number }],
    beginKouteiId: { type: Number, required: true },
});

/**
 * @param {number} lastKouteiId
 */
const findPattern = async (lastKouteiId) => {
    const q = {
        beginKouteiId: { $gt: lastKouteiId },
    };

    return await HoseiPaternSwimmyModel.find(q).sort({ _id: 1 }).exec();
};

HoseiPaternSwimmySchema.statics = {
    findPattern,
};

const HoseiPaternSwimmyModel = mongoose.model('hosei_patern_swimmy', HoseiPaternSwimmySchema, 'hosei_patern_swimmy');
module.exports = HoseiPaternSwimmyModel;
