const dayjs = require('dayjs');
const mongoose = require('mongoose');
const appConfig = require('../../config/appConfig');
const {
    SWIMMY_VLM_LOG_TRANSACTION_TYPE,
    SWIMMY_VLM_STATUS,
    SWIMMY_VLM_LOG_LINK_TYPE,
    SWIMMY_VLM_INTERNATIONAL_CALL,
} = require('../../constants/swimmyVLMTransactions');
const { isNone, removeNullValue } = require('../../helpers/baseHelper');
const { getSwimmyVLMPriority, getLinkType } = require('../../helpers/swimmyVLMHelper');
const { COCN_TENANT_ID } = require('../../constants/specialDefinitions');
const { setHasError0035 } = require('../../services/redisCache.service');

const Schema = mongoose.Schema;
const portalConfig = appConfig.getPortalConfig();

/**
 * @typedef {object} ActiveOrderRequest
 * @property {string} phoneNumber
 * @property {string} servicePlan
 * @property {string} internationalCall
 * @property {string} [costStartDate]
 **/

/**
 * @typedef {object} ActiveOrderResponse
 * @property {string} [phoneNumber]
 * @property {string} [servicePlan]
 * @property {string} [serviceStatus]
 * @property {string} [internationalCall]
 * @property {string} errorCode
 * @property {string} [costStartDate]
 */

/**
 * @typedef {object} CancelOrderRequest
 * @property {string} phoneNumber
 * @property {string} [costEndDate]
 */

/**
 * @typedef {object} CancelOrderResponse
 * @property {string} [phoneNumber]
 * @property {string} [serviceStatus]
 * @property {string} errorCode
 * @property {string} [costEndDate]
 */

/**
 * @typedef {object} ChangeOrderRequest
 * @property {string} phoneNumber
 * @property {string} servicePlan
 * @property {string} internationalCall
 * @property {string} [costStartDate]
 **/

/**
 * @typedef {object} ChangeOrderResponse
 * @property {string} [phoneNumber]
 * @property {string} [servicePlan]
 * @property {string} [serviceStatus]
 * @property {string} [internationalCall]
 * @property {string} errorCode
 * @property {string} [costStartDate]
 */

const swimmyVLMApiLogSchema = new Schema({
    soId: { type: String, trim: true },
    tenantId: { type: String, trim: true },
    kaisenNo: { type: String, trim: true },
    transactionType: { type: String, trim: true },
    requestOrderType: { type: Number },
    linkType: { type: String, trim: true },

    status: { type: Number }, // lastStatus
    statusTime: { type: Number }, // lastStatusStime (in millisecond)
    statusLog: { type: Object, default: {} }, // key in millisecond

    priority: { type: Number },
    jikkouchuFlg: { type: Boolean },

    requestParam: {
        costStartDate: { type: String },
        costEndDate: { type: String },
        phoneNumber: { type: String, trim: true },
        servicePlan: { type: String, trim: true },
        internationalCall: { type: String, trim: true },
    },
    responseLog: {},
    fileRequestParam: {
        changeDate: { type: String },
    },
    fileRequestLog: {},
    fileResponseLog: {},

    createdDateTime: { type: Number },
    completedDateTime: { type: Number },
    needsReActive: { type: Boolean },

    updateRetryUsers: {},
    updateStatusUsers: {},

    internationalCall: { type: String, trim: true },
    sameMvneInFlag: { type: Boolean },
});

// swimmyVLMApiLogSchema.index({ soId: 1, transactionType: 1 });

/**
 * 旧メソッド：create
 * @param {object} context
 * @param {string} soId
 * @param {string} tenantId
 * @param {string} [kaisenNo]
 * @param {number} requestOrderType
 * @param {string} transactionType
 * @param {object} requestParam
 * @param {object} responseParam
 * @param {number} status
 * @param {boolean} needsReActive
 * @param {boolean=false} sameMvneInFlag
 * @returns {Promise<string>} `soId` parameter
 */
const createNew = async (
    context,
    soId,
    tenantId,
    kaisenNo,
    requestOrderType,
    transactionType,
    requestParam,
    responseParam,
    //Requests start on hold until we get the Aladin OK and we can start the process
    status,
    needsReActive,
    sameMvneInFlag = false
) => {
    context.log('swimmyVLMApiLog createNew:', { soId, tenantId, kaisenNo, requestOrderType, transactionType });
    // duplcate check before insert
    const exist = await SwimmyVLMApiLogModel.exists({ soId, transactionType });
    if (exist !== null) {
        context.log.warn('swimmyVLMApiLog soId-transactionType pair exists!', { soId, transactionType });
        return soId;
    }
    const now = dayjs();
    const nowSeconds = now.unix();
    const nowMillis = now.valueOf();

    const priority = getSwimmyVLMPriority(context, requestOrderType);

    const data = {
        soId,
        tenantId,
        kaisenNo: kaisenNo ?? '',
        requestOrderType,
        transactionType,
        needsReActive,
        sameMvneInFlag,
        linkType: getLinkType(tenantId),
        statusLog: {
            [`${nowMillis}`]: status,
        },
        status, // lastStatus
        statusTime: nowMillis,
        priority,
        jikkouchuFlg: false,
        requestParam: removeNullValue(requestParam),
        createdDateTime: nowSeconds,
    };
    if (!isNone(responseParam)) {
        data.responseLog = {
            [`${nowMillis}`]: responseParam,
        };
    }
    const apiLog = new SwimmyVLMApiLogModel(removeNullValue(data));
    await apiLog.save();
    return soId;
};

/**
 * @param {object} context
 * @param {string} soId aladinSO so_id
 * @param {string} tenantId
 * @param {number} requestType SWIMMY_VLM_REQUEST_TYPES
 * @param {ActiveOrderRequest} requestParam
 * @param {ActiveOrderResponse} responseParam
 * @param {number} status SWIMMY_VLM_STATUS
 * @param {boolean} [sameMvneInFlag]
 * @returns {Promise<string>} SO ID (`soId` parameter)
 */
const createActiveOrder = async (
    context,
    soId,
    tenantId,
    requestType,
    requestParam,
    responseParam,
    status,
    sameMvneInFlag
) => {
    return await createNew(
        context,
        soId,
        tenantId,
        requestParam?.phoneNumber,
        requestType,
        SWIMMY_VLM_LOG_TRANSACTION_TYPE.SHINKI,
        requestParam,
        responseParam,
        status ?? SWIMMY_VLM_STATUS.ON_HOLD,
        false,
        sameMvneInFlag ?? false
    );
};

/**
 * @param {object} context
 * @param {string} soId aladinSO so_id
 * @param {string} tenantId
 * @param {number} requestType SWIMMY_VLM_REQUEST_TYPES
 * @param {CancelOrderRequest} requestParam
 * @param {CancelOrderResponse} responseParam
 * @param {number} status SWIMMY_VLM_STATUS
 * @param {boolean} [sameMvneInFlag]
 * @param {boolean} [needsReActive]
 * @returns {Promise<string>} SO ID (`soId` parameter)
 */
const createCancelOrder = async (
    context,
    soId,
    tenantId,
    requestType,
    requestParam,
    responseParam,
    status,
    needsReActive,
    sameMvneInFlag
) => {
    return await createNew(
        context,
        soId,
        tenantId,
        requestParam?.phoneNumber,
        requestType,
        SWIMMY_VLM_LOG_TRANSACTION_TYPE.HAISHI,
        requestParam,
        responseParam,
        status ?? SWIMMY_VLM_STATUS.ON_HOLD,
        needsReActive ?? false,
        sameMvneInFlag ?? false
    );
};

/**
 * get swimmy VLM transaction
 * @param {object} context - azure context
 * @returns {object} swimmy VLM transaction
 */
// eslint-disable-next-line no-unused-vars
const get1RequestForPolling = async (context) => {
    const query = {
        linkType: SWIMMY_VLM_LOG_LINK_TYPE.API_GATEWAY,
        jikkouchuFlg: { $ne: true },
        status: SWIMMY_VLM_STATUS.TOUROKU_IRAI_NOT_YET_SENT,
    };

    const orderBy = {
        priority: 1,
        createdDateTime: 1,
    };

    const update = {
        $set: {
            jikkouchuFlg: true,
        },
    };

    return await SwimmyVLMApiLogModel.findOneAndUpdate(query, update, { sort: orderBy }).lean();
};

const fixRequestParams = (tenantId, requestParam) => {
    const newRequestParam = { ...requestParam };
    if (newRequestParam?.internationalCall) {
        newRequestParam.internationalCall =
            tenantId === COCN_TENANT_ID ? SWIMMY_VLM_INTERNATIONAL_CALL.ON : SWIMMY_VLM_INTERNATIONAL_CALL.OFF;
    }
    return newRequestParam;
};

const updateRequestResponse = async (
    context,
    {
        soId,
        tenantId,
        transactionType,
        status,
        kaisenNo,
        requestParam,
        responseParam,
        jikkouchuuFlag,
        updateStatusUsers,
    }
) => {
    const now = dayjs();
    const nowMillis = now.valueOf();
    const query = {
        soId,
        transactionType,
    };

    const updateObj = {};
    if (!isNone(status)) {
        context.log(`SwimmyVLMLog.updateRequestResponse ${soId}+${transactionType} status -> ${status}`);
        updateObj.status = status;
        updateObj.statusTime = nowMillis;
        updateObj[`statusLog.${nowMillis}`] = status;
    }

    if (!isNone(kaisenNo)) {
        updateObj.kaisenNo = kaisenNo;
    }

    if (!isNone(jikkouchuuFlag)) {
        context.log(`SwimmyVLMLog.updateRequestResponse ${soId}+${transactionType} jikkouchuFlag -> ${jikkouchuuFlag}`);
        updateObj.jikkouchuFlg = jikkouchuuFlag;
    }

    if (!isNone(requestParam)) {
        updateObj.requestParam = fixRequestParams(tenantId, requestParam);
    }

    if (!isNone(responseParam)) {
        updateObj[`responseLog.${nowMillis}`] = responseParam;
    }

    if (!isNone(updateStatusUsers)) {
        updateObj[`updateStatusUsers.${nowMillis}`] = updateStatusUsers;
    }

    const setter = {
        $set: updateObj,
    };

    return await SwimmyVLMApiLogModel.updateOne(query, setter);
};

const updateErrorFlag = async (context) => {
    try {
        context.log(`SwimmyVLMLog.updateErrorFlag start`);
        const totalErrorFound = await SwimmyVLMApiLogModel.find({
            status: {
                $in: [SWIMMY_VLM_STATUS.TOUROKU_IRAI_RESULT_NG, SWIMMY_VLM_STATUS.STOPPING_FOR_ERROR],
            },
            transactionType: {
                $ne: SWIMMY_VLM_LOG_TRANSACTION_TYPE.HENKOU, // https://mobilus.backlog.jp/view/MVNO_N_M-3036: 「0035でんわ変更でエラーになった場合、0035でんわ管理の「！」を出さない」ように変更
            }
        }).count();
        context.log(`SwimmyVLMLog.updateErrorFlag totalErrorFound ${totalErrorFound}`);
        if (totalErrorFound > 0) {
            await setHasError0035(context, true);
        } else {
            await setHasError0035(context, false);
        }
    } catch (error) {
        context.log(`SwimmyVLMLog.updateErrorFlag error ${error}`);
    }
};

const pollingUpdate = async (
    context,
    { soId, tenantId, transactionType, status, kaisenNo, requestParam, responseParam, jikkouchuuFlag }
) => {
    const updateResult = await updateRequestResponse(context, {
        soId,
        tenantId,
        transactionType,
        status,
        kaisenNo,
        requestParam,
        responseParam,
        jikkouchuuFlag,
    });
    if (updateResult.modifiedCount > 0) {
        await updateErrorFlag(context);
    }

    return updateResult;
};

const pollingUpdateActiveOrder = async (
    context,
    { soId, tenantId, status, kaisenNo, request, response, jikkouchuuFlag }
) => {
    return await pollingUpdate(context, {
        soId,
        tenantId,
        transactionType: SWIMMY_VLM_LOG_TRANSACTION_TYPE.SHINKI,
        status,
        kaisenNo,
        requestParam: request,
        responseParam: response,
        jikkouchuuFlag,
    });
};

const pollingUpdateCancelOrder = async (
    context,
    { soId, tenantId, status, kaisenNo, request, response, jikkouchuuFlag }
) => {
    return await pollingUpdate(context, {
        soId,
        tenantId,
        transactionType: SWIMMY_VLM_LOG_TRANSACTION_TYPE.HAISHI,
        status,
        kaisenNo,
        requestParam: request,
        responseParam: response,
        jikkouchuuFlag,
    });
};

const pollingUpdateHenkouOrder = async (
    context,
    { soId, tenantId, status, kaisenNo, request, response, jikkouchuuFlag }
) => {
    return await pollingUpdate(context, {
        soId,
        tenantId,
        transactionType: SWIMMY_VLM_LOG_TRANSACTION_TYPE.HENKOU,
        status,
        kaisenNo,
        requestParam: request,
        responseParam: response,
        jikkouchuuFlag,
    });
};

const updateStatus = async (context, { soId, tenantId, transactionType, newStatus }) => {
    await updateRequestResponse(context, {
        soId,
        tenantId,
        transactionType,
        status: newStatus,
    });

    if (newStatus === SWIMMY_VLM_STATUS.STOPPING_FOR_ERROR && transactionType !== SWIMMY_VLM_LOG_TRANSACTION_TYPE.HENKOU) { // https://mobilus.backlog.jp/view/MVNO_N_M-3036: 「0035でんわ変更でエラーになった場合、0035でんわ管理の「！」を出さない」ように変更
        try {
            await setHasError0035(context, true);
        } catch (error) {
            context.log(`SwimmyVLMLog.updateStatus error ${error}`);
        }
    }
};
const updateStatusAndRequestParams = async (
    context,
    { soId, transactionType, newStatus, costStartDate, costEndDate }
) => {
    let result;
    const updateObj = {};
    const now = dayjs();
    const nowMillis = now.valueOf();
    if (!isNone(newStatus)) {
        updateObj.status = newStatus;
        updateObj.statusTime = nowMillis;
        updateObj[`statusLog.${nowMillis}`] = newStatus;
    }
    if (!isNone(costStartDate)) {
        updateObj['requestParam.costStartDate'] = costStartDate;
    }
    if (!isNone(costEndDate)) {
        updateObj['requestParam.costEndDate'] = costEndDate;
    }

    if (!isNone(updateObj)) {
        result = await SwimmyVLMApiLogModel.updateOne(
            { soId, transactionType, status: SWIMMY_VLM_STATUS.ON_HOLD },
            { $set: updateObj },
            { runValidators: true, rawResult: true }
        );

        if (newStatus === SWIMMY_VLM_STATUS.STOPPING_FOR_ERROR && transactionType !== SWIMMY_VLM_LOG_TRANSACTION_TYPE.HENKOU) { // https://mobilus.backlog.jp/view/MVNO_N_M-3036: 「0035でんわ変更でエラーになった場合、0035でんわ管理の「！」を出さない」ように変更
            try {
                await setHasError0035(context, true);
            } catch (error) {
                context.log(`SwimmyVLMLog.updateStatus error ${error}`);
            }
        }
    }

    return result;
};

const updateJikkouchuFlg = async (context, { soId, tenantId, transactionType, newJikkouchuFlg }) => {
    await updateRequestResponse(context, {
        soId,
        tenantId,
        transactionType,
        jikkouchuuFlag: newJikkouchuFlg,
    });
};

const findOneBySoIdAndType = async (context, soId, transactionType) => {
    const query = {
        soId,
        transactionType,
    };

    return await SwimmyVLMApiLogModel.findOne(query).lean();
};

const findActiveOrderBySoId = async (context, soId) => {
    return await findOneBySoIdAndType(context, soId, SWIMMY_VLM_LOG_TRANSACTION_TYPE.SHINKI);
};

/**
 * SwimmyVLM-API管理画面の検索する
 * @param {object} context
 * @param {number} searchType
 * @param {string} searchValue
 * @param {Array<string>} tenantIds
 * @param {Array<number>} orderTypes
 * @param {Array<number>} status
 * @param {string} transactionType
 * @param {number} actionType
 * @param {number} sameMvneInFlag
 * @param {number} requestedDateTimeFrom
 * @param {number} requestedDateTimeTo
 * @param {string} sortBy
 * @param {number} skip
 * @param {number} limit
 * @param {boolean} isSortOrderAscending
 * @returns {Promise<Array<swimmyVLMApiLogSchema>,number>} [swimmyVLMApiLogSchema] and count
 */
const searchSwimmyVLMLog = async (
    context,
    {
        searchType,
        searchValue,
        tenantIds,
        orderTypes,
        status,
        transactionType,
        actionType,
        sameMvneInFlag,
        requestedDateTimeFrom,
        requestedDateTimeTo,
        sortBy,
        skip,
        limit,
        isSortOrderAscending,
    }
) => {
    context.log('SwimmyVLMApiLogModel searchSwimmyVLMLog START');
    let ands = [];
    switch (searchType) {
        case 0: // requestOrderId で検索する
            ands.push({ soId: { $eq: searchValue } });
            break;
        case 1: // 回線番号で検索する
            ands.push({ kaisenNo: { $eq: searchValue } });
            break;
        default:
            break;
    }
    if (!isNone(tenantIds)) {
        ands.push({ tenantId: { $in: tenantIds } });
    }
    if (!isNone(orderTypes)) {
        ands.push({ requestOrderType: { $in: orderTypes } });
    }
    if (!isNone(status)) {
        ands.push({ status: { $in: status } });
    }
    if (!isNone(transactionType)) {
        ands.push({ transactionType: { $eq: transactionType } });
    }
    switch (actionType) {
        case 0:
            ands.push({ updateRetryUsers: { $exists: true } });
            break;
        case 1:
            ands.push({ updateStatusUsers: { $exists: true } });
            break;
        default:
            break;
    }
    switch (sameMvneInFlag) {
        case 0:
            ands.push({ sameMvneInFlag: { $eq: true } });
            break;
        case 1:
            ands.push({ sameMvneInFlag: { $ne: true } });
            break;
        default:
            break;
    }
    if (!isNone(requestedDateTimeFrom) || !isNone(requestedDateTimeTo)) {
        let requestTime = [];
        if (!isNone(requestedDateTimeFrom)) {
            requestTime.push({ createdDateTime: { $gte: requestedDateTimeFrom } });
        }
        if (!isNone(requestedDateTimeTo)) {
            requestTime.push({ createdDateTime: { $lt: requestedDateTimeTo } });
        }
        ands.push({ $and: requestTime });
    }
    let query = {};
    if (ands.length > 0) {
        query = { $and: ands };
    }
    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = { createdDateTime: sortAscendingInt };
    if (!isNone(sortBy)) {
        switch (sortBy) {
            case 'request_order_id':
                sort = { soId: sortAscendingInt };
                break;
            case 'transaction_type':
                sort = { transactionType: sortAscendingInt };
                break;
            case 'order_type':
                sort = { requestOrderType: sortAscendingInt };
                break;
            case 'tenant_id':
                sort = { tenantId: sortAscendingInt };
                break;
            case 'kaisen_no':
                sort = { kaisenNo: sortAscendingInt };
                break;
            case 'start_date':
                sort = { createdDateTime: sortAscendingInt };
                break;
        }
    }
    const totalFound = await SwimmyVLMApiLogModel.find(query).count();
    let orderRequestIdList = [];
    if (!isNone(limit) && isNone(skip)) {
        orderRequestIdList = await SwimmyVLMApiLogModel.find(query).sort(sort).limit(limit);
    } else if (isNone(limit) && !isNone(skip)) {
        orderRequestIdList = await SwimmyVLMApiLogModel.find(query).sort(sort).skip(skip);
    } else {
        orderRequestIdList = await SwimmyVLMApiLogModel.find(query).sort(sort).limit(limit).skip(skip);
    }
    return {
        orderRequestIdList,
        totalFound,
    };
};

/**
 * SwimmyVLM-ログ詳細の検索する。
 * @param {object} context
 * @param {string} requestOrderId
 * @param {string} transactionType
 * @returns {Promise<Array<swimmyVLMApiLogSchema>|[]>} [swimmyVLMApiLogSchema] or []
 */
const findAllByRequestOrderId = async (context, requestOrderId, transactionType) => {
    context.log('SwimmyVLMApiLogModel findAllByRequestOrderId START ', requestOrderId, transactionType);

    let query = {};
    query.soId = requestOrderId;
    query.transactionType = transactionType;
    return await SwimmyVLMApiLogModel.find(query).exec();
};

/**
 * for retry-request
 * @param {object} context
 * @param {string} soId
 * @param {string} transactionType
 * @param {string} linkType
 * @param {string} updateRetryUsers
 * @returns {Promise<number>} update result count
 */
const updateForRetryRequest = async (context, soId, transactionType, linkType, updateRetryUsers) => {
    context.log('SwimmyVLMApiLogModel updateForRetryRequest START ', soId, transactionType, linkType, updateRetryUsers);

    const status = SWIMMY_VLM_STATUS.TOUROKU_IRAI_NOT_YET_SENT;
    let query = { soId: soId };
    query.transactionType = transactionType;
    query.jikkouchuFlg = false;
    query.linkType = linkType;
    // query.status = { $ne: status }; // 「0: 未登録依頼」も再送できるようにする

    let updateObj = {};
    const nowMillis = dayjs().valueOf();
    updateObj.status = status;
    updateObj.statusTime = nowMillis;
    updateObj[`statusLog.${nowMillis}`] = status;
    updateObj[`updateRetryUsers.${nowMillis}`] = updateRetryUsers;

    // change date when link type is FILE
    if (linkType === SWIMMY_VLM_LOG_LINK_TYPE.FILE) {
        const now = dayjs().format('YYYYMMDD');
        updateObj['fileRequestParam.changeDate'] = now;
    }
    const setter = { $set: updateObj };
    const updateResult = await SwimmyVLMApiLogModel.updateOne(query, setter);
    await updateErrorFlag(context);

    return updateResult.modifiedCount;
};

/**
 * ステータス変更
 * @param {object} context
 * @param {string} soId
 * @param {string} tenantId
 * @param {string} transactionType
 * @param {number} newStatus
 * @param {string} updateStatusUsers
 * @returns {Promise<updateResult>} update result
 */
const updateStatusForUI = async (context, soId, tenantId, transactionType, newStatus, updateStatusUsers) => {
    context.log(
        'SwimmyVLMApiLogModel updateStatusForUI START ',
        soId,
        tenantId,
        transactionType,
        newStatus,
        updateStatusUsers
    );

    const updateResult = await updateRequestResponse(context, {
        soId,
        tenantId,
        transactionType,
        status: newStatus,
        updateStatusUsers,
    });
    await updateErrorFlag(context);
    return updateResult;
};

/**
 * @param {object} context
 * @param {string} soId
 */
const findOneBySoIdSortByTransactionType = async (context, soId) => {
    return await SwimmyVLMApiLogModel.findOne({ soId }).sort({ transactionType: -1 }).exec();
};

/**
 * 工程補正の中で行う0035でんわ連携の処理
 * @param {object} context
 * @param {string} soId
 * @param {string} transactionType
 * @param {string} updateRetryUser
 */
const updateStatusForRecovery = async (context, soId, transactionType, updateRetryUser) => {
    const nowMillisecs = dayjs().valueOf();
    const newStatus = SWIMMY_VLM_STATUS.TOUROKU_IRAI_NOT_YET_SENT;
    let result = null;
    try {
        await SwimmyVLMApiLogModel.updateOne(
            { soId, transactionType },
            {
                $set: {
                    status: newStatus,
                    statusTime: nowMillisecs,
                    [`statusLog.${nowMillisecs}`]: newStatus,
                    [`updateRetryUsers.${nowMillisecs}`]: updateRetryUser,
                },
            },
            { runValidators: true }
        ).exec();
        result = soId;
        await updateErrorFlag(context);
    } catch (exception) {
        context.log.error('updateStatusForRecovery error:', exception);
    }
    return result;
};

swimmyVLMApiLogSchema.statics = {
    createNew,
    createActiveOrder,
    createCancelOrder,
    get1RequestForPolling,
    pollingUpdateActiveOrder,
    pollingUpdateCancelOrder,
    pollingUpdateHenkouOrder,
    updateStatus,
    updateStatusAndRequestParams,
    updateJikkouchuFlg,
    findActiveOrderBySoId,
    searchSwimmyVLMLog,
    findAllByRequestOrderId,
    updateForRetryRequest,
    updateStatusForUI,
    findOneBySoIdSortByTransactionType,
    updateStatusForRecovery,
};

const SwimmyVLMApiLogModel = mongoose.connection
    .useDb(portalConfig.DB_NAME.SWIMMY_VLM_0035_SERVICE)
    .model('swimmy_vlm_api_log', swimmyVLMApiLogSchema, 'swimmy_vlm_api_log');

module.exports = SwimmyVLMApiLogModel;
