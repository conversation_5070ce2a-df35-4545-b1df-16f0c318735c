const dayjs = require('dayjs');
const AppConfig = require('../../config/appConfig');
const {
    SWIMMY_REQUEST_TYPES,
    ALADIN_KEKKA_TSUCHI_TYPE,
    SWIMMY_KOUTEI_IDS,
    SWIMMY_CONSTANTS,
    SWIMMY_APP_ATTRIBUTE_VALUE,
    SWIMMY_API_LOG_STATUS,
} = require('../../constants/swimmyTransactions');
const { ORDER_TYPE, SUCCESS_SHORI_KEKKA_CODES } = require('../../constants/aladinTransactions');
const { isNone } = require('../../helpers/baseHelper');

const SwimmySoModel = require('../swimmySo.model');
const SwimmyApiLogModel = require('./swimmyApiLog.model');
const SwimmyHelper = require('../../helpers/swimmyHelper');
const SwimmyService = require('../../services/swimmy.service');

const portalConfig = AppConfig.getPortalConfig();

/**
 * @param {object} context
 * @param {object} obj AladinApiLog
 */
const doAladinKekkaTsuchiProcess = async (context, obj) => {
    context.log('Start doAladinKekkaTsuchiProcess');
    if (isNone(obj)) {
        throw new Error('doAladinKekkaTsuchiProcess error: aladin api log is required');
    }
    const transactionId = obj.transactionId;
    const requestOrderId = obj.requestOrderId;

    const responseParam = obj.responseParam;
    const requestParam = obj.requestParam;

    let hasErr = false;

    let line = responseParam?.denwabango;
    if (isNone(line) || (typeof line === 'string' && line.length < 11)) {
        line = requestParam?.denwabango;
    }
    if (isNone(line)) {
        context.log.error(
            'doAladinKekkaTsuchiProcess: Not found param denwabango in response params aladin SO-ID:',
            requestOrderId,
            transactionId,
            responseParam
        );
        hasErr = true;
    }

    const sentAladinResult = responseParam?.syorikekkaKbn;
    if (isNone(sentAladinResult)) {
        context.log.error(
            'doAladinKekkaTsuchiProcess: Not found param syorikekkaKbn on aladin SO-ID:',
            requestOrderId,
            transactionId,
            responseParam
        );
        hasErr = true;
    }

    const soKind = getSoKind(obj.requestOrderType);
    if (isNone(soKind)) {
        context.log.error(`Sokind ${obj.requestOrderType} is not valid`);
        hasErr = true;
    }

    const aladinResult = SUCCESS_SHORI_KEKKA_CODES.includes(sentAladinResult) ? '00' : '01';
    const messageId = aladinResult === '01' ? sentAladinResult : null;

    // update status Swimmy実施中
    await SwimmySoModel.updateKouteiInfo(context, {
        so_id: requestOrderId,
        koutei: {
            kouteiId: SWIMMY_KOUTEI_IDS.SWIMMY_PROCESSING,
        },
        updatedUserId: portalConfig.API_USER_ID,
    });

    if (!hasErr) {
        const swimmyType = SWIMMY_REQUEST_TYPES.ALADIN_KEKKA_TSUCHI; // = FM_ALADIN_KEKKA_TSUCHI
        // TODO confirm timezone used
        const now = dayjs().format('YYYYMMDDHHmmss');
        const errList = !isNone(messageId) ? [{ errorCode: messageId, errorMessage: null }] : [];

        const swimmyApiLog = await SwimmyApiLogModel.findOneByRequestOrderIdAndSwimmyType(requestOrderId, swimmyType);

        if (!isNone(swimmyApiLog)) {
            // if swimmy api log already exists, update instead creating new
            // copied default values from `createSwimmyApiLog`
            const requestParam = SwimmyHelper.createRequestParamsForSwimmyApiLog(
                swimmyType,
                now,
                undefined,
                undefined,
                requestOrderId,
                soKind,
                line,
                aladinResult,
                errList,
                '',
                '',
                [
                    {
                        appAttributeCode: SWIMMY_CONSTANTS.APP_ATTRIBUTE_CODE_1,
                        appAttributeValue: SWIMMY_APP_ATTRIBUTE_VALUE[swimmyType],
                    },
                    {
                        appAttributeCode: SWIMMY_CONSTANTS.APP_ATTRIBUTE_CODE_2,
                        appAttributeValue: line,
                    },
                ],
                undefined,
                '',
                ''
            );
            context.log('doAladinKekkaTsuchiProcess update:', requestParam);
            await SwimmyApiLogModel.updateStatusAndRequestParamByRequestOrderIdAndSwimmyType(
                requestOrderId,
                swimmyType,
                SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_NOT_YET_SENT,
                requestParam,
                line
            );
        } else {
            const swimmyParams = {
                swimmyType,
                requestOrderId,
                transactionId,
                tenantId: obj.tenantId,
                daihyouBango: obj.daihyouBango,
                otherSystemSendDateTime: now,
                soKind,
                lineNo: line,
                resultCode: aladinResult,
                errMessageId: messageId,
            };
            context.log('doAladinKekkaTsuchiProcess create:', swimmyParams);
            await SwimmyService.registerSwimmyTransaction(context, swimmyParams);
        }
    }
};

/**
 *
 * @param {string} aladinType
 */
const getSoKind = (aladinType) => {
    switch (aladinType) {
        case ORDER_TYPE.SHINKI_MOUSHIKOMI_DAI_DAIHYOU:
        case ORDER_TYPE.SHINKI_MOUSHIKOMI_DAIHYOU:
        case ORDER_TYPE.SHINKI_MOUSHIKOMI_KO_KAISEN:
        case ORDER_TYPE.SHINKI_MOUSHIKOMI_KO_KAISEN_YUUSEN:
            return ALADIN_KEKKA_TSUCHI_TYPE.KAISEN_TSUIKA;
        case ORDER_TYPE.KAISEN_HAISHI_SOKUJI:
            return ALADIN_KEKKA_TSUCHI_TYPE.KAISEN_HAISHI;
        default:
            return null;
    }
};

module.exports = {
    doAladinKekkaTsuchiProcess,
};
