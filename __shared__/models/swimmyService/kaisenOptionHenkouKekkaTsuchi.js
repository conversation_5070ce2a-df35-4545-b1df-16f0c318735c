const dayjs = require('dayjs');
const { SUCCESS_SHORI_KEKKA_CODES } = require('../../constants/aladinTransactions');
const { KOUTEI_IDS } = require('../../constants/orderConstants');
const { SWIMMY_REQUEST_TYPES, SWIMMY_API_LOG_STATUS } = require('../../constants/swimmyTransactions');

const { findAladinErrorMsgByErrorCode } = require('../../helpers/aladinApiLogHelper');
const { isNone } = require('../../helpers/baseHelper');
const SwimmyHelper = require('../../helpers/swimmyHelper');

const SwimmyApiLogModel = require('./swimmyApiLog.model');
const SwimmyService = require('../../services/swimmy.service');

/**
 *
 * @param {object} context
 * @param {object} obj  AladinApiLog
 * @param {object} soObj MNPTennyuServiceOrder (full object with virtuals)
 * @param {boolean} [recoveryCorrectProcess]
 */
const doKaisenOptionHenkouKekkaTsuchiProcess = async (context, obj, soObj, recoveryCorrectProcess) => {
    context.log('Start doKaisenOptionHenkouKekkaTsuchiProcess');

    const responseParam = obj?.responseParam ?? {};
    const requestParam = obj?.requestParam ?? {};
    let hasErr = false;

    let lineNo = responseParam.denwabango;
    if (isNone(lineNo)) {
        lineNo = requestParam.denwabango;
    }
    if (isNone(lineNo)) {
        context.log.error(
            'doKaisenOptionHenkouKekkaTsuchiProcess: Not found param denwabango in response param aladin SO-ID:',
            obj.requestOrderId,
            obj.transactionId
        );
        hasErr = true;
    }

    const result = {
        /** @type {string|undefined} */
        code: undefined,
        /** @type {string|undefined} */
        messageId: undefined,
        /** @type {string|undefined} */
        message: undefined,
    };

    if (!isNone(recoveryCorrectProcess) && recoveryCorrectProcess) {
        // no need to see the syorikekkaKbn param which will not be updated
        const resCode = soObj.koutei === KOUTEI_IDS.ALADIN_KOUJI_NG ? '01' : '00';
        // no err code bcz update only ngReason="その他" in 工程補正 func
        const errMsgId = resCode === '01' ? '' : undefined;
        const errMsg = isNone(errMsgId) ? undefined : soObj.ngReason;

        result.code = resCode;
        result.messageId = errMsgId;
        result.message = errMsg;
    } else {
        const sentAladinResult = responseParam.syorikekkaKbn;
        if (isNone(sentAladinResult)) {
            context.log.error(
                'doKaisenOptionHenkouKekkaTsuchiProcess: Not found param syorikekkaKbn on aladin SO-ID:',
                obj.requestOrderId,
                obj.transactionId,
                responseParam
            );
            hasErr = true;
        }

        const aladinResult = SUCCESS_SHORI_KEKKA_CODES.includes(sentAladinResult) ? '00' : '01';
        const msgId = aladinResult === '01' ? sentAladinResult : undefined;
        const msg = isNone(msgId) ? undefined : findAladinErrorMsgByErrorCode(msgId) ?? 'その他';
        result.code = aladinResult;
        result.messageId = msgId;
        result.message = msg;
    }

    if (!hasErr) {
        // TODO confirm timezone
        const now = dayjs().format('YYYYMMDDHHmmss');
        const statusParam = SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_NOT_YET_SENT;
        const swimmyTypeParam = SWIMMY_REQUEST_TYPES.KAISEN_OPTION_HENKOU_KEKKA_TSUCHI;

        const swimmyApiLog = await SwimmyApiLogModel.findOneByRequestOrderId(obj.requestOrderId);

        if (!isNone(swimmyApiLog)) {
            context.log(`doKaisenOptionHenkouKekkaTsuchiProcess: already sent notice to Swimmy: ${obj.requestOrderId}`);
            // update
            const errorList = isNone(result.messageId)
                ? []
                : [{ errorCode: result.messageId, errorMessage: result.message }];
            const requestParam = SwimmyHelper.createRequestParamsForSwimmyApiLog(
                swimmyTypeParam,
                now,
                undefined,
                undefined,
                obj.requestOrderId,
                undefined,
                lineNo ?? '',
                result.code ?? '',
                errorList,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined
            );
            await SwimmyApiLogModel.updateStatusAndRequestParam(obj.requestOrderId, statusParam, requestParam, lineNo);
        } else {
            // create
            await SwimmyService.registerSwimmyTransaction(context, {
                swimmyType: swimmyTypeParam,
                requestOrderId: obj.requestOrderId,
                transactionId: obj.transactionId,
                tenantId: obj.tenantId,
                otherSystemSendDateTime: now,
                lineNo: lineNo ?? '',
                resultCode: result.code,
                errMessageId: result.messageId,
                errMessage: result.message,
                responseParam: 'responseParams',
            });
        }
    }
};

module.exports = {
    doKaisenOptionHenkouKekkaTsuchiProcess,
};
