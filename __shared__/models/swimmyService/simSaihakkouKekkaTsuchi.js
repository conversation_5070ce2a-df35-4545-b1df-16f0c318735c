const dayjs = require('dayjs');
const { KOUTEI_IDS } = require('../../constants/orderConstants');
const { SWIMMY_REQUEST_TYPES, SWIMMY_API_LOG_STATUS } = require('../../constants/swimmyTransactions');
const { SUCCESS_SHORI_KEKKA_CODES } = require('../../constants/aladinTransactions');
const { SWIMMY_NOTIFY_ERROR_CODE, SWIMMY_NOTIFY_ERROR_MESSAGE } = require('../../constants/swimmyErrorMsgList');
const { SIM_ORDER_TYPES } = require('../../constants/simConstants');

const { findAladinErrorMsgByErrorCode } = require('../../helpers/aladinApiLogHelper');
const { isNone } = require('../../helpers/baseHelper');
const SwimmyHelper = require('../../helpers/swimmyHelper');

const SwimmyApiLogModel = require('./swimmyApiLog.model');
const SwimmyService = require('../../services/swimmy.service');

/**
 * @param {object} context
 * @param {object} obj AladinApiLog
 * @param {object} soObj MNPTennyuServiceOrder (full object with virtuals)
 * @param {boolean} [recoveryCorrectProcess]
 */
const doSIMSaihakkouKekkaTsuChiProcess = async (context, obj, soObj, recoveryCorrectProcess) => {
    context.log('Start doSIMSaihakkouKekkaTsuChiProcess');
    const priority = SwimmyHelper.getSwimmyApiPriority(context, SWIMMY_REQUEST_TYPES.SIM_SAIHAKKOU_KEKKA_TSUCHI);

    let requestOrderId = obj?.requestOrderId;

    const hasAladinLog = !isNone(obj);
    if (!hasAladinLog) {
        context.log("doSIMSaihakkouKekkaTsuChiProcess doesn't have aladin log", soObj.so_id);
        requestOrderId = soObj.so_id;
    }

    const responseParam = obj?.responseParam ?? {};
    const requestParam = obj?.requestParam ?? {};
    let hasErr = false;

    let lineNo = responseParam.denwabango;
    if (isNone(lineNo)) {
        lineNo = requestParam.denwabango;
    }
    if (isNone(lineNo) && !hasAladinLog) {
        lineNo = soObj.kaisenNo;
    }
    if (isNone(lineNo)) {
        context.log.error(
            `doSIMSaihakkouKekkaTsuChiProcess: Not found param denwabango in response param aladin SO-ID: ${requestOrderId}`
        );
        hasErr = true;
    }

    // has simNo when 12:ALADIN工事OK response
    if (soObj.koutei !== KOUTEI_IDS.ALADIN_KOUJI_NG && isNone(soObj.simInfo) && isNone(recoveryCorrectProcess)) {
        context.log.error(`doSIMSaihakkouKekkaTsuChiProcess: Not found param simNo in aladin SO-ID: ${requestOrderId}`);
        hasErr = true;
    }

    const shippingNumber = !isNone(soObj.denpyoNo) ? soObj.denpyoNo : null;
    context.log('doSIMSaihakkouKekkaTsuChiProcess shippingNumber:', shippingNumber, soObj.so_id);

    const result = {
        /** @type {string|undefined} */
        code: undefined,
        /** @type {string|undefined} */
        messageId: undefined,
        /** @type {string|undefined} */
        message: undefined,
    };

    if (!isNone(recoveryCorrectProcess) && recoveryCorrectProcess) {
        // no need to see the syorikekkaKbn param which will not be updated
        const resCode = [KOUTEI_IDS.ALADIN_KOUJI_NG, KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_NG].includes(soObj.koutei)
            ? '01'
            : '00';
        const errMsgId = resCode === '01' ? '' : undefined; // no err code bcz update only ngReason="その他" in 工程補正 func
        const errMsg = isNone(errMsgId) ? undefined : soObj.ngReason;

        result.code = resCode;
        result.messageId = errMsgId;
        result.message = errMsg;
    } else {
        const sentAladinResult = responseParam.syorikekkaKbn;
        if (isNone(sentAladinResult)) {
            context.log.error(
                `doSIMSaihakkouKekkaTsuChiProcess: Not found param syorikekkaKbn on aladin SO-ID: ${requestOrderId} ${responseParam}`
            );
            hasErr = true;
        }

        const aladinResult = SUCCESS_SHORI_KEKKA_CODES.includes(sentAladinResult) ? '00' : '01';
        const msgId = aladinResult === '01' ? sentAladinResult : undefined;
        const msg = isNone(msgId) ? undefined : findAladinErrorMsgByErrorCode(msgId) ?? 'その他';
        result.code = aladinResult;
        result.messageId = msgId;
        result.message = msg;
    }

    context.log('doSIMSaihakkouKekkaTsuChiProcess: error code/message:', result);
    // for 焼付SIM選択NG: use hard-coded error code
    if (soObj.koutei === KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_NG && soObj.hankuro === SIM_ORDER_TYPES.OTA) {
        result.code = '01';
        result.messageId = SWIMMY_NOTIFY_ERROR_CODE.YAKITSUKE_SIM_NG;
        result.message = SWIMMY_NOTIFY_ERROR_MESSAGE[SWIMMY_NOTIFY_ERROR_CODE.YAKITSUKE_SIM_NG];
        context.log('doSIMSaihakkouKekkaTsuChiProcess: SIM_YAKITSUKE_SENTAKU_NG error code/message:', result);
    }

    if (!hasErr) {
        // TODO confirm timezone
        const now = dayjs().format('YYYYMMDDHHmmss');
        const statusParam = SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_NOT_YET_SENT;
        const swimmyTypeParam = SWIMMY_REQUEST_TYPES.SIM_SAIHAKKOU_KEKKA_TSUCHI;

        const swimmyApiLog = await SwimmyApiLogModel.findOneByRequestOrderId(requestOrderId);
        if (!isNone(swimmyApiLog)) {
            context.log(`doSIMSaihakkouKekkaTsuChiProcess: already sent notice to Swimmy: ${requestOrderId}`);
            // update
            const simNo = isNone(soObj.simInfo) ? undefined : soObj?.simInfo;
            const errorList = isNone(result.messageId)
                ? []
                : [{ errorCode: result.messageId, errorMessage: result.message }];
            const requestParam = SwimmyHelper.createRequestParamsForSwimmyApiLog(
                swimmyTypeParam,
                now,
                undefined,
                undefined,
                requestOrderId,
                undefined,
                lineNo ?? '',
                result.code,
                errorList,
                undefined,
                undefined,
                undefined,
                undefined,
                simNo,
                undefined,
                undefined,
                shippingNumber,
                undefined,
                undefined,
                undefined
            );
            await SwimmyApiLogModel.updateStatusAndRequestParam(requestOrderId, statusParam, requestParam, lineNo);
        } else {
            // create
            await SwimmyService.registerSwimmyTransaction(context, {
                status: statusParam,
                swimmyType: swimmyTypeParam,
                priority,
                requestOrderId: requestOrderId,
                transactionId: obj?.transactionId ?? 'has-no-transactionId', // when aladinApiLog not created yet: use fake transactionId
                tenantId: obj?.tenantId ?? soObj.tenantId,
                otherSystemSendDateTime: now,
                lineNo: lineNo ?? '',
                simNo: soObj.simInfo,
                shippingNumber,
                resultCode: result.code,
                errMessageId: result.messageId,
                errMessage: result.message,
                responseParam: 'responseParams',
            });
        }
    }
    context.log('End doSIMSaihakkouKekkaTsuChiProcess ');
};

module.exports = {
    doSIMSaihakkouKekkaTsuChiProcess,
};
