// TODO move this file to different location?
// file src: NTTCMVNOKurokaKanryouTsuChiAction
const dayjs = require('dayjs');
const { ORDER_TYPE, SUCCESS_SHORI_KEKKA_CODES } = require('../../constants/aladinTransactions');
const { SWIMMY_REQUEST_TYPES, SWIMMY_API_LOG_STATUS } = require('../../constants/swimmyTransactions');

const { isNone } = require('../../helpers/baseHelper');
const SwimmyHelper = require('../../helpers/swimmyHelper');

const SwimmyApiLogModel = require('./swimmyApiLog.model');
const SwimmyService = require('../../services/swimmy.service');
const { findAladinErrorMsgByErrorCode } = require('../../helpers/aladinApiLogHelper');

/**
 *
 * @param {object} context
 * @param {object} [obj] - <PERSON>adin<PERSON><PERSON><PERSON><PERSON> document (null)
 * @param {object} blackingObj
 * @param {string} swimmyErrId
 * @param {string} swimmyErrMsg
 */
const doKurokaKanryouTsuchiProcess = async (context, obj, blackingObj, swimmyErrId, swimmyErrMsg) => {
    // TODO [WIP] need to check MVNO repo
    const swimmyTypeParam = SWIMMY_REQUEST_TYPES.KUROKA_KANRYOU_TSUCHI;

    let hasErr = false;
    let lineNo, soKind, transactionId, aladinResult, swimmyResult, messageId, message;

    if (!isNone(obj)) {
        if (obj.responseParam) {
            lineNo = obj.responseParam.denwabango;
        }
        if (!lineNo || String(lineNo).length < 11) {
            if (obj.requestParam) {
                lineNo = obj.requestParam.denwabango;
            } else {
                lineNo = null;
            }
        }

        let sentAladinResult;
        if (obj.responseParam) {
            sentAladinResult = obj.responseParam.syorikekkaKbn;
        }
        if (!sentAladinResult) {
            context.log.error(
                `doKurokaKanryouTsuchiProcess: Not found param syorikekkaKbn on aladin SO-ID:`,
                transactionId
            );
            hasErr = true;
        }

        soKind = getSoKind(obj.requestOrderType);
        transactionId = obj.transactionId;
        aladinResult = sentAladinResult && SUCCESS_SHORI_KEKKA_CODES.includes(sentAladinResult) ? '00' : '01';
        messageId = aladinResult === '01' ? sentAladinResult : null;

        message = isNone(messageId) ? null : findAladinErrorMsgByErrorCode(messageId) || 'その他';
    } else {
        // case fullMVNOKuroka
        lineNo = blackingObj.kaisenNo;
        soKind = '半黒 黒化'; //full mvno has only '半黒'
        swimmyResult = isNone(swimmyErrId) ? '00' : '01';
        messageId = swimmyResult === '01' ? swimmyErrId : null;
        message = isNone(messageId) ? null : swimmyErrMsg;
    }

    if (!lineNo) {
        context.log.error(`doKurokaKanryouTsuchiProcess: denwabango not found (${blackingObj.so_id})`);
        hasErr = true;
    }
    if (!soKind) {
        context.log.error(`doKurokaKanryouTsuchiProcess: soKind invalid (${blackingObj.so_id})`);
        hasErr = true;
    }
    if (!blackingObj.simInfo) {
        context.log.error(`doKurokaKanryouTsuchiProcess: simNo not found (${blackingObj.so_id})`);
        hasErr = true;
    }
    if (!blackingObj.notifyHost) {
        context.log.error(`doKurokaKanryouTsuchiProcess: notifyHost not found (${blackingObj.so_id})`);
        hasErr = true;
    }

    if (!hasErr) {
        // TODO confirm timezone used
        const now = dayjs().format('YYYYMMDDHHmmss');
        const statusParam = SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_NOT_YET_SENT;

        //As swimmyType:012 & 092 can be the same requestOrderIds, find if already have the requestOrderId with swimmyType(092: 黒化済通知)
        const swimmyApiLog = await SwimmyApiLogModel.findOneByRequestOrderIdAndSwimmyType(
            blackingObj.so_id,
            swimmyTypeParam
        );
        if (!isNone(swimmyApiLog)) {
            context.log(`doKurokaKanryouTsuchiProcess: already sent notice to Swimmy (${blackingObj.so_id})`);
            // update
            const resCode = (!isNone(aladinResult) ? aladinResult : swimmyResult) ?? '';
            const errList = !isNone(messageId) ? [{ errorCode: messageId, errorMessage: message }] : [];

            const requestParam = SwimmyHelper.createRequestParamsForSwimmyApiLog(
                swimmyTypeParam,
                now,
                undefined,
                undefined,
                blackingObj.so_id,
                soKind ?? '',
                lineNo ?? '',
                resCode,
                errList,
                undefined,
                undefined,
                undefined,
                undefined,
                blackingObj.simInfo ?? ''
            );
            context.log('doKurokaKanryouTsuchiProcess update:', requestParam);
            await SwimmyApiLogModel.updateStatusAndRequestParamByRequestOrderIdAndSwimmyType(
                blackingObj.so_id,
                swimmyTypeParam,
                statusParam,
                requestParam,
                lineNo
            );
        } else {
            // create
            const swimmyParams = {
                swimmyType: swimmyTypeParam,
                requestOrderId: blackingObj.so_id,
                transactionId,
                tenantId: blackingObj.tenantId,
                otherSystemSendDateTime: now,
                soKind,
                lineNo,
                simNo: blackingObj.simInfo,
                notifyHost: blackingObj.notifyHost,
                resultCode: aladinResult,
                swimmyResultCode: swimmyResult,
                errMessageId: messageId,
                errMessage: messageId ? message : null,
            };
            context.log('doKurokaKanryouTsuchiProcess create:', swimmyParams);
            await SwimmyService.registerSwimmyTransaction(context, swimmyParams);
        }
    }
};

/**
 *
 * @param {string} aladinType
 */
const getSoKind = (aladinType) => {
    let result = null;
    switch (aladinType) {
        case ORDER_TYPE.SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU: // 12
        case ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU: // 38
        case ORDER_TYPE.IKKATSU_SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU: // 62
        case ORDER_TYPE.IKKATSU_KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU: // 64
            result = '半黒 黒化';
            break;
        case ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU: // 22
        case ORDER_TYPE.IKKATSU_MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU: // 63
            result = '半黒MNP黒化';
            break;
    }
    return result;
};

module.exports = {
    doKurokaKanryouTsuchiProcess,
};
