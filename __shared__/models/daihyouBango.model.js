const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { escapeRegex } = require('../utils/stringUtils');
const { CONTRACT_TYPES } = require('../constants/simConstants');
const Schema = mongoose.Schema;

/** @typedef {mongoose.HydratedDocumentFromSchema<daihyouBangoSchema>} DaihyouBangoDocument */

const daihyouBangoSchema = new Schema({
    daihyouId: { type: String, trim: true, required: true },
    daiDaihyouBango: { type: String, trim: true, required: true },
    daihyouBango: { type: String, trim: true, required: true },
    tenantId: { type: String, trim: true, default: '' },
    ryoukinPlan: { type: String, trim: true, required: true },

    quotaQuantity: { type: Number, default: 0 },
    kaisenOptions: { type: [String] },
    tenantDisplayName: { type: String, trim: true, required: true },
    contractType: { type: String, trim: true, required: true },
    ansyobango: { type: String, trim: true, required: true },
    sousaservice: { type: String, trim: true },

    //! ↓ correct name is WWtukehenhaiFLG, but old DB got it wrong
    //! Aladin API still use WWtukehenhaiFLG
    WWtukekenhaiFLG: { type: String, trim: true, default: '' },
    WWriyouteisimeyasugaku: { type: String, trim: true, default: '' },
    WWdaisankokuhassinkisei: { type: String, trim: true },
    WCtukehenhaiFLG: { type: String, trim: true, default: '' },
    WCriyouteisimeyasugaku: { type: String, trim: true, default: '' },
    WCtuwateisi: { type: String, trim: true, required: true },

    lastUpdateUserId: { type: String, trim: true, required: true },
    updatedAt: { type: Number, required: true, set: (d) => dayjs(d).unix() },
    createdDateTime: { type: Number, set: (d) => dayjs(d).unix() },
    activateDate: { type: Number, set: (d) => dayjs(d).unix() },

    forAladinAPI: { type: Boolean, required: true, default: false },
    forMNP: { type: Boolean, default: false },
    statusOfUse: { type: Boolean, default: false },
    mvnoKakinJyoho: { type: Boolean, default: false },
    tensoDenwa: { type: Boolean, default: false },
    worldWing: { type: Boolean, default: false },
    kokusaiTyakushinTenso: { type: Boolean, default: false },
    worldCall: { type: Boolean, default: false },
    rusubanDenwa: { type: Boolean, default: false },
    catchPhone: { type: Boolean, default: false },

    wwTeishiMeyasu: { type: String, trim: true, default: '' },
    wcTeishiMeyasu: { type: String, trim: true, default: '' },
    parentNo: { type: String, trim: true, required: true },
    memo: { type: String, trim: true, default: '' },
});

daihyouBangoSchema.virtual('activeDate').get(function () {
    // in case field `activeDate` is used instead of activateDate
    return this.activateDate;
});

// Model Constant
let DaihyouBangoConstants = {
    FIVE_G_CONTRACT_CODE: 'PCS04',

    FIXED_QUOTA_QUANTITY: 14999,

    // 料金プランID(ryoukinPlan)
    FOMA_TOKUTEI_SETSUZOKU_PLAN: 'A1089',
    FOMA_YUPIKITASU_PLAN: 'AC001',
    XI_TOKUSEI_SETSUZOKU_PLAN: 'AJ010',
    XI_YUPIKITASU: 'AJ055',
    TYPE_XI: 'AJ034',
    //STEP 14.0 FULL MVNO
    HLR_HSS_XI_SPECIFIC_CONNECTION: 'AJ123',
    HLR_HSS_XI_UBIQUITOUSU: 'AJ124',
    //STEP 18: 5G(NSA)
    FIVE_G_TOKUTEI_SETSUZOKU: 'AS020', // TODO: 使わない？
    OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN: 'AS021',
    OROSHI_TYPE_FIVE_G: 'AS022',

    // MVNOのline_option_type (daihyouBangoで使う値のmapingは下にある)
    WORLD_WING: 1,
    RUSUBAN: 2,
    CATCH_HON: 3,
    WORLD_CALL: 4,
    TENSOU_DENWA: 5,
    KOKUSAI_DENWA: 6,
    MVNO_KAKIN_JOHOU: 7,

    MVNO_WW_TO_DOCOMO_VALUES: {
        AO104: '100000',
        AO105: '50000',
        AO107: '200000',
        AO108: '300000',
        AO109: '400000',
        AO110: '500000',
        AO111: '600000',
        AO112: '700000',
        AO113: '800000',
        AO114: '900000',
        AO115: '1000000',
        AO147: '99999999',
    },
    MVNO_WC_TO_DOCOMO_VALUES: {
        AO122: '5000',
        AO123: '10000',
        AO124: '20000',
        AO125: '30000',
        AO126: '40000',
        AO127: '50000',
        AO128: '60000',
        AO129: '70000',
        AO130: '80000',
        AO131: '90000',
        AO132: '100000',
        AO133: '150000',
        AO134: '200000',
        AO135: '250000',
        AO136: '300000',
        AO137: '400000',
        AO138: '500000',
        AO139: '600000',
        AO140: '800000',
        AO141: '1000000',
        AO148: '99999999',
    },
};

//STEP 8.0 020対応
//STEP 11.0 (020)のパラメタを修正
//STEP 18 5G
DaihyouBangoConstants.RYOKIN_PLAN_NAMES = {
    [DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN]: '卸FOMA特定接続プラン',
    [DaihyouBangoConstants.FOMA_YUPIKITASU_PLAN]: '卸FOMAユビキタスプラン',
    [DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN]: '卸Xi特定接続プラン',
    [DaihyouBangoConstants.XI_YUPIKITASU]: '卸Xiユビキタス',
    [DaihyouBangoConstants.TYPE_XI]: 'タイプXi',
    [DaihyouBangoConstants.FIVE_G_TOKUTEI_SETSUZOKU]: '5G特定接続',
    [DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN]: '卸5G特定接続プラン',
    [DaihyouBangoConstants.OROSHI_TYPE_FIVE_G]: '卸タイプ5G',
};

DaihyouBangoConstants.MVNO_LINE_OPTION_TYPE_TITLES = {
    [DaihyouBangoConstants.WORLD_WING]: '国際ローミング利用限度額（WORLD WING）',
    [DaihyouBangoConstants.RUSUBAN]: '留守番電話サービス',
    [DaihyouBangoConstants.CATCH_HON]: 'キャッチホン',
    [DaihyouBangoConstants.WORLD_CALL]: '国際電話（WORLD CALL）',
    [DaihyouBangoConstants.TENSOU_DENWA]: '転送でんわサービス',
    [DaihyouBangoConstants.KOKUSAI_DENWA]: '国際転送サービス',
    [DaihyouBangoConstants.MVNO_KAKIN_JOHOU]: 'MVNO課金情報',
};

DaihyouBangoConstants.MVNO_LINE_OPTION_TO_DOMOCO_VALUE = {
    [DaihyouBangoConstants.WORLD_WING]: 'C0027',
    [DaihyouBangoConstants.RUSUBAN]: 'C0020',
    [DaihyouBangoConstants.CATCH_HON]: 'C0005',
    [DaihyouBangoConstants.WORLD_CALL]: 'C0025',
    [DaihyouBangoConstants.TENSOU_DENWA]: 'C0013',
    [DaihyouBangoConstants.KOKUSAI_DENWA]: 'C0007',
    [DaihyouBangoConstants.MVNO_KAKIN_JOHOU]: 'C0324',
};

/**
 * find daihyouBangoInfo by tenantId and ryoukinPlan
 * @param {string} tenantId
 * @param {string} ryoukinPlan
 * @returns {daihyouBangoSchema|null} daihyouBango object or null
 */
const findByTenantAndryoukinPlan = async (tenantId, ryoukinPlan) => {
    if (!tenantId) throw new Error('tenantId is required');
    if (!ryoukinPlan) throw new Error('ryoukinPlan is required');

    const conditions = {
        tenantId,
        ryoukinPlan,
        forAladinAPI: true,
        forMNP: true,
        quotaQuantity: { $gt: 0 },
    };
    return await DaihyouBangoModel.findOne(conditions).exec();
};

/**
 * find daihyouBangoInfo by tenantId and ryoukinPlan (Shinki)
 * @param {string} tenantId
 * @param {string} ryoukinPlan
 * @param {boolean} [mvnoKakinJyoho]
 * @returns {daihyouBangoSchema|null} daihyouBango object or null
 */
const findByTenantAndryoukinPlanForShinki = async (tenantId, ryoukinPlan, mvnoKakinJyoho) => {
    if (!tenantId) throw new Error('tenantId is required');
    if (!ryoukinPlan) throw new Error('ryoukinPlan is required');

    let conditions = {
        tenantId,
        ryoukinPlan,
        forAladinAPI: true,
        forMNP: false,
        quotaQuantity: { $gt: 0 },
    };
    if (typeof mvnoKakinJyoho === 'boolean') {
        conditions.mvnoKakinJyoho = mvnoKakinJyoho;
    }
    return await DaihyouBangoModel.findOne(conditions).exec();
};

/**
 * update corespond quotaQuantity to new value
 * @param {string} daihyouId
 * @param {number} quotaQuantity
 * @returns {daihyouBangoSchema|null} daihyouBango object or null
 */
const updateQuotaQuantity = async (daihyouId, quotaQuantity) => {
    if (!daihyouId) throw new Error('daihyouId is required');
    if (typeof quotaQuantity !== 'number' || isNaN(quotaQuantity)) throw new Error('quotaQuantity is required');

    return await DaihyouBangoModel.findOneAndUpdate(
        { daihyouId },
        { quotaQuantity },
        { runValidators: true, context: 'query', new: true }
    ).exec();
};

/**
 * decrease corespond quotaQuantity by 1
 * @param {string} daihyouId
 * @returns {daihyouBangoSchema|null} daihyouBango object or null
 */
const decreaseQuotaQuantity = async (daihyouId) => {
    if (!daihyouId) throw new Error('daihyouId is required');

    return await DaihyouBangoModel.findOneAndUpdate(
        { daihyouId },
        { $inc: { quotaQuantity: -1 } },
        { runValidators: true, context: 'query', new: true }
    ).exec();
};

/**
 * increase/decrease quota quantity
 * @param {object} query
 * @param {string|undefined} query.daihyouId
 * @param {string|undefined} query.daihyouBango
 * @param {number} incrementValue
 * @returns
 */
const incrementQuotaQuantity = async (query, incrementValue) => {
    if (!query || (!query.daihyouId && !query.daihyouBango)) throw new Error('daihyouId or daihyoBango is required');
    if (typeof incrementValue !== 'number' || incrementValue === 0)
        throw new Error('increment value must be non-zero number');

    const q = {};
    if (query.daihyouId) {
        q.daihyouId = query.daihyouId;
    } else {
        q.daihyouBango = query.daihyouBango;
    }
    return await DaihyouBangoModel.findOneAndUpdate(
        q,
        { $inc: { quotaQuantity: incrementValue } },
        { runValidators: true, context: 'query', new: true }
    ).exec();
};

/**
 * get ryoukin plans
 * @returns {DaihyouBangoConstants.RYOKIN_PLAN_NAMES} ryoukin plan names
 */
const getRyoukinPlans = async (context) => {
    let ryoukinPlanList = [];
    context.log(`ryoukinPlanNames: ${Object.entries(DaihyouBangoConstants.RYOKIN_PLAN_NAMES)}`);

    Object.entries(DaihyouBangoConstants.RYOKIN_PLAN_NAMES).forEach(([k, v]) => {
        ryoukinPlanList.push({
            ryoukin_plan_id: k,
            ryoukin_plan_name: v,
        });
    });
    return ryoukinPlanList;
};

/**
 *
 * @param {object} context
 * @param {Array<string>} daihyouBangoList
 */
const getDaihyouBangoMap = async (context, daihyouBangoList) => {
    const result = await DaihyouBangoModel.find({ daihyouBango: { $in: daihyouBangoList } }).exec();
    // return _.groupBy(result, _.property('daihyouBango'))
    return (result ?? []).reduce((acc, cur) => {
        acc[cur.daihyouBango] = cur;
        return acc;
    }, {});
};

/**
 *
 * @param {object} context
 * @param {string} daihyouBango
 */
const findByDaihyouBango = async (context, daihyouBango) => {
    return await DaihyouBangoModel.findOne({ daihyouBango }).exec();
};

/**
 * 代表番号管理 → 検索
 * @param {object} context
 * @param {Array<string>} tenantIds
 * @param {Array<string>} ryoukinPlanIds
 * @param {string} searchString
 * @param {number} [skip]
 * @param {number} [limit]
 * @param {boolean} isSortOrderAsc
 * @param {string} [sortBy]
 * @param {boolean} [isForShinkiOpt]
 * @param {boolean} [mvnoKakinJyoho]
 */
const searchExtended = async (
    context,
    tenantIds,
    ryoukinPlanIds,
    searchString,
    skip,
    limit,
    isSortOrderAsc,
    sortBy,
    isForShinkiOpt,
    mvnoKakinJyoho
) => {
    const query = {};
    if (Array.isArray(tenantIds) && tenantIds.length > 0) {
        query.tenantId = { $in: tenantIds };
    }
    if (Array.isArray(ryoukinPlanIds) && ryoukinPlanIds.length > 0) {
        query.ryoukinPlan = { $in: ryoukinPlanIds };
    }
    if (typeof searchString === 'string' && searchString.length > 0) {
        query.daihyouBango = { $regex: `.*${escapeRegex(searchString)}.*`, $options: 'i' };
    }
    const sortOrder = isSortOrderAsc ? 1 : -1;

    const sortMap = {
        status_of_use: 'statusOfUse',
        daihyou_bango: 'daihyouBango',
        dai_daihyou_bango: 'daiDaihyouBango',
        tenant_id: 'tenantId',
        tenant_display_name: 'tenantDisplayName',
        ryoukin_plan_id: 'ryoukinPlan',
        ryoukin_plan_name: 'ryoukinPlan',
        for_mnp: 'forMNP',
        for_aladin_api: 'forAladinAPI',
        mvno_kakin_jyoho: 'mvnoKakinJyoho',
        quota_quantity: 'quotaQuantity',
        tenso_denwa: 'tensoDenwa',
        kokusai_tyakushin_tenso: 'kokusaiTyakushinTenso',
        world_wing: 'worldWing',
        ww_teishi_meyasu: 'wwTeishiMeyasu',
        world_call: 'worldCall',
        wc_teishi_meyasu: 'wcTeishiMeyasu',
        rusuban_denwa: 'rusubanDenwa',
        catch_phone: 'catchPhone',
        ansyobango: 'ansyobango',
        activate_date: 'activateDate',
        parent_no: 'parentNo',
        memo: 'memo',
    };
    const sortByField = sortMap[sortBy] ?? '_id';
    if (isForShinkiOpt) {
        // defined and true
        query.forAladinAPI = true;
        query.forMNP = false;
    }
    if (typeof mvnoKakinJyoho === 'boolean') {
        query.mvnoKakinJyoho = mvnoKakinJyoho;
    }
    let doc = {};
    doc.count = await DaihyouBangoModel.find(query).count();

    const skipOk = typeof skip === 'number' && !isNaN(skip);
    const limitOk = typeof limit === 'number' && !isNaN(limit);

    const sortQuery = { [sortByField]: sortOrder };

    doc.cursor = DaihyouBangoModel.find(query).sort(sortQuery)
    if (skipOk) {
        doc.cursor = doc.cursor.skip(skip)
    }
    if (limitOk) {
        doc.cursor = doc.cursor.limit(limit)
    }
    doc.cursor = await doc.cursor
    return doc;
};

//return [isVoice, isSms]: !isVoice && !isSms -> isData
const getSimType = (ryoukinPlan) => {
    switch (ryoukinPlan) {
        case DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN:
        case DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN:
            return [false, false];
        case DaihyouBangoConstants.FOMA_YUPIKITASU_PLAN:
        case DaihyouBangoConstants.XI_YUPIKITASU:
            return [false, true];
        case DaihyouBangoConstants.TYPE_XI:
            return [true, true];
        case DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN:
            return [false, false];
        case DaihyouBangoConstants.OROSHI_TYPE_FIVE_G:
            return [true, true];
        default:
            return null;
    }
};

const getSimContractType = (ryoukinPlan) => {
    const simType = getSimType(ryoukinPlan);
    if (!simType) {
        return null;
    }

    const [isVoice, isSms] = simType;
    if (isVoice) {
        return CONTRACT_TYPES.VOICE;
    } else if (isSms) {
        return CONTRACT_TYPES.SMS;
    } else {
        return CONTRACT_TYPES.DATA;
    }
};

/**
 *
 * @param {object} context
 * @param {string} daihyouId
 * @returns {Promise<object|null>} daihyouBangoSchema | null
 */
const findByDaihyouId = async (context, daihyouId) => {
    context.log('DaihyouBangoModel findByDaihyouId START', daihyouId);
    if (!daihyouId) throw new Error('daihyouId is required');

    return await DaihyouBangoModel.findOne({ daihyouId }).exec();
};

//statics
daihyouBangoSchema.statics = {
    findByTenantAndryoukinPlan,
    findByTenantAndryoukinPlanForShinki,
    updateQuotaQuantity,
    decreaseQuotaQuantity,
    incrementQuotaQuantity,
    getRyoukinPlans,
    getDaihyouBangoMap,
    findByDaihyouBango,
    searchExtended,
    getSimContractType,
    findByDaihyouId,
};

const DaihyouBangoModel = mongoose.model('daihyou_bango', daihyouBangoSchema, 'daihyou_bango');

const DaihyouBangoModule = (module.exports = DaihyouBangoModel);
DaihyouBangoModule.DaihyouBangoConstants = DaihyouBangoConstants;
