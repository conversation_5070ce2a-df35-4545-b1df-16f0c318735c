const dayjs = require('dayjs');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// DB
const CustomerInfoPgModel = require('../pgModels/customerInfo');
const CustomerInfoModel = require('./customerInfo.model');
const ServiceOrderCountModel = require('./serviceOrderCount.model');
const MNPTennyuSoModel = require('./mnpTennyuSo.model');

// utils
const { createPrefixTempoFind } = require('../utils/stringUtils');

// Helpers
const { removeNullValue, createAddressNo, isNone } = require('../helpers/baseHelper');
const { SO_KIND_TYPE, KOUTEI_IDS } = require('../constants/orderConstants');
const { MVNO_SERVICES } = require('../constants/mvnoServicesConstants');

const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

const mnpNewParentSoSchema = new Schema({
    p_so_id: { type: String, trim: true, unique: true },

    createUserId: { type: String, trim: true, required: true },
    uketsukeNo: { type: String, trim: true },
    uketsukeDateTime: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },

    tenantId: { type: String, trim: true, required: true },
    tempoId: { type: String, trim: true, required: true },
    planId: { type: Number },

    lastKoutei: {
        kouteiId: { type: Number, required: true },
        timestamp: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
        userId: { type: String, trim: true },
        ngReason: { type: String, trim: true },
    },
    kouteis: [
        {
            _id: false,
            kouteiId: { type: Number, required: true },
            timestamp: { type: Number },
            userId: { type: String, trim: true },
            //===Begin of 4.0====
            //NG理由
            ngReason: { type: String, trim: true },
        },
    ],

    N_no: { type: String, trim: true },
    hankuro: { type: String, trim: true },
    simType: { type: String, trim: true },
    contractType: { type: String, trim: true },
    quantity: { type: Number },
    desiredDeliveryDate: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    ngReason: { type: String },
    soKind: { type: String, trim: true, required: true },
    deliveryAddress: { type: String, trim: true },

    //STEP7.0で個配申込受付番号と伝票番号を追加
    addressNo: { type: String, trim: true },
    defaultAddressNo: { type: String, trim: true },
    denpyoNo: { type: String, trim: true },
    deliveryFlag: { type: String, enum: ['0', '1'] },
    postalCode: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    shippingAddress1: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    shippingAddress2: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    shippingAddress3: { type: String, trim: true },
    shippingAddress4: { type: String, trim: true },
    shippingAddress5: { type: String, trim: true },
    shippingAddress6: { type: String, trim: true },
    shippingAddress7: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    shippingAddress8: { type: String, trim: true },
    shippingAddress9: { type: String, trim: true },
    contactPhoneNumber: { type: String, trim: true, required: () => '1' === this.deliveryFlag },
    deliveryPattern: { type: String, trim: true },
    exception: { type: String, trim: true },
    //STEP14 isFM
    isFM: { type: Boolean },
    // STEP 16.0 0035denwa
    voicePlanId: { type: String, trim: true },

    // TODO: we might need to rename this field
    // [MONGOOSE] Warning: `isNew` is a reserved schema pathname and may break some functionality.
    isNew: { type: Boolean, default: false },
});

mnpNewParentSoSchema.virtual('koutei').get(function () {
    if (this.lastKoutei) {
        return this.lastKoutei.kouteiId;
    } else {
        return KOUTEI_IDS.UKETSUKE_CHECK_NG;
    }
});

const MNPNewParentSoConstants = {
    //APIの場合にUserIdのフィールドに記入するためのId
    API_ACTION_ID: '-1',
};

/**
 * 5.0で配送先CSVの申し込み番号のフォーマットを定義する
 * ex: NWC-1-000001
 * @param {string} orderType
 * @param {string} tenantId
 * @param {string} nNumber
 * @return {string}          - UketsukeNo
 */
const createUketsukeNo = async (orderType, tenantId, nNumber) => {
    let nextSeq = 0;
    // 万が一、連番取得に失敗したら正常な連番が取れるまで実施する
    do {
        nextSeq = await ServiceOrderCountModel.getNextSeq(orderType, tenantId, nNumber);
    } while (nextSeq === 0);
    const sixDigitsNumber = nextSeq.toLocaleString('en-US', { minimumIntegerDigits: 6, useGrouping: false });
    return tenantId.substring(0, 3) + '-1-' + sixDigitsNumber;
};

const prepareAndCreateMNPTennyuSO = async (context, { doc, shippingInfo }) => {
    if (doc.lastKoutei.kouteiId !== KOUTEI_IDS.UKETSUKE_CHECK_OK) return;

    const data = {
        pSoId: doc.p_so_id,
        tenantId: doc.tenantId,
        tempoId: doc.tempoId,
        planId: doc.planId,
        uketsukeDateTime: doc.uketsukeDateTime,
        desiredDeliveryDate: doc.desiredDeliveryDate,
        deliveryAddress: doc.deliveryAddress,
        simType: doc.simType,
        contractType: doc.contractType,
        hankuro: doc.hankuro,
        Nno: doc.N_no,
        ngReason: doc.ngReason,
        uketsukeNo: doc.uketsukeNo,
        addressNo: doc.addressNo || doc.defaultAddressNo,
        isFM: doc.isFM,
        voicePlanId: doc.voicePlanId,
    };

    await MNPTennyuSoModel.createMultipleSO(context, {
        quantity: doc.quantity,
        createdUserId: doc.createUserId,
        mnpTennyuData: data,
        shippingInfo,
    });
};

/**
 * Create MNPNewParent soId
 * @param  {string} createdUserId        - created userId
 * @param  {KouteiInfo} koutei           - order processing status
 * @param  {object} data                 - required data for import
 * @param  {ShippingInfo} [shippingInfo] - shipping address info (with deliveryFlag)
 * @return {string|null} soId                 - if successful return order id (soId)
 */
const createSO = async (context, { createdUserId, koutei, data, shippingInfo }) => {
    if (!createdUserId) throw new Error('createdUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');
    if (!data || Object.keys(data).length === 0) throw new Error('data is required');

    const nowSec = dayjs().unix();
    let mnpNewParentSo = new MNPNewParentSoModel(removeNullValue(data));

    mnpNewParentSo.soKind = SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER;
    mnpNewParentSo.createUserId = createdUserId;
    mnpNewParentSo.uketsukeDateTime = nowSec;

    mnpNewParentSo.lastKoutei = removeNullValue(koutei);
    mnpNewParentSo.lastKoutei.userId = createdUserId;
    mnpNewParentSo.lastKoutei.timestamp = nowSec;
    mnpNewParentSo.kouteis = [mnpNewParentSo.lastKoutei];

    const [addressNo, uketsukeNo] = await Promise.all([
        createAddressNo(mnpNewParentSo.tenantId),
        createUketsukeNo(mnpNewParentSo.soKind, mnpNewParentSo.tenantId, mnpNewParentSo.N_no),
    ]);

    mnpNewParentSo.uketsukeNo = uketsukeNo;
    if (shippingInfo?.deliveryFlag === '1') {
        mnpNewParentSo.addressNo = addressNo;
    } else {
        mnpNewParentSo.defaultAddressNo = addressNo;
    }

    if (shippingInfo?.deliveryFlag === '1') {
        Object.assign(mnpNewParentSo, removeNullValue(shippingInfo));
    } else if (mnpNewParentSo.deliveryAddress) {
        //デフォルト配送先を確定させる
        const deliveryAddress = await CustomerInfoModel.findBySeqStr(
            mnpNewParentSo.deliveryAddress,
            mnpNewParentSo.N_no,
            true
        );
        const defaultAddress = {
            postalCode: deliveryAddress?.addresseePostcode,
            shippingAddress1: deliveryAddress?.addresseePrefectures,
            shippingAddress2: deliveryAddress?.addresseeCities,
            shippingAddress3: deliveryAddress?.addresseeOoaza,
            shippingAddress4: deliveryAddress?.addresseeAza,
            shippingAddress5: deliveryAddress?.addresseeBlock,
            shippingAddress6: deliveryAddress?.addresseeMansion,
            shippingAddress7: deliveryAddress?.addresseeName,
            shippingAddress8: deliveryAddress?.addresseeSectionName,
            shippingAddress9: deliveryAddress?.addresseeRepresentativeName,
            contactPhoneNumber: deliveryAddress?.addresseePhoneNumber,
        };
        Object.assign(mnpNewParentSo, removeNullValue(defaultAddress));
    } else {
        // デフォルト配送先情報を指定しない場合、卸コアからの情報を取って確定させる
        const deliveryAddress = await CustomerInfoPgModel.getAddressInfoByNNo(context, mnpNewParentSo.N_no);
        const defaultAddress = {
            postalCode: deliveryAddress?.addressee_postcode,
            shippingAddress1: deliveryAddress?.addressee_prefectures,
            shippingAddress2: deliveryAddress?.addressee_cities,
            shippingAddress3: deliveryAddress?.addressee_daiji,
            shippingAddress4: deliveryAddress?.addressee_ji,
            shippingAddress5: deliveryAddress?.addressee_block,
            shippingAddress6: deliveryAddress?.addressee_mansion,
            shippingAddress7: deliveryAddress?.addressee_name,
            shippingAddress8: deliveryAddress?.addressee_section_name,
            shippingAddress9: deliveryAddress?.addressee_representative_name,
            contactPhoneNumber: deliveryAddress?.addressee_phone_number,
        };
        Object.assign(mnpNewParentSo, removeNullValue(defaultAddress));
    }

    if (mnpNewParentSo.isFM === true) {
        mnpNewParentSo.p_so_id = portalConfig.GAIBU_API_FULL_PROCESS_ID_PREFIX + mnpNewParentSo._id;
    } else {
        mnpNewParentSo.p_so_id = portalConfig.GAIBU_API_PROCESS_ID_PREFIX + mnpNewParentSo._id;
    }

    const savedDoc = await mnpNewParentSo.save();

    //この新規受付に所属するSOを作成
    await prepareAndCreateMNPTennyuSO(context, { doc: savedDoc, shippingInfo });
    return savedDoc?.p_so_id;
};

/**
 * find NewParentInfo by so_id
 * @param {object} context - API context
 * @param {string} p_so_id
 * @returns {mnpNewParentSoSchema|null} NewParentInfo object or null
 */
const getNewParentInfoByPSoId = async (context, p_so_id) => {
    if (!p_so_id) throw new Error('p_so_id is required');

    return await MNPNewParentSoModel.findOne({ p_so_id }).exec();
};

/**
 *
 * @param {object} context - context object
 * @param {string[]} tenantIdo - tenantId
 * @param {string[]} tempoIdo - tempoId
 * @param {number[]} processIdo - processId
 * @param {number} fromDateo - receptDateBeginKey
 * @param {number} toDateo - receptDateEndKey
 * @param {string[]} hankuroo - simOrderType
 * @param {number} fromOpenningDate - fromOpenningDate
 * @param {number} toOpenningDate - toOpenningDate
 * @param {string} kohaiMoushikomiBango - kohaiMoushikomiBango
 * @param {string} defaultHaisouMoushikomiBango - defaultHaisouMoushikomiBango
 * @param {string} kohaiFlag - kohaiFlag
 * @param {number} limito - limit
 * @param {number} service - liteMVNO or fullMVNO or both
 * @param {string[]} prefixTempoIdo - prefixTempoId
 * @returns {array.<newParentSO>} - newParentSO list
 */
const searchParentSO = async (
    context,
    {
        tenantIdo,
        tempoIdo,
        processIdo,
        fromDateo,
        toDateo,
        hankuroo,
        fromOpenningDate,
        toOpenningDate,
        kohaiMoushikomiBango,
        defaultHaisouMoushikomiBango,
        kohaiFlag,
        limito,
        service,
        prefixTempoIdo,
    }
) => {
    context.log('MNPNewParentSOModel searchParentSO START');
    let query = {};
    // 個配申込受付番号とデフォルト配送申込受付番号をフィルターする場合
    if (kohaiMoushikomiBango && defaultHaisouMoushikomiBango) {
        let ors = [];
        // 個配申込受付番号をフィルターにセット
        ors.push({ addressNo: { $regex: kohaiMoushikomiBango, $options: 'i' } });
        // デフォルト配送申込受付番号をフィルターにセット
        ors.push({ defaultAddressNo: { $regex: defaultHaisouMoushikomiBango, $options: 'i' } });
        query = { $or: ors };
    } else {
        // 個配申込受付番号のみがある場合
        if (kohaiMoushikomiBango) {
            query.addressNo = { $regex: kohaiMoushikomiBango, $options: 'i' };
        } else if (defaultHaisouMoushikomiBango) {
            // デフォルト配送申込受付番号のみがある場合
            query.defaultAddressNo = { $regex: defaultHaisouMoushikomiBango, $options: 'i' };
        }
    }

    if (Array.isArray(tenantIdo) && tenantIdo.length > 0) {
        query.tenantId = { $in: tenantIdo };
    }

    if (Array.isArray(tempoIdo) && tempoIdo.length > 0) {
        query.tempoId = { $in: tempoIdo };
    }

    //部分一致かつ複数検索: https://mobilus.backlog.jp/view/MVNO_N_M-2123#comment-131332889
    if (Array.isArray(prefixTempoIdo) && prefixTempoIdo.length > 0) {
        const prefixRegex = createPrefixTempoFind(prefixTempoIdo);
        query.tempoId = { $regex: prefixRegex };
    }

    if (Array.isArray(processIdo) && processIdo.length > 0) {
        query['lastKoutei.kouteiId'] = { $in: processIdo };
    }

    if (fromDateo && !toDateo) {
        query.uketsukeDateTime = { $gte: fromDateo };
    } else if (!fromDateo && toDateo) {
        query.uketsukeDateTime = { $lt: toDateo };
    } else if (fromDateo && toDateo) {
        query.uketsukeDateTime = { $gte: fromDateo, $lt: toDateo };
    }

    if (fromOpenningDate && !toOpenningDate) {
        query.desiredDeliveryDate = { $gte: fromOpenningDate };
    } else if (!fromOpenningDate && toOpenningDate) {
        query.desiredDeliveryDate = { $lt: toOpenningDate };
    } else if (fromOpenningDate && toOpenningDate) {
        query.desiredDeliveryDate = { $gte: fromOpenningDate, $lt: toOpenningDate };
    }

    if (Array.isArray(hankuroo) && hankuroo.length > 0) {
        query.hankuro = { $in: hankuroo };
    }

    if (kohaiFlag) {
        query.deliveryFlag = kohaiFlag === '1' ? kohaiFlag : { $ne: '1' };
    }

    //STEP 14 FULL MVNO
    switch (service) {
        case MVNO_SERVICES.LITE:
            query.isFM = { $ne: true };
            break;
        case MVNO_SERVICES.FULL:
            query.isFM = true;
            break;
        default:
            break;
    }

    const cursor = limito
        ? await MNPNewParentSoModel.find(query).sort({ _id: 1 }).limit(limito)
        : await MNPNewParentSoModel.find(query).sort({ _id: 1 });

    return cursor;
};

/**
 * find By 新規受付のSOID and Tenant Id
 * @param {object} context - context object
 * @param {string} soId - soId
 * @param {string} tenantId - tenantId
 * @returns {mnpNewParentSoSchema|null} - newParentSO object or null
 */
const findSOByParentSOIdAndTenantId = async (context, { soId, tenantId }) => {
    context.log('MNPNewParentSOModel findSOByParentSOIdAndTenantId START');
    if (!soId) throw new Error('soId is required');
    if (!tenantId) throw new Error('tenantId is required');

    const query = {
        p_so_id: soId,
        tenantId,
    };
    return await MNPNewParentSoModel.findOne(query).exec();
};

/**
 * find By 新規受付のSOID
 * @param {object} context - context object
 * @param {string} pSoId - pSoId
 * @returns {mnpNewParentSoSchema|null} - newParentSO object or null
 */
const findSOByParentSOId = async (context, pSoId) => {
    context.log('MNPNewParentSOModel findSOByParentSOId START');
    if (!pSoId) throw new Error('pSoId is required');

    const query = {
        p_so_id: pSoId,
    };
    return await MNPNewParentSoModel.findOne(query).exec();
};

/**
 * @param {object} context
 * @param {string} soId
 * @param {boolean} isNew
 */
const updateIsNewStatus = async (context, soIds, isNew) => {
    context.log('MNPNewParentSOModel updateIsNewStatus START');

    await MNPNewParentSoModel.update({ p_so_id: { $in: soIds } }, { $set: { isNew: isNew } }).exec();
};

/**
 * find documents by p_so_id and update kouteis info
 * 旧メソッド: addKouteiInfo
 * @param  {object} context
 * @param  {string} p_so_id
 * @param  {string} updatedUserId
 * @param  {KouteiInfo} koutei
 */
const updateKouteiInfo = async (context, { p_so_id, updatedUserId, koutei }) => {
    context.log('MNPNewParentSoModel updateKouteiInfo START ', p_so_id);
    if (!p_so_id) throw new Error('p_so_id is required');
    if (!updatedUserId) throw new Error('updatedUserId is required');
    if (!koutei || !koutei.kouteiId) throw new Error('koutei is required');

    koutei.userId = updatedUserId;
    koutei.timestamp ||= dayjs().unix();

    const lastKoutei = removeNullValue(koutei);

    await MNPNewParentSoModel.findOneAndUpdate(
        { p_so_id },
        {
            $set: { lastKoutei, isNew: false },
            $push: { kouteis: lastKoutei },
        },
        { runValidators: true, context: 'query', new: true }
    ).exec();
};

/**
 * @param {string} p_so_id 
 */
const checkIsFM = async (p_so_id) => {
    return (await MNPNewParentSoModel.exists({ p_so_id, isFM: true })) ? true : false;
};

/**
 * SO取得(SO_ID複数 or 条件)
 * @param {object} context - context object
 * @param {string[]} pSoIds - pSoIds
 * @param {number} sortType - sortType
 * @returns {array.<mnpNewParentSoSchema>|[]} - [mnpNewParentSoSchema] or []
 */
const findSOByParentSOIDs = async (context, pSoIds, sortType = -1) => {
    context.log('MNPNewParentSOModel findSOByParentSOIDs START: ' + pSoIds);
    if (isNone(pSoIds)) {
        return [];
    } else {
        const query = { p_so_id: { $in: pSoIds } };
        const sort = { _id: sortType };
        return await MNPNewParentSoModel.find(query).sort(sort).exec();
    }
};

/**
* 
* @param {object} context - context object
* @param {string} addressNo - addressNo
* @returns {mnpNewParentSoSchema|null} - mnpNewParentSoSchema or null
*/
const findSOByAddressNo = async (context, addressNo) => {
    context.log('MNPNewParentSOModel findSOByAddressNo START ', addressNo);
    if (!addressNo) throw new Error('addressNo is required');

    return await MNPNewParentSoModel.findOne({ addressNo: addressNo }).exec();
};

/**
* step 7.1: 配送申込受付番号をチェック
* @param {object} context - context object
* @param {string} addressNo - addressNo
* @returns {mnpNewParentSoSchema|null} - mnpNewParentSoSchema or null
*/
const findSOByDefaultAddressNo = async (context, addressNo) => {
    context.log('MNPNewParentSOModel findSOByDefaultAddressNo START ', addressNo);
    if (!addressNo) throw new Error('addressNo is required');

    return await MNPNewParentSoModel.findOne({ defaultAddressNo: addressNo }).exec();
};

/**
* 
* @param {object} context - context object
* @param {string} addressNo - addressNo
* @param {string} denpyoNo - denpyoNo
* @returns {boolean}
*/
const setDenpyoNo = async (context, addressNo, denpyoNo) => {
    context.log('MNPNewParentSOModel setDenpyoNo START ', addressNo, ', ', denpyoNo);
    if (!addressNo) throw new Error('addressNo is required');
    if (!denpyoNo) throw new Error('denpyoNo is required');

    let isNew = true;
    // デフォルト配送申込受付番号の場合をチェック
    const soByDefaultAddressNo = await findSOByDefaultAddressNo(context, addressNo);
    if (!isNone(soByDefaultAddressNo)) {
        isNew = isNone(soByDefaultAddressNo.denpyoNo);
        await MNPNewParentSoModel.updateOne({ defaultAddressNo: addressNo }, { $set: { denpyoNo: denpyoNo } }).exec();
        // 子SOの伝票番号も更新する
        await MNPTennyuSoModel.updateMany(
            {
                $and: [
                    { defaultAddressNo: addressNo },
                    { soKind: { $eq: SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER } }
                ]
            },
            {
                $set: { denpyoNo: denpyoNo }
            }
        ).exec();
    } else {
        const soByAddressNo = await findSOByAddressNo(context, addressNo);
        isNew = isNone(soByAddressNo.denpyoNo);
        await MNPNewParentSoModel.updateOne({ addressNo: addressNo }, { $set: { denpyoNo: denpyoNo } }).exec();
        // 子SOの伝票番号も更新する
        await MNPTennyuSoModel.updateMany(
            {
                $and: [
                    { addressNo: addressNo },
                    { soKind: { $eq: SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER } }
                ]
            },
            {
                $set: { denpyoNo: denpyoNo }
            }
        ).exec();
    }
    return isNew;
};

//statics
mnpNewParentSoSchema.statics = {
    createSO,
    getNewParentInfoByPSoId,
    searchParentSO,
    findSOByParentSOIdAndTenantId,
    findSOByParentSOId,
    updateIsNewStatus,
    updateKouteiInfo,
    checkIsFM,
    findSOByParentSOIDs,
    findSOByAddressNo,
    findSOByDefaultAddressNo,
    setDenpyoNo,
};

const MNPNewParentSoModel = mongoose.model('mnp_new_parent_so', mnpNewParentSoSchema, 'mnp_new_parent_so');

const MNPNewParentSoModule = (module.exports = MNPNewParentSoModel);
MNPNewParentSoModule.MNPNewParentSoConstants = MNPNewParentSoConstants;
