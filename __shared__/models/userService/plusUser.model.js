const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { randomBytes } = require('crypto');
const { getSha256Hash } = require('../../helpers/cryptoHelper');
const appConfig = require('../../config/appConfig');
const { isNone } = require('../../helpers/baseHelper');
const { USER_TYPE, USER_TYPE_NAME, PERMIT_LEVEL, TWO_FA_STATUS } = require('../../constants/userConstants');

const tenantDBsConnection = {};
const Schema = mongoose.Schema;

const SORT_BY_FIELDS = {
    userId: 'plusId',
    userName: 'name',
    tempoId: 'tempoId',
    twoFAStatus: 'twoFAStatus',
    accountLocked: 'accountLock',
    maskingEnabled: 'maskingEnabled',
};

const plusUserSchema = new Schema({
    _id: {
        type: String,
    },
    plusId: {
        type: String,
        required: true,
        unique: true,
        index: true,
    },
    name: {
        type: String,
        required: true,
        index: true,
    },
    permitLevel: {
        type: Number,
        default: 1,
    },
    removed: {
        type: Boolean,
        default: false,
    },
    createdDate: {
        type: Number,
        index: true,
    },
    updatedDate: {
        type: Number,
    },
    accountLock: {
        type: Boolean,
        default: false,
        index: true,
    },
    accountLockMemo: {
        type: String,
    },
    passwdFailCount: {
        type: Number,
        default: 0,
    },
    passwdHash: {
        type: Buffer,
        required: true,
    },
    passwdResetTime: {
        type: Number,
    },
    passwdResetTimeNTTCMVNO: {
        type: Number,
    },
    passwdSalt: {
        type: Buffer,
        required: true,
    },
    passwdTmp: {
        type: String,
    },
    passwdUpdateHistory: {
        type: [
            {
                passwdHash: Buffer,
                passwdSalt: Buffer,
            },
        ],
        default: [],
    },
    // 0: 未利用 1: 必須(未設定) 2: 必須(設定済)
    twoFAStatus: {
        type: Number,
        default: 0,
        enum: [0, 1, 2],
        index: true,
    },
    authCode: {
        type: String,
    },
    authCodeCreatedAt: {
        type: Number,
    },
    tempoId: {
        type: String,
        required: true,
        index: true,
    },
    maskingEnabled: {
        type: Boolean,
        default: false,
        index: true,
    },
    lastLoginAt: { type: Number },
});

/**
 * Check if the password is the same as the password of the user
 * Note: This is a alternative version of the original function checkPasswordNoAccountLock.
 * @param {string} password
 * @returns {boolean}
 */
plusUserSchema.methods.isPasswordMatch = function (password) {
    const { passwdHash, passwdSalt } = this;
    return passwdHash.equals(createPasswdHash(passwdSalt, password));
};

/**
 * @param {object} context
 * @param {object} params
 * @param {string} params.password
 * @returns {Promise<plusUserSchema>}
 */
plusUserSchema.methods.setNewPassword = async function (context, { password }) {
    context.log('PlusUserModel instance setNewPassword START');

    const now = dayjs().unix();
    const newPasswordFields = makeNewPasswordFields(password, now);

    Object.assign(this, newPasswordFields);

    const { passwdHash, passwdSalt } = newPasswordFields;
    this.passwdUpdateHistory.push({ passwdHash, passwdSalt });
    this.passwdUpdateHistory = this.passwdUpdateHistory.slice(-3);

    return await this.save();
};

/**
 * @param {object} context
 * @param {object} params
 * @param {string} params.userId
 * @param {string} params.userName
 * @param {string} params.tempoId
 * @param {string} params.password
 * @param {number} params.twoFAStatus
 * @param {boolean} params.maskingEnabled
 * @returns {Promise<plusUserSchema>}
 */
const createUser = async function (context, { userId, userName, tempoId, password, twoFAStatus, maskingEnabled }) {
    context.log('PlusUserModel createUser START');

    const now = dayjs().unix();

    const newPasswordFields = makeNewPasswordFields(password, now);
    const { passwdHash, passwdSalt } = newPasswordFields;

    const userData = {
        _id: userId,
        plusId: userId,
        permitLevel: PERMIT_LEVEL.USER,
        removed: false,
        ...newPasswordFields,
        passwdUpdateHistory: [{ passwdHash, passwdSalt }],
        name: userName,
        tempoId,
        twoFAStatus,
        createdDate: now,
        updatedDate: now,
    };

    if (!isNone(maskingEnabled)) {
        userData.maskingEnabled = maskingEnabled;
    }

    return await this.create(userData);
};

/**
 * Create random salt
 * @returns {Buffer}
 */
const createNewSalt = () => {
    return randomBytes(8);
};

/**
 * Create password hash
 * @param {Buffer} passwdSalt
 * @param {string} password
 * @returns {Buffer}
 */
const createPasswdHash = (passwdSalt, password) => {
    return Buffer.from(getSha256Hash(Uint8Array.from([...passwdSalt, ...Buffer.from(password)])), 'hex');
};

/**
 * Check if the password is the same as the password in the history
 * @param {object} context
 * @param {object} params
 * @param {plusUserSchema} params.user
 * @param {string} params.password
 * @returns {boolean}
 */
const doesExistOldMVNOTempoUserPasswordSameToNewPassword = (context, { user, newPassword }) => {
    context.log('PlusUserModel doesExistOldMVNOTempoUserPasswordSameToNewPassword START');

    const { passwdUpdateHistory } = user;
    for (const { passwdHash, passwdSalt } of passwdUpdateHistory) {
        if (passwdHash.equals(createPasswdHash(passwdSalt, newPassword))) {
            return true;
        }
    }

    return false;
};

/**
 * @param {object} context
 * @param {object} params
 * @param {plusUserSchema} params.user
 * @param {string} params.userName
 * @param {string} params.password
 * @param {number} params.twoFAStatus
 * @param {boolean} params.maskingEnabled
 * @returns {Promise<plusUserSchema>}
 */
const updateUser = async function (context, { user, userName, password, twoFAStatus, maskingEnabled }) {
    context.log('PlusUserModel updateUser START');

    const now = dayjs().unix();

    let hasUpdate = false;

    if (!isNone(userName)) {
        hasUpdate = true;
        user.name = userName;
    }

    if (!isNone(twoFAStatus)) {
        hasUpdate = true;
        user.twoFAStatus = twoFAStatus;
    }

    if (!isNone(maskingEnabled)) {
        hasUpdate = true;
        user.maskingEnabled = maskingEnabled;
    }

    if (!isNone(password)) {
        hasUpdate = true;
        const newPasswordFields = makeNewPasswordFields(password, now);
        const { passwdHash, passwdSalt } = newPasswordFields;

        Object.assign(user, newPasswordFields);

        user.passwdUpdateHistory.push({ passwdHash, passwdSalt });
        user.passwdUpdateHistory = user.passwdUpdateHistory.slice(-3);
    }

    if (hasUpdate) {
        user.updatedDate = now;
    }

    return await user.save();
};

/**
 * @param {object} context
 * @param {object} params
 * @param {string[]} params.userIds
 * @returns {Promise<UpdateResult>}
 */
const deleteNTTCMVNOTempoUsers = async function (context, { userIds }) {
    context.log('PlusUserModel deleteNTTCMVNOTempoUser START');

    const now = dayjs().unix();
    return await this.updateMany(
        { plusId: { $in: userIds } },
        {
            $set: {
                removed: true,
                updatedDate: now,
            },
        }
    );
};

/**
 * Set twoFAStatus to NOT_SETUP_YET
 * @param {object} context
 * @param {object} params
 * @param {string[]} params.userIds
 * @returns {Promise<UpdateResult>}
 */
const enableTwoFAStatusByPlusIds = async function (context, { userIds }) {
    context.log('PlusUserModel enableTwoFAStatusByPlusIds START');

    const now = dayjs().unix();
    return await this.updateMany(
        { plusId: { $in: userIds } },
        {
            $set: {
                twoFAStatus: TWO_FA_STATUS.NOT_SETUP_YET,
                updatedDate: now,
            },
        }
    );
};

/**
 * Set maskingEnabled to true
 * @param {object} context
 * @param {object} params
 * @param {string[]} params.userIds
 * @returns {Promise<UpdateResult>}
 */
const enableMaskingEnabledByPlusIds = async function (context, { userIds }) {
    context.log('PlusUserModel enableMaskingEnabledByPlusIds START');

    const now = dayjs().unix();
    return await this.updateMany(
        { plusId: { $in: userIds } },
        {
            $set: {
                maskingEnabled: true,
                updatedDate: now,
            },
        }
    );
};

/**
 * @param {object} context
 * @param {string} plusId
 * @returns {Promise<plusUserSchema>}
 */
const findByPlusId = async function (context, plusId) {
    context.log('PlusUserModel findByPliusId START');

    return await this.findOne({ plusId });
};

/**
 * @deprecated Use findNotRemovedUserByPlusId instead
 * @param {object} context
 * @param {string} id
 * @returns {Promise<plusUserSchema>}
 */
const findById = async function (context, { id }) {
    context.log('PlusUserModel findById START');

    return await this.findOne({ _id: id, removed: false });
};

/**
 * @param {object} context
 * @param {string} plusId
 * @returns {Promise<plusUserSchema>}
 */
const findNotRemovedUserByPlusId = async function (context, { plusId }) {
    context.log('PlusUserModel findNotRemovedUserByPlusId START');

    return await this.findOne({ plusId, removed: { $ne: true } });
};

/**
 * @param {object} context
 * @param {object} params
 * @param {string} params.userType
 * @param {string} params.userId
 * @param {string} params.userName
 * @param {string} params.tempoId
 * @param {string} params.sortBy
 * @param {boolean} params.isSortOrderAscending
 * @param {number} params.skip
 * @param {number} params.limit
 * @returns {Promise<{results: plusUserSchema[], count: number, skip: number}>}
 */
const searchNTTCMVNOTempoUser = async function (
    context,
    { userType, userId, userName, tempoId, sortBy, isSortOrderAscending, skip, limit }
) {
    context.log('PlusUserModel searchNTTCMVNOTempoUser START');

    if (userType === USER_TYPE_NAME[USER_TYPE.DAIHYOU_USER]) {
        return { result: [], resultCount: 0, skip };
    }

    const filter = {
        removed: false,
    };

    if (!isNone(userId)) {
        filter.plusId = { $regex: userId };
    }

    if (!isNone(userName)) {
        filter.name = { $regex: userName };
    }

    let isSuperTempoUser;
    if (userType === USER_TYPE_NAME[USER_TYPE.SUPER_TEMPO_USER]) {
        isSuperTempoUser = true;
    } else if (userType === USER_TYPE_NAME[USER_TYPE.TEMPO_USER]) {
        isSuperTempoUser = false;
    }

    if (isNone(tempoId)) {
        if (!isNone(isSuperTempoUser)) {
            if (isSuperTempoUser) {
                filter.tempoId = { $regex: /^S/ };
            } else {
                filter.tempoId = { $regex: /^T/ };
            }
        }
    } else {
        if (
            !isNone(isSuperTempoUser) &&
            ((isSuperTempoUser && !tempoId.startsWith('S')) || (!isSuperTempoUser && tempoId.startsWith('S')))
        ) {
            return { result: [], resultCount: 0, skip };
        } else {
            filter.tempoId = tempoId;
        }
    }

    const sortAscendingInt = isSortOrderAscending ? 1 : -1;
    let sort = { createdDate: sortAscendingInt };
    if (Object.hasOwn(SORT_BY_FIELDS, sortBy)) {
        sort = { [SORT_BY_FIELDS[sortBy]]: sortAscendingInt };
    }

    const query = this.find(filter);
    const resultCountQuery = query.clone().countDocuments();
    const resultQuery = query.sort(sort).skip(skip).limit(limit);
    const [resultCount, result] = await Promise.all([resultCountQuery, resultQuery]);
    return {
        result,
        resultCount,
        skip,
    };
};

/**
 * @param {string} password
 * @param {number} updateTime
 * @returns {{passwdHash: Buffer, passwdSalt: Buffer, passwdFailCount: number, passwdTmp: string, passwdResetTime: number, passwdResetTimeNTTCMVNO: number, accountLock: boolean, accountLockMemo: string}}
 */
const makeNewPasswordFields = (password, updateTime = dayjs().unix()) => {
    const passwdSalt = createNewSalt();
    const passwdHash = createPasswdHash(passwdSalt, password);

    return {
        passwdHash,
        passwdSalt,
        passwdFailCount: 0,
        passwdTmp: '',
        passwdResetTime: 0,
        passwdResetTimeNTTCMVNO: updateTime,
        accountLock: false,
        accountLockMemo: '',
    };
};

/**
 * virtual property for role that is not saved in DB
 * 999: 店舗オペレータユーザ
 */
plusUserSchema.virtual('roleId').get(function () {
    return 999;
});

//statics
plusUserSchema.statics = {
    retrieveAllNotRemovedTempoUser: async function () {
        return await this.find({ removed: false }).sort({ _id: 1 });
    },
    createUser,
    enableTwoFAStatusByPlusIds,
    enableMaskingEnabledByPlusIds,
    deleteNTTCMVNOTempoUsers,
    createPasswdHash,
    doesExistOldMVNOTempoUserPasswordSameToNewPassword,
    updateUser,
    findByPlusId,
    findById,
    findNotRemovedUserByPlusId,
    searchNTTCMVNOTempoUser,
};

/**
 *
 * @param {*} tenantId
 * @returns
 */
module.exports = function (tenantId) {
    if (tenantDBsConnection[tenantId]) {
        return tenantDBsConnection[tenantId].model('plus_users', plusUserSchema, 'plus_users');
    }

    const tenantDB = mongoose.connection.useDb(appConfig.getPortalConfig().DB_NAME.USER_SERVICE + '_' + tenantId);
    tenantDBsConnection[tenantId] = tenantDB;
    const PlusUsersModel = tenantDB.model('plus_users', plusUserSchema, 'plus_users');

    return PlusUsersModel;
};
