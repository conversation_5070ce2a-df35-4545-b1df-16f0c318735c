const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { isNone, removeNullValue } = require('../helpers/baseHelper');
const Schema = mongoose.Schema;

/**
 * @typedef {object} NGResultDetail kaisenHaishiResult model's detailsNG
 * @property {string} tenantId
 * @property {string} tempoId
 * @property {string} kaisenNo
 * @property {number} reservedDate - db field is haishiDate!
 * @property {string|undefined} errorMsg
 */

const kaisenHaishiResultSchema = new Schema({
    tenantId: { type: String, trim: true, required: true, index: true },
    startTime: { type: Number, required: true },
    endTime: { type: Number, required: true },
    total: { type: Number, required: true },
    checkNG: { type: Number },
    checkOK: { type: Number },
    noProcess: { type: Number },
    createAt: { type: Number, set: (d) => (typeof d == 'number' ? d : dayjs(d).unix()) },
    detailsNG: [
        {
            tenantId: { type: String, required: true },
            tempoId: { type: String, required: true },
            kaisenNo: { type: String, required: true },
            haishiDate: { type: Number },
            errorMsg: { type: String },
        },
    ],
});

/**
 * @param {string?} tenantId
 */
const searchKaisenhaishiIkkatuResult = async (tenantId) => {
    const query = {};
    if (!isNone(tenantId)) query.tenantId = tenantId;
    return await KaisenHaishiResultModel.find(query, {
        _id: 1,
        startTime: 1,
        endTime: 1,
        total: 1,
        checkNG: 1,
        checkOK: 1,
        noProcess: 1,
    })
        .sort({ createAt: -1 })
        .limit(5)
        .exec();
};

const searchKaisenhaishiIkkatuResultDetailById = async (id) => {
    if (isNone(id)) return null;
    const found = await KaisenHaishiResultModel.find({ _id: id }, { _id: 1, detailsNG: 1 }).limit(1).exec();
    return !isNone(found) ? found[0] : null;
};

/**
 *
 * @param {object} context
 * @param {object} infoInput
 * @param {string} infoInput.tenantId
 * @param {number} infoInput.startTime
 * @param {number} infoInput.endTime
 * @param {number} infoInput.total
 * @param {number|undefined} infoInput.checkNG
 * @param {number|undefined} infoInput.checkOK
 * @param {number|undefined} infoInput.noProcess
 * @param {number|undefined} infoInput.createAt
 * @param {Array<{tenantId:string,tempoId:string,kaisenNo:string,errorMsg:string|undefined}>|undefined} infoInput.detailsNG
 */
const kaisenhaishiIkkatuInsert = async (context, infoInput) => {
    const newKaisenHaishiSO = new KaisenHaishiResultModel(
        removeNullValue({
            createAt: dayjs().unix(),
            ...infoInput,
        })
    );
    await newKaisenHaishiSO.save();
    return newKaisenHaishiSO._id.toString();
};

/**
 *
 * @param {string} objectId
 * @param {object} fields
 * @param {number|undefined} fields.checkNG
 * @param {number|undefined} fields.checkOK
 * @param {number|undefined} fields.noProcess
 * @param {number} fields.endtime
 * @param {Array<NGResultDetail} ngResultList
 */
const updateResult = async (objectId, fields, ngResultList) => {
    const query = { _id: mongoose.Types.ObjectId(objectId) };
    const updateData = fields ?? {};
    if (Array.isArray(ngResultList) && ngResultList.length > 0) {
        updateData.detailsNG = ngResultList.map((r) => ({
            tenantId: r.tenantId,
            tempoId: r.tempoId,
            kaisenNo: r.kaisenNo,
            haishiDate: r.reservedDate,
            errorMsg: r.errorMsg,
        }));
    }
    await KaisenHaishiResultModel.updateOne(query, updateData).exec();
};

kaisenHaishiResultSchema.statics = {
    searchKaisenhaishiIkkatuResult,
    searchKaisenhaishiIkkatuResultDetailById,
    kaisenhaishiIkkatuInsert,
    updateResult,
};
const KaisenHaishiResultModel = mongoose.model(
    'kaisen_haishi_result',
    kaisenHaishiResultSchema,
    'kaisen_haishi_result'
);

module.exports = KaisenHaishiResultModel;
