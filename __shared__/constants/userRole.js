const MAWP_ROLE_ID = {
    SUPER_USER: 0,
    NTTCOM_OPERATOR_USER: 1,
    TENANT_DAIHYO_USER: 2,
    TENANT_OPERATOR_USER: 3,
    TENANT_ETSURAN_USER: 4,
    TEMPO_USER: 999, // DB上存在しないが便宜的に入れる
};

// mRoleNamesMap
const MAWP_ROLE_NAMES = {
    [MAWP_ROLE_ID.SUPER_USER]: 'スーパユーザ',
    [MAWP_ROLE_ID.NTTCOM_OPERATOR_USER]: 'コムオペレータユーザ',
    [MAWP_ROLE_ID.TENANT_DAIHYO_USER]: '代表ユーザ',
    [MAWP_ROLE_ID.TENANT_OPERATOR_USER]: 'オペレータユーザ',
    [MAWP_ROLE_ID.TENANT_ETSURAN_USER]: '閲覧ユーザ',
    [MAWP_ROLE_ID.TEMPO_USER]: '店舗オペレータユーザ',
};

// mRoleIdMap
const MAWP_ROLE_IDS_BY_NAME = Object.keys(MAWP_ROLE_NAMES).reduce((acc, cur) => {
    acc[MAWP_ROLE_NAMES[cur]] = +cur;
    return acc;
}, {});

module.exports = {
    MAWP_ROLE_ID,
    MAWP_ROLE_NAMES,
    MAWP_ROLE_IDS_BY_NAME,
};
