const SO_KIND_TYPE = {
    ALL: '全部',
    OTHER: 'その他',

    MNP_TENNYU_VER20: 'MNP転入(旧)',
    MNP_TENNYU: 'MNP転入',
    KUROKA: '黒化',
    SHINKI_KUROKA: '新規黒化',
    MNP_TENSHUTSU: 'MNP転出予約',
    MNP_TENSHUTSU_CANCEL: 'MNP転出予約キャンセル',

    //=====BEGIN OF 4.0========
    MNP_SIM_SAIHAKKOU: 'SIM再発行',
    MNP_KAISEN_OPTION_HENKOU: '回線オプション変更',
    MNP_NWPASSWORD_HENKOU: 'ネットワーク暗証番号変更',
    MNP_NEW_SERVICE_ORDER: '新規受付',

    // 以下の値、外部API <=> HTTPクライアント、のやりとり専用。DB格納値に用いてはならない(過去VER.互換性)
    GAIBU_API_HANKURO_MNP_KUROKA: '半黒MNP黒化',
    GAIBU_API_SHINKI_KUROKA: '新規黒化',
    GAIBU_API_KAISEN_SHINSETSU: '新規受付',
    //=====END OF OF 4.0========

    //=====BEGIN OF 5.0 ========
    KAISEN_HAISHI_SERVICE_ORDER: '回線廃止予約',
    STOP_SERVICE_ORDER: '利用中断',
    RESUME_SERVICE_ORDER: '利用再開',
    SWITCH_STATUS_SERVICE_ORDER: '利用中断・再開切替',
    //=====END OF OF 5.0========

    //=====BEGIN OF 7.0 ========
    NEW_OTA_SERVICE_ORDER: '新規受付(OTA)',
    //=====END OF OF 7.0========

    //=====BEGIN OF 7.1 ========
    CONTRACT_TYPE_CHANGE_ORDER: 'ネットワーク契約変更',
    CHANGE_PROCESS: '工程変更',
    //=====END OF OF 7.1========

    SWIMMY_FUMEI_SERVICE_ORDER: '不明',
    SWIMMY_DAIDAIHYOU_KAISEN_SERVICE_ORDER: '大代表回線作成',
    SWIMMY_DAIHYOU_KAISEN_SERVICE_ORDER: '代表回線作成',
    SWIMMY_KO_KAISEN_TSUUJOU_SERVICE_ORDER: '子回線作成(3)',
    SWIMMY_KO_KAISEN_YUUSEN_SERVICE_ORDER: '子回線作成(4)',
    SWIMMY_KAISEN_HAISHI_SOKUJI_SERVICE_ORDER: '回線廃止（即時）',
    KAISEN_SUPPEND: 'サスペンド',
    KAISEN_SUPPEND_KAIJO: 'サスペンド解除',

    MNP_NEW_ESIM_SERVICE_ORDER: '新規受付(eSIM)',
    MNP_CHECK: 'MNP関連照会',

    // STEP25: 0035でんわプラン対応
    VOICE_PLAN_CHANGE: '0035でんわプラン変更',
};

const SWIMMY_SO_KIND = [
    SO_KIND_TYPE.SWIMMY_DAIDAIHYOU_KAISEN_SERVICE_ORDER,
    SO_KIND_TYPE.SWIMMY_DAIHYOU_KAISEN_SERVICE_ORDER,
    SO_KIND_TYPE.SWIMMY_KO_KAISEN_TSUUJOU_SERVICE_ORDER,
    SO_KIND_TYPE.SWIMMY_KO_KAISEN_YUUSEN_SERVICE_ORDER,
    SO_KIND_TYPE.SWIMMY_KAISEN_HAISHI_SOKUJI_SERVICE_ORDER,
];

const KOUTEI_IDS = {
    UKETSUKE_CHECK_NG: 1,
    UKETSUKE_CHECK_OK: 2,
    SIM_YAKITSUKE_SENTAKU_OK: 4,
    SIM_YAKITSUKE_SENTAKU_NG: 3,
    ALADIN_KOUJI_WAITING: 5,
    ALADIN_KOUJI_NG: 11,
    ALADIN_KOUJI_OK: 12,
    ALADIN_KOUJI_TMP: 14, // KaisenHaishiSO
    SMS_KAITSU_OK: 13,
    KOUKANKI_SETTEI_NG: 21,
    KOUKANKI_SETTEI_OK: 22,
    KANRYOU: 31,
    TEN_SHUTSU_SUMI_OK: 40,
    CANCEL_NG: 41,
    CANCEL_OK: 42,
};

const KOUTEI_TITLES = {
    [KOUTEI_IDS.UKETSUKE_CHECK_NG]: '受付チェックNG',
    [KOUTEI_IDS.UKETSUKE_CHECK_OK]: '受付チェックOK',
    [KOUTEI_IDS.ALADIN_KOUJI_WAITING]: 'ALADIN実施中',
    [KOUTEI_IDS.ALADIN_KOUJI_NG]: 'ALADIN工事NG',
    [KOUTEI_IDS.ALADIN_KOUJI_OK]: 'ALADIN工事OK',
    [KOUTEI_IDS.ALADIN_KOUJI_TMP]: 'ALADIN工事(仮)',
    [KOUTEI_IDS.KOUKANKI_SETTEI_NG]: '交換機設定NG',
    [KOUTEI_IDS.KOUKANKI_SETTEI_OK]: '交換機設定OK',
    [KOUTEI_IDS.KANRYOU]: '完了',
    [KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_OK]: '焼付けSIM選択OK',
    [KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_NG]: '焼付けSIM選択NG',
    [KOUTEI_IDS.SMS_KAITSU_OK]: 'SMS開通OK',
    [KOUTEI_IDS.TEN_SHUTSU_SUMI_OK]: '転出済',
    [KOUTEI_IDS.CANCEL_NG]: 'キャンセルNG',
    [KOUTEI_IDS.CANCEL_OK]: 'キャンセル済',
};

const KUROKA_KOUTEI_TITLES = {
    [KOUTEI_IDS.UKETSUKE_CHECK_NG]: '受付チェックNG',
    [KOUTEI_IDS.UKETSUKE_CHECK_OK]: '受付チェックOK',
    [KOUTEI_IDS.ALADIN_KOUJI_WAITING]: 'ALADIN実施中',
    [KOUTEI_IDS.ALADIN_KOUJI_NG]: 'ALADIN工事NG',
    [KOUTEI_IDS.ALADIN_KOUJI_OK]: 'ALADIN工事OK',
    [KOUTEI_IDS.KOUKANKI_SETTEI_NG]: '交換機設定NG',
    [KOUTEI_IDS.KOUKANKI_SETTEI_OK]: '交換機設定OK',
    [KOUTEI_IDS.KANRYOU]: '完了',
};

const MNP_TENNYU_KOUTEI_TITLES = {
    [KOUTEI_IDS.UKETSUKE_CHECK_NG]: '受付チェックNG',
    [KOUTEI_IDS.UKETSUKE_CHECK_OK]: '受付チェックOK',
    [KOUTEI_IDS.ALADIN_KOUJI_WAITING]: 'ALADIN実施中',
    [KOUTEI_IDS.ALADIN_KOUJI_NG]: 'ALADIN工事NG',
    [KOUTEI_IDS.ALADIN_KOUJI_OK]: 'ALADIN工事OK',
    [KOUTEI_IDS.KOUKANKI_SETTEI_NG]: '交換機設定NG',
    [KOUTEI_IDS.KOUKANKI_SETTEI_OK]: '交換機設定OK',
    [KOUTEI_IDS.KANRYOU]: '完了',
    [KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_OK]: '焼付けSIM選択OK',
    [KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_NG]: '焼付けSIM選択NG',
    [KOUTEI_IDS.SMS_KAITSU_OK]: 'SMS開通OK',
    [KOUTEI_IDS.TEN_SHUTSU_SUMI_OK]: '転出済',
};

const ORDER_STATUS_LIST = ['完了', '失敗', '予約中', 'キャンセル済み'];

const SUB_ORDER_TYPE = [
    'アクティベート',
    'ディアクティベート',
    'クーポン追加',
    'クーポンON',
    'クーポンOFF',
    'プラン変更',
];

const FULL_ORDER_TYPE = [
    'アクティベート',
    'ディアクティベート',
    'クーポン追加',
    'クーポンON',
    'クーポンOFF',
    'プラン変更',
    'データ譲渡',
    '回線グループクーポン追加',
    '回線グループクーポンON',
    '回線グループクーポンOFF',
    '回線グループ作成',
    '回線グループ廃止',
    '回線グループ所属回線変更(割当)',
    '回線グループ所属回線変更(割当解除)',
    '回線グループ基本容量変更',
    '回線グループプラン変更',
];

const KOKANKI_SETTEI_NG_TAG = 'KOKANKI_SETTEI_NG';

module.exports = {
    SO_KIND_TYPE,
    SWIMMY_SO_KIND,
    KOUTEI_IDS,
    KOUTEI_TITLES,
    KUROKA_KOUTEI_TITLES,
    MNP_TENNYU_KOUTEI_TITLES,
    ORDER_STATUS_LIST,
    SUB_ORDER_TYPE,
    FULL_ORDER_TYPE,
    KOKANKI_SETTEI_NG_TAG,
};
