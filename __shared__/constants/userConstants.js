const { MAWP_ROLE_ID } = require('./userRole');

const PERMIT_LEVEL = {
    ADMIN: 0,
    USER: 1,
    GUEST: 2,
};

const USER_TYPE = {
    TEMPO_USER: 0, // 店舗オペレータユーザ
    SUPER_TEMPO_USER: 1, // S店舗ユーザ
    DAIHYOU_USER: 2, // 代表ユーザ
    UNDEFINED: 3,
};

const USER_TYPE_NAME = {
    [USER_TYPE.TEMPO_USER]: '店舗オペレータユーザ',
    [USER_TYPE.SUPER_TEMPO_USER]: 'S店舗ユーザ',
    [USER_TYPE.DAIHYOU_USER]: '代表ユーザ',
};

/**
 * @deprecated use value in portalConfig instead
 */
const TENPO_LOGIN_ID_PREFIX = '@';

const ADDABLE_ROLE_ID_BY_SUPER_USER = [MAWP_ROLE_ID.NTTCOM_OPERATOR_USER, MAWP_ROLE_ID.TENANT_DAIHYO_USER];
const ADDABLE_ROLE_ID_BY_DAIHYO_USER = [MAWP_ROLE_ID.TENANT_OPERATOR_USER, MAWP_ROLE_ID.TENANT_ETSURAN_USER];

const TWO_FA_STATUS = {
    NOT_USED: 0,
    NOT_SETUP_YET: 1,
    SETUP_DONE: 2,
};

module.exports = {
    PERMIT_LEVEL,
    USER_TYPE,
    USER_TYPE_NAME,
    TENPO_LOGIN_ID_PREFIX,
    ADDABLE_ROLE_ID_BY_SUPER_USER,
    ADDABLE_ROLE_ID_BY_DAIHYO_USER,
    TWO_FA_STATUS,
};
