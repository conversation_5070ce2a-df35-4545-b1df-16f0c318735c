const SWIMMY_VLM_INTERNATIONAL_CALL = {
    ON: 'on',
    OFF: 'off',
};

const SWIMMY_VLM_REQUEST_TYPES = {
    fumei: -1,
    SHINKI_UKETSUKE_KURO: 10,
    SHINKI_UKETSUKE_HANKURO: 12,
    SHIN<PERSON>I_UKETSUKE_OTA: 13,
    SHINKI_UKETSUKE_ESIM: 14,
    M<PERSON>_TENNYUU_KURO: 20,
    MNP_TENNYUU_HANKURO: 22,
    MNP_TENNYUU_OTA: 23,
    MNP_TENNYUU_ESIM: 24,
    KAISEN_HAISHI: 40,
    KAISEN_HAISHI_MNP_TENNYUU: 43,
    M<PERSON>_TENNYUU_KURO_SAME_MVNE_HAISHI: 44,
    MNP_TENNYUU_HANKURO_SAME_MVNE_HAISHI: 45,
    MNP_TENNYUU_OTA_SAME_MVNE_HAISHI: 46,
    M<PERSON>_TENNYUU_ESIM_SAME_MVNE_HAISHI: 47, // [同一MVNE廃止] MNP転入（eSIM）
    DENWA_PLAN_HENKOU_0035: 60,
};

const SWIMMY_VLM_REQUEST_TYPE_NAME = {
    [SWIMMY_VLM_REQUEST_TYPES.fumei]: '不明',
    [SWIMMY_VLM_REQUEST_TYPES.SHINKI_UKETSUKE_KURO]: '新規受付（黒）',
    [SWIMMY_VLM_REQUEST_TYPES.SHINKI_UKETSUKE_HANKURO]: '新規受付（半黒）',
    [SWIMMY_VLM_REQUEST_TYPES.SHINKI_UKETSUKE_OTA]: '新規受付（OTA）',
    [SWIMMY_VLM_REQUEST_TYPES.SHINKI_UKETSUKE_ESIM]: '新規受付（eSIM）',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_KURO]: 'MNP転入（黒）',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_HANKURO]: 'MNP転入（半黒MNP）',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_OTA]: 'MNP転入（OTA）',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_ESIM]: 'MNP転入（eSIM）',
    [SWIMMY_VLM_REQUEST_TYPES.KAISEN_HAISHI]: '回線廃止',
    [SWIMMY_VLM_REQUEST_TYPES.KAISEN_HAISHI_MNP_TENNYUU]: '回線廃止（MNP転出）',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_KURO_SAME_MVNE_HAISHI]: '[同一MVNE廃止] MNP転入（黒）',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_HANKURO_SAME_MVNE_HAISHI]: '[同一MVNE廃止] MNP転入（半黒MNP）',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_OTA_SAME_MVNE_HAISHI]: '[同一MVNE廃止] MNP転入（OTA）',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_ESIM_SAME_MVNE_HAISHI]: '[同一MVNE廃止] MNP転入（eSIM）',
    [SWIMMY_VLM_REQUEST_TYPES.DENWA_PLAN_HENKOU_0035]: '0035でんわプラン変更',
};

const SWIMMY_VLM_REQUEST_TYPE_PRIORTY_KEY = {
    [SWIMMY_VLM_REQUEST_TYPES.SHINKI_UKETSUKE_KURO]: 'SHINKI_UKETSUKE_KURO',
    [SWIMMY_VLM_REQUEST_TYPES.SHINKI_UKETSUKE_HANKURO]: 'SHINKI_UKETSUKE_HANKURO',
    [SWIMMY_VLM_REQUEST_TYPES.SHINKI_UKETSUKE_OTA]: 'SHINKI_UKETSUKE_OTA',
    [SWIMMY_VLM_REQUEST_TYPES.SHINKI_UKETSUKE_ESIM]: 'SHINKI_UKETSUKE_ESIM',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_KURO]: 'MNP_TENNYUU_KURO',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_HANKURO]: 'MNP_TENNYUU_HANKURO',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_OTA]: 'MNP_TENNYUU_OTA',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_ESIM]: 'MNP_TENNYUU_ESIM',
    [SWIMMY_VLM_REQUEST_TYPES.KAISEN_HAISHI]: 'KAISEN_HAISHI',
    [SWIMMY_VLM_REQUEST_TYPES.KAISEN_HAISHI_MNP_TENNYUU]: 'KAISEN_HAISHI_MNP_TENNYUU',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_KURO_SAME_MVNE_HAISHI]: 'SAME_MVNE_HAISHI_MNP_TENNYUU_KURO',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_HANKURO_SAME_MVNE_HAISHI]: 'SAME_MVNE_HAISHI_MNP_TENNYUU_HANKURO',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_OTA_SAME_MVNE_HAISHI]: 'SAME_MVNE_HAISHI_MNP_TENNYUU_OTA',
    [SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_ESIM_SAME_MVNE_HAISHI]: 'SAME_MVNE_HAISHI_MNP_TENNYUU_ESIM',
    [SWIMMY_VLM_REQUEST_TYPES.DENWA_PLAN_HENKOU_0035]: 'DENWA_PLAN_HENKOU_0035',
};

/** SwimmyVLMLog.LOWEST_API_PRIORITY */
const VLM_LOWEST_API_PRIORITY = 100;

const SWIMMY_VLM_STATUS = {
    ON_HOLD: -1,
    TOUROKU_IRAI_NOT_YET_SENT: 0,
    TOUROKU_IRAI_RESULT_OK: 1,
    TOUROKU_IRAI_RESULT_NG: 2,
    TORISAGE_RESULT_NG: 3,
    STOPPING_FOR_ERROR: 90,
    // fumei: -1,
};

const SWIMMY_VLM_STATUS_GUICODE = {
    [SWIMMY_VLM_STATUS.ON_HOLD]: 'W',
    [SWIMMY_VLM_STATUS.TOUROKU_IRAI_NOT_YET_SENT]: '0',
    [SWIMMY_VLM_STATUS.TOUROKU_IRAI_RESULT_OK]: '1',
    [SWIMMY_VLM_STATUS.TOUROKU_IRAI_RESULT_NG]: '2',
    [SWIMMY_VLM_STATUS.TORISAGE_RESULT_NG]: '3',
    [SWIMMY_VLM_STATUS.STOPPING_FOR_ERROR]: '90',
};

const SWIMMY_VLM_STATUS_NAME = {
    [SWIMMY_VLM_STATUS.ON_HOLD]: '待機中',
    [SWIMMY_VLM_STATUS.TOUROKU_IRAI_NOT_YET_SENT]: '未登録依頼',
    [SWIMMY_VLM_STATUS.TOUROKU_IRAI_RESULT_OK]: '登録依頼済OK',
    [SWIMMY_VLM_STATUS.TOUROKU_IRAI_RESULT_NG]: '登録依頼済NG',
    [SWIMMY_VLM_STATUS.TORISAGE_RESULT_NG]: '取り下げ',
    [SWIMMY_VLM_STATUS.STOPPING_FOR_ERROR]: '不明なエラー',
};

const SWIMMY_VLM_LOG_LINK_TYPE = {
    FILE: 'FILE',
    API_GATEWAY: 'API',
    FUMEI: '',
};

const SWIMMY_VLM_LOG_LINK_TYPE_NAME = {
    [SWIMMY_VLM_LOG_LINK_TYPE.FILE]: 'File',
    [SWIMMY_VLM_LOG_LINK_TYPE.API_GATEWAY]: 'API Gateway',
    [SWIMMY_VLM_LOG_LINK_TYPE.FUMEI]: '不明',
};

const SWIMMY_VLM_LOG_TRANSACTION_TYPE = {
    STATUS_CHECK: '000',
    SHINKI: '010',
    HENKOU: '020',
    HAISHI: '030',
    FUMEI: '',
};

const SWIMMY_VLM_LOG_TRANSACTION_TYPE_NAME = {
    [SWIMMY_VLM_LOG_TRANSACTION_TYPE.STATUS_CHECK]: 'ステータスチェック',
    [SWIMMY_VLM_LOG_TRANSACTION_TYPE.SHINKI]: '新規',
    [SWIMMY_VLM_LOG_TRANSACTION_TYPE.HENKOU]: '変更',
    [SWIMMY_VLM_LOG_TRANSACTION_TYPE.HAISHI]: '廃止',
    [SWIMMY_VLM_LOG_TRANSACTION_TYPE.FUMEI]: '不明',
};
module.exports = {
    SWIMMY_VLM_REQUEST_TYPES,
    SWIMMY_VLM_REQUEST_TYPE_NAME,
    SWIMMY_VLM_INTERNATIONAL_CALL,
    SWIMMY_VLM_REQUEST_TYPE_PRIORTY_KEY,
    VLM_LOWEST_API_PRIORITY,
    SWIMMY_VLM_STATUS,
    SWIMMY_VLM_STATUS_GUICODE,
    SWIMMY_VLM_STATUS_NAME,
    SWIMMY_VLM_LOG_LINK_TYPE,
    SWIMMY_VLM_LOG_LINK_TYPE_NAME,
    SWIMMY_VLM_LOG_TRANSACTION_TYPE,
    SWIMMY_VLM_LOG_TRANSACTION_TYPE_NAME,
};
