// config/opferrormsg.conf (必要であれば、confファイルを参考に、追加してください)
const opferrormsg = {
    990002: 'サーバにて処理できません。',

    991001: '正しくないパラメータが含まれています。',
    991002: 'この操作を実行する権限がありません。',
    991004: 'パスワード要件を満たしていません。',
    991005: '正しくないパラメータが含まれています。',
    991007: 'IDまたはパスワードが間違っています。',
    991008: 'パスワード要件を満たしていません。',
    991009: '正しくないパラメータが含まれています。',
    991010: 'この操作を実行する権限がありません。',
    991012: 'この操作を実行する権限がありません。',
    991013: '正しくないパラメータが含まれています。',
    991014: 'この操作を実行する権限がありません。',
    991015: 'ユーザID要件を満たしていません。',
    991016: '過去に使用されたユーザIDは再度使用できません。',
    991017: 'パスワード要件を満たしていません。',
    991018: '正しくないパラメータが含まれています。',
    991019: 'この操作を実行する権限がありません。',
    991020: '存在しないユーザIDが含まれています。',
    991021: 'パスワード要件を満たしていません。',
    991022: '正しくないパラメータが含まれています。',
    991023: 'この操作を実行する権限がありません。',
    991024: '正しくないパラメータが含まれています。',
    991025: '正しくないパラメータが含まれています。',

    992003: '正しくないパラメータが含まれています。',
    992004: '正しくないパラメータが含まれています。',
    992005: '店舗が存在しません。',
    992006: 'プランが存在しません。',
    992007: '回線オプションが存在しません。',
    992008: 'N番が存在しません。',
    992011: 'N番が正しくありません。',
    992013: '代表番号が存在しません。',

    993002: '正しくないパラメータが含まれています。',
    993003: 'SO-IDが見つかりません。',
    993004: '正しくないパラメータが含まれています。',
    993006: 'SO-IDが見つかりません。',
    993007: 'ALADIN-SOを実行できない工程です。',
    993008: '正しくないパラメータが含まれています。',
    993010: '不正な棚卸結果報告データです。',
    993011: '棚卸結果報告が既に実行されています。',
    993015: '回線情報が見つかりません。',
    993016: '整合性のチェックに失敗しました。',
    993017: '在庫情報と合致しません。',
    993018: '移動・変更が出来ない在庫ステータスです。',
    993019: '移動先が不正です。',
    993020: '在庫データのSIMステータスが不正です。',
    993021: '在庫データの回線番号が不正です。',
    993022: 'SIM契約種別が不正です。',

    994001: '在庫情報が見つかりません。',
    994002: 'ALADIN-SOが見つかりません。',
    994003: 'SO種別が新規受付ではりません。',
    994004: 'オーダ不備',
    994005: '在庫データのSIM用途が不正です。',
    994006: '在庫ステータスが不正です。',
    994007: '在庫データの店舗IDが不正です。',
    994008: '在庫データのSIM形状が不正です。',
    994009: 'N番に対応するカード用途とプランの整合性のチェックに失敗しました。',
    994010: 'N番に対応するカード用途とカードの整合性のチェックに失敗しました。',
    994011: '回線がテナントに所属していません',
    994013: 'SIMステータスが半黒です',
    994014: '不正な工程です',
    994015: '不正なSO種別です',
    994016: '予約キャンセル不可期間です',
    994018: '予約日が不正です。',
    994019: 'SIMオーダ種別が不正です。',
    994020: '配送先が不正です。',
    994021: 'テナントが不正です。',
    994023: '回線がテナントに所属しています',
    994030: '注文できる枚数を超えています。',
    994031: '代表番号がCSV内重複',
    994034: '個配申込受付番号が存在しません。',
    994035: '個配申込受付番号が不正です。',
    994036: '伝票番号が不正です。',
    994037: '個配申込受付番号がCSV内重複',
    994038: '伝票番号がCSV内重複',
    994039: '属性が不正です。',
    994040: '回線番号が不正です。',
    994041: '生年月日が不正です。',
    994042: '予約者名義（半角カナ）ではありません。',
    994043: '代表番号のフォーマットが不正です。',
    994045: '転送でんわと国際着信転送のオプションが不正です。',
    994046: '該当ファイルが存在しません。',
    994047: '回線オプションが一致しません。',
    994048: '配送先申込受付番号が存在しません。',
    994051: '店舗使用欄が20文字以上です。',
    994052: '対象回線の廃止オーダが受付中です。',
    994053: '廃止SOのS日以降のSOが存在しています。',
    994054: '回線番号とSO-IDはいずれか1つをご指定ください。',
    994055: '回線番号が不正です。',
    994056: 'SO-IDが不正です。',
    994057: 'テナントIDが不正です。',
    994058: 'ステータスが存在しません。',
    994059: '種別が存在しません。',
    994060: '投入日期間指定の終了日が開始日より過去の日付です。',
    994061: '完了日期間指定の終了日が開始日より過去の日付です。',
    994062: 'SO一覧の取得に失敗しました。',
    994064: 'SO予約キャンセルAPIの取得に失敗しました。',
    994065: '削除対象ユーザ種別の取得に失敗しました。',
    994068: '追加対象ユーザIDが既に存在しています。',
    994069: '追加対象テナントIDが存在しません。',
    994070: '過去３世代パスワードは再利用できません。',
    994071: '変更対象ユーザIDが存在しません。',
    994072: '削除対象ユーザ種別の取得に失敗しました。',
    994073: '削除対象ユーザをDBから削除に失敗しました。',
    994074: '削除対象ユーザIDが存在しません。',
    994075: 'ユーザIDを含むパスワードは設定できません。',
    994076: '追加対象パスワードが不正です',
    994077: '追加対象ユーザIDが不正です。',
    994078: '対象ユーザ種別が不正です。',
    994079: '対象テナントIDが不正です。',
    994080: 'ORDER BYで指定した列名が不正です。',
    994081: '検索対象回線が不正です。',
    994082: '回線検索取得に失敗しました。',
    994083: '回線検索のプラン名リストの取得に失敗しました。',
    994084: '回線詳細情報取得に失敗しました。',
    994085: 'created_atが不正です。',
    994086: 'アクティベートステータスが不正です。',
    994087: '予約日が不正です。',
    994088: '回線所属テナント情報の取得に失敗しました。',
    994089: 'SO実行が失敗しました。',
    994090: 'クーポンステータスが不正です。',
    994091: 'プランIDが不正です。',
    994092: 'オプションプランIDが不正です。',
    994093: 'プラン詳細情報取得に失敗しました。',
    994094: '回線取得に失敗しました。',
    994095: '対象回線のプラン変更オーダが予約中です。',

    994104: '補正日時が不正です',
    994105: '補正日時のフォーマットがYYYYMMDDではありません',

    994300: 'オーダー種別が不正です。',
    994301: '最後の工程が不正です。',
    994302: '補正パターンが不正です。',
    994303: '工程補正時刻が不正です。',

    996101: 'テナントIDが不正です。',
    996102: '年が不正です。',
    996103: 'MVNO回線情報がありません。',
    996104: 'MVNO回線分析の取得に失敗しました。',
    996105: '月が不正です。',
    996106: 'MVNO通信ユーザ分析の取得に失敗しました。',
    996107: 'MVNO通信プラン分析の取得に失敗しました。',
    996108: 'MVNO通信プランCSVの取得に失敗しました。',
    996109: 'プラン割合情報がありません。',
    996110: 'MVNOプラン分析の取得に失敗しました。',
    996111: 'MVNOプラン数CSVの取得に失敗しました。',

    996301: 'リクエストパラメータが不正です。',
    996302: 'レスポンスが不正です。',
    996303: 'プロセスが不正です。',
    996304: 'Noが不正です。',
    996305: '名が不正です。',
    996306: 'メソッドが不正です。',
    996307: 'Urlが不正です。',
    996308: 'デスクリプションが不正です。',
    996309: 'インデックスが不正です。',
    996310: 'ステータスが不正です。',
    996311: 'Noが既に存在しています。',
    996312: 'Docs追加APIの取得に失敗しました。',
    996313: 'DocsApi一覧が編集できません。',
    996314: 'Docs編集APIの取得に失敗しました。',
    996315: 'DocsApi一覧が削除できません。',
    996316: 'Docs削除APIの取得に失敗しました。',
    996317: 'DocsApi一覧の取得に失敗しました。',
    996318: 'コードが不正です。',
    996319: 'コンテントが不正です。',
    996320: 'コンテントが更新できません。',
    996321: 'Docs更新情報の取得に失敗しました。',
    996322: 'Docs情報がありません。',
    996323: 'Docs情報の取得に失敗しました。',
    996324: 'ステータスが不正です。',
    996325: 'oldNoが不正です。',

    996400: '正しくないパラメータが含まれています。',

    996500: '正しくないパラメータが含まれています。',
    996501: 'SwimmyAPIログの取得に失敗しました。',

    997001: 'SOはフルMVNO対象ではありません。',
    997002: '在庫情報はimsiが見つかりません。',
    997003: '在庫情報はpuk1が見つかりません。',
    997004: '在庫情報はpuk2が見つかりません。',
    997005: '正しくないパラメータが含まれています。',
    997007: '機能が使えません',
    997008: 'SOはライトMVNO対象ではありません。',
    997009: 'SIMステータスが黒ではありません。',
    997010: 'フルMVNOに対応するのをできません。',
    997011: '回線番号の利用状態はサスペンド中です。',
    997012: 'SIM種別が不正です。',
    997013: 'サスペンドSO又はサスペンド解除SOのS日以降のSOが存在しています。',
    997014: '残回線数が足りません。',

    997101: '古いパターン名が不正です。',
    997102: '新しいパターン名が不正です。',
    997103: 'パターン種類が不正です。',
    997104: '検索条件保存APIの取得に失敗しました。',
    997105: '検索パターン一覧APIの取得に失敗しました。',
    997106: 'ユーザ名が見つかりません。',
    997107: '電話番号が存在しません。',

    997201: 'リカバリのエラーが発生したため、工程が補正されませんでした。',

    997301: '5G回線用のSIMではありません。',
    997302: 'LTE回線用のSIMではありません。',
    997303: 'ネットワーク契約種別が不正です。',
    997304: '5G利用可能なテナントではありません。',
    997305: '半黒時にNW契約を変更することはできません。',
    997306: '5G専用プランを利用中の回線です。',
    997399: '回線番号マスク化が不正です。',

    997400: 'この操作を実行できないユーザIDが含まれています。',
    997404: '対象通信明細DLが不正です。',
    997405: 'プランとSIMオーダ種別の黒/半黒が不一致です。',
    997406: '回線が既に黒化されています。',

    997501: '同じ仮回線番号を持つSIMが複数存在します。',

    997601: 'eSIM利用可能なテナントではありません。',
    997602: 'EIDが存在しません。',
    997603: 'EIDのフォーマットが不正です。',
    997604: '転出元キャリアの値が不正です。',
    997605: 'プランとSIM種別・SIMオーダ種別のeSIMが不一致です。',
      // Aladin/Swimmy再送機能
    997700: '正しくないパラメータが含まれています。',
    997701: '重複しているAPIログIDが存在します。',
    997702: 'APIログIDが指定されていません。',
};

module.exports = {
    opferrormsg,
};
