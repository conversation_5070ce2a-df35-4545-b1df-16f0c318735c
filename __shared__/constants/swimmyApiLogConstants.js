const SwimmyApiLogStatus = {
    values: [
        { key: 'TOUROKU_IRAI_NOT_YET_SENT', status: 10, guiCode: '0', name: '未登録依頼' },
        { key: 'TOUROKU_IRAI_SENT_OK', status: 11, guiCode: '1', name: '登録依頼済OK' },
        { key: 'TOUROKU_IRAI_SENT_NG', status: 12, guiCode: '2', name: '登録依頼済NG' },
        { key: 'TOUROKU_YOUKYUU_RESPONSE_OK', status: 13, guiCode: '3', name: '登録要求OK' },
        { key: 'TOUROKU_YOUKYUU_RESPONSE_NG', status: 14, guiCode: '4', name: '登録要求NG' },
        { key: 'STOPPING_FOR_ERROR', status: 90, guiCode: '90', name: '不明なエラー' },
        { key: 'unknown', status: -1, guiCode: '', name: '' },
    ],
    get statuses() {
        return this.values.reduce((acc, cur) => {
            acc[cur.key] = cur.status;
            return acc;
        }, {});
    },
    get statusesByGuiCode() {
        return this.values.reduce((acc, cur) => {
            acc[cur.guiCode] = cur.status;
            return acc;
        }, {});
    },
    get namesByStatus() {
        return this.values.reduce((acc, cur) => {
            acc[cur.status] = cur.name;
            return acc;
        }, {});
    },
};

module.exports = { SwimmyApiLogStatus };
