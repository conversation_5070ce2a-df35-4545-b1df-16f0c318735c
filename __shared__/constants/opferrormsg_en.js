/** English version of opferrormsg */
const opferrormsg_en = {
    996301: 'Invalid RequestParams',
    996302: 'Invalid Response',
    996303: 'Invalid Process',
    996304: 'Invalid No',
    996305: 'Invalid Name',
    996306: 'Invalid Method',
    996307: 'Invalid Url',
    996308: 'Invalid Description',
    996309: 'Invalid Index',
    996310: 'Invalid Status',
    996311: 'No is already existed',
    996312: 'Cannot connect to api server',
    996313: 'Cannot edit docsApiList',
    996314: 'Cannot connect to api server',
    996315: 'Cannot delete docsApiList',
    996316: 'Cannot connect to api server',
    996317: 'Cannot connect to api server',
    996318: 'Invalid Code',
    996319: 'Invalid Content',
    996320: 'Cannot update content',
    996321: 'Cannot connect to api server',
    996322: 'Docs information is not found',
    996323: 'Cannot connect to api server',
    996324: 'Invalid Status',
    996325: 'Invalid oldNo',
};

module.exports = {
    opferrormsg_en,
};
