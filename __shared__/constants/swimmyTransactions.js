/** Swimmy API HTTP protocol */
const SWIMMY_API_HTTP = 'https://';

const SWIMMY_REQUEST_TYPES = {
    KUROKA: '1', //フルMVNO黒化
    SIM_SAIHAKKO: '2', //SIM再発行
    KAISEN_SUPPEND: '3', //利用休止
    KAISEN_SUPPEND_KAIJO: '4', //利用休止解除
    KAISEN_RIYOU_CHUUDAN: '5', //利用停止
    KAISEN_RIYOU_SAIKAI: '6', //利用停止解除
    ALADIN_KEKKA_TSUCHI: '7', //ALADIN結果通知
    MNP_TENSHUTSU_KANRYOU_TSUCHI: '8', //MNP転出済通知
    KUROKA_KANRYOU_TSUCHI: '9', // 黒化済通知

    /** @deprecated STEP25: 不要機能の削除 */
    OTA_KEKKA_TSUCHI: '10', //OTA結果通知

    KAISEN_OPTION_HENKOU_KEKKA_TSUCHI: '11', //回線オプション変更結果通知
    SIM_SAIHAKKOU_KEKKA_TSUCHI: '12', //SIM再発行結果通知

    /** @deprecated STEP25: 不要機能の削除 */
    ESIM_KEKKA_TSUCHI: '13', //eSIM結果通知

    fumei: '-1',
};

const SWIMMY_REQUEST_TYPE_NAMES = {
    [SWIMMY_REQUEST_TYPES.KUROKA]: 'フルMVNO黒化',
    [SWIMMY_REQUEST_TYPES.SIM_SAIHAKKO]: 'SIM再発行',
    [SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND]: '利用休止',
    [SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND_KAIJO]: '利用休止解除',
    [SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_CHUUDAN]: '利用停止',
    [SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_SAIKAI]: '利用停止解除',
    [SWIMMY_REQUEST_TYPES.ALADIN_KEKKA_TSUCHI]: 'ALADIN結果通知',
    [SWIMMY_REQUEST_TYPES.MNP_TENSHUTSU_KANRYOU_TSUCHI]: 'MNP転出済通知',
    [SWIMMY_REQUEST_TYPES.KUROKA_KANRYOU_TSUCHI]: '黒化済通知',
    [SWIMMY_REQUEST_TYPES.OTA_KEKKA_TSUCHI]: 'OTA結果通知',
    [SWIMMY_REQUEST_TYPES.KAISEN_OPTION_HENKOU_KEKKA_TSUCHI]: '回線オプション変更結果通知',
    [SWIMMY_REQUEST_TYPES.SIM_SAIHAKKOU_KEKKA_TSUCHI]: 'SIM再発行結果通知',
    [SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI]: 'eSIM結果通知',
    [SWIMMY_REQUEST_TYPES.fumei]: '',
};

/** `appAttributeValue` property of SWIMMY_REQUEST_TYPES */
const SWIMMY_APP_ATTRIBUTE_VALUE = {
    [SWIMMY_REQUEST_TYPES.KUROKA]: '012',
    [SWIMMY_REQUEST_TYPES.SIM_SAIHAKKO]: '013',
    [SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND]: '014',
    [SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND_KAIJO]: '015',
    [SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_CHUUDAN]: '016',
    [SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_SAIKAI]: '017',
    [SWIMMY_REQUEST_TYPES.ALADIN_KEKKA_TSUCHI]: '090',
    [SWIMMY_REQUEST_TYPES.MNP_TENSHUTSU_KANRYOU_TSUCHI]: '091',
    [SWIMMY_REQUEST_TYPES.KUROKA_KANRYOU_TSUCHI]: '092',
    [SWIMMY_REQUEST_TYPES.OTA_KEKKA_TSUCHI]: '093',
    [SWIMMY_REQUEST_TYPES.KAISEN_OPTION_HENKOU_KEKKA_TSUCHI]: '094',
    [SWIMMY_REQUEST_TYPES.SIM_SAIHAKKOU_KEKKA_TSUCHI]: '095',
    [SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI]: '096',
    [SWIMMY_REQUEST_TYPES.fumei]: '',
};

const SWIMMY_COMPLETE_RESULT = {
    /** 処理完了 */
    OK: '00',
    /** 処理NG */
    NG: '01',
    /** 不明 */
    fumei: '-1',
};

/**
 * @deprecated Use SwimmyApiLogStatus.statuses in swimmyApiLogConstants.js instead
 */
const SWIMMY_API_LOG_STATUS = {
    TOUROKU_IRAI_NOT_YET_SENT: 10, // guiCode: '10', name: '未登録依頼'
    TOUROKU_IRAI_SENT_OK: 11, // guiCode: '11', name: '登録依頼済OK'
    TOUROKU_IRAI_SENT_NG: 12, // guiCode: '12', name: '登録依頼済NG'
    TOUROKU_YOUKYUU_RESPONSE_OK: 13, // guiCode: '13', name: '登録要求OK'
    TOUROKU_YOUKYUU_RESPONSE_NG: 14, // guiCode: '14', name: '登録要求NG'
    STOPPING_FOR_ERROR: 90, // guiCode: '90', name: '不明エラー'
    unknown: -1, // guiCode: '', name: ''
};

/**
 * @deprecated Use SwimmyApiLogStatus in swimmyApiLogConstants.js instead
 */
const SWIMMY_API_LOG_STATUS_GUI_CODE = {
    TOUROKU_IRAI_NOT_YET_SENT: '0',
    TOUROKU_IRAI_SENT_OK: '1',
    TOUROKU_IRAI_SENT_NG: '2',
    TOUROKU_YOUKYUU_RESPONSE_OK: '3',
    TOUROKU_YOUKYUU_RESPONSE_NG: '4',
    STOPPING_FOR_ERROR: '90',
    unknown: '',
};

/**
 * @deprecated Use SwimmyApiLogStatus.statusesByGuiCode in swimmyApiLogConstants.js instead
 */
const SWIMMY_API_LOG_STATUS_BY_GUI_CODE = {
    [SWIMMY_API_LOG_STATUS_GUI_CODE.TOUROKU_IRAI_NOT_YET_SENT]: SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_NOT_YET_SENT,
    [SWIMMY_API_LOG_STATUS_GUI_CODE.TOUROKU_IRAI_SENT_OK]: SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_SENT_OK,
    [SWIMMY_API_LOG_STATUS_GUI_CODE.TOUROKU_IRAI_SENT_NG]: SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_SENT_NG,
    [SWIMMY_API_LOG_STATUS_GUI_CODE.TOUROKU_YOUKYUU_RESPONSE_OK]: SWIMMY_API_LOG_STATUS.TOUROKU_YOUKYUU_RESPONSE_OK,
    [SWIMMY_API_LOG_STATUS_GUI_CODE.TOUROKU_YOUKYUU_RESPONSE_NG]: SWIMMY_API_LOG_STATUS.TOUROKU_YOUKYUU_RESPONSE_NG,
    [SWIMMY_API_LOG_STATUS_GUI_CODE.STOPPING_FOR_ERROR]: SWIMMY_API_LOG_STATUS.STOPPING_FOR_ERROR,
    [SWIMMY_API_LOG_STATUS_GUI_CODE.unknown]: SWIMMY_API_LOG_STATUS.unknown,
};

const SWIMMY_API_LOG_COMPLETE_STATUS = {
    IN_PROCESS: '未終了',
    COMPLETE_NG: 'NG終了',
    COMPLETE_OK: '正常終了',
    UNKNOWN: '不明',
};

const SWIMMY_REQUEST_TYPE_PRIORITY_KEY = {
    [SWIMMY_REQUEST_TYPES.KUROKA]: 'KUROKA',
    [SWIMMY_REQUEST_TYPES.SIM_SAIHAKKO]: 'SIM_SAIHAKKO',
    [SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND]: 'KAISEN_SUPPEND',
    [SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND_KAIJO]: 'KAISEN_SUPPEND_KAIJO',
    [SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_CHUUDAN]: 'KAISEN_RIYOU_CHUUDAN',
    [SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_SAIKAI]: 'KAISEN_RIYOU_SAIKAI',
    [SWIMMY_REQUEST_TYPES.ALADIN_KEKKA_TSUCHI]: 'ALADIN_KEKKA_TSUCHI',
    [SWIMMY_REQUEST_TYPES.MNP_TENSHUTSU_KANRYOU_TSUCHI]: 'MNP_TENSHUTSU_KANRYOU_TSUCHI',
    [SWIMMY_REQUEST_TYPES.KUROKA_KANRYOU_TSUCHI]: '',
    [SWIMMY_REQUEST_TYPES.OTA_KEKKA_TSUCHI]: '',
    [SWIMMY_REQUEST_TYPES.KAISEN_OPTION_HENKOU_KEKKA_TSUCHI]: '',
    [SWIMMY_REQUEST_TYPES.SIM_SAIHAKKOU_KEKKA_TSUCHI]: '',
    [SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI]: '',
    [SWIMMY_REQUEST_TYPES.fumei]: '',
};

const SWIMMY_API_LOG_LOWEST_PRIORITY = 100;

const SWIMMY_API_LOG_SEARCH_TYPE = {
    REQUEST_ORDER_ID: 0,
    KAISEN_NO: 1,
    SWIMMY_RECEIPT_ID: 2,
};

const SWIMMY_API_LOG_SORT_BY_FIELDS = {
    request_order_id: 'requestOrderId',
    order_type: 'swimmyType',
    tenant_id: 'tenantId',
    kaisen_no: 'kaisenNo',
    swimmy_receipt_id: 'swimmyReceiptId',
    start_date: 'createdDateTime',
};

// grouped constants from SwimmyApiLog.scala (might better be ungrouped)
const SWIMMY_CONSTANTS = {
    APP_ATTRIBUTE_CODE_1: 'M0000002',
    APP_ATTRIBUTE_CODE_2: 'M0000012',
    PROCESS_TYPE: '03',
    INPUT_FROM_TYPE: '01',
    ORDER_TYPE: '11',
    APP_STATUS: '15',
    SALES_CHANNEL_NAME: '*',
    REQUEST_FROM_SYSTEM_ID: 'MAP',
};

const ALADIN_KEKKA_TSUCHI_TYPE = {
    /** ALADIN_KEKKA_TSUCHI_KAISEN_TSUIKA_TYPE */
    KAISEN_TSUIKA: '【フルMVNO】回線追加',
    /** ALADIN_KEKKA_TSUCHI_KAISEN_HAISHI_TYPE */
    KAISEN_HAISHI: '【フルMVNO】回線廃止（即時）',
};

const SWIMMY_KOUTEI_IDS = {
    UKETSUKE_CHECK_OK: 2,
    ALADIN_PROCESSING: 5,
    ALADIN_KOUJI_NG: 11,
    ALADIN_KOUJI_OK: 12,
    SWIMMY_PROCESSING: 20,
    SWIMMY_KOUJI_NG: 21,
    SWIMMY_TSUUCHI_ALADIN_NG: 22,
    SWIMMY_TSUUCHI_ALADIN_OK: 23,
};

const SWIMMY_KOUTEI_ID_NAME = {
    [SWIMMY_KOUTEI_IDS.UKETSUKE_CHECK_OK]: '受付チェックOK',
    [SWIMMY_KOUTEI_IDS.ALADIN_PROCESSING]: 'ALADIN実施中',
    [SWIMMY_KOUTEI_IDS.ALADIN_KOUJI_NG]: 'ALADIN工事NG',
    [SWIMMY_KOUTEI_IDS.ALADIN_KOUJI_OK]: 'ALADIN工事OK',
    [SWIMMY_KOUTEI_IDS.SWIMMY_PROCESSING]: 'Swimmy連携中',
    [SWIMMY_KOUTEI_IDS.SWIMMY_KOUJI_NG]: 'Swimmy連携NG',
    [SWIMMY_KOUTEI_IDS.SWIMMY_TSUUCHI_ALADIN_NG]: 'Swimmy通知済[ALADIN工事NG]',
    [SWIMMY_KOUTEI_IDS.SWIMMY_TSUUCHI_ALADIN_OK]: 'Swimmy通知済[ALADIN工事OK]',
};

const SWIMMY_SYORI_KEKKA = {
    /** SYORI_KEKKA_OK */
    OK: '00',
    /** SYORI_KEKKA_NG */
    NG: '01',
    /** COMMON_SYORI_KEKKA_OK */
    COMMON_OK: '0001',
    /** COMMON_SYORI_KEKKA_NG */
    COMMON_NG: '0002',
};

module.exports = {
    SWIMMY_API_HTTP,
    SWIMMY_REQUEST_TYPES,
    SWIMMY_REQUEST_TYPE_NAMES,
    SWIMMY_APP_ATTRIBUTE_VALUE,
    SWIMMY_COMPLETE_RESULT,
    SWIMMY_API_LOG_STATUS,
    SWIMMY_API_LOG_STATUS_BY_GUI_CODE,
    SWIMMY_API_LOG_COMPLETE_STATUS,
    SWIMMY_REQUEST_TYPE_PRIORITY_KEY,
    SWIMMY_API_LOG_LOWEST_PRIORITY,
    SWIMMY_API_LOG_SEARCH_TYPE,
    SWIMMY_API_LOG_SORT_BY_FIELDS,
    SWIMMY_CONSTANTS,
    ALADIN_KEKKA_TSUCHI_TYPE,
    SWIMMY_KOUTEI_IDS,
    SWIMMY_KOUTEI_ID_NAME,
    SWIMMY_SYORI_KEKKA,
};
