const { CARD_KEIJO } = require('./aladinTransactions');

const LINE_NO_REGEX = /^([0-9]{11}|020[0-9]{11})$/;
const DAIHYO_BANGO_REGEX = /^([0-9]{11}|[0-9]{14})$/;
const SIM_TYPES = {
    HYOJUN: '標準SIM',
    MICRO: 'microSIM',
    NANO: 'nanoSIM',
    MULTI: 'マルチカットSIM',
};
// eSIM type (can't be added to `SIM_TYPES` const)
const SIM_TYPES_ESIM = 'eSIM';

const SIM_TYPES_FULL = {
    MULTI_N: 'フルマルチカットSIM(N)',
    MULTI_I: 'フルマルチカットSIM(I)',
    CHIP_N: 'フルchipSIM(N)',
    CHIP_I: 'フルchipSIM(I)',
};

// for `simShape` field of AladinRequestOrder (`*_SIM_CARD`)
const SIM_SHAPE = {
    /** 通常SIMカード(標準SIM) */
    TSUJOU_SIM_CARD: '0',
    /** miniSIMカード(microSIM) */
    MICRO_SIM_CARD: '1',
    /** nanoSIMカード */
    NANO_SIM_CARD: '3',
    MULTI_SIM_CARD: '4',
};

/**
 * convertToDocomoSimType
 *
 * マルチカットSIMの場合はnanoSIM扱いにする
 * (https://mobilus.backlog.jp/view/MVNO_N_M-980#)
 */
const DOCOMO_SIM_TYPE = {
    [SIM_SHAPE.TSUJOU_SIM_CARD]: SIM_SHAPE.TSUJOU_SIM_CARD,
    [SIM_SHAPE.MICRO_SIM_CARD]: SIM_SHAPE.MICRO_SIM_CARD,
    [SIM_SHAPE.NANO_SIM_CARD]: SIM_SHAPE.NANO_SIM_CARD,
    [SIM_SHAPE.MULTI_SIM_CARD]: SIM_SHAPE.NANO_SIM_CARD,
};

// val HANKURO_VALUE_*
const SIM_ORDER_TYPES = {
    SHIRO: '白',
    KURO: '黒',
    HANKURO: '半黒',
    HANKURO_MNP: '半黒MNP',
    OTA: 'OTA',
    ESIM: SIM_TYPES_ESIM,
};
const SIM_ORDER_TYPES_KURO_HANKURO = '黒/半黒';

/** SIM再発行 - for request validation */
const SIM_ORDER_TYPES_REISSUE = {
    KURO: '黒',
    HANKURO: '半黒',
    OTA: 'OTA',
    ESIM: SIM_TYPES_ESIM,
};

const SYM_TYPE_TO_ALADIN_CARD_KEIJO = {
    [SIM_TYPES.HYOJUN]: CARD_KEIJO.HYOUJYUN,
    [SIM_TYPES.MICRO]: CARD_KEIJO.MICRO,
    [SIM_TYPES.NANO]: CARD_KEIJO.NANO,
    [SIM_TYPES.MULTI]: CARD_KEIJO.NANO,
    [SIM_TYPES_ESIM]: CARD_KEIJO.ESIM,
};

const SIM_STATUS = {
    SHIRO: '白',
    OTA: 'OTA',
    HANKURO: '半黒',
    KURO: '黒',
    SHINKI_HANKURO: '新規半黒',
    SHINKI_KURO: '新規黒',
};

const SIM_YOUTO = {
    ALADIN_MNP: 'ALADIN-MNP',
    OTA: 'OTA-MNP',
    HANKURO_MNP: '半黒MNP',
    OTA_SIM: 'OTA-SIM',
    WHITE_SIM: '白-SIM',
    COM_SIM: 'Com-SIM',
};

const CONTRACT_TYPES = {
    DATA: 'データ',
    SMS: 'SMS',
    VOICE: '音声',
    THREE_G: '3G',
    THREE_G_SMS: '3G(SMS)',
    LTE: 'LTE',
    LTE_SMS: 'LTE(SMS)',
    LTE_VOICE: 'LTE(音声)',
    FIVE_G_NSA: '5G(NSA)',
    FIVE_G_NSA_VOICE: '5G(NSA)(音声)',
    FIVE_G_SA: '5G(SA)',
};

/** 旧メソッド: getContractTypeByNWType */
const CONTRACT_TYPE_NW_MAP = {
    [CONTRACT_TYPES.THREE_G]: 'SA',
    [CONTRACT_TYPES.THREE_G_SMS]: 'SB',
    [CONTRACT_TYPES.LTE]: 'SC',
    [CONTRACT_TYPES.LTE_SMS]: 'SD',
    [CONTRACT_TYPES.LTE_VOICE]: 'SE',
    [CONTRACT_TYPES.FIVE_G_NSA]: 'SH',
    [CONTRACT_TYPES.FIVE_G_NSA_VOICE]: 'SJ',
};

const CONTRACT_TYPE_REGEX = /^S(?![FG])[A-J]/;

const NOTIFY_PATTERN = {
    SWIMMY_V2: '1',
    SWIMMY_V3: '2',
};

const CONTRACT_TYPES_FULL = {
    LTE: 'フルLTE',
    LTE_SMS: 'フルLTE(SMS)',
};

const SIM_ZAIKO_SEARCH_TYPES = {
    SIM_NUMBER: 0, // SIM番号
    KAISEN_NO: 1, // 回線番号
    KARI_KAISEN_NO: 2, // 仮回線番号
    TEMPO_NAME: 3, // 店舗名
};

module.exports = {
    LINE_NO_REGEX,
    DAIHYO_BANGO_REGEX,
    SIM_TYPES,
    SIM_TYPES_ESIM,
    SIM_TYPES_FULL,
    SIM_SHAPE,
    DOCOMO_SIM_TYPE,
    SIM_ORDER_TYPES,
    SIM_ORDER_TYPES_REISSUE,
    SYM_TYPE_TO_ALADIN_CARD_KEIJO,
    SIM_STATUS,
    SIM_YOUTO,
    CONTRACT_TYPES,
    CONTRACT_TYPE_NW_MAP,
    CONTRACT_TYPE_REGEX,
    NOTIFY_PATTERN,
    CONTRACT_TYPES_FULL,
    SIM_ORDER_TYPES_KURO_HANKURO,
    SIM_ZAIKO_SEARCH_TYPES,
};
