const SWIMMY_SENDER_SYSTEM_ID = ['9a1z'];
const COCN_TENANT_ID = 'CON000';
const SUPER_TENANT_ID = 'ZZZ000';
const SHIN_MVNO_TENANTS = ['BON000', 'UNM000', 'ICX000', 'ICS000', 'RKM000'];
const FIVEG_CONTRACT_TYPES = ['SH', 'SJ'];
const M2M_SOUSA_SERVICE = '0D0021';
/** 処理完了通知テナント */
const NOTIFY_RESULT_TENANTS = ['FUM000'];
/** 回線廃止即時 - フルMVNOテナント */
const KAISEN_HAISHI_SOKUJI_TENANT = ['FUM000'];
const KAISEN_HAISHI_SOKUJI_CONTRACT_TYPES = ['SF', 'SG'];
const SWIMMY_CONTRACT_TYPE = {
    /** SwimmyContractTypeLTEFullMVNO */
    SF: { name: 'LTE/フルMVNO', aladinContractType: 'PCJ42' },
    /** SwimmyContractTypeLTESMSFullMVNO */
    SG: { name: 'LTE(SMS)/フルMVNO', aladinContractType: 'PCJ42' },
};

const DETAIL_CONTRACT_TYPES = [
    { code: 'SC', name: 'LTE', type: 'PCJ04' },
    { code: 'SD', name: 'LTE(SMS)', type: 'PCJ04' },
    { code: 'SE', name: 'LTE(音声)', type: 'PCJ04' },
    { code: 'SH', name: '5G(NSA)', type: 'PCS04' },
    { code: 'SJ', name: '5G(NSA)(音声)', type: 'PCS04' },
];

/**
 * config SIMSaihakkouKekkaNotifyTenantList
 * STEP22でCON000テナントについてSwimmyにSIM再発行結果通知のため
 */
const SIM_REISSUE_NOTIFY_TENANT_LIST = ['CON000'];

/** STEP23.0「通知パターン」テナント */
const NOTIFY_PATTERN_TENANT_LIST = ['CON000'];

/** superTempoOperatorManagementId */
const SUPER_TEMPO_OPERATOR_MANAGEMENT_ID = 'xx';

module.exports = {
    SWIMMY_SENDER_SYSTEM_ID,
    COCN_TENANT_ID,
    SHIN_MVNO_TENANTS,
    FIVEG_CONTRACT_TYPES,
    M2M_SOUSA_SERVICE,
    NOTIFY_RESULT_TENANTS,
    KAISEN_HAISHI_SOKUJI_TENANT,
    KAISEN_HAISHI_SOKUJI_CONTRACT_TYPES,
    SWIMMY_CONTRACT_TYPE,
    DETAIL_CONTRACT_TYPES,
    SIM_REISSUE_NOTIFY_TENANT_LIST,
    NOTIFY_PATTERN_TENANT_LIST,
    SUPER_TENANT_ID,
    SUPER_TEMPO_OPERATOR_MANAGEMENT_ID
};
