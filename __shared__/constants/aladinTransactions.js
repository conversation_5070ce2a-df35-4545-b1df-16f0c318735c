const TRANSACTION_TYPE = {
    SHINKI_MOUSHIKOMI: '01',
    SHINKI_MOUSHIKOMI_HANKURO: 'A2',
    KISETSU_HENKOU: '02',
    RIYOU_CHUUDAN_SAIKAI: '21',
    RIYOU_CHUUDAN: '100', 
    RIYOU_SAIKAI: '200',
    MNP_YOYAKU_KEIZOKU_RIYOU: 'A5',
    MNP_YOYAKU_YOYAKU_KAIJO: 'A6',
    MOSHIDE_KAIYAKU: '49',
    KENSAKU: 'M01',
    KAITSU: 'M02',
    MNP_KAIYAKU_TSUCHI: 'M04',
    OTA_KAITSU: 'M05',
};

const TRANSACTION_TYPE_NAME = {
    [TRANSACTION_TYPE.SHINKI_MOUSHIKOMI]: '新規申込',
    [TRANSACTION_TYPE.SHINKI_MOUSHIKOMI_HANKURO]: '新規申込（半黒）',
    [TRANSACTION_TYPE.KISETSU_HENKOU]: '既設変更',
    [TRANSACTION_TYPE.RIYOU_CHUUDAN_SAIKAI]: '利用中断・再開',
    [TRANSACTION_TYPE.RIYOU_CHUUDAN]: '利用中断',
    [TRANSACTION_TYPE.RIYOU_SAIKAI]: '利用再開',
    [TRANSACTION_TYPE.MNP_YOYAKU_KEIZOKU_RIYOU]: 'MNP予約（継続利用）',
    [TRANSACTION_TYPE.MNP_YOYAKU_YOYAKU_KAIJO]: 'MNP予約解除',
    [TRANSACTION_TYPE.MOSHIDE_KAIYAKU]: '申出解約',
    [TRANSACTION_TYPE.KENSAKU]: '検索',
    [TRANSACTION_TYPE.KAITSU]: '開通',
    [TRANSACTION_TYPE.MNP_KAIYAKU_TSUCHI]: 'MNP解約通知',
    [TRANSACTION_TYPE.OTA_KAITSU]: 'OTA開通',
};

const ORDER_TYPE = {
    SHINKI_MOUSHIKOMI_TSUJOU_KAITSU: '10',
    SHINKI_MOUSHIKOMI_HANKURO_ROM_SAKUSEI: '11',
    SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU: '12',
    SHINKI_MOUSHIKOMI_OTA_ROM: '13',
    SHINKI_MOUSHIKOMI_DAI_DAIHYOU: '14',
    SHINKI_MOUSHIKOMI_DAIHYOU: '15',
    SHINKI_MOUSHIKOMI_KO_KAISEN: '16',
    SHINKI_MOUSHIKOMI_KO_KAISEN_YUUSEN: '17',
    SHINKI_MOUSHIKOMI_ESIM_KAITSU: '18', //ESIM
    MNP_TENNYU_UKETSUKE_TSUJOU_KAITSU: '20',
    MNP_TENNYU_UKETSUKE_HANKURO_ROM_SAKUSEI: '21',
    MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU: '22',
    MNP_TENNYU_UKETSUKE_OTA_ROM_KAITSU: '23',
    MNP_TENNYU_UKETSUKE_ESIM_KAITSU: '24', //ESIM
    KISETSU_HENKOU_KAISEN_OPTION: '30',
    KISETSU_HENKOU_ANSHO_BANGOU: '31',
    KISETSU_HENKOU_RIYOU_CHUUDAN_SAIKAI: '32',
    KISETSU_HENKOU_MNP_YOYAKU_KAIZOKU_RIYOU: '33',
    KISETSU_HENKOU_MNP_YOYAKU_KAIJO: '34',
    KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM: '35',
    KISETSU_HENKOU_SAIHAKKOU_OTA_ROM_KAITSU: '36',
    KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU: '37',
    KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU: '38',
    KISETSU_HENKOU_CONTRACT_CHANGE: '39',
    KAISEN_HAISHI: '40',
    KAISEN_HAISHI_SOKUJI: '41',
    KAISEN_HAISHI_FULL_MVNO: '42',
    KISETSU_HENKOU_SAIHAKKOU_ESIM_KAITSU: '43', //ESIM
    KISETSU_HENKOU_RIYOU_CHUUDAN: '44', 
    KISETSU_HENKOU_RIYOU_SAIKAI: '45',
    KISETSU_HENKOU_RIYOU_CHUUDAN_SOKUJI: '46',
    KENSAKU: '50',
    OTA_IKKATSU_KAITSU: '60',
    OTA_IKKATSU_HAISHI: '61',
    IKKATSU_SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU: '62',
    IKKATSU_MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU: '63',
    IKKATSU_KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU: '64',
};

const ORDER_TYPE_NAME = {
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_TSUJOU_KAITSU]: '新規申込（通常開通）',
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_HANKURO_ROM_SAKUSEI]: '新規申込（半黒ROM作成）',
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU]: '新規申込（半黒ROM開通）',
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_OTA_ROM]: '新規申込（OTA-ROM使用）',

    [ORDER_TYPE.SHINKI_MOUSHIKOMI_DAI_DAIHYOU]: '新規申込（大代表）',
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_DAIHYOU]: '新規申込（代表）',
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_KO_KAISEN]: '子回線作成(3)',
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_KO_KAISEN_YUUSEN]: '子回線作成(4)',
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_ESIM_KAITSU]: '新規申込（eSIM開通）', //ESIM

    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_TSUJOU_KAITSU]: 'MNP転入受付（通常開通）',
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_SAKUSEI]: 'MNP転入受付（半黒ROM作成）',
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU]: 'MNP転入受付（半黒ROM開通）',
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_OTA_ROM_KAITSU]: 'MNP転入受付（OTA-ROM開通）',
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_ESIM_KAITSU]: 'MNP転入受付（eSIM開通）', //ESIM

    [ORDER_TYPE.KISETSU_HENKOU_KAISEN_OPTION]: '既設変更：回線オプション',
    [ORDER_TYPE.KISETSU_HENKOU_ANSHO_BANGOU]: '既設変更：暗証番号',
    [ORDER_TYPE.KISETSU_HENKOU_RIYOU_CHUUDAN_SAIKAI]: '利用中断・再開',
    [ORDER_TYPE.KISETSU_HENKOU_MNP_YOYAKU_KAIZOKU_RIYOU]: 'MNP転出予約',
    [ORDER_TYPE.KISETSU_HENKOU_MNP_YOYAKU_KAIJO]: 'MNP予約解除',
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM]: 'カード再発行（白ROM＜PINロック解除なし＞）',
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_OTA_ROM_KAITSU]: 'カード再発行（OTA-ROM開通＜PINロック解除なし＞）',
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU]: 'カード再発行（白ROM未開通＜PINロック解除なし＞）',
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU]:
        'カード再発行（白ROM未開通=>開通＜PINロック解除なし＞）',
    [ORDER_TYPE.KISETSU_HENKOU_CONTRACT_CHANGE]: '契約変更',

    [ORDER_TYPE.KAISEN_HAISHI]: '回線廃止',
    [ORDER_TYPE.KAISEN_HAISHI_SOKUJI]: '回線廃止（即時）',
    [ORDER_TYPE.KAISEN_HAISHI_FULL_MVNO]: '回線廃止（フルMVNO）',
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_ESIM_KAITSU]: 'カード再発行（eSIM開通＜PINロック解除なし＞）', //ESIM
    [ORDER_TYPE.KENSAKU]: '検索',
    [ORDER_TYPE.OTA_IKKATSU_KAITSU]: '一括OTA開通',
    [ORDER_TYPE.OTA_IKKATSU_HAISHI]: '一括OTA廃止',
    [ORDER_TYPE.IKKATSU_SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU]: '一括新規申込（半黒ROM開通）',
    [ORDER_TYPE.IKKATSU_MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU]: '一括MNP転入受付（半黒ROM開通）',
    [ORDER_TYPE.IKKATSU_KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU]: '一括カード再発行（白ROM未開通=>開通＜PINロック解除なし＞）',

    [ORDER_TYPE.KISETSU_HENKOU_RIYOU_CHUUDAN]: '利用中断',
    [ORDER_TYPE.KISETSU_HENKOU_RIYOU_SAIKAI]: '利用再開',
    [ORDER_TYPE.KISETSU_HENKOU_RIYOU_CHUUDAN_SOKUJI]: '利用中断（即時利用停止）',

    fumei: 'undefined order type',
};

const FM_ORDER_TYPE_LIST = [
    ORDER_TYPE.SHINKI_MOUSHIKOMI_DAI_DAIHYOU,
    ORDER_TYPE.SHINKI_MOUSHIKOMI_DAIHYOU,
    ORDER_TYPE.SHINKI_MOUSHIKOMI_KO_KAISEN,
    ORDER_TYPE.SHINKI_MOUSHIKOMI_KO_KAISEN_YUUSEN,
    ORDER_TYPE.KAISEN_HAISHI_SOKUJI,
    ORDER_TYPE.KAISEN_HAISHI_FULL_MVNO,
];

const CARD_KEIJO = {
    HYOUJYUN: '0',
    MICRO: '1',
    NANO: '3',
    ESIM: '8',
};

const ZOKUSEI_CODE = {
    男性: '1',
    女性: '2',
    法人: '3',
    その他: '9',
    //外部API対応
    男: '1',
    女: '2',
};

/* A constant that is used to set the lowest priority for the API. */
const LOWEST_API_PRIORITY = 100;

/* A map of order type to a string. */
const PRIORITY_KEY_STRING = new Map([
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_TSUJOU_KAITSU, 'shinkiMoushikomiTsujouKaitsu'],
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_HANKURO_ROM_SAKUSEI, 'shinkiMoushikomiHankuroRomSakusei'],
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU, 'shinkiMoushikomiHankuroRomKaitsu'],
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_OTA_ROM, 'shinkiMoushikomiOTAROM'],
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_TSUJOU_KAITSU, 'mnpTennyuUketsukeTsujouKaitsu'],
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_SAKUSEI, 'mnpTennyuUketsukeHankuroRomSakusei'],
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU, 'mnpTennyuUketsukeHankuroRomKaitsu'],
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_OTA_ROM_KAITSU, 'mnpTennyuUketsukeOtaRomKaitsu'],
    [ORDER_TYPE.KISETSU_HENKOU_MNP_YOYAKU_KAIZOKU_RIYOU, 'kisetsuHenkouMnpYoyakuKaizokuRiyou'],
    [ORDER_TYPE.KISETSU_HENKOU_KAISEN_OPTION, 'kisetsuHenkouKaisenOption'],
    [ORDER_TYPE.KISETSU_HENKOU_ANSHO_BANGOU, 'kisetsuHenkouAnshoBangou'],
    [ORDER_TYPE.KISETSU_HENKOU_RIYOU_CHUUDAN_SAIKAI, 'kisetsuHenkouRiyouChuudanSaikai'],
    [ORDER_TYPE.KISETSU_HENKOU_MNP_YOYAKU_KAIJO, 'kisetsuHenkouMnpYoyakuKaijo'],
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM, 'kisetsuHenkouSaihakkouShiroRom'],
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_OTA_ROM_KAITSU, 'kisetsuHenkouSaihakkouOtaRomKaitsu'],
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU, 'kisetsuHenkouSaihakkouShiroRomMikaitsu'],
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU, 'kisetsuHenkouSaihakkouShiroRomMikaitsuToKaitsyu'],
    [ORDER_TYPE.KAISEN_HAISHI, 'kaisenHaishi'],
    [ORDER_TYPE.KENSAKU, 'kensaku'],
    [ORDER_TYPE.KISETSU_HENKOU_CONTRACT_CHANGE, 'keiyakuHenkou'],
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_DAI_DAIHYOU, 'shinkiMoushiKomiDaiDaihyou'],
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_DAIHYOU, 'shinkiMoushiKomiDaihyou'],
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_KO_KAISEN, 'shinkiMoushiKomiKoKaisen'],
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_KO_KAISEN_YUUSEN, 'shinkiMoushiKomiKoKaisenYuusen'],
    [ORDER_TYPE.KAISEN_HAISHI_SOKUJI, 'kaisenHaishiSokuji'],
    [ORDER_TYPE.OTA_IKKATSU_KAITSU, 'ikkatsuOTAKaitsu'],
    [ORDER_TYPE.OTA_IKKATSU_HAISHI, 'ikkatsuOTAHaishi'],
    [ORDER_TYPE.SHINKI_MOUSHIKOMI_ESIM_KAITSU, 'eSIMIn'],
    [ORDER_TYPE.MNP_TENNYU_UKETSUKE_ESIM_KAITSU, 'eSIMMNPIn'],
    [ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_ESIM_KAITSU, 'eSIMReissue'],
    [ORDER_TYPE.IKKATSU_SHINKI_MOUSHIKOMI_HANKURO_ROM_KAITSU, 'ikkatsuShinkiMoushikomiHankuroRomKaitsu'],
    [ORDER_TYPE.IKKATSU_MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU, 'ikkatsuMnpTennyuUketsukeHankuroRomKaitsu'],
    [ORDER_TYPE.IKKATSU_KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU, 'ikkatsuKisetsuHenkouSaihakkouShiroRomMikaitsuToKaitsyu'],
    [ORDER_TYPE.KISETSU_HENKOU_RIYOU_CHUUDAN, 'kisetsuHenkouRiyouChuudan'],
    [ORDER_TYPE.KISETSU_HENKOU_RIYOU_SAIKAI, 'kisetsuHenkouRiyouSaikai'],
    [ORDER_TYPE.KISETSU_HENKOU_RIYOU_CHUUDAN_SOKUJI, 'kisetsuHenkouRiyouChuudanSokuji'],
]);

const TRANSACTION_STATUS = {
    TOUROKU_IRAI_NOT_YET_SENT: 10,
    TOUROKU_IRAI_SENT_OK: 11,
    TOUROKU_IRAI_SENT_NG: 12,
    TOUROKU_YOUKYUU_RESPONSE_OK: 13,
    TOUROKU_YOUKYUU_RESPONSE_NG: 14,
    HENKOU_IRAI_NOT_YET_SENT: 20,
    HENKOU_IRAI_SENT_OK: 21,
    HENKOU_IRAI_SENT_NG: 22,
    KAIYAKU_IRAI_NOT_YET_SENT: 30,
    KAIYAKU_IRAI_SENT_OK: 31,
    KAIYAKU_IRAI_SENT_NG: 32,
    KENSAKU_IRAI_NOT_YET_SENT: 40,
    KENSAKU_IRAI_SENT_OK: 41,
    KENSAKU_IRAI_SENT_NG: 42,
    STOPPING_FOR_ERROR: 90,
};

const TRANSACTION_STATUS_NAME = {
    [TRANSACTION_STATUS.TOUROKU_IRAI_NOT_YET_SENT]: '未登録依頼',
    [TRANSACTION_STATUS.TOUROKU_IRAI_SENT_OK]: '登録依頼済OK',
    [TRANSACTION_STATUS.TOUROKU_IRAI_SENT_NG]: '登録依頼済NG',
    [TRANSACTION_STATUS.TOUROKU_YOUKYUU_RESPONSE_OK]: '登録要求OK',
    [TRANSACTION_STATUS.TOUROKU_YOUKYUU_RESPONSE_NG]: '登録要求NG',
    [TRANSACTION_STATUS.HENKOU_IRAI_NOT_YET_SENT]: '未変更依頼',
    [TRANSACTION_STATUS.HENKOU_IRAI_SENT_OK]: '変更依頼済OK',
    [TRANSACTION_STATUS.HENKOU_IRAI_SENT_NG]: '変更依頼済NG',
    [TRANSACTION_STATUS.KAIYAKU_IRAI_NOT_YET_SENT]: '未解約依頼',
    [TRANSACTION_STATUS.KAIYAKU_IRAI_SENT_OK]: '解約依頼済OK',
    [TRANSACTION_STATUS.KAIYAKU_IRAI_SENT_NG]: '解約依頼済NG',
    [TRANSACTION_STATUS.KENSAKU_IRAI_NOT_YET_SENT]: '未検索依頼',
    [TRANSACTION_STATUS.KENSAKU_IRAI_SENT_OK]: '検索依頼済OK',
    [TRANSACTION_STATUS.KENSAKU_IRAI_SENT_NG]: '検索依頼済NG',
    [TRANSACTION_STATUS.STOPPING_FOR_ERROR]: '不明なエラー',
};

const SUCCESS_SHORI_KEKKA_CODES = ['00000', '00004'];

/** 処理のステータス (`AladinApiLog.REQUEST_ORDER_*`) */
const ALADIN_API_REQUEST_ORDER_STATUS = {
    NOT_FINISHED: '0',
    FINISHED_PARTLY_NG: '1',
    FINISHED_FULLY_NG: '2',
    FINISHED_FULLY_OK: '3',
};

const ALADIN_API_REQUEST_ORDER_STATUS_NAME = {
    [ALADIN_API_REQUEST_ORDER_STATUS.NOT_FINISHED]: '未終了',
    [ALADIN_API_REQUEST_ORDER_STATUS.FINISHED_PARTLY_NG]: '一部NG終了',
    [ALADIN_API_REQUEST_ORDER_STATUS.FINISHED_FULLY_NG]: '全てNG終了',
    [ALADIN_API_REQUEST_ORDER_STATUS.FINISHED_FULLY_OK]: '正常終了',
};

const DOCOMO_RESPONSE_RESULT_CODE = {
    OK: '00',
    NG: '99',
};

const ALADIN_REQ_TAG = {
    SERVICE_ORDER_NG: 'SERVICE_ORDER_NG',
    SIM_ZAIKO_NG: 'SIM_ZAIKO_NG',
    KOKANKI_SETTEI_NG: 'KOKANKI_SETTEI_NG',
    SERVER_ERROR: 'SERVER_ERROR',
};

module.exports = {
    TRANSACTION_TYPE,
    TRANSACTION_TYPE_NAME,
    ORDER_TYPE,
    ORDER_TYPE_NAME,
    CARD_KEIJO,
    ZOKUSEI_CODE,
    LOWEST_API_PRIORITY,
    PRIORITY_KEY_STRING,
    TRANSACTION_STATUS,
    TRANSACTION_STATUS_NAME,
    SUCCESS_SHORI_KEKKA_CODES,
    ALADIN_API_REQUEST_ORDER_STATUS,
    ALADIN_API_REQUEST_ORDER_STATUS_NAME,
    DOCOMO_RESPONSE_RESULT_CODE,
    ALADIN_REQ_TAG,
    FM_ORDER_TYPE_LIST,
};
