{"name": "api", "version": "1.0.0", "description": "", "scripts": {"start": "func start --javascript", "test-unit": "env-cmd --file .env.json mocha --recursive '**/__tests__/*' --timeout 30000", "test": "npm run test-unit", "test-unit-file": "env-cmd --file .env.json mocha --recursive **/__tests__/$npm_config_file --require __tests__/hooks.js --timeout 30000", "test-unit-with-coverage": "env-cmd --file .env.json nyc --reporter=html --lines 90 mocha --recursive '**/__tests__/*' --timeout 30000", "format:check": "prettier --check .", "format:write": "prettier --write .", "lint:check": "eslint .", "lint:fix": "eslint --fix ."}, "dependencies": {"@azure/app-configuration": "^1.3.1", "@azure/service-bus": "^7.6.0", "@azure/storage-blob": "^12.11.0", "@joi/date": "^2.1.0", "axios": "^0.27.2", "dayjs": "^1.11.2", "iconv-urlencode": "^1.0.0", "ioredis": "^5.3.1", "joi": "^17.6.0", "js-md2": "^0.2.2", "lodash": "^4.17.21", "mongoose": "^6.3.4", "pg": "^8.7.3"}, "devDependencies": {"axios-mock-adapter": "^1.21.1", "chai": "^4.3.6", "env-cmd": "^10.1.0", "eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "sinon": "^14.0.0"}}