let chai = require('chai');
let expect = chai.expect;
const _ = require('lodash');
const { validateAndParseShippingAddress } = require('../validations/shippingAddressValidator');

describe('個別配送-入力項目 validation', () => {
    // define common test data
    const shippingAddressRequest = {
        tenantId: 'CON000',
        deliveryFlag: '1',
        postalCode: '1234567',
        shippingAddress1: '東京都',
        shippingAddress2: '千代田区内幸町',
        shippingAddress3: '大字川田',
        shippingAddress4: '字東藤ノ木',
        shippingAddress5: '１－１－６',
        shippingAddress6: 'ＮＴＴ日比谷ビル７Ｆ',
        shippingAddress7: 'NTTコミュニケーションズ（株）',
        shippingAddress8: 'ＮＳ部ＯＮＳ部門',
        shippingAddress9: 'コム太郎',
        contactPhoneNumber: '03-6700-8320',
        deliveryPattern: 'NTC-C-20170325-001-01',
        exception: 'test',
    };

    it('expect true with valid 個別配送 request', async () => {
        const { shippingInfo } = await validateAndParseShippingAddress(shippingAddressRequest);

        expect(shippingInfo.length).to.not.equal(0);
    });

    it('expect true without optional params when 個別配送 request', async () => {
        let validRequest = _.cloneDeep(shippingAddressRequest);
        delete validRequest.shippingAddress3;
        delete validRequest.shippingAddress4;
        delete validRequest.shippingAddress5;
        delete validRequest.shippingAddress5;
        delete validRequest.shippingAddress8;
        delete validRequest.shippingAddress9;
        delete validRequest.deliveryPattern;
        delete validRequest.exception;
        const { shippingInfo } = await validateAndParseShippingAddress(shippingAddressRequest);

        expect(shippingInfo.length).to.not.equal(0);
    });

    it('expect true when optional params are empty when 個別配送 request', async () => {
        let validRequest = _.cloneDeep(shippingAddressRequest);
        validRequest.shippingAddress3 = '';
        validRequest.shippingAddress4 = '';
        validRequest.shippingAddress5 = '';
        validRequest.shippingAddress5 = '';
        validRequest.shippingAddress8 = '';
        validRequest.shippingAddress9 = '';
        validRequest.deliveryPattern = '';
        validRequest.exception = '';
        const { shippingInfo } = await validateAndParseShippingAddress(shippingAddressRequest);

        expect(shippingInfo.length).to.not.equal(0);
    });

    // tenantId is empty
    it('expect ERROR code ADDRESS_ERROR_001 if tenantId is empty', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);

        // empty tenantId
        invalidRequest.tenantId = '';
        let { errorCode } = await validateAndParseShippingAddress(invalidRequest);
        expect(errorCode).to.be.equal('ADDRESS_ERROR_001');

        // null tenantId
        delete invalidRequest.tenantId;
        ({ errorCode } = await validateAndParseShippingAddress(invalidRequest));
        expect(errorCode).to.be.equal('ADDRESS_ERROR_001');
    });

    // deliveryService has turned off but address is passed
    describe('when deliveryService not supported for tenantId', function () {
        it('expect ERROR code ADDRESS_ERROR_002 if deliveryFlag is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                deliveryFlag: '1',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if postalCode is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                postalCode: '1234567',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress1 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress1: '東京都',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress2 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress2: '千代田区内幸町',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress3 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress3: '大字川田',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress4 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress4: '字東藤ノ木',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress5 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress5: '１－１－６',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress6 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress6: 'ＮＴＴ日比谷ビル７Ｆ',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress7 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress7: 'NTTコミュニケーションズ（株）',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress8 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress8: 'ＮＳ部ＯＮＳ部門',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if shippingAddress9 is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                shippingAddress9: 'コム太郎',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if contactPhoneNumber is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                contactPhoneNumber: '03-6700-8320',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if deliveryPattern is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                deliveryPattern: 'NTC-C-20170325-001-01',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });

        it('expect ERROR code ADDRESS_ERROR_002 if exception is passed', async () => {
            let invalidRequest = {
                tenantId: 'ZZZ000', // 個配サービス非対応テナント
                exception: 'test',
            };

            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);
            expect(errorCode).to.be.equal('ADDRESS_ERROR_002');
        });
    });

    // validate deliveryFlag
    it('expect ERROR code ADDRESS_ERROR_003 if requested with invalid deliveryFlag', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        const dataTest = ['abc', '2'];
        for (const deliveryFlag of dataTest) {
            invalidRequest.deliveryFlag = deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_003');
        }
    });

    // deliveryService has turned off but address is passed
    describe('when deliveryFlag=0', function () {
        it('expect ERROR code ADDRESS_ERROR_0024 if postalCode is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                postalCode: '1234567',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress1 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress1: '東京都',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress2 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress2: '千代田区内幸町',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress3 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress3: '大字川田',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress4 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress4: '字東藤ノ木',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress5 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress5: '１－１－６',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress6 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress6: 'ＮＴＴ日比谷ビル７',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress7 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress7: 'NTTコミュニケーシ,ンズ（株）',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress8 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress8: 'ＮＳ部ＯＮＳ部門',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if shippingAddress9 is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                shippingAddress9: 'コム太郎',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if contactPhoneNumber is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                contactPhoneNumber: '03-6700-8320',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if deliveryPattern is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                deliveryPattern: 'NTC-C-20170325-,01-01',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });

        it('expect ERROR code ADDRESS_ERROR_0024 if exception is passed', async () => {
            let invalidRequest = {
                tenantId: 'CON000', // 個配サービス対応テナント
                exception: 'test',
            };

            const dataTest = ['', '0'];
            for (const deliveryFlag of dataTest) {
                invalidRequest.deliveryFlag = deliveryFlag;
                const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

                expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
            }

            delete invalidRequest.deliveryFlag;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_0024');
        });
    });

    // validate postalCode
    it('expect ERROR code ADDRESS_ERROR_004 if requested without postalCode or with empty postalCode', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        //empty postalCode case
        invalidRequest.postalCode = '';
        let { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_004');

        //without postalCode case
        delete invalidRequest.postalCode;
        ({ errorCode } = await validateAndParseShippingAddress(invalidRequest));

        expect(errorCode).to.be.equal('ADDRESS_ERROR_004');
    });

    it('expect ERROR code ADDRESS_ERROR_005 if requested with invalid format of postalCode', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        const dataTest = ['12345678', '123-4567'];
        for (const postalCode of dataTest) {
            invalidRequest.postalCode = postalCode;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_005');
        }
    });

    // validate shippingAddress1
    it('expect ERROR code ADDRESS_ERROR_006 if requested without shippingAddress1 or with empty shippingAddress1', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        //empty shippingAddress1 case
        invalidRequest.shippingAddress1 = '';
        let { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_006');

        //without shippingAddress1 case
        delete invalidRequest.shippingAddress1;
        ({ errorCode } = await validateAndParseShippingAddress(invalidRequest));

        expect(errorCode).to.be.equal('ADDRESS_ERROR_006');
    });

    it('expect ERROR code ADDRESS_ERROR_012 if requested with invalid length of shippingAddress1', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress1 = 'x'.repeat(17);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_012');
    });

    // validate shippingAddress2
    it('expect ERROR code ADDRESS_ERROR_007 if requested without shippingAddress2 or with empty shippingAddress2', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        //empty shippingAddress2 case
        invalidRequest.shippingAddress2 = '';
        let { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_007');

        //without shippingAddress2 case
        delete invalidRequest.shippingAddress2;
        ({ errorCode } = await validateAndParseShippingAddress(invalidRequest));

        expect(errorCode).to.be.equal('ADDRESS_ERROR_007');
    });

    it('expect ERROR code ADDRESS_ERROR_013 if requested with invalid length of shippingAddress2', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress2 = 'x'.repeat(13);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_013');
    });

    // validate shippingAddress3
    it('expect ERROR code ADDRESS_ERROR_014 if requested with invalid length of shippingAddress3', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress3 = 'x'.repeat(17);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_014');
    });

    // validate shippingAddress123
    it('expect ERROR code ADDRESS_ERROR_009 if requested with invalid length of shippingAddress123', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress1 = 'x'.repeat(10);
        invalidRequest.shippingAddress2 = 'x'.repeat(10);
        invalidRequest.shippingAddress3 = 'x'.repeat(10);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_009');
    });

    // validate shippingAddress4
    it('expect ERROR code ADDRESS_ERROR_015 if requested with invalid length of shippingAddress4', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress4 = 'x'.repeat(13);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_015');
    });

    // validate shippingAddress5
    it('expect ERROR code ADDRESS_ERROR_016 if requested with invalid length of shippingAddress5', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress5 = 'x'.repeat(17);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_016');
    });

    // validate shippingAddress45
    it('expect ERROR code ADDRESS_ERROR_010 if requested with invalid length of shippingAddress45', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress4 = 'x'.repeat(10);
        invalidRequest.shippingAddress5 = 'x'.repeat(10);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_010');
    });

    // validate shippingAddress6
    it('expect ERROR code ADDRESS_ERROR_017 if requested with invalid length of shippingAddress6', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress6 = 'x'.repeat(17);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_017');
    });

    // validate shippingAddress7
    it('expect ERROR code ADDRESS_ERROR_008 if requested without shippingAddress7 or with empty shippingAddress7', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        //empty shippingAddress7 case
        invalidRequest.shippingAddress7 = '';
        let { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_008');

        //without shippingAddress7 case
        delete invalidRequest.shippingAddress7;
        ({ errorCode } = await validateAndParseShippingAddress(invalidRequest));

        expect(errorCode).to.be.equal('ADDRESS_ERROR_008');
    });

    it('expect ERROR code ADDRESS_ERROR_018 if requested with invalid length of shippingAddress7', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress7 = 'x'.repeat(17);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_018');
    });

    // validate shippingAddress8
    it('expect ERROR code ADDRESS_ERROR_019 if requested with invalid length of shippingAddress8', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress8 = 'x'.repeat(17);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_019');
    });

    // validate shippingAddress9
    it('expect ERROR code ADDRESS_ERROR_020 if requested with invalid length of shippingAddress9', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress9 = 'x'.repeat(11);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_020');
    });

    // validate shippingAddress89
    it('expect ERROR code ADDRESS_ERROR_0025 if requested with invalid length of shippingAddress89', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.shippingAddress8 = 'x'.repeat(10);
        invalidRequest.shippingAddress9 = 'x'.repeat(10);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_025');
    });

    // validate contactPhoneNumber
    it('expect ERROR code ADDRESS_ERROR_011 if requested without contactPhoneNumber or with empty contactPhoneNumber', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        //empty contactPhoneNumber case
        invalidRequest.contactPhoneNumber = '';
        let { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_011');

        //without contactPhoneNumber case
        delete invalidRequest.contactPhoneNumber;
        ({ errorCode } = await validateAndParseShippingAddress(invalidRequest));

        expect(errorCode).to.be.equal('ADDRESS_ERROR_011');
    });

    it('expect ERROR code ADDRESS_ERROR_021 if requested with invalid format of contactPhoneNumber', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        const dataTest = ['abc', '09012345678', '0312345678'];
        for (const contactPhoneNumber of dataTest) {
            invalidRequest.contactPhoneNumber = contactPhoneNumber;
            const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

            expect(errorCode).to.be.equal('ADDRESS_ERROR_021');
        }
    });

    // validate deliveryPattern
    it('expect ERROR code ADDRESS_ERROR_022 if requested with invalid length of deliveryPattern', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.deliveryPattern = 'x'.repeat(31);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_022');
    });

    // validate exception
    it('expect ERROR code ADDRESS_ERROR_023 if requested with invalid length of exception', async () => {
        let invalidRequest = _.cloneDeep(shippingAddressRequest);
        invalidRequest.exception = 'x'.repeat(101);
        const { errorCode } = await validateAndParseShippingAddress(invalidRequest);

        expect(errorCode).to.be.equal('ADDRESS_ERROR_023');
    });
});
