let chai = require('chai');
const { encryptText, decryptText } = require('../utils/symmetricCipher');
let expect = chai.expect;

describe('Symmetric Cipher', function () {
    it('should return same string', () => {
        const testdata = ['20221121', 'ﾔﾏﾀﾞｲﾁﾛｳ', '山田一郎'];
        for (const text of testdata) {
            const ctext = encryptText(text);
            const ptext = decryptText(ctext);
            expect(ptext).eq(text);
        }
    });
});
