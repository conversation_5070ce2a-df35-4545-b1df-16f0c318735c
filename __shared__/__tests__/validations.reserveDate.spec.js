let chai = require('chai');
const { getPortalConfig } = require('../config/appConfig');
const { getTimeInJST } = require('../utils/datetimeUtils');
const { validateReserveDate } = require('../validations/reserveDateValidator');
let expect = chai.expect;

const portalConfig = getPortalConfig();

describe('Reserve date validator', function () {
    const now = getTimeInJST();
    const maxAcceptableAfter = portalConfig.KAISEN_HAISHI_RESERVATION_ACCEPTABLE_PERIOD_DAYS;
    const canRegisterAfter = portalConfig.KAISEN_HAISHI_RESERVATION_ACCEPTABLE_BEFORE_X_DAY;

    // today<------>canRegisterAfter<---->予約可能日<------>maxAcceptableAfter

    it('should return true if reserve date is between y日 and max', () => {
        const afterPlusOneDay = now.add(canRegisterAfter + 1, 'days').unix();
        expect(validateReserveDate(afterPlusOneDay, maxAcceptableAfter, canRegisterAfter)).equal(true);
    });

    it('returns true if reserve date is first day of allowed period', () => {
        const firstDay = now.add(canRegisterAfter, 'days').unix();
        expect(validateReserveDate(firstDay, maxAcceptableAfter, canRegisterAfter)).equal(true);
    });

    it('returns true if reserve date is last day of allowed period', () => {
        const lastDay = now.add(maxAcceptableAfter, 'days').unix();
        expect(validateReserveDate(lastDay, maxAcceptableAfter, canRegisterAfter)).equal(true);
    });

    it('should return false if reserve date is today (before allowed period)', () => {
        const afterMinusOneDay = now.unix();
        expect(validateReserveDate(afterMinusOneDay, maxAcceptableAfter, canRegisterAfter)).equal(false);
    });

    it('should return error if reserve date is after allowed period', () => {
        const afterMaxPlusOneDay = now.add(maxAcceptableAfter + 1, 'days').unix();
        expect(validateReserveDate(afterMaxPlusOneDay, maxAcceptableAfter, canRegisterAfter)).equal(false);
    });
});
