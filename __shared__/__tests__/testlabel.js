const { isNone } = require('../helpers/baseHelper');

/** @type {{[tag: string]: number}} */
const counter = {};

/**
 * create test case label
 * @param {string} tag unique string to keep track last test number
 * @param {string} prefix e.g. `CASE_71.`
 * @param {string} code expected result code
 * @param {string} description test case description
 * @returns test case's title, e.g. `[711068] [CASE_71.3] eSIM無効テナント`
 */
module.exports = (tag, prefix, code, description) => {
    if (isNone(counter[tag])) {
        counter[tag] = 0;
    }
    counter[tag] += 1;
    // return `[${code}] [${prefix}${counter[tag]}] ${description}`;
    return `[${prefix}${counter[tag]}] [${code}] ${description}`;
};
