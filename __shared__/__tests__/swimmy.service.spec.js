let chai = require('chai');
let expect = chai.expect;
const context = require('./defaultContext');
const _ = require('lodash');
const { registerSwimmyTransaction } = require('../services/swimmy.service');

const { SWIMMY_REQUEST_TYPES } = require('../constants/swimmyTransactions');

describe.skip('Swimmy service', async () => {
    const _normalData = {
        tenantId: 'TST000',
        swimmyType: SWIMMY_REQUEST_TYPES.KUROKA,
        requestOrderId: 'PF00000000001',
        lineNo: '08012345678',
        transactionId: '041234567890123456789',
    };

    it('expect Exception if register if register data without tenantId', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.tenantId;
        try {
            await registerSwimmyTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('tenantId is required');
        }
    });

    it('expect Exception if register if register data without requestOrderId', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.requestOrderId;
        try {
            await registerSwimmyTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('requestOrderId is required');
        }
    });

    it('expect Exception if register if register data without swimmyType', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.swimmyType;
        try {
            await registerSwimmyTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('swimmyType is required');
        }
    });

    it('expect Exception if register if register data without lineNo', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.lineNo;
        try {
            await registerSwimmyTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('lineNo is required');
        }
    });

    it('expect Exception if register if register data with invalid swimmyType', async () => {
        let _data1 = _.cloneDeep(_normalData);
        _data1.swimmyType = 'xx';
        try {
            await registerSwimmyTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('Invalid swimmyType');
        }
    });
});
