let chai = require('chai');
let expect = chai.expect;
const context = require('./defaultContext');
const _ = require('lodash');
const { register0035ActiveOrder, register0035CancelOrder } = require('../services/swimmyVLM.service');

const {
    SWIMMY_VLM_REQUEST_TYPES,
    SWIMMY_VLM_INTERNATIONAL_CALL,
    SWIMMY_VLM_STATUS,
} = require('../constants/swimmyVLMTransactions');


describe.skip('Swimmy VLM service', async () => {
    const _normalData = {
        tenantId: 'TST000',
        soId: 'PF00000000001',
        requestType: SWIMMY_VLM_REQUEST_TYPES.MNP_TENNYUU_HANKURO,
        requestParam: {},
        status: SWIMMY_VLM_STATUS.ON_HOLD,
        sameMvneInFlag: true,
    };

    const _normalActiveRequestParams = {
        phoneNumber: '08012345678',
        servicePlan: '90000',
        internationalCall: SWIMMY_VLM_INTERNATIONAL_CALL.OFF,
        costStartDate: null,
    };

    const _normalCancelRequestParams = {
        phoneNumber: '08012345678',
        costStartDate: null,
    };


    it('expect Exception if register data without tenantId', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.tenantId;
        const funcs = [register0035ActiveOrder, register0035CancelOrder];
        for (let func of funcs) {
            try {
                await func(context, _data1);
            } catch (err) {
                expect(err.message).to.equal('tenantId is required');
            }
        }
    });

    it('expect Exception if register data without soId', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.soId;
        const funcs = [register0035ActiveOrder, register0035CancelOrder];
        for (let func of funcs) {
            try {
                await func(context, _data1);
            } catch (err) {
                expect(err.message).to.equal('soId is required');
            }
        }
    });

    it('expect Exception if register data without requestType', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.requestType;
        const funcs = [register0035ActiveOrder, register0035CancelOrder];
        for (let func of funcs) {
            try {
                await func(context, _data1);
            } catch (err) {
                expect(err.message).to.equal('requestType is required');
            }
        }
    });

    it('expect Exception if register data with invalid requestType', async () => {
        let _data1 = _.cloneDeep(_normalData);
        _data1.requestType = 'invalid';
        const funcs = [register0035ActiveOrder, register0035CancelOrder];
        for (let func of funcs) {
            try {
                await func(context, _data1);
            } catch (err) {
                expect(err.message).to.equal('Invalid requestType');
            }
        }
    });

    it('expect Exception if register data with invalid status', async () => {
        let _data1 = _.cloneDeep(_normalData);
        _data1.status = 'invalid';
        const funcs = [register0035ActiveOrder, register0035CancelOrder];
        for (let func of funcs) {
            try {
                await func(context, _data1);
            } catch (err) {
                expect(err.message).to.equal('Invalid status');
            }
        }
    });

    it('expect Exception if register data without phoneNumber', async () => {
        let _data = _.cloneDeep(_normalData);

        try {
            _data.requestParam = _.cloneDeep(_normalActiveRequestParams);
            delete _data.requestParam.phoneNumber;
            await register0035ActiveOrder(context, _data);
        } catch (err) {
            expect(err.message).to.equal('"phoneNumber" is required');
        }

        try {
            _data.requestParam = _.cloneDeep(_normalCancelRequestParams);
            delete _data.requestParam.phoneNumber;
            await register0035CancelOrder(context, _data);
        } catch (err) {
            expect(err.message).to.equal('"phoneNumber" is required');
        }
    });

    it('expect Exception if register data without servicePlan', async () => {
        let _data = _.cloneDeep(_normalData);

        try {
            _data.requestParam = _.cloneDeep(_normalActiveRequestParams);
            delete _data.requestParam.servicePlan;
            await register0035ActiveOrder(context, _data);
        } catch (err) {
            expect(err.message).to.equal('"servicePlan" is required');
        }
    });

    it('expect Exception if register data without internationalCall', async () => {
        let _data = _.cloneDeep(_normalData);

        try {
            _data.requestParam = _.cloneDeep(_normalActiveRequestParams);
            delete _data.requestParam.internationalCall;
            await register0035ActiveOrder(context, _data);
        } catch (err) {
            expect(err.message).to.equal('"internationalCall" is required');
        }
    });

    it('expect Exception if register data with invalid internationalCall', async () => {
        let _data = _.cloneDeep(_normalData);

        try {
            _data.requestParam = _.cloneDeep(_normalActiveRequestParams);
            _data.requestParam.internationalCall = 'invalid';
            await register0035ActiveOrder(context, _data);
        } catch (err) {
            expect(err.message).to.equal('"internationalCall" must be one of [on, off]');
        }
    });
});
