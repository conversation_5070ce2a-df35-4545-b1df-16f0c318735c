const _ = require('lodash');
let chai = require('chai');
const sinon = require('sinon');
const dayjs = require('dayjs');
const AppConfig = require('../config/appConfig');
const context = require('./defaultContext');
const { SO_KIND_TYPE, KOUTEI_IDS } = require('../constants/orderConstants');
const { checkError } = require('../models/swimmyService/esimKekkaTsuchi');
const SwimmyApiLogModel = require('../models/swimmyService/swimmyApiLog.model');

let expect = chai.expect;
const portalConfig = AppConfig.getPortalConfig();

describe('esimKekkaTsuchi', () => {
    const __mnpTennyuSo = {
        lastUpdateUserId: '2000',
        pSoId: 'FM5e4df375e4b06f953c6ee925',
        updatedAt: 1582166901,
        tenantId: 'TST000',
        tempoId: 'ZMKTST0001',
        planId: 40175,
        kouteis: [
            {
                kouteiId: 2,
                timestamp: 1582166901,
                userId: '2000',
            },
            {
                kouteiId: 12,
                timestamp: 1582166901,
                userId: '2000',
            },
            {
                kouteiId: 22,
                timestamp: 1582167351,
                userId: '2000',
            },
            {
                kouteiId: 3,
                timestamp: 1582250454,
                userId: '',
            },
        ],
        lastKoutei: {
            kouteiId: 3,
            timestamp: 1582250454,
            userId: '',
        },
        hankuro: '半黒',
        soKind: 'MNP転入',
        so_id: 'FM5e4df375e4b06f953c6ee92c',
        zm_id: 'ZM15e4df4c8d0a966adb5be4b3d',
        simInfo: '000PPP1893',
        kaisenNo: '02006101963',
    };
    const now = dayjs().format('YYYYMMDDHHmmss');

    describe('#checkError', () => {
        describe('with valid data', async () => {
            it('returns hasError false', async () => {
                const obj = _.cloneDeep(__mnpTennyuSo);

                const { hasError, kaisenNoAreNeeded, simNoAreNeeded } = await checkError(context, {
                    swimmyVersion: 'V2',
                    obj,
                    kouteiId: obj.lastKoutei.kouteiId,
                    now,
                });

                expect(hasError).to.equal(false);
                expect(kaisenNoAreNeeded).to.exist;
                expect(simNoAreNeeded).to.exist;
            });
        });

        describe('when swimmy version invalid', async () => {
            it('returns hasError true', async () => {
                const obj = _.cloneDeep(__mnpTennyuSo);

                const { hasError } = await checkError(context, {
                    swimmyVersion: 'V1',
                    obj,
                    kouteiId: obj.lastKoutei.kouteiId,
                    now,
                });

                expect(hasError).to.equal(true);
            });
        });

        describe('when soKind invalid', async () => {
            it('returns hasError true', async () => {
                const obj = _.cloneDeep(__mnpTennyuSo);
                obj.soKind = SO_KIND_TYPE.KUROKA;

                const { hasError } = await checkError(context, {
                    swimmyVersion: 'V2',
                    obj,
                    kouteiId: obj.lastKoutei.kouteiId,
                    now,
                });

                expect(hasError).to.equal(true);
            });
        });

        describe('when lastKoutei.kouteiId invalid', async () => {
            it('returns hasError true', async () => {
                const obj = _.cloneDeep(__mnpTennyuSo);
                obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_OK;

                const { hasError } = await checkError(context, {
                    swimmyVersion: 'V2',
                    obj,
                    kouteiId: obj.lastKoutei.kouteiId,
                    now,
                });

                expect(hasError).to.equal(true);
            });
        });

        describe('when kouteiId is ALADIN_KOUJI_NG', async () => {
            describe('when have kaisenNo', async () => {
                it('returns hasError false', async () => {
                    const obj = _.cloneDeep(__mnpTennyuSo);
                    obj.soKind = SO_KIND_TYPE.MNP_TENNYU;
                    obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                    obj.ngReason = portalConfig.ALADIN_KOUJI_NG_REASON;
                    const { hasError } = await checkError(context, {
                        swimmyVersion: 'V2',
                        obj,
                        kouteiId: obj.lastKoutei.kouteiId,
                        now,
                    });

                    expect(hasError).to.equal(false);
                });
            });

            describe('when kaisenNo is empty', async () => {
                it('returns hasError true', async () => {
                    const obj = _.cloneDeep(__mnpTennyuSo);
                    obj.soKind = SO_KIND_TYPE.MNP_TENNYU;
                    obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                    obj.ngReason = portalConfig.ALADIN_KOUJI_NG_REASON;
                    obj.kaisenNo = '';

                    const { hasError } = await checkError(context, {
                        swimmyVersion: 'V2',
                        obj,
                        kouteiId: obj.lastKoutei.kouteiId,
                        now,
                    });

                    expect(hasError).to.equal(true);
                });
            });

            describe('when ngReason is empty', async () => {
                it('returns hasError true', async () => {
                    const obj = _.cloneDeep(__mnpTennyuSo);
                    obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                    obj.ngReason = '';

                    const { hasError } = await checkError(context, {
                        swimmyVersion: 'V2',
                        obj,
                        kouteiId: obj.lastKoutei.kouteiId,
                        now,
                    });

                    expect(hasError).to.equal(true);
                });
            });
        });

        describe("when it's simmy version V2", async () => {
            describe('when soKind is MNP_NEW_ESIM_SERVICE_ORDER', async () => {
                describe('when have simInfo', async () => {
                    it('returns hasError false', async () => {
                        const obj = _.cloneDeep(__mnpTennyuSo);
                        obj.soKind = SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER;
                        obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                        obj.ngReason = portalConfig.ALADIN_KOUJI_NG_SPECIAL_REASON_FOR_NEW_ESIM;

                        const { hasError } = await checkError(context, {
                            swimmyVersion: 'V2',
                            obj,
                            kouteiId: obj.lastKoutei.kouteiId,
                            now,
                        });

                        expect(hasError).to.equal(false);
                    });
                });

                describe('when simInfo is empty', async () => {
                    it('returns hasError true', async () => {
                        const obj = _.cloneDeep(__mnpTennyuSo);
                        obj.soKind = SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER;
                        obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                        obj.ngReason = portalConfig.ALADIN_KOUJI_NG_SPECIAL_REASON_FOR_NEW_ESIM;
                        obj.simInfo = '';

                        const { hasError } = await checkError(context, {
                            swimmyVersion: 'V2',
                            obj,
                            kouteiId: obj.lastKoutei.kouteiId,
                            now,
                        });

                        expect(hasError).to.equal(true);
                    });
                });
            });
        });

        describe("when it's simmy version V3", async () => {
            describe('when soKind is MNP_NEW_ESIM_SERVICE_ORDER', async () => {
                describe('when have eid and dummyLineNo', async () => {
                    it('returns hasError false', async () => {
                        const obj = _.cloneDeep(__mnpTennyuSo);
                        obj.soKind = SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER;
                        obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                        obj.ngReason = portalConfig.ALADIN_KOUJI_NG_SPECIAL_REASON_FOR_NEW_ESIM;
                        obj.dummyLineNo = '02006101963';
                        obj.eid = '89049032006008882600069221459589';

                        const { hasError } = await checkError(context, {
                            swimmyVersion: 'V3',
                            obj,
                            kouteiId: obj.lastKoutei.kouteiId,
                            now,
                        });

                        expect(hasError).to.equal(false);
                    });
                });

                describe('when eid is empty', async () => {
                    it('returns hasError true', async () => {
                        const obj = _.cloneDeep(__mnpTennyuSo);
                        obj.soKind = SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER;
                        obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                        obj.ngReason = portalConfig.ALADIN_KOUJI_NG_SPECIAL_REASON_FOR_NEW_ESIM;
                        obj.dummyLineNo = '02006101963';
                        obj.eid = '';

                        const { hasError } = await checkError(context, {
                            swimmyVersion: 'V3',
                            obj,
                            kouteiId: obj.lastKoutei.kouteiId,
                            now,
                        });

                        expect(hasError).to.equal(true);
                    });
                });

                describe('when dummyLineNo is empty', async () => {
                    it('returns hasError true', async () => {
                        const obj = _.cloneDeep(__mnpTennyuSo);
                        obj.soKind = SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER;
                        obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                        obj.ngReason = portalConfig.ALADIN_KOUJI_NG_SPECIAL_REASON_FOR_NEW_ESIM;
                        obj.dummyLineNo = '';
                        obj.eid = '89049032006008882600069221459589';

                        const { hasError } = await checkError(context, {
                            swimmyVersion: 'V3',
                            obj,
                            kouteiId: obj.lastKoutei.kouteiId,
                            now,
                        });

                        expect(hasError).to.equal(true);
                    });
                });
            });

            describe('when soKind is MNP_TENNYU and kaisenNo is empty', async () => {
                it('returns hasError true', async () => {
                    const obj = _.cloneDeep(__mnpTennyuSo);
                    obj.soKind = SO_KIND_TYPE.MNP_TENNYU;
                    obj.lastKoutei.kouteiId = KOUTEI_IDS.ALADIN_KOUJI_NG;
                    obj.ngReason = portalConfig.ALADIN_KOUJI_NG_REASON;
                    obj.kaisenNo = '';

                    const { hasError } = await checkError(context, {
                        swimmyVersion: 'V3',
                        obj,
                        kouteiId: obj.lastKoutei.kouteiId,
                        now,
                    });

                    expect(hasError).to.equal(true);
                });
            });
        });

        describe('when swimmyApiLog according to so_id exists', async () => {
            const sandbox = sinon.createSandbox();
            afterEach(() => sandbox.restore());

            it('returns hasError true', async () => {
                const obj = _.cloneDeep(__mnpTennyuSo);
                sandbox.stub(SwimmyApiLogModel, 'findOneByRequestOrderId').resolves({});

                const { hasError } = await checkError(context, {
                    swimmyVersion: 'V3',
                    obj,
                    kouteiId: obj.lastKoutei.kouteiId,
                    now,
                });

                expect(hasError).to.equal(true);
            });

            describe('when forceNotify is true', async () => {
                it('updates swimmyApiLog', async () => {
                    const obj = _.cloneDeep(__mnpTennyuSo);
                    sandbox.stub(SwimmyApiLogModel, 'findOneByRequestOrderId').resolves({});
                    const updateFunction = sandbox
                        .stub(SwimmyApiLogModel, 'updateStatusAndRequestParamByRequestOrderIdAndSwimmyType')
                        .resolves(true);

                    const { hasError } = await checkError(context, {
                        swimmyVersion: 'V3',
                        obj,
                        kouteiId: obj.lastKoutei.kouteiId,
                        now,
                        forceNotify: true,
                    });

                    expect(updateFunction.calledOnce).to.equal(true);
                    expect(hasError).to.equal(true);
                });
            });
        });
    });
});
