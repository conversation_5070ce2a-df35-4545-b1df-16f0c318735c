const mongoose = require('../database/mongoose');
var sinon = require('sinon');
const context = {
    log: sinon.stub(),
};
context.log.error = sinon.stub();

exports.mochaHooks = {
    async beforeAll() {
        // init mongoose connection
        console.log('init mongoose connection...');
        mongoose.init(context);
    },
    async afterAll() {
        console.log('close mongoose connection...');
        await mongoose.disconnect();
    },
};
