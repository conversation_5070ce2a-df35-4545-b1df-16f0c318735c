let chai = require('chai');
const { formatDateForAladin } = require('../utils/stringUtils');
let expect = chai.expect;

describe('String Utils', function () {
    it('[formatDateForAladin] should return correct format', () => {
        const testdata = [
            ['2010/08/31', '20100831'],
            ['20100831', '20100831'],
        ];
        for (const data of testdata) {
            expect(formatDateForAladin(data[0])).equal(data[1]);
        }
    });

    it('[formatDateForAladin] should return default value', () => {
        const testdata = [null, undefined, 12345, '123', 'abc123'];
        for (const data of testdata) {
            expect(formatDateForAladin(data)).null;
        }
    });
});
