/* eslint-disable no-unused-vars */
let chai = require('chai');
let expect = chai.expect;
const _ = require('lodash');
const { getSeqNoAndAPIKey, callAPISOFrontCancel } = require('../services/core.service');
const context = require('./defaultContext');

describe('Core service', async () => {
    it('expect seqNo and apiKeyLowerCase is returned', async () => {
        const { seqNoStr, apiKeyStrLowerCase } = getSeqNoAndAPIKey();
        expect(seqNoStr.length).to.equal(7);
        expect(seqNoStr).to.match(/^G[0-9]+$/);
        expect(apiKeyStrLowerCase).to.be.not.null;
    });

    const _normalData = {
        targetTenantId: 'TST000',
        serviceOrderIdKey: 'PF00000000001',
        csvUnnecessaryFlag: '0',
        targetSoId: 'PF0123456789abcdef123456789',
    };
    it('[callAPISOFrontCancel] expect processCode 000000 with normal request params', async () => {
        const processCode = await callAPISOFrontCancel(context, _normalData);
        expect(processCode).to.equal('000000');
    });

    it('[callAPISOFrontCancel] expect exception with invalid request params', async () => {
        try {
            let _data = _.cloneDeep(_normalData);
            delete _data.targetTenantId;
            const processCode = await callAPISOFrontCancel(context, _data);
        } catch (error) {
            expect(error.message).to.equal('targetTenantId is required');
        }

        try {
            let _data = _.cloneDeep(_normalData);
            delete _data.serviceOrderIdKey;
            const processCode = await callAPISOFrontCancel(context, _data);
        } catch (error) {
            expect(error.message).to.equal('serviceOrderIdKey is required');
        }

        try {
            let _data = _.cloneDeep(_normalData);
            delete _data.csvUnnecessaryFlag;
            const processCode = await callAPISOFrontCancel(context, _data);
        } catch (error) {
            expect(error.message).to.equal('csvUnnecessaryFlag is required');
        }
    });
});
