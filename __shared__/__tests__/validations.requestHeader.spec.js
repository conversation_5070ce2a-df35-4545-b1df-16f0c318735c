let chai = require('chai');
let expect = chai.expect;
const context = require('./defaultContext');
const _ = require('lodash');
const { validateCommonHeader } = require('../validations/requestHeader');

describe('Common header validation', () => {
    // define common test data
    const commonHeaderNormal = {
        requestHeader: {
            sequenceNo: '1234567',
            senderSystemId: '9a1z',
            apiKey: '$02$952A539B37A792169B18AE246A1Btest$02$A9F6DDD4144344EDFDD5A50EFB814119',
            functionType: 'xx',
        },
        tenantId: 'TST000',
    };

    it('expect true with valid requestHeader header', async () => {
        const result = await validateCommonHeader(context, { body: commonHeaderNormal });

        expect(result).to.be.true;
    });

    // validate sequenceNo
    it('expect ERROR code 0001 with invalid length of sequenceNo', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        const dataTest = ['123456', '12345678', '123456789'];
        for (const sequenceNo of dataTest) {
            invalidCommonHeader.requestHeader.sequenceNo = sequenceNo;
            const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

            expect(errorCode).to.be.equal('0001');
        }
    });

    it('expect ERROR code 0002 with invalid character of sequenceNo', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        const dataTest = ['123456あ', '@234567', '123$567'];
        for (const sequenceNo of dataTest) {
            invalidCommonHeader.requestHeader.sequenceNo = sequenceNo;
            const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

            expect(errorCode).to.be.equal('0002');
        }
    });

    it('expect ERROR code 0003 if without sequenceNo or empty', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        //empty sequenceNo case
        invalidCommonHeader.requestHeader.sequenceNo = '';
        let { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

        expect(errorCode).to.be.equal('0003');

        //without sequenceNo case
        delete invalidCommonHeader.requestHeader.sequenceNo;
        ({ errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader }));

        expect(errorCode).to.be.equal('0003');
    });

    // validate senderSystemId
    it('expect ERROR code 0004 with invalid length of senderSystemId', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        const dataTest = ['123', '12345', '123456'];
        for (const senderSystemId of dataTest) {
            invalidCommonHeader.requestHeader.senderSystemId = senderSystemId;
            const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

            expect(errorCode).to.be.equal('0004');
        }
    });

    it('expect ERROR code 0005 with invalid character of senderSystemId', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        const dataTest = ['123あ', '@234', '1$34'];
        for (const senderSystemId of dataTest) {
            invalidCommonHeader.requestHeader.senderSystemId = senderSystemId;
            const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

            expect(errorCode).to.be.equal('0005');
        }
    });

    it('expect ERROR code 0006 if without senderSystemId or empty', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        //empty senderSystemId case
        invalidCommonHeader.requestHeader.senderSystemId = '';
        let { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

        expect(errorCode).to.be.equal('0006');

        //without senderSystemId case
        delete invalidCommonHeader.requestHeader.senderSystemId;
        ({ errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader }));

        expect(errorCode).to.be.equal('0006');
    });

    // validate apiKey
    it('expect ERROR code 0007 with invalid length of apiKey', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        const dataTest = ['x'.repeat(71), 'x'.repeat(73)];
        for (const apiKey of dataTest) {
            invalidCommonHeader.requestHeader.apiKey = apiKey;
            const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

            expect(errorCode).to.be.equal('0007');
        }
    });

    it('expect ERROR code 0007 with invalid apiKey', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        invalidCommonHeader.requestHeader.apiKey = 'x'.repeat(72);
        const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

        expect(errorCode).to.be.equal('0007');
    });

    it('expect ERROR code 0009 if without apiKey or empty', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        //empty apiKey case
        invalidCommonHeader.requestHeader.apiKey = '';
        let { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

        expect(errorCode).to.be.equal('0009');

        //without apiKey case
        delete invalidCommonHeader.requestHeader.apiKey;
        ({ errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader }));

        expect(errorCode).to.be.equal('0009');
    });

    // validate functionType
    it('expect ERROR code 0010 with invalid length of functionType', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        const dataTest = ['7', '711', '7111'];
        for (const functionType of dataTest) {
            invalidCommonHeader.requestHeader.functionType = functionType;
            const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

            expect(errorCode).to.be.equal('0010');
        }
    });

    it('expect ERROR code 0011 if without functionType or empty', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        //empty functionType case
        invalidCommonHeader.requestHeader.functionType = '';
        let { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

        expect(errorCode).to.be.equal('0011');

        //without functionType case
        delete invalidCommonHeader.requestHeader.functionType;
        ({ errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader }));

        expect(errorCode).to.be.equal('0011');
    });

    // validate tenantId
    it('expect ERROR code 0012 with invalid length of tenantId', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        const dataTest = ['TST0', 'TST00', 'TST01234567', 'TST012345678'];
        for (const tenantId of dataTest) {
            invalidCommonHeader.tenantId = tenantId;
            const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

            expect(errorCode).to.be.equal('0012');
        }
    });

    it('expect ERROR code 0013 with invalid character of tenantId', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        const dataTest = ['TST00あ', '@TST000', 'TST$00'];
        for (const tenantId of dataTest) {
            invalidCommonHeader.tenantId = tenantId;
            const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

            expect(errorCode).to.be.equal('0013');
        }
    });

    it('expect ERROR code 0014 when tenantId not in DB', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        invalidCommonHeader.tenantId = 'TJ0000';
        const { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

        expect(errorCode).to.be.equal('0014');
    });

    it('expect ERROR code 0015 if without tenantId or empty', async () => {
        let invalidCommonHeader = _.cloneDeep(commonHeaderNormal);
        //empty tenantId case
        invalidCommonHeader.tenantId = '';
        let { errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader });

        expect(errorCode).to.be.equal('0015');

        //without tenantId case
        delete invalidCommonHeader.tenantId;
        ({ errorCode } = await validateCommonHeader(context, { body: invalidCommonHeader }));

        expect(errorCode).to.be.equal('0015');
    });
});
