const { HEADER_KEY } = require('../constants/headerKeys');
const { MAWP_ROLE_ID } = require('../constants/userRole');

/**
 *
 * @param {number} role - default to SUPER_USER
 * @param {string} tenant - default to ZZZ000
 * @param {object} user - user object
 * @param {string?} user.internalId - user's internal id
 * @param {string?} user.name - user's name
 * @param {boolean?} user.masking - mask phone number (tempo user only)
 * @param {boolean?} user.passwordExpired - user's password is expired or not
 * @param {boolean?} user.canDownloadMesai - user's flag for download ryoukin mesai
 * @param {string?} tempo - user's tempo id
 * @returns
 */
const getDefaultHeader = (role = null, tenant = null, user = null, tempo = null) => {
    return {
        [HEADER_KEY.ROLE_ID]: role === null || role === undefined ? MAWP_ROLE_ID.SUPER_USER : role,
        [HEADER_KEY.TENANT_ID]: tenant ?? 'ZZZ000',
        [HEADER_KEY.USER_ID]: user ? user.internalId : undefined,
        [HEADER_KEY.USER_PLUS_ID]: user ? user.plusId : undefined,
        [HEADER_KEY.USER_NAME]: user ? user.name : undefined,
        [HEADER_KEY.MASK_PHONE_NUMBER]: user ? user.masking : undefined,
        [HEADER_KEY.TEMPO_ID]: tempo,
        [HEADER_KEY.PASSWORD_EXPIRED]: user?.passwordExpired,
        [HEADER_KEY.CAN_DOWNLOAD_MEISA]: user?.canDownloadMesai,
    };
};

module.exports = {
    getDefaultHeader,
};
