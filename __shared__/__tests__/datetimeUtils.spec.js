let chai = require('chai');
const { getTimeInJST, getTimeAfterXDay } = require('../utils/datetimeUtils');
let expect = chai.expect;

describe('Datetime Utils', () => {
    it('[getTimeAfterXDay] should return `startDay` + `afterDay` - 1 second correctly', () => {
        const now = getTimeInJST('2022-09-07T16:18:31+09:00');
        const newDate = getTimeAfterXDay(now, 5);
        expect(newDate.format()).equal('2022-09-11T23:59:59+09:00');
    });
});
