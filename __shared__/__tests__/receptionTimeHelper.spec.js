let chai = require('chai');
const sinon = require('sinon');
let expect = chai.expect;
const { getTimeInJST } = require('../utils/datetimeUtils');

// for mock
const appConfig = require('../config/appConfig');
const dtUtils = require('../utils/datetimeUtils');

const { parseUketsukeFukaJikan, getAladinAPIRequestFukajikanInfo } = require('../helpers/receptionTimeHelper');
const { ORDER_TYPE, ORDER_TYPE_NAME } = require('../constants/aladinTransactions');

describe('ReceptionTime Helper', function () {
    describe('parseUketsukeFukaJikan', function () {
        it('should parse 受付不可時間 correctly [same day]', () => {
            const now = getTimeInJST().second(0).millisecond(0);
            const config = '08:58-13:50';

            const start = now.hour(8).minute(58);
            const end = now.hour(13).minute(50);

            const result = parseUketsukeFukaJikan(config);
            expect(result).not.null;
            expect(result.start.unix()).equal(start.unix());
            expect(result.end.unix()).equal(end.unix());
        });

        it('should parse 受付不可時間 correctly [next day]', () => {
            const now = getTimeInJST().second(0).millisecond(0);
            const config = '20:58-09:00';

            const start = now.hour(20).minute(58);
            // date should be same as today date (handled later by `isUketsukeFukaJikan` function)
            const end = now.hour(9).minute(0);

            const result = parseUketsukeFukaJikan(config);
            expect(result).not.null;
            expect(result.start.unix()).equal(start.unix());
            expect(result.end.unix()).equal(end.unix());
        });

        it('should return null if config value is NG', () => {
            expect(parseUketsukeFukaJikan('23-01')).null;
        });
    });

    describe('getAladinAPIRequestFukajikanInfo without order type', function () {
        // test by date
        const getNowJST = dtUtils.getTimeInJST;

        const sandbox = sinon.createSandbox();
        beforeEach(() => {
            // set setting value to be a constant
            sandbox
                .stub(appConfig, 'getPortalConfig')
                .returns({ ALADIN_API_REQUEST_UKETSUKE_FUKA_JIKAN: '20:58-09:00' });
        });
        afterEach(() => {
            sandbox.restore();
        });

        // [0, 1, ..., 23]
        Array.from(Array(24).keys()).forEach((nowHour) => {
            const nowMinute = 30; // 0:30, 1:30, ..., 23:30
            const fuka = nowHour > 20 || nowHour < 9; // 20:30 可, 21:30 不可, 8:30 不可, 09:30 可
            it(`[20:58-09:00] (now: ${nowHour}:${nowMinute}) should be ${fuka}`, () => {
                sandbox.stub(dtUtils, 'getTimeInJST').returns(getNowJST().hour(nowHour).minute(nowMinute));

                expect(getAladinAPIRequestFukajikanInfo(null)).equal(fuka);
            });
        });
    });

    describe('getAladinAPIRequestFukajikanInfo with order type', function () {
        // test by date
        const getNowJST = dtUtils.getTimeInJST;
        // special order type
        const ALLOWED_TYPES = [
            ORDER_TYPE.MNP_TENNYU_UKETSUKE_TSUJOU_KAITSU,
            ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_SAKUSEI,
            ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU,
            ORDER_TYPE.MNP_TENNYU_UKETSUKE_OTA_ROM_KAITSU,
        ];

        const sandbox = sinon.createSandbox();
        beforeEach(() => {
            // set setting value to be a constant
            sandbox
                .stub(appConfig, 'getPortalConfig')
                .returns({ ALADIN_API_REQUEST_UKETSUKE_FUKA_JIKAN: '20:58-09:00' });
        });
        afterEach(() => {
            sandbox.restore();
        });

        Object.values(ORDER_TYPE).forEach((orderType) => {
            const orderTypeName = ORDER_TYPE_NAME[orderType];
            // [0, 1, ..., 23]
            Array.from(Array(24).keys()).forEach((nowHour) => {
                const nowMinute = 30; // 0:30, 1:30, ..., 23:30

                // if in allowed order types: always return false
                const fuka = !ALLOWED_TYPES.includes(orderType) ? false : nowHour > 20 || nowHour < 9; // 20:30 可, 21:30 不可, 8:30 不可, 09:30 可

                it(`${orderTypeName} [20:58-09:00] (now: ${nowHour}:${nowMinute}) should be ${fuka}`, () => {
                    sandbox.stub(dtUtils, 'getTimeInJST').returns(getNowJST().hour(nowHour).minute(nowMinute));

                    expect(getAladinAPIRequestFukajikanInfo(orderType)).equal(fuka);
                });
            });
        });
    });
});
