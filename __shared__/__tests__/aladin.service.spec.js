let chai = require('chai');
let expect = chai.expect;
const context = require('../__tests__/defaultContext');
const _ = require('lodash');
const { registerAladinTransaction } = require('../services/aladin.service');

const { TRANSACTION_TYPE, ORDER_TYPE } = require('../constants/aladinTransactions');

describe('Aladin service', async () => {
    const _normalData = {
        tenantId: 'TST000',
        tempoId: 'TSTAAA0001',
        daihyouBango: '08012345678',
        transactionType: TRANSACTION_TYPE.MOSHIDE_KAIYAKU,
        requestOrderType: ORDER_TYPE.KAISEN_HAISHI,
        requestOrderId: 'PF00000000001',
        requestParam: {
            hanbaitencode: '0330000000300',
            denwabango: '09054749372',
            transactionTYPE: '49',
            keiyakusyubetu: 'PCJ04',
        },
    };

    it('expect Exception if register if register data without tenantId', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.tenantId;
        try {
            await registerAladinTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('tenantId is required');
        }
    });

    it('expect Exception if register if register data without requestOrderId', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.requestOrderId;
        try {
            await registerAladinTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('requestOrderId is required');
        }
    });

    it('expect Exception if register if register data without requestParam', async () => {
        let _data1 = _.cloneDeep(_normalData);
        delete _data1.requestParam;
        try {
            await registerAladinTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('requestParam is required');
        }
    });

    it('expect Exception if register if register data with invalid transactionType', async () => {
        let _data1 = _.cloneDeep(_normalData);
        _data1.transactionType = 'xx';
        try {
            await registerAladinTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('Invalid transactionType');
        }
    });

    it('expect Exception if register if register data with invalid requestOrderType', async () => {
        let _data1 = _.cloneDeep(_normalData);
        _data1.requestOrderType = 'xx';
        try {
            await registerAladinTransaction(context, _data1);
        } catch (error) {
            expect(error.message).to.equal('Invalid requestOrderType');
        }
    });
});
