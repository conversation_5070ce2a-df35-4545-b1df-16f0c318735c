/* eslint-disable no-unused-vars */
const { Pool } = require('pg');
const { getDBConfig } = require('../config/appConfig');
const { sleepPromise } = require('../utils/timerUtils');

let pool = null;
let retryCount = 3; // number of retries if database request fails
let retryWait = 5; // seconds to wait for next retry

const initPool = async (context) => {
    const dbConfig = await getDBConfig(context);
    const connectionString = dbConfig['DATABASE_POSTGRESQL_CONNECTION_STRING'];
    const poolOptions = {
        connectionString,
        max: dbConfig['DATABASE_POSTGRESQL_POOL_MAX_CLIENT'] || 200,
        idleTimeoutMillis: dbConfig['DATABASE_POSTGRESQL_POOL_IDLE_TIMEOUT_MILLIS'] || 10000,
        connectionTimeoutMillis: dbConfig['DATABASE_POSTGRESQL_POOL_CONNECTION_TIMEOUT_MILLIS'] || 2000,
    }
    pool = new Pool(poolOptions);

    const dbRetryCount = dbConfig['DATABASE_POSTGRESQL_RETRY_COUNT'];
    const dbRetryWait = dbConfig['DATABASE_POSTGRESQL_RETRY_WAIT'];
    if (!isNaN(parseInt(dbRetryCount))) {
        retryCount = parseInt(dbRetryCount);
    }
    if (!isNaN(parseInt(dbRetryWait))) {
        retryWait = parseInt(dbRetryWait);
    }

    // log pool options
    context.log.info('pg pool options:', poolOptions);
    // log pool retry config
    context.log.info('pg pool retry config:', { retryCount, retryWait });

    // handle pool's error events
    pool.on('error', (err) => console.error('pg pool error:', err));
};

module.exports = {
    /**
     *
     * @param {object} context - context object of the function
     * @param {object} queryParam - param object { sql: string, params: array }
     * @returns {object} res result of query { rows: array, rowCount: number }
     */
    async query(context, { sql, params }) {
        if (!sql) throw new Error('sql is required');
        // init pool if not exist
        if (!pool) {
            await initPool(context);
        }
        let res = null;
        for (let i = 0; i <= retryCount; i++) {
            try {
                const start = Date.now();
                res = await pool.query(sql, params);
                const duration = Date.now() - start;
                context.log('executed query', { sql, duration, rows: res.rowCount });
                break;
            } catch (error) {
                context.log.warn(`pg query failed:`, { sql, error });
                if (i < retryCount) {
                    // pause before next retry
                    const retryIndex = i + 1;
                    const pauseTime = retryWait * retryIndex * retryIndex;
                    context.log.warn(`[${retryIndex}回目] retrying pg query in ${pauseTime} seconds...`);
                    await sleepPromise(pauseTime)
                } else {
                    // last retry but still error
                    throw error;
                }
            }
        }
        return res;
    },
    async getClient() {
        const client = await pool.connect();
        // handle error event so that program don't crash
        const onError = (err) => console.error('pg client error:', err);
        client.once('error', onError);
        const query = client.query;
        const release = client.release;
        // set a timeout of 5 seconds, after which we will log this client's last query
        const timeout = setTimeout(() => {
            console.error('A client has been checked out for more than 5 seconds!');
            console.error(`The last executed query on this client was: ${client.lastQuery}`);
        }, 5000);
        // monkey patch the query method to keep track of the last query executed
        client.query = (...args) => {
            client.lastQuery = args;
            return query.apply(client, args);
        };
        client.release = () => {
            client.removeListener('error', onError);
            // clear our timeout
            clearTimeout(timeout);
            // set the methods back to their old un-monkey-patched version
            client.query = query;
            client.release = release;
            return release.apply(client);
        };
        return client;
    },
};
