const Redis = require('ioredis');
const appConfig = require('../config/appConfig');

/** @type {Redis.Redis|null} */
let redisClient = null;

/**
 * > This function returns a Redis client object that can be used to interact with the Redis database
 * @param context - The context object that is passed to the function.
 * @returns A promise that resolves to a redis client.
 */
const getRedisClient = async (context) => {
    if (redisClient) return redisClient;

    const dbConfig = await appConfig.getDBConfig();
    const redisConfig = {
        port: dbConfig.DATABASE_REDIS_PORT,
        host: dbConfig.DATABASE_REDIS_HOSTNAME,
        password: dbConfig.DATABASE_REDIS_ACCESS_KEY,
    };
    context.log('redisConfig', redisConfig);
    redisClient = new Redis(redisConfig);

    redisClient.on('error', (error) => {
        if (error.code === 'ECONNRESET') {
            context.log.warn('Connection to Redis timed out.');
        } else if (error.code === 'ECONNREFUSED') {
            context.log.warn('Connection to Redis refused!'); // should severityLevel be error ?
        } else {
            context.log.warn('Redis client error:', error);
        }
    });

    redisClient.on('reconnecting', (err) => {
        if (redisClient.status === 'reconnecting') context.log.info('Reconnecting to Redis...');
        else context.log.warn('Error reconnecting to Redis:', err);
    });

    redisClient.on('connect', (err) => {
        if (!err) context.log.info('Connected to Redis!');
    });

    return redisClient;
};

module.exports = {
    getRedisClient,
};
