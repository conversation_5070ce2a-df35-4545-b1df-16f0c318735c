const mongoose = require('mongoose');
const appConfig = require('../config/appConfig');

let db = null;

const init = async (context) => {
    if (!db) {
        try {
            const dbConfig = await appConfig.getDBConfig(context);
            const connectionString = dbConfig['DATABASE_MONGODB_CONNECTION_STRING']
                .concat(appConfig.getPortalConfig().DB_NAME.ORDER_SERVICE)
                .concat(dbConfig['DATABASE_MONGODB_CONNECTION_OPTIONS'] || '');
            db = await mongoose.connect(connectionString, {
                useNewUrlParser: true,
                useUnifiedTopology: true,
            });
            context.log.info('Connect mongoDB success !');
        } catch (error) {
            context.log.error('Connect mongoDB error:', error);
        }
    }
};

const disconnect = async () => {
    await mongoose.disconnect();
};

module.exports = {
    init,
    disconnect,
};
