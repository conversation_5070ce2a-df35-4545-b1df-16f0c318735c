const Joi = require('joi');
const { retrieveMAWPTenantPassword } = require('../pgModels/tenants');
var md2 = require('js-md2');

// Helpers
const { RESULT_CODE_JSON_PARSE_ERROR } = require('../helpers/responseHelper');

const REQUEST_HEADER_ERROR_MAP = {
    SEQUENCE_NO_TOO_LONG: '0001',
    SEQUENCE_NO_INVALID: '0002',
    SEQUENCE_NO_EMPTY: '0003',
    SENDER_SYSTEM_ID_TOO_LONG: '0004',
    SENDER_SYSTEM_ID_INVALID: '0005',
    SENDER_SYSTEM_ID_EMPTY: '0006',
    API_KEY_INVALID: '0007',
    API_KEY_EMPTY: '0009',
    FUNCTION_TYPE_TOO_LONG: '0010',
    FUNCTION_TYPE_EMPTY: '0011',
    TENANT_ID_INVALID_LENGTH: '0012',
    TENANT_ID_INVALID_FORMAT: '0013',
    TENANT_ID_NOT_EXISTS: '0014',
    TENANT_ID_EMPTY: '0015',
};

const RequestHeaderSchema = Joi.object({
    sequenceNo: Joi.string()
        .length(7)
        .required()
        .pattern(/^[a-zA-Z0-9]*$/)
        .messages({
            'string.base': RESULT_CODE_JSON_PARSE_ERROR,
            'string.length': REQUEST_HEADER_ERROR_MAP.SEQUENCE_NO_TOO_LONG, //送信番号の文字長が異常 例：12AB3cde（8桁）
            'string.pattern.base': REQUEST_HEADER_ERROR_MAP.SEQUENCE_NO_INVALID, //送信番号の文字種が異常 例：1234567あ（全角）
            'any.required': REQUEST_HEADER_ERROR_MAP.SEQUENCE_NO_EMPTY, //送信番号の値がない
            'string.empty': REQUEST_HEADER_ERROR_MAP.SEQUENCE_NO_EMPTY, //送信番号の値がない
        }),
    senderSystemId: Joi.string()
        .length(4)
        .required()
        .pattern(/^[a-zA-Z0-9]*$/)
        .messages({
            'string.base': RESULT_CODE_JSON_PARSE_ERROR,
            'string.length': REQUEST_HEADER_ERROR_MAP.SENDER_SYSTEM_ID_TOO_LONG, //送信元システムIDの文字長が異常 例：11111（5桁）
            'string.pattern.base': REQUEST_HEADER_ERROR_MAP.SENDER_SYSTEM_ID_INVALID, //送信元システムIDの文字種が異常 例：111あ（全角）
            'any.required': REQUEST_HEADER_ERROR_MAP.SENDER_SYSTEM_ID_EMPTY, //送信元システムIDの値がない
            'string.empty': REQUEST_HEADER_ERROR_MAP.SENDER_SYSTEM_ID_EMPTY, //送信元システムIDの値がない
        }),
    apiKey: Joi.string().length(72).required().messages({
        'string.base': RESULT_CODE_JSON_PARSE_ERROR,
        'string.length': REQUEST_HEADER_ERROR_MAP.API_KEY_INVALID, //API認証キー：ハッシュ値１が間違い
        'any.required': REQUEST_HEADER_ERROR_MAP.API_KEY_EMPTY, //API認証キーの値がない
        'string.empty': REQUEST_HEADER_ERROR_MAP.API_KEY_EMPTY, //API認証キーの値がない
    }),
    functionType: Joi.string().length(2).required().messages({
        'string.base': RESULT_CODE_JSON_PARSE_ERROR,
        'string.length': REQUEST_HEADER_ERROR_MAP.FUNCTION_TYPE_TOO_LONG, //機能類別の文字長が異常 例：003（3桁）
        'any.required': REQUEST_HEADER_ERROR_MAP.FUNCTION_TYPE_EMPTY, //機能類別の値がない
        'string.empty': REQUEST_HEADER_ERROR_MAP.FUNCTION_TYPE_EMPTY, //機能類別の値がない
    }),
}).unknown();

const CommonHeaderSchema = Joi.object({
    requestHeader: RequestHeaderSchema.required().messages({
        'any.required': RESULT_CODE_JSON_PARSE_ERROR,
    }),
    tenantId: Joi.string()
        .min(6)
        .max(10)
        .required()
        .pattern(/^[a-zA-Z0-9]*$/)
        .messages({
            'string.base': RESULT_CODE_JSON_PARSE_ERROR,
            'string.min': REQUEST_HEADER_ERROR_MAP.TENANT_ID_INVALID_LENGTH, //テナントIDの文字長が異常 例：例：1NS00
            'string.max': REQUEST_HEADER_ERROR_MAP.TENANT_ID_INVALID_LENGTH, //テナントIDの文字長が異常 例：12345678901
            'string.pattern.base': REQUEST_HEADER_ERROR_MAP.TENANT_ID_INVALID_FORMAT, //テナントIDの文字種が異常 例：123456789あ（全角）
            'any.required': REQUEST_HEADER_ERROR_MAP.TENANT_ID_EMPTY, //テナントIDの値がない
            'string.empty': REQUEST_HEADER_ERROR_MAP.TENANT_ID_EMPTY, //テナントIDの値がない
        }),
});

/**
 * Validate common Header (共通ヘッダー)
 * @param {object} context context
 * @param {object} req リクエスト
 * @return {{errorCode:string}|boolean} object - with errorCode indicate validate failed
 *                                      true - indicate validate succeed
 */
const validateCommonHeader = async (context, req) => {
    const { body } = req;
    context.log.info('request body', body);

    const commonHeader = {
        requestHeader: body?.requestHeader,
        tenantId: body?.tenantId,
    };
    const { error } = CommonHeaderSchema.validate(commonHeader);
    //if joi validate failed, return as errorCode
    if (error) return { errorCode: error.details[0].message };

    //check apiKey
    const tenantHashPassword = await retrieveMAWPTenantPassword(context, {
        tenantId: commonHeader.tenantId,
    });

    //テナントID：マスタテーブルに存在しません
    if (!tenantHashPassword) return { errorCode: REQUEST_HEADER_ERROR_MAP.TENANT_ID_NOT_EXISTS };

    const calculatedAPIKey = tenantHashPassword + '$02$' + md2(commonHeader.requestHeader.sequenceNo);

    context.log.info('calc API key:', calculatedAPIKey.toUpperCase());
    context.log.info('request API key:', commonHeader.requestHeader.apiKey);

    if (calculatedAPIKey.toUpperCase() !== commonHeader.requestHeader.apiKey.toUpperCase()) {
        //API認証キーが間違い
        context.log.info('wrong api key, tenant id : ', commonHeader.tenantId);
        return { errorCode: REQUEST_HEADER_ERROR_MAP.API_KEY_INVALID };
    }

    // API key が正しい
    return true;
};

module.exports = {
    validateCommonHeader,
    REQUEST_HEADER_ERROR_MAP,
};
