const GeneralSettingsModel = require('../models/adminService/generalSettings.model');
const { modulo } = require('../utils/numberUtils');

const EID_REGEX = /^[0-9]{32}$/;

/**
 * EIDチェック
 * @param {string} eid
 * @returns {Promise<boolean>} `true` if EID is ok
 */
const validateEID = async (eid) => {
    /** @type Array<string> */
    const prefix = await GeneralSettingsModel.getEIDPrefix();
    if (!prefix.some((p) => eid.startsWith(p))) return false;
    if (!EID_REGEX.test(eid)) return false;
    return modulo(eid, 97) === 1;
};

module.exports = {
    validateEID,
};
