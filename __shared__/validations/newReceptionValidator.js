// 新規受付OTA・新規受付eSIM

const { LINE_OPTION_IDS } = require('../constants/lineOptionsConstants');
const { CONTRACT_TYPES } = require('../constants/simConstants');
const { COCN_TENANT_ID } = require('../constants/specialDefinitions');
const ServicesRestrictSettingModel = require('../models/adminService/servicesRestrictSetting.model');
const { getVoicemailCallWaitingOptionByNNo } = require('../pgModels/customerInfo');
const { findLineOptions, findAllMAWPLineOptions } = require('../pgModels/lineOptions');
const { getNetworkInfoByPlanId } = require('../pgModels/plans');
const { findDaihyoNNumbersByTenantId } = require('../pgModels/tenantNNumbers');
const { checkTenantPlanExists } = require('../pgModels/tenantPlans');
const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
/**
 *
 * @param {object} context
 * @param {string} tenantId
 * @param {string} planId
 * @param {string} [contractType]
 * @param {string} errorCode 契約種別のパラメータが不正
 */
const checkContractTypeForGaibu = async (context, tenantId, planId, contractType, errorCode) => {
    let result = { ok: false, error: '' };
    // 契約種別とプランの契約種別の一致、5Gの場合のチェック、フォーマットのチェックをここで行う
    // (未指定の場合はプランに合わせてあげればいいのでパス)
    const planContractType = await getNetworkInfoByPlanId(context, planId);
    if (planContractType) {
        const is5GEnabledFlag = await ServicesRestrictSettingModel.is5GEnabled(tenantId);

        context.log('checkContractTypeForGaibu', { planContractType, is5GEnabledFlag, contractType });

        if (is5GEnabledFlag && planContractType.network === CONTRACT_TYPES.FIVE_G_NSA) {
            // 5G専用プラン指定時
            if (!contractType) {
                result.ok = true;
            } else if (/^S[HJ]/.test(contractType)) {
                if (
                    // validation for valid values is done using Joi
                    (contractType === 'SH' && !planContractType.voice_flag && !planContractType.sms_enable) ||
                    (contractType === 'SJ' && planContractType.voice_flag && planContractType.sms_enable)
                ) {
                    result.ok = true;
                } else {
                    // プランと契約種別がマッチしていない場合（音声フラグfalseで5G(NSA)(音声)指定時など）
                    result.error = errorCode;
                    context.log('checkContractTypeForGaibu - fail case 1');
                }
            } else {
                // 不正なフォーマット
                result.error = errorCode;
                context.log('checkContractTypeForGaibu - fail case 2');
            }
        } else if (is5GEnabledFlag && planContractType.network === CONTRACT_TYPES.LTE) {
            // 5G利用可能テナントかつnetworkの値がLTE(つまりLTE/5G共用プラン)指定時
            if (!contractType) {
                result.ok = true;
            } else if (/^S[CDEHJ]/.test(contractType)) {
                if (
                    (contractType === 'SC' && !planContractType.voice_flag && !planContractType.sms_enable) ||
                    (contractType === 'SD' && !planContractType.voice_flag && planContractType.sms_enable) ||
                    (contractType === 'SE' && planContractType.voice_flag && planContractType.sms_enable) ||
                    (contractType === 'SH' && !planContractType.voice_flag && !planContractType.sms_enable) ||
                    (contractType === 'SJ' && planContractType.voice_flag && planContractType.sms_enable)
                ) {
                    result.ok = true;
                } else {
                    // プランと契約種別がマッチしていない場合
                    result.error = errorCode;
                    context.log('checkContractTypeForGaibu - fail case 3');
                }
            } else {
                // 不正なフォーマット
                result.error = errorCode;
                context.log('checkContractTypeForGaibu - fail case 4');
            }
        } else if (!is5GEnabledFlag && planContractType.network === CONTRACT_TYPES.LTE) {
            // 5G利用不可テナントかつLTEプラン指定時
            if (!contractType) {
                result.ok = true;
            } else if (/^S[CDE]/.test(contractType)) {
                if (
                    (contractType === 'SC' && !planContractType.voice_flag && !planContractType.sms_enable) ||
                    (contractType === 'SD' && !planContractType.voice_flag && planContractType.sms_enable) ||
                    (contractType === 'SE' && planContractType.voice_flag && planContractType.sms_enable)
                ) {
                    result.ok = true;
                } else {
                    // プランと契約種別がマッチしていない場合
                    result.error = errorCode;
                    context.log('checkContractTypeForGaibu - fail case 5');
                }
            } else {
                // 不正なフォーマット
                result.error = errorCode;
                context.log('checkContractTypeForGaibu - fail case 6');
            }
        } else {
            // エラー(5G利用不可テナントで5Gを指定している場合etc.)
            result.error = errorCode;
            context.log('checkContractTypeForGaibu - fail case 7');
        }
    } else {
        context.log(`checkContractTypeForGaibu - planContractType not found for planId: ${planId}`);
        result.error = errorCode;
    }
    return result;
};

/**
 * Check planId exists in DB
 * @param {object} context
 * @param {string} tenantId
 * @param {string} planId
 * @returns
 */
const validatePlanId = async (context, tenantId, planId) => {
    return await checkTenantPlanExists(context, tenantId, planId);
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {string} data.tenantId
 * @param {string} data.planId
 * @param {string} data.nBan
 * @param {Array<string>} data.lineOptions
 *
 * @param {object} errorCodes
 * @param {string} errorCodes.notFound 回線オプションがマスタテーブルに存在しない
 * @param {string} errorCodes.duplicates 1つの回線オプション分類内で2個以上の回線オプションが指定されている
 */
const validateLineOptions = async (context, data, errorCodes) => {
    let result = { ok: true, error: '' };
    const filteredLineOptions = data.lineOptions.filter((l) => l.length > 0); // remove '' value in data.lineOptions array
    const lineOptions = filteredLineOptions && filteredLineOptions.length > 0 ? filteredLineOptions : null;

    const rawLineOptionSeq =
        (await findLineOptions(context, {
            tenantId: data.tenantId,
            planId: data.planId,
            nNo: data.nBan,
        })) ?? [];
    const nnoOptions = await getVoicemailCallWaitingOptionByNNo(context, data.nBan);
    const allLineOptionSeq = await findAllMAWPLineOptions(context);

    let lineOptionSeq = rawLineOptionSeq;
    if (nnoOptions) {
        const nnoOptionInfos = allLineOptionSeq.filter(
            (x) =>
                x.line_option_id === nnoOptions.voicemail_option_id ||
                x.line_option_id === nnoOptions.callwaiting_option_id
        );
        const lineOptionsWithWholeFlagSeq = rawLineOptionSeq.map((x) => {
            let lineOptionId = x.line_option_id;
            if (x.line_option_type === 2 && nnoOptions.voicemail_option_id) {
                lineOptionId = nnoOptions.voicemail_option_id;
            } else if (x.line_option_type === 3 && nnoOptions.callwaiting_option_id) {
                lineOptionId = nnoOptions.callwaiting_option_id;
            }
            return nnoOptionInfos.find((z) => z.line_option_id === lineOptionId) || x;
        });
        lineOptionSeq = lineOptionsWithWholeFlagSeq;
    }

    let areAllParamLineOptionsInDBLineOptions = true;
    if (lineOptions) {
        // check whether all values in lineOptions exist in lineOptionSeq
        areAllParamLineOptionsInDBLineOptions = lineOptions.every((lineOpt) =>
            lineOptionSeq.some((z) => z.line_option_id === lineOpt)
        );
    }

    if (!areAllParamLineOptionsInDBLineOptions) {
        result.ok = false;
        result.error = errorCodes.notFound;
    } else {
        // １つのLine_option_typeの中に複数のかいせnオプションがあるが、これらは単一選択または選択しない
        // というのが正しくて、複数選択する事は業務ロジック的に不可であるため、これをチェックする。
        let multipleSelectedAtOneLineOptionType = false;
        if (lineOptions) {
            if (!areAllParamLineOptionsInDBLineOptions) {
                // この場合、そもそも回線オプション指定が誤りなのでチェックが不可能で、エラー側に振っておく
                multipleSelectedAtOneLineOptionType = true;
            } else {
                const selectedLineOptionTypeSeq = lineOptions.map((lineOpt) => {
                    return lineOptionSeq.find((z) => z.line_option_id === lineOpt).line_option_type;
                });
                const selectedLineOptionTypeSet = new Set(selectedLineOptionTypeSeq);
                // 複数選択しているものがあれば重複が出るはずで、その場合に、SeqとSetのサイズに違いが出るはず
                multipleSelectedAtOneLineOptionType =
                    selectedLineOptionTypeSet.size !== selectedLineOptionTypeSeq.length;
            }
        }

        if (multipleSelectedAtOneLineOptionType) {
            result.ok = false;
            result.error = errorCodes.duplicates;
        }
    }
    return result;
};

/**
 * テナントのN番をチェック
 * @param {object} context
 * @param {string} tenantId
 * @param {string} nBan
 * @returns {Promise<boolean>}
 */
const validateDaihyoNNo = async (context, tenantId, nBan) => {
    let daihyoNNoSeq = [];
    if (tenantId !== COCN_TENANT_ID) {
        daihyoNNoSeq = await findDaihyoNNumbersByTenantId(context, { tenantId }); // array
    } else {
        daihyoNNoSeq = portalConfig.COCN_DAIHYOU_NNUMBER || ''; // string
    }
    context.log('validateDaihyoNNo', daihyoNNoSeq, nBan);
    return daihyoNNoSeq.includes(nBan);
};

/**
 * @param {object} context
 * @param {Array<string>} lineOptions
 * @returns {boolean}
 */
const validateForwarding = (context, lineOptions) => {
    let isForwardingIsOk = false;
    let isForwardingOption = '',
        isIntlForwardingOption = '';

    if (lineOptions && lineOptions.length > 0) {
        isForwardingOption = lineOptions.find((x) => x === LINE_OPTION_IDS.FORWARD_CALLS) || '';
        isIntlForwardingOption = lineOptions.find((x) => x === LINE_OPTION_IDS.INTERNATIONAL_FORWARD_CALLS) || '';
    }

    if (
        isForwardingOption !== LINE_OPTION_IDS.FORWARD_CALLS &&
        isIntlForwardingOption === LINE_OPTION_IDS.INTERNATIONAL_FORWARD_CALLS
    ) {
        isForwardingIsOk = false;
    } else {
        isForwardingIsOk = true;
    }
    return isForwardingIsOk;
};

/**
 * Check whether fiveGEnabled or not
 * @param {object} context
 * @param {string} tenantId
 * @param {string} contractType
 * @param {string} errorCode
 * @returns {object} result
 */
const checkIfFiveGEnabled = async (context, tenantId, contractType, errorCode) => {
    let result = { ok: false, error: '' };
    // 契約種別が5Gで5G利用不可テナントだった場合エラーを返す
    context.log('checkIfFiveGEnabled START', { tenantId, contractType });

    const is5GEnabledFlag = await ServicesRestrictSettingModel.is5GEnabled(tenantId);
    if (/^S[HJ]/.test(contractType) && !is5GEnabledFlag) {
        result.error = errorCode;
    } else {
        result.ok = true;
    }
    return result;
};

module.exports = {
    checkContractTypeForGaibu,
    validateLineOptions,
    validatePlanId,
    validateDaihyoNNo,
    validateForwarding,
    checkIfFiveGEnabled,
};
