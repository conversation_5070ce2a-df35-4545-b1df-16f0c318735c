const Joi = require('joi').extend(require('@joi/date'));

const ServicesRestrictSettingModel = require('../models/adminService/servicesRestrictSetting.model');

// Helpers
const { RESULT_CODE_JSON_PARSE_ERROR } = require('../helpers/responseHelper');

const SHIPPING_ADDRESS_ERRORS = {
    TENANT_EMTPY: 'ADDRESS_ERROR_001',

    DELIVERY_FLAG_FORMAT_INVALID: 'ADDRESS_ERROR_003',
    POST_CODE_IS_EMPTY: 'ADDRESS_ERROR_004',
    POST_CODE_FORMAT_INVALID: 'ADDRESS_ERROR_005',

    SHIPPING_ADDRESS1_IS_EMPTY: 'ADDRESS_ERROR_006',
    SHIPPING_ADDRESS2_IS_EMPTY: 'ADDRESS_ERROR_007',
    SHIPPING_ADDRESS7_IS_EMPTY: 'ADDRESS_ERROR_008',

    SHIPPING_ADDRESS123_TOO_LONG: 'ADDRESS_ERROR_009',
    SHIPPING_ADDRESS45_TOO_LONG: 'ADDRESS_ERROR_010',
    SHIPPING_ADDRESS89_TOO_LONG: 'ADDRESS_ERROR_025',

    CONTACT_PHONE_NUMBER_IS_EMPTY: 'ADDRESS_ERROR_011',
    DELIVERY_SERVICE_NOT_SUPPORT: 'ADDRESS_ERROR_002',

    SHIPPING_ADDRESS1_TOO_LONG: 'ADDRESS_ERROR_012',
    SHIPPING_ADDRESS2_TOO_LONG: 'ADDRESS_ERROR_013',
    SHIPPING_ADDRESS3_TOO_LONG: 'ADDRESS_ERROR_014',
    SHIPPING_ADDRESS4_TOO_LONG: 'ADDRESS_ERROR_015',
    SHIPPING_ADDRESS5_TOO_LONG: 'ADDRESS_ERROR_016',
    SHIPPING_ADDRESS6_TOO_LONG: 'ADDRESS_ERROR_017',
    SHIPPING_ADDRESS7_TOO_LONG: 'ADDRESS_ERROR_018',
    SHIPPING_ADDRESS8_TOO_LONG: 'ADDRESS_ERROR_019',
    SHIPPING_ADDRESS9_TOO_LONG: 'ADDRESS_ERROR_020',

    DELIVERY_PATTERN_IS_TOO_LONG: 'ADDRESS_ERROR_022',
    EXCEPTION_IS_TOO_LONG: 'ADDRESS_ERROR_023',
    CONTACT_PHONE_NUMBER_FORMAT_INVALID: 'ADDRESS_ERROR_021',

    DELIVERY_FLAG_NOT_ENABLE: 'ADDRESS_ERROR_0024',
};

const shippingAddressSchema = Joi.object({
    deliveryFlag: Joi.string().valid('0', '1').messages({
        'string.base': RESULT_CODE_JSON_PARSE_ERROR,
        'any.only': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_FORMAT_INVALID, //個配フラグが異常。"0"と"1"以外の値
    }),
})
    .when(Joi.object({ deliveryFlag: Joi.valid('1') }).unknown(), {
        then: Joi.object({
            //個別配送フラグが1の場合、住所必須とする
            postalCode: Joi.string()
                .required()
                .regex(/^[0-9]{7}$/)
                .messages({
                    'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                    'any.required': SHIPPING_ADDRESS_ERRORS.POST_CODE_IS_EMPTY, //郵便番号の値がない。
                    'string.empty': SHIPPING_ADDRESS_ERRORS.POST_CODE_IS_EMPTY, //郵便番号の値がない。
                    'string.pattern.base': SHIPPING_ADDRESS_ERRORS.POST_CODE_FORMAT_INVALID, //郵便番号が異常。７桁以外の値
                }),
            shippingAddress1: Joi.string().required().allow('').max(16).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'any.required': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS1_IS_EMPTY, //送付先住所_都道府県の値がない
                'string.empty': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS1_IS_EMPTY, //送付先住所_都道府県の値がない
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS1_TOO_LONG, //送付先住所_都道府県の長さが不正
            }),
            shippingAddress2: Joi.string().required().allow('').max(12).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'any.required': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS2_IS_EMPTY, //送付先住所_市区郡町村の値がない
                'string.empty': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS2_IS_EMPTY, //送付先住所_市区郡町村の値がない
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS2_TOO_LONG, //送付先住所_市区郡町村の長さが不正
            }),
            shippingAddress3: Joi.string().optional().allow(null, '').max(16).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS3_TOO_LONG, //送付先住所_大字通称の長さが不正
            }),
            shippingAddress123: Joi.string().allow('').max(16).messages({
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS123_TOO_LONG, //送付先の長さが異常（１６文字以内ではない）
            }),
            shippingAddress4: Joi.string().optional().allow(null, '').max(12).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS4_TOO_LONG, //送付先住所_字の長さが不正
            }),
            shippingAddress5: Joi.string().optional().allow(null, '').max(16).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS5_TOO_LONG, //送付先住所_丁目番地の長さが不正
            }),
            shippingAddress45: Joi.string().allow('').max(16).messages({
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS45_TOO_LONG, //送付先の長さが異常（１６文字以内ではない）
            }),
            shippingAddress6: Joi.string().optional().allow(null, '').max(16).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS6_TOO_LONG, //送付先住所_ビル名等の長さが不正
            }),
            shippingAddress7: Joi.string().required().allow('').max(16).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'any.required': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS7_IS_EMPTY, //送付先_宛先会社名/氏名の値がない
                'string.empty': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS7_IS_EMPTY, //送付先_宛先会社名/氏名の値がない
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS7_TOO_LONG, //送付先_宛先会社名/氏名の長さが不正
            }),
            shippingAddress8: Joi.string().optional().allow(null, '').max(16).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS8_TOO_LONG, //送付先_宛先部課名の長さが不正
            }),
            shippingAddress9: Joi.string().optional().allow(null, '').max(10).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS9_TOO_LONG, //送付先_宛先担当者の長さが不正
            }),
            shippingAddress89: Joi.string().allow('').max(16).messages({
                'string.max': SHIPPING_ADDRESS_ERRORS.SHIPPING_ADDRESS89_TOO_LONG, //送付先の長さが異常（１６文字以内ではない）
            }),
            contactPhoneNumber: Joi.string()
                .required()
                .max(13)
                .regex(/^0[0-9]+-[0-9]+-[0-9]+$/)
                .messages({
                    'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                    'any.required': SHIPPING_ADDRESS_ERRORS.CONTACT_PHONE_NUMBER_IS_EMPTY, //連絡先電話番号の値がない
                    'string.empty': SHIPPING_ADDRESS_ERRORS.CONTACT_PHONE_NUMBER_IS_EMPTY, //連絡先電話番号の値がない
                    'string.max': SHIPPING_ADDRESS_ERRORS.CONTACT_PHONE_NUMBER_FORMAT_INVALID, //電話番号のフォーマットが不正
                    'string.pattern.base': SHIPPING_ADDRESS_ERRORS.CONTACT_PHONE_NUMBER_FORMAT_INVALID, //電話番号のフォーマットが不正
                }),
            deliveryPattern: Joi.string().optional().allow(null, '').max(30).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'string.max': SHIPPING_ADDRESS_ERRORS.DELIVERY_PATTERN_IS_TOO_LONG, //個別配送パターンの長さが不正
            }),
            exception: Joi.string().optional().allow(null, '').max(100).messages({
                'string.base': RESULT_CODE_JSON_PARSE_ERROR,
                'string.max': SHIPPING_ADDRESS_ERRORS.EXCEPTION_IS_TOO_LONG, //Com使用欄の長さが不正
            }),
        }),
        otherwise: Joi.object({
            //個別配送フラグが1でないのに配送情報を送信しようとする
            postalCode: Joi.forbidden().messages({ 'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE }),
            shippingAddress1: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            shippingAddress2: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            shippingAddress3: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            shippingAddress4: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            shippingAddress5: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            shippingAddress6: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            shippingAddress7: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            shippingAddress8: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            shippingAddress9: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            contactPhoneNumber: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            deliveryPattern: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE,
            }),
            exception: Joi.forbidden().messages({ 'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_FLAG_NOT_ENABLE }),
        }),
    })
    // Joi overide validate, so validate at bottom will be applied first
    .when(Joi.object({ isDeliveryServiceEnabled: false }).unknown(), {
        then: Joi.object({
            //個別配送対応テナントではないのに、個配情報を送信しようとする。
            deliveryFlag: Joi.invalid('1').messages({
                'any.only': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            postalCode: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress1: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress2: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress3: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress4: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress5: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress6: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress7: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress8: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            shippingAddress9: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            contactPhoneNumber: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            deliveryPattern: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
            exception: Joi.forbidden().messages({
                'any.unknown': SHIPPING_ADDRESS_ERRORS.DELIVERY_SERVICE_NOT_SUPPORT,
            }),
        }),
    })
    .unknown();

/**
 * An object containing delivery addressInfo
 * @typedef {object} ShippingInfo
 * @property {string} [deliveryFlag] - 個別配送フラグ
 * @property {string} [postalCode] - 送付先住所_郵便番号
 * @property {string} [shippingAddress1] - 送付先住所_都道府県
 * @property {string} [shippingAddress2] - 送付先住所_市区郡町村
 * @property {string} [shippingAddress3] - 送付先住所_大字通称
 * @property {string} [shippingAddress4] - 送付先住所_字
 * @property {string} [shippingAddress5] - 送付先住所_丁目番地
 * @property {string} [shippingAddress6] - 送付先住所_ビル名等
 * @property {string} [shippingAddress7] - 送付先_宛先会社名/氏名
 * @property {string} [shippingAddress8] - 送付先_宛先部課名
 * @property {string} [shippingAddress9] - 送付先_宛先担当者
 * @property {string} [contactPhoneNumber] - 連絡先電話番号
 * @property {string} [deliveryPattern] - 個別配送パターン
 * @property {string} [exception] - Com使用欄
 */

/**
 * Function to validate and parse shippingAddress (個別配送)
 * @param  {object} body - request params
 * @return {{errorCode:string}|ShippingInfo} errorObject  - indicate validate failed
 *                                           shippingInfo - indicate validate succeed
 */
const validateAndParseShippingAddress = async (body) => {
    //テナントID: テナントIDが存在しない
    if (!body.tenantId) return { errorCode: SHIPPING_ADDRESS_ERRORS.TENANT_EMTPY };

    body.isDeliveryServiceEnabled = await ServicesRestrictSettingModel.isDeliveryServiceEnabled(body.tenantId);
    body.deliveryFlag ||= '0';
    body.shippingAddress123 = [body.shippingAddress1 ?? '', body.shippingAddress2 ?? '', body.shippingAddress3 ?? ''].join('');
    body.shippingAddress45 = [body.shippingAddress4 ?? '', body.shippingAddress5 ?? ''].join('');
    body.shippingAddress89 = [body.shippingAddress8 ?? '', body.shippingAddress9 ?? ''].join('');

    const { error } = shippingAddressSchema.validate(body);

    // if joi validate failed, return as errorCode
    if (error) return { errorCode: error.details[0].message };

    const shippingInfo = {
        deliveryFlag: body.deliveryFlag,
        postalCode: body.postalCode,
        shippingAddress1: body.shippingAddress1,
        shippingAddress2: body.shippingAddress2,
        shippingAddress3: body.shippingAddress3,
        shippingAddress4: body.shippingAddress4,
        shippingAddress5: body.shippingAddress5,
        shippingAddress6: body.shippingAddress6,
        shippingAddress7: body.shippingAddress7,
        shippingAddress8: body.shippingAddress8,
        shippingAddress9: body.shippingAddress9,
        contactPhoneNumber: body.contactPhoneNumber,
        deliveryPattern: body.deliveryPattern,
        exception: body.exception,
    };

    return { shippingInfo };
};

module.exports = {
    validateAndParseShippingAddress,
    SHIPPING_ADDRESS_ERRORS,
};
