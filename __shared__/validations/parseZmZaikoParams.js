const _ = require('lodash');
const { MAWP_ROLE_ID } = require('../constants/userRole');
const { isNone } = require('../helpers/baseHelper');
const { getParamIntValue } = require('../utils/stringUtils');
const { mvnoServiceFromCode } = require('../constants/mvnoServicesConstants');
const { SIM_ZAIKO_SEARCH_TYPES } = require('../constants/simConstants');

const SEARCH_TYPES = Object.values(SIM_ZAIKO_SEARCH_TYPES);
const SORT_BY_FIELDS = [
    'sim_youto',
    'tenant_id',
    'tempo_id',
    'sim_type',
    'sim_info',
    'kari_kaisen_no',
    'kaisen_no',
    'zaiko_status',
    'sim_status',
    'sim_contract_type',
    'zaiko_status_change_date',
    'sim_expired',
    'makuhari_zaiko_kanri_ran',
];
const SORT_ORDER_ASCENDING = 'asc';

const parseZmZaikoParams = (body, roleId, currentTenantId, currentTempoId, superTempoCode) => {
    const searchTypeParam = _.parseInt(body.search_type);
    const searchType = SEARCH_TYPES.includes(searchTypeParam) ? searchTypeParam : null;
    const searchValue = body.search_value && body.search_value.length > 0 ? body.search_value : null;

    let tenantIds = [];
    switch (roleId) {
        case MAWP_ROLE_ID.TEMPO_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
            tenantIds = [currentTenantId];
            break;
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
            tenantIds = getArray(body.tenant_id);
            break;
    }

    // テナントIDを選択する事が可能なユーザ種別で、テナントIDの指定があり、かつ2つ以上指定されている場合は、
    // 店舗IDの指定を無かった事にする
    let tempoIds = [];
    if (tenantIds.length < 2) {
        if (roleId == MAWP_ROLE_ID.TEMPO_USER) {
            // 店舗オペレータは自分の店舗しか検索できない
            if (superTempoCode) {
                // スーパー店舗オペラータは管理する店舗で検索できる
                const tempoIdParam = getArray(body.tempo_id);
                tempoIds = tempoIdParam.filter((tempoId) => tempoId.includes(superTempoCode));
            } else {
                tempoIds = [currentTempoId];
            }
        } else {
            tempoIds = getArray(body.tempo_id);
        }
    }

    const sortBy = SORT_BY_FIELDS.includes(body.sort_by) ? body.sort_by : null;
    const isSortOrderAscending = body.sort_order === SORT_ORDER_ASCENDING;

    return {
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses: anyToIntArray(body.zaiko_status),
        simStatuses: anyToStringArray(body.sim_status),
        simContractTypes: anyToStringArray(body.sim_contract_type),
        simTypes: anyToStringArray(body.sim_type),
        simYoutos: anyToStringArray(body.sim_youto),
        fromSimStatusChangeDate: _.parseInt(body.from_sim_status_change_date),
        toSimStatusChangeDate: _.parseInt(body.to_sim_status_change_date),
        isNeedCheckOption: isNone(body.kaisen_option_flag) ? null : !!body.kaisen_option_flag,
        setChecked: !!body.set_checked,
        zmIds: anyToStringArray(body.zm_ids),
        isAllSelected: !!body.is_all_selected,
        skip: _.parseInt(body.skip ?? 0),
        limit: _.parseInt(body.limit ?? 0),
        sortBy,
        isSortOrderAscending,
        service: mvnoServiceFromCode(getParamIntValue(body.service)),
        fiveGContractTypes: anyToStringArray(body.five_g_contract_type),
        fromZaikoStatusChangeDate: _.parseInt(body.from_zaiko_status_change_date),
        toZaikoStatusChangeDate: _.parseInt(body.to_zaiko_status_change_date),
    };
};

const anyToStringArray = (v) => {
    if (_.isArray(v)) {
        return _.compact(v.map((e) => _.toString(e)));
    }
    if (_.isString(v)) {
        return _.compact([v]);
    }
    return [];
};

const anyToIntArray = (v) => {
    if (_.isArray(v)) {
        return _.compact(v.map((e) => _.parseInt(e)));
    }
    if (_.isString(v)) {
        return _.compact([_.parseInt(v)]);
    }
    if (_.isNumber(v)) {
        return _.compact([v]);
    }
    return [];
};

const getArray = (v) => {
    return isNone(v) || !Array.isArray(v) ? [] : v;
};

module.exports = {
    parseZmZaikoParams,
};
