// 新規受付eSIMのリクエストバリデーション
// バリデーションを実行する前に、リクエストテナントがeSIM対応のテナントであるかを確認する必要がある
const Joi = require('joi');
const { FIVEG_CONTRACT_TYPES, COCN_TENANT_ID } = require('../../constants/specialDefinitions');
const { checkIs020Support } = require('../../helpers/daihyouBangoHelper');
const { RESULT_CODE_JSON_PARSE_ERROR } = require('../../helpers/responseHelper');
const TenantPlanLineOptionSettingModel = require('../../models/adminService/tenantPlanLineOptionSetting.model');
const DaihyouBangoModel = require('../../models/daihyouBango.model');
const { convertPlanIdToRyoukinPlan } = require('../../pgModels/plans');
const { isTempoValid } = require('../../pgModels/shops');
const { tenantNBanYukoHantei } = require('../../pgModels/tenantNNumbers');
const { validateEID } = require('../../validations/eidValidator');
const {
    checkContractTypeForGaibu,
    validateLineOptions,
    validateDaihyoNNo,
    validateForwarding,
    validatePlanId,
} = require('../../validations/newReceptionValidator');
const { REQUEST_HEADER_ERROR_MAP } = require('../../validations/requestHeader');
const { ERROR_MAP } = require('./errorHelper');

const NeweSIMRequestSchema = Joi.object({
    tenantId: Joi.string()
        .min(6)
        .max(10)
        .required()
        .pattern(/^[a-zA-Z0-9]*$/)
        .messages({
            'string.base': RESULT_CODE_JSON_PARSE_ERROR,
            'string.min': REQUEST_HEADER_ERROR_MAP.TENANT_ID_INVALID_LENGTH, //テナントIDの文字長が異常 例：例：1NS00
            'string.max': REQUEST_HEADER_ERROR_MAP.TENANT_ID_INVALID_LENGTH, //テナントIDの文字長が異常 例：12345678901
            'string.pattern.base': REQUEST_HEADER_ERROR_MAP.TENANT_ID_INVALID_FORMAT, //テナントIDの文字種が異常 例：123456789あ（全角）
            'any.required': REQUEST_HEADER_ERROR_MAP.TENANT_ID_EMPTY, //テナントIDの値がない
            'string.empty': REQUEST_HEADER_ERROR_MAP.TENANT_ID_EMPTY, //テナントIDの値がない
        }),
    shopId: Joi.string().required().max(10).messages({
        'string.base': RESULT_CODE_JSON_PARSE_ERROR,
        'any.required': ERROR_MAP.SHOP_ID_INVALID,
        'string.empty': ERROR_MAP.SHOP_ID_INVALID,
        'string.max': ERROR_MAP.SHOP_ID_INVALID,
    }),
    planId: Joi.string().required().length(5).messages({
        'string.base': RESULT_CODE_JSON_PARSE_ERROR,
        'any.required': ERROR_MAP.PLAN_ID_INVALID,
        'string.empty': ERROR_MAP.PLAN_ID_INVALID,
        'string.length': ERROR_MAP.PLAN_ID_INVALID,
    }),
    eid: Joi.string().required().max(32).messages({
        // format is validated by `validateEID`
        'string.base': RESULT_CODE_JSON_PARSE_ERROR,
        'any.required': ERROR_MAP.EID_INVALID,
        'string.empty': ERROR_MAP.EID_INVALID,
        'string.max': ERROR_MAP.EID_INVALID,
    }),
    contractType: Joi.string()
        .max(2)
        .valid(...FIVEG_CONTRACT_TYPES)
        .messages({
            'string.base': RESULT_CODE_JSON_PARSE_ERROR,
            'string.empty': ERROR_MAP.CONTRACT_TYPE_INVALID,
            'string.max': ERROR_MAP.CONTRACT_TYPE_INVALID,
            'any.only': ERROR_MAP.CONTRACT_TYPE_INVALID,
        }),
    lineOptions: Joi.array().required().items(Joi.string()).messages({
        'array.base': ERROR_MAP.KAISEN_OPTION_INVALID,
        'any.required': ERROR_MAP.KAISEN_OPTION_EMPTY,
        'string.base': ERROR_MAP.KAISEN_OPTION_INVALID,
        'string.empty': ERROR_MAP.KAISEN_OPTION_INVALID,
    }),
    nBan: Joi.string().required().length(10).messages({
        'string.base': RESULT_CODE_JSON_PARSE_ERROR,
        'any.required': ERROR_MAP.NBAN_EMPTY,
        'string.empty': ERROR_MAP.NBAN_INVALID,
        'string.length': ERROR_MAP.NBAN_INVALID,
    }),
    shopMemo: Joi.string().max(20).allow('').messages({
        'string.base': RESULT_CODE_JSON_PARSE_ERROR,
        'string.max': ERROR_MAP.SHOP_MEMO_OVERLENGTH,
    }),
    notifyPattern: Joi.string()
        .length(1)
        .valid('1', '2')
        .when('tenantId', {
            not: COCN_TENANT_ID,
            then: Joi.forbidden(), // CON000の時のみ指定可能
        })
        .messages({
            'string.base': RESULT_CODE_JSON_PARSE_ERROR,
            'string.length': ERROR_MAP.NOTIFY_PATTERN_INVALID,
            'string.empty': ERROR_MAP.NOTIFY_PATTERN_INVALID,
            'any.only': ERROR_MAP.NOTIFY_PATTERN_INVALID,
            'any.unknown': ERROR_MAP.NOTIFY_PATTERN_EXISTS,
        }),
    dummyLineNo: Joi.string()
        .pattern(/^[0-9]+$/)
        .max(14)
        .when('tenantId', {
            is: COCN_TENANT_ID,
            then: Joi.required(),
        })
        .messages({
            'string.base': RESULT_CODE_JSON_PARSE_ERROR,
            'string.max': ERROR_MAP.DUMMY_LINE_NO_INVALID,
            'string.pattern.base': ERROR_MAP.DUMMY_LINE_NO_INVALID,
            'any.required': ERROR_MAP.DUMMY_LINE_NO_MISSING,
        }),
}).unknown();

const validatePlanIdWithRyoukinPlan = async (context, planId, contractType) => {
    return await convertPlanIdToRyoukinPlan(context, {
        planId,
        contractType,
    });
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {string} data.tenantId
 * @param {string} data.planId
 * @param {string|null} ryoukinPlan get from `convertPlanIdToRyoukinPlan`
 */
const validateDaihyoBango = async (context, data, ryoukinPlan) => {
    let mvnoKakinJyoho = false;
    if (checkIs020Support(ryoukinPlan)) {
        const tenantLineSetting = await TenantPlanLineOptionSettingModel.getDefaultLineOption(
            data.tenantId,
            data.planId,
            false, // isShinki
            false, // isMnp
            true, // iseSIM
            false // is020Support
        );
        if (tenantLineSetting) {
            mvnoKakinJyoho = tenantLineSetting.mvnoKakinJyoho;
        }
    }
    const daihyouBangoInfo = await DaihyouBangoModel.findByTenantAndryoukinPlanForShinki(
        data.tenantId,
        ryoukinPlan,
        mvnoKakinJyoho
    );
    return daihyouBangoInfo ? true : false;
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {string} data.tenantId
 * @param {string}data.planId
 * @param {string} data.shopId
 * @param {string} data.nBan
 * @param {Array<string>} data.lineOptions
 * @param {string} [data.contractType]
 * @returns
 */
const validateWithDbConnection = async (context, data) => {
    let result = { ok: false, error: '' };

    const [
        validTempo,
        isPlanIdValid,
        isLineOptValid,
        ryoukinPlan,
        isNBanExist,
        isNBanYuko,
        isForwardingIsOk,
        allowedForReception,
    ] = await Promise.all([
        isTempoValid(context, data.shopId),
        validatePlanId(context, data.tenantId, data.planId),
        validateLineOptions(context, data, {
            notFound: ERROR_MAP.KAISEN_OPTION_NOT_FOUND,
            duplicates: ERROR_MAP.KAISEN_OPTION_DUPLICATES,
        }),
        validatePlanIdWithRyoukinPlan(context, data.planId, data.contractType),
        validateDaihyoNNo(context, data.tenantId, data.nBan),
        tenantNBanYukoHantei(context, {
            tenantId: data.tenantId,
            Nno: data.nBan,
        }),
        validateForwarding(context, data.lineOptions),
        TenantPlanLineOptionSettingModel.checkPlanExists(data.tenantId, parseInt(data.planId), { iseSIM: true }),
    ]);

    const isRyoukinPlanValid = ryoukinPlan ? true : false;

    if (!validTempo) {
        result.error = ERROR_MAP.SHOP_ID_NOT_FOUND;
    } else if (!isPlanIdValid) {
        result.error = ERROR_MAP.PLAN_ID_NOT_FOUND;
    } else if (!isLineOptValid.ok) {
        result.error = isLineOptValid.error;
    } else if (!isRyoukinPlanValid) {
        result.error = ERROR_MAP.PLAN_ID_NOT_FOUND;
    } else if (!isNBanExist) {
        result.error = ERROR_MAP.NBAN_NOT_FOUND;
    } else if (!isNBanYuko) {
        result.error = ERROR_MAP.NBAN_NOT_AVAILABLE;
    } else if (!isForwardingIsOk) {
        result.error = ERROR_MAP.FORWARDING_INVALID;
    } else if (!allowedForReception) {
        // https://mobilus.backlog.jp/view/MVNO_N_M-2623#comment-200574323
        // not found means 新規受付（eSIM)可否 is ×
        result.error = ERROR_MAP.PLAN_ID_NOT_FOUND;
    } else {
        result.ok = true;
    }

    // (登録する, 受付NG理由(登録する= true でこの値がNoneの場合は受付OK), nttc_mvno_error_code)
    if (result.ok) {
        if (!(await validateDaihyoBango(context, data, ryoukinPlan))) {
            result.ok = false;
            result.error = ERROR_MAP.DAIHYOU_BANGO_INVALID;
        }
    }

    return result;
};

const validateNeweSIMRequest = async (context, body) => {
    let result = { ok: false, error: '' };
    let { error } = NeweSIMRequestSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
        return result;
    }

    // early return if planId is non numeric
    if (isNaN(parseInt(body.planId))) {
        result.error = ERROR_MAP.PLAN_ID_NOT_FOUND;
        return result;
    }

    // validate EID format
    const eidCheck = await validateEID(body.eid);
    if (!eidCheck) {
        result.error = ERROR_MAP.EID_INVALID;
        return result;
    }

    // TODO check contract type validation
    const contractCheck = await checkContractTypeForGaibu(
        context,
        body.tenantId,
        body.planId,
        body.contractType,
        ERROR_MAP.CONTRACT_TYPE_INVALID
    );
    if (!contractCheck.ok) {
        result.error = contractCheck.error;
        return result;
    }
    const dbCheck = await validateWithDbConnection(context, body);
    if (!dbCheck.ok) {
        result.error = dbCheck.error;
        return result;
    }

    result.ok = true;
    return result;
};

module.exports = {
    validateNeweSIMRequest,
};
