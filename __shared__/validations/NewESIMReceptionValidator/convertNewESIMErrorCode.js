const { ERROR_MAP } = require('./errorHelper');

const NEW_ESIM_ERROR_TO_OPFERROR = {
    [ERROR_MAP.SHOP_ID_NOT_FOUND]: '992005',
    [ERROR_MAP.SHOP_ID_INVALID]: '992005',
    [ERROR_MAP.PLAN_ID_NOT_FOUND]: '992006',
    [ERROR_MAP.PLAN_ID_INVALID]: '994091',
    [ERROR_MAP.EID_INVALID]: '997603',
    [ERROR_MAP.CONTRACT_TYPE_INVALID]: '991001',
    [ERROR_MAP.KAISEN_OPTION_INVALID]: '991001',
    [ERROR_MAP.KAISEN_OPTION_EMPTY]: '992007',
    [ERROR_MAP.KAISEN_OPTION_NOT_FOUND]: '992007',
    [ERROR_MAP.NBAN_NOT_FOUND]: '992008',
    [ERROR_MAP.NBAN_INVALID]: '992011',
    [ERROR_MAP.NBAN_EMPTY]: '992011',
    [ERROR_MAP.SHOP_MEMO_OVERLENGTH]: '994051',
    [ERROR_MAP.NOTIFY_PATTERN_INVALID]: '991002',
    [ERROR_MAP.DUMMY_LINE_NO_MISSING]: '990002',
    [ERROR_MAP.DUMMY_LINE_NO_INVALID]: '990002',
    [ERROR_MAP.TENANT_CANNOT_USE_ESIM]: '997601',
    [ERROR_MAP.DAIHYOU_BANGO_INVALID]: '992004', // parameter error
    [ERROR_MAP.KAISEN_OPTION_DUPLICATES]: '992004', // parameter error
    [ERROR_MAP.FORWARDING_INVALID]: '994045',
    [ERROR_MAP.NBAN_NOT_AVAILABLE]: '992004', // parameter error
    [ERROR_MAP.NOTIFY_PATTERN_EXISTS]: '992004', // parameter error

    [ERROR_MAP.SYSTEM_ERROR]: '990002',
};

module.exports = {
    NEW_ESIM_ERROR_TO_OPFERROR,
};
