const ERROR_MAP = {
    /** 店舗IDがマスタテーブルに存在しない */
    SHOP_ID_NOT_FOUND: '1001',
    /** 店舗IDの値がない、または異常値 */
    SHOP_ID_INVALID: '1002',
    /** プランIDがマスタテーブルに存在しない */
    PLAN_ID_NOT_FOUND: '1003',
    /** プランIDの値がない、または異常値 */
    PLAN_ID_INVALID: '1004',
    /** EIDの値がない、または異常値 */
    EID_INVALID: '1005',
    /** NW契約の値が異常値 (SH・SJ以外) */
    CONTRACT_TYPE_INVALID: '1006',
    /** 回線オプションのフォーマットが異常 */
    KAISEN_OPTION_INVALID: '1007',
    /** 回線オプションの値がない */
    KAISEN_OPTION_EMPTY: '1008',
    /** 回線オプション：マスタテーブルに存在しない */
    KAISEN_OPTION_NOT_FOUND: '1009',
    /** N番：マスタテーブルに存在しない */
    NBAN_NOT_FOUND: '1010',
    /** N番の文字長が異常 ＜文字長＞：10桁（固定） */
    NBAN_INVALID: '1011',
    /** N番の値がない ＜必須＞：あり */
    NBAN_EMPTY: '1012',
    /** 店舗使用欄の長さが異常 */
    SHOP_MEMO_OVERLENGTH: '1013',

    /** 通知パータンの値が異常 */
    NOTIFY_PATTERN_INVALID: '1014',
    /** テナントIDがCON000なのに、ダミー回線番号がない */
    DUMMY_LINE_NO_MISSING: '1015',
    /** ダミー回線番号のフォーマットが異常(桁数か数字のチェック) */
    DUMMY_LINE_NO_INVALID: '1016',

    /** テナントが利用できない */
    TENANT_CANNOT_USE_ESIM: '1017',

    /** 代表番号が不正 */
    DAIHYOU_BANGO_INVALID: '1018',

    /** 1つの回線オプション分類内で2個以上の回線オプションが指定されている */
    KAISEN_OPTION_DUPLICATES: '1019',
    /** 国際着信転送があるけど、転送でんわの値がない、または異常 */
    FORWARDING_INVALID: '1020',
    /** テナントN番有効判定が「N番が紐づいていない」 */
    NBAN_NOT_AVAILABLE: '1021',

    /** テナントIDがCON000ではないけど、通知パータンが指定されている */
    NOTIFY_PATTERN_EXISTS: '1022',

    SYSTEM_ERROR: '1999',
};

module.exports = {
    ERROR_MAP,
};
