const Joi = require('joi');
const { SIM_ZAIKO_SEARCH_TYPES } = require('../constants/simConstants');
const { MAWP_ROLE_ID } = require('../constants/userRole');
const { isNone } = require('../helpers/baseHelper');
const { getSuperTempoCode, getParamIntValue, getParamBooleanValue } = require('../utils/stringUtils');

const SEARCH_TYPE = Object.values(SIM_ZAIKO_SEARCH_TYPES);
const SORT_BY = [
    'sim_youto',
    'tenant_id',
    'tempo_id',
    'sim_type',
    'sim_info',
    'kari_kaisen_no',
    'kaisen_no',
    'zaiko_status',
    'sim_status',
    'sim_contract_type',
    'zaiko_status_change_date',
    'sim_expired',
    'makuhari_zaiko_kanri_ran',
];

const SearchSimZaikoSchema = Joi.object({
    search_type: Joi.number().valid(0, 1, 2, 3),
    search_value: Joi.string().allow(''),
    tenant_id: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    tempo_id: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    zaiko_status: Joi.array().items(Joi.number()),
    zm_ids: Joi.array().items(Joi.string()),
    sort_by: Joi.string(),
    sim_status: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    sim_contract_type: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    sim_type: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    sim_youto: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    from_sim_status_change_date: Joi.number(),
    to_sim_status_change_date: Joi.number(),
    kaisen_option_flag: Joi.boolean(),
    set_checked: Joi.boolean(),
    is_all_selected: Joi.boolean(),
    skip: Joi.number(),
    limit: Joi.number(),
    sort_order: Joi.string(),
    service: Joi.number(),
    five_g_contract_type: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
    from_zaiko_status_change_date: Joi.number(),
    to_zaiko_status_change_date: Joi.number(),
}).unknown();

/**
 * @typedef ZaikoPostParamType
 * @property {number} [searchType]
 * @property {string} [searchValue]
 * @property {Array<string>} [tenantIds]
 * @property {Array<string>} [tempoIds]
 * @property {Array<number>} [zaikoStatuses]
 * @property {Array<string>} [simStatuses]
 * @property {Array<string>} [simContractTypes]
 * @property {Array<string>} [simTypes]
 * @property {Array<string>} [simYoutos]
 * @property {number} [fromSimStatusChangeDate]
 * @property {number} [toSimStatusChangeDate]
 * @property {boolean} [isNeedCheckOption]
 * @property {boolean} setChecked
 * @property {Array<string>} [zmIds]
 * @property {boolean} isAllSelected
 * @property {number} [skip]
 * @property {number} [limit]
 * @property {string} [sortBy]
 * @property {boolean} isSortOrderAscending
 * @property {number} service
 * @property {Array<string>} [fiveGContractTypes]
 * @property {number} [fromZaikoStatusChangeDate]
 * @property {number} [toZaikoStatusChangeDate]
 */

/**
 *
 * @param {object} body
 * @param {number} roleId
 * @param {string} tenantId
 * @param {string} userTempoId
 * @param {object} errorCodes
 * @param {string} errorCodes.errorInvalidParameter
 * @param {string} errorCodes.errorSearchQueryRequired
 */
const validateAndParseZaikoPostParam = (
    body,
    roleId,
    tenantId,
    userTempoId,
    { errorInvalidParameter, errorSearchQueryRequired }
) => {
    const { error } = SearchSimZaikoSchema.validate(body);
    if (error) {
        return {
            error: errorInvalidParameter,
            errorMessage: error.details[0].message,
        };
    }

    const searchType = SEARCH_TYPE.includes(+body.search_type) ? +body.search_type : undefined;
    const searchValue = !isNone(body.search_value) ? body.search_value : undefined;

    /** @type {Array<string>|undefined} */
    let tenantIds = undefined;
    switch (roleId) {
        case MAWP_ROLE_ID.TEMPO_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
            tenantIds = [tenantId];
            break;
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
            if (!isNone(body.tenant_id)) {
                if (Array.isArray(body.tenant_id)) {
                    tenantIds = body.tenant_id;
                } else {
                    tenantIds = [body.tenant_id];
                }
            }
            break;
    }

    /** @type {Array<string>|undefined} */
    let tempoIds = undefined;
    if (isNone(tenantIds) || tenantIds.length < 2) {
        if (roleId === MAWP_ROLE_ID.TEMPO_USER) {
            // 店舗オペレータは自分の店舗しか検索できない
            const superTempoCode = getSuperTempoCode(userTempoId);
            if (isNone(superTempoCode)) {
                tempoIds = [userTempoId];
            } else {
                // スーパー店舗オペラータは管理する店舗で検索できる
                tempoIds = [];
                if (!isNone(body.tempo_id)) {
                    if (Array.isArray(body.tempo_id)) {
                        tempoIds = body.tempo_id;
                    } else if (typeof body.tempo_id === 'string') {
                        tempoIds = [body.tempo_id];
                    }
                }
                tempoIds = tempoIds.filter((v) => v.includes(superTempoCode));
            }
        } else {
            if (!isNone(body.tempo_id)) {
                tempoIds = Array.isArray(body.tempo_id) ? body.tempo_id : [body.tempo_id];
            }
        }
    }

    const zaikoStatuses = isNone(body.zaiko_status) ? undefined : body.zaiko_status.map((v) => +v);
    const zmIds = isNone(body.zm_ids) ? undefined : body.zm_ids;
    const sortBy = SORT_BY.includes(body.sort_by) ? body.sort_by : undefined;

    let fiveGTypes = undefined;
    if (typeof body.five_g_contract_type === 'string') {
        fiveGTypes = [body.five_g_contract_type];
    } else if (Array.isArray(body.five_g_contract_type)) {
        fiveGTypes = body.five_g_contract_type;
    }

    /** @type {ZaikoPostParamType} */
    const param = {
        searchType,
        searchValue,
        tenantIds,
        tempoIds,
        zaikoStatuses,
        simStatuses: tryArray(body.sim_status),
        simContractTypes: tryArray(body.sim_contract_type),
        simTypes: tryArray(body.sim_type),
        simYoutos: tryArray(body.sim_youto),
        fromSimStatusChangeDate: getParamIntValue(body.from_sim_status_change_date, undefined),
        toSimStatusChangeDate: getParamIntValue(body.to_sim_status_change_date, undefined),
        isNeedCheckOption: getParamBooleanValue(body.kaisen_option_flag, undefined),
        setChecked: getParamBooleanValue(body.set_checked, false),
        zmIds,
        isAllSelected: getParamBooleanValue(body.is_all_selected, false),
        skip: getParamIntValue(body.skip, undefined),
        limit: getParamIntValue(body.limit, undefined),
        sortBy,
        isSortOrderAscending: body.sort_order === 'asc',
        service: getParamIntValue(body.service, 0),
        fiveGContractTypes: fiveGTypes,
        fromZaikoStatusChangeDate: getParamIntValue(body.from_zaiko_status_change_date),
        toZaikoStatusChangeDate: getParamIntValue(body.to_zaiko_status_change_date),
    };

    if ((isNone(param.searchType) || isNone(param.searchValue)) && !param.setChecked && isNone(param.limit)) {
        return {
            error: errorSearchQueryRequired,
            errorMessage: 'invalid parameter',
        };
    }

    return { param };
};

module.exports = {
    validateAndParseZaikoPostParam,
};

const tryArray = (v, defaultValue = undefined) => {
    if (isNone(v)) {
        return defaultValue;
    } else {
        return Array.isArray(v) ? v : [v];
    }
};
