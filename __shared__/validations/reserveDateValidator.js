const { getTimeInJST, getTimeAfterXDay } = require('../utils/datetimeUtils');

/**
 * 当日<------>y日<---->予約可能日<------>MAX
 * @param {number} reserveDateUnix unix timestamp in second
 * @param {number} cannotRegisterAfterDays MAX
 * @param {number} acceptableBeforeXDays y日
 */
const validateReserveDate = (reserveDateUnix, cannotRegisterAfterDays, acceptableBeforeXDays) => {
    const currentTime = getTimeInJST();

    // 予約日のy日前でないと予約できない*/
    const isBefore = +reserveDateUnix < getTimeAfterXDay(currentTime, acceptableBeforeXDays).unix();

    // x日先の日付を計算するときは+1をしないと１ずれてしまいます。
    const isAfter = +reserveDateUnix > getTimeAfterXDay(currentTime, cannotRegisterAfterDays + 1).unix();

    return !isBefore && !isAfter;
};

module.exports = {
    validateReserveDate,
};
