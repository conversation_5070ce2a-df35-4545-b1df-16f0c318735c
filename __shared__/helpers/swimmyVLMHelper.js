const appConfig = require('../config/appConfig');
const {
    SWIMMY_VLM_LOG_LINK_TYPE,
    SWIMMY_VLM_REQUEST_TYPE_PRIORTY_KEY,
    VLM_LOWEST_API_PRIORITY,
} = require('../constants/swimmyVLMTransactions');
const { getTimeInJST } = require('../utils/datetimeUtils');

const portalConfig = appConfig.getPortalConfig();

const getLinkType = (tenantId) => {
    if (portalConfig.SWIMMY_VLM_API.LINK_BY_FILE_TENANTS.includes(tenantId)) {
        return SWIMMY_VLM_LOG_LINK_TYPE.FILE;
    } else {
        return SWIMMY_VLM_LOG_LINK_TYPE.API_GATEWAY;
    }
};

/**
 * getPriority for Swimmy VLM API Log
 * @param {object} context
 * @param {number} requestOrderType SWIMMY_VLM_REQUEST_TYPES
 * @returns {number}
 */
const getSwimmyVLMPriority = (context, requestOrderType) => {
    const priorityKey = SWIMMY_VLM_REQUEST_TYPE_PRIORTY_KEY[requestOrderType];
    if (priorityKey === undefined || priorityKey === null) {
        context.log(`Cannot get key string for Request order type value: ${requestOrderType}`);
        return VLM_LOWEST_API_PRIORITY;
    } else {
        /** @type {number|undefined} */
        let priority = portalConfig.SWIMMY_VLM_API.API_PRIORITY_LIST[priorityKey];
        if (priority === undefined || priority === null) {
            context.log(`Cannot get value in config for ${priorityKey}`);
            priority = VLM_LOWEST_API_PRIORITY;
        }
        return priority;
    }
};

/**
 * Check if current time is in monthly maintenance time
 * @param {object} context 
 * @returns {boolean}
 */
const isMonthEndMaintenance = (context) => {
    const { MONTHLY_MAINTENANCE_START, MONTHLY_MAINTENANCE_END } = portalConfig.SWIMMY_VLM_API.POLLING;
    const now = getTimeInJST();

    //If currentDay == first day of month && time <= end time set in config file
    if (now.date() === 1) {
        const maintenanceEnd = now.hour(MONTHLY_MAINTENANCE_END.HOUR).minute(MONTHLY_MAINTENANCE_END.MINUTE);
        if (now.isBefore(maintenanceEnd)) {
            context.log('Monthly maintenance is running');
            return true;
        } else {
            return false;
        }
    }

    //If currentDay == last day of month && time >= start time set in config file
    if (now.date() === now.daysInMonth()) {
        const maintenanceStart = now.hour(MONTHLY_MAINTENANCE_START.HOUR).minute(MONTHLY_MAINTENANCE_START.MINUTE);
        if (now.isAfter(maintenanceStart)) {
            context.log('Monthly maintenance is running');
            return true;
        } else {
            return false;
        }
    }

    return false;
};

module.exports = {
    getLinkType,
    getSwimmyVLMPriority,
    isMonthEndMaintenance
};
