const OTADaihyouBangoModel = require('../models/simInventoryService/otaDaihyouBango.model');
const { OTADaihyouBangoConstants } = OTADaihyouBangoModel;

// STEP21: 3G(SMS) (FOMA_YUPIKITASU_PLAN)の場合、LTE(SMS)としてALADIN連携するようにした
const getContractType = (ryoukinPlan) => {
    switch (ryoukinPlan) {
        case OTADaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN:
            return 'PC004';
        case OTADaihyouBangoConstants.FOMA_YUPIKITASU_PLAN:
        case OTADaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN:
        case OTADaihyouBangoConstants.XI_YUPIKITASU:
        case OTADaihyouBangoConstants.TYPE_XI:
            return 'PCJ04';
        default:
            return '';
    }
};

module.exports = {
    getContractType,
};
