const _ = require('lodash');
const { CONTRACT_TYPES } = require('../constants/simConstants');
const DaihyouBangoHelper = require('./daihyouBangoHelper');
const pgPlans = require('../pgModels/plans');

/** @typedef {import('../services/core.service').LinePreAddRequestParams} LinePreAddRequestParams */

/**
 * Build request parameter for API `LinePreAdd`
 * @param {object} soData this should be a MNPTennyuSo document!
 * @param {string} simNo
 * @returns {Promise<LinePreAddRequestParams>}
 */
const buildParamForPreAdd = async (context, soData, simNo) => {
    const networkOpt = soData.is5GNSA ? CONTRACT_TYPES.FIVE_G_NSA : undefined;
    const [{ modifiedLineOptions }, cardTypeId] = await Promise.all([
        DaihyouBangoHelper.getModifiedLineOptions(context, {
            tenantId: soData.tenantId,
            planId: soData.planId,
            nNo: soData.Nno,
        }),
        pgPlans.retrieveCardTypeIdByNno(context, soData.Nno, soData.planId, soData.simType, networkOpt),
    ]);

    const tmpMap = _.groupBy(modifiedLineOptions, _.property('line_option_type'));
    const tmpMap2 = _.transform(
        tmpMap,
        (result, seq, lineOptionType) => {
            const selectedLineOptionId = seq.find((lineOption) =>
                (soData.kaisenOptions ?? []).includes(lineOption.line_option_id)
            );
            result[lineOptionType] = selectedLineOptionId?.line_option_id;
        },
        {}
    );

    return {
        targetSoId: soData.so_id,
        targetTenantId: soData.tenantId,
        nBan: soData.Nno,
        lineNo: soData.kaisenNo,
        portalPlanID: `${soData.planId}`,
        id_intlRoaming: tmpMap2[1],
        id_voiceMail: tmpMap2[2],
        id_callWaiting: tmpMap2[3],
        id_intlCall: tmpMap2[4],
        id_forwarding: tmpMap2[5],
        id_intlForwarding: tmpMap2[6],
        sim_no: simNo,
        sim_type: soData.simType,
        cardTypeId,
        mnpInFlag: soData.sameMvneInFlag ? '2' : '1',
        csvUnnecessaryFlag: '0',
        lineDelTenantId: soData.sameMvneInFlag ? soData.lineDelTenantId : undefined,
        reserve_date: null,
        access: soData.contractType,
    };
};

module.exports = {
    buildParamForPreAdd,
};
