const _ = require('lodash');
const dayjs = require('dayjs');

const LineOptionsPgModel = require('../pgModels/lineOptions');
const CustomerInfoPgModel = require('../pgModels/customerInfo');
const DaihyouBangoModel = require('../models/daihyouBango.model');
const { isNone } = require('./baseHelper');
const { DaihyouBangoConstants } = DaihyouBangoModel;

/** @typedef {import('../pgModels/lineOptions').lineOptionInfo} lineOptionInfo */

//TODO: compare with __shared__2/helpers/daihyouBangoHelper.js, fix: ctx -> context
/**
 * get modifiedLineOptions array
 * @param  {object} context
 * @param  {object} data
 * @param  {string} data.tenantId
 * @param  {string} data.planId
 * @param  {string} data.nNo
 * @return {Promise<{modifiedLineOptions:Array<lineOptionInfo>, allLineOptions:Array<lineOptionInfo>}>}
 */
const getModifiedLineOptions = async (context, { tenantId, planId, nNo }) => {
    if (isNone(tenantId)) throw new Error('tenantId is required');
    if (isNone(planId)) throw new Error('planId is required');
    // if (!nNo) throw new Error('nNo is required');
    const getCustomerInfo = async (ctx, nNumber) => {
        if (nNumber) return await CustomerInfoPgModel.getVoicemailCallWaitingOptionByNNo(ctx, nNumber);
        return null;
    };

    const [tenantLineOptions, customerInfo, allLineOptions] = await Promise.all([
        LineOptionsPgModel.findLineOptions(context, {
            tenantId: tenantId,
            planId: planId,
            nNo: nNo,
        }),
        getCustomerInfo(context, nNo),
        LineOptionsPgModel.findAllMAWPLineOptions(context),
    ]);
    let modifiedLineOptions = tenantLineOptions;
    if (customerInfo) {
        const filteredLineOptions = allLineOptions.filter((option) => {
            option.line_option_id === customerInfo.voicemail_option_id ||
                option.line_option_id === customerInfo.callwaiting_option_id;
        });

        // If voicemail plan exists use the info of voicemail plan instead
        // If callwaiting plan exists use the info of callwaiting plan instead
        modifiedLineOptions = tenantLineOptions.map((option) => {
            let lineOptionId = option.line_option_id;
            if (option.line_option_type === DaihyouBangoConstants.RUSUBAN && customerInfo.voicemail_option_id) {
                lineOptionId = customerInfo.voicemail_option_id;
            } else if (
                option.line_option_type === DaihyouBangoConstants.CATCH_HON &&
                customerInfo.callwaiting_option_id
            ) {
                lineOptionId = customerInfo.callwaiting_option_id;
            }

            const found = filteredLineOptions.find((o) => o.line_option_id == lineOptionId);
            return found || option;
        });
    } else {
        context.log('getModifiedLineOption customer info not found', nNo);
    }

    return { modifiedLineOptions, allLineOptions };
};

/**
 * An object containing differences of currentKaisenOption and newKaisenOption
 * @typedef {object} DiffKaisenObject
 * @property {string} kaisenOptionType
 * @property {string} currentKaisenOptionId
 * @property {string} updateKaisenOptionId
 */

/**
 * An object containing diffKaisenOption for sending to AladinServiceBus
 * @typedef {object} KaisenOptionForAladinServiceBus
 * @property {string} sousaservice
 * @property {string} WWtukehenhaiFLG
 * @property {string} WWriyouteisimeyasugaku
 * @property {string} WWdaisankokuhassinkisei
 * @property {string} WCtukehenhaiFLG
 * @property {string} WCriyouteisimeyasugaku
 * @property {string} WCtuwateisi
 */

/**
 * parse diffKaisenOptions to params object for sending to AladinServiceBus
 * @param  {array.<DiffKaisenObject>} diffKaisenOptions
 * @return {KaisenOptionForAladinServiceBus}           - an object for sending to AladinServiceBus
 */
const parseDiffKaisenOptionForAladinServiceBus = (diffKaisenOptions) => {
    if (!diffKaisenOptions) throw new Error('diffKaisenOptions is required');

    let parsedObject = {
        sousaservice: '',
        WWtukehenhaiFLG: null,
        WWriyouteisimeyasugaku: null,
        WWdaisankokuhassinkisei: null,
        WCtukehenhaiFLG: null,
        WCriyouteisimeyasugaku: null,
        WCtuwateisi: null,
    };

    diffKaisenOptions.forEach((obj) => {
        switch (obj.kaisenOptionType) {
            case DaihyouBangoConstants.RUSUBAN:
            case DaihyouBangoConstants.CATCH_HON:
            case DaihyouBangoConstants.TENSOU_DENWA:
            case DaihyouBangoConstants.KOKUSAI_DENWA:
            case DaihyouBangoConstants.MVNO_KAKIN_JOHOU:
                if (obj.updateKaisenOptionId !== obj.currentKaisenOptionId) {
                    const isNewLineOption = obj.updateKaisenOptionId.length > obj.currentKaisenOptionId.length;
                    parsedObject.sousaservice +=
                        (DaihyouBangoConstants.MVNO_LINE_OPTION_TO_DOMOCO_VALUE[obj.kaisenOptionType] || '') +
                        (isNewLineOption ? 1 : 0);
                }
                break;
            case DaihyouBangoConstants.WORLD_WING:
                if (obj.updateKaisenOptionId !== obj.currentKaisenOptionId) {
                    if (obj.updateKaisenOptionId === '') {
                        //廃止 "A0194" -> ""
                        parsedObject.WWtukehenhaiFLG = '0';
                    } else if (obj.currentKaisenOptionId === '') {
                        //新付
                        parsedObject.WWtukehenhaiFLG = '1';
                        parsedObject.WWriyouteisimeyasugaku =
                            DaihyouBangoConstants.MVNO_WW_TO_DOCOMO_VALUES[obj.updateKaisenOptionId] || '50000';
                    } else {
                        //変更
                        parsedObject.WWtukehenhaiFLG = '2';
                        parsedObject.WWriyouteisimeyasugaku =
                            DaihyouBangoConstants.MVNO_WW_TO_DOCOMO_VALUES[obj.updateKaisenOptionId] || '50000';
                    }
                }
                break;
            case DaihyouBangoConstants.WORLD_CALL:
                if (obj.updateKaisenOptionId !== obj.currentKaisenOptionId) {
                    if (obj.updateKaisenOptionId === '') {
                        //廃止 "A0194" -> ""
                        parsedObject.WCtukehenhaiFLG = '0';
                    } else if (obj.currentKaisenOptionId === '') {
                        //新付
                        parsedObject.WCtukehenhaiFLG = '1';
                        parsedObject.WCriyouteisimeyasugaku =
                            DaihyouBangoConstants.MVNO_WC_TO_DOCOMO_VALUES[obj.updateKaisenOptionId] || '20000';
                    } else {
                        //変更
                        parsedObject.WCtukehenhaiFLG = '2';
                        parsedObject.WCriyouteisimeyasugaku =
                            DaihyouBangoConstants.MVNO_WC_TO_DOCOMO_VALUES[obj.updateKaisenOptionId] || '20000';
                    }
                }
                break;
        }
    });

    if (parsedObject.sousaservice.length === 0) parsedObject.sousaservice = null;
    return parsedObject;
};

/**
 * get diff lineOption
 * @param  {object} context                               - API context
 * @param  {object} data                                  - required params for query
 * @param  {string} data.tenantId
 * @param  {string} data.planId
 * @param  {string} data.nNo
 * @param  {array.<string>} data.kaisenOptionIds
 * @param  {array.<string>} [data.kaisenOptionIdsOfDaihyouBango]
 * @return {Promise<KaisenOptionForAladinServiceBus>}                     - diff lineOption
 * @throws Error if required data is not passed
 */
const getDiffOfKaisenOption = async (
    context,
    { tenantId, planId, nNo, kaisenOptionIds, kaisenOptionIdsOfDaihyouBango = [] }
) => {
    if (!tenantId) throw new Error('tenantId is required');
    if (!planId) throw new Error('planId is required');
    if (!nNo) throw new Error('nNo is required');
    // allow undefined kaisenOptionsId -> set default as empty array later
    // if (!kaisenOptionIds) throw new Error('kaisenOptionIds is required');
    const kaisenOptionIdsArray = Array.isArray(kaisenOptionIds) ? kaisenOptionIds : [];

    const { modifiedLineOptions, allLineOptions } = await getModifiedLineOptions(context, {
        tenantId,
        planId,
        nNo,
    });

    /** convert kaisenOptionIds array into options info, and sort by displayOrder
     * example format:
     * ['AO001', 'AO002']
     * ↓
     * [
     *   { line_option_type: 2, line_option_title: '留守番電話サービス', line_option_id: '', line_option_name: 'なし' },
     *   { line_option_type: 3, line_option_title: 'キャッチホン', line_option_id: '', line_option_name: 'なし' },
     *   { line_option_type: 5, line_option_title: '転送でんわサービス', line_option_id: 'A0001', line_option_name: 'A0001Name' },
     *   ...
     * ]
     */

    const lineTypeGroup = _.groupBy(modifiedLineOptions, _.property('line_option_type'));
    let tmpMap = {};
    _.map(lineTypeGroup, (options, type) => {
        let values;
        if (options.size < 2) {
            values = options.map((o) => ({ [o.line_option_id]: 'あり' }));
        } else {
            values = options.map((o) => ({ [o.line_option_id]: o.option_plan_name }));
        }
        tmpMap[type] = values;
    });
    // tmpMap = {
    //  '1': [{"LO001": "LO001Name"}, {"LO002": "LO002Name"}],
    //  '2': [{"LO010": "あり"}],
    //  ...
    // }

    let tmpMap2 = _.mapValues(tmpMap, (options) => {
        const found = options.find((o) => kaisenOptionIdsArray.includes(Object.keys(o)[0]));
        return found || { '': 'なし' };
    });
    // tmpMap2 = { '1': {"LO001": "LO001Name"}, '2': {"", "なし"}, 3: {"LO021": "あり"}, ... }

    let sortedMap = Object.entries(tmpMap2).sort((a, b) => {
        const aOrder = LineOptionsPgModel.getDisplayOrderForLineType(parseInt(a[0]));
        const bOrder = LineOptionsPgModel.getDisplayOrderForLineType(parseInt(b[0]));
        return aOrder - bOrder;
    });

    const newKaisenOptions = sortedMap.map((obj) => {
        const id = obj[0],
            planObj = obj[1];
        const type = parseInt(id);
        return {
            line_option_type: type,
            line_option_title: DaihyouBangoConstants.MVNO_LINE_OPTION_TYPE_TITLES[type],
            line_option_id: Object.keys(planObj)[0],
            line_option_name: Object.values(planObj)[0],
        };
    });

    /** convert kaisenOptionIdsOfDaihyouBango array into options info, and sort by displayOrder */
    const filteredLineOptions = allLineOptions.filter((option) =>
        kaisenOptionIdsOfDaihyouBango.includes(option.line_option_id)
    );

    tmpMap2 = {};
    _.map(tmpMap, (options, optionType) => {
        const type = parseInt(optionType);
        let value;
        if (type === DaihyouBangoConstants.RUSUBAN || type === DaihyouBangoConstants.CATCH_HON) {
            if (filteredLineOptions.some((o) => o.line_option_type === type)) {
                value = options.find((o) => Object.keys(o) !== '');
            }
        } else {
            value = options.find((o) => kaisenOptionIdsOfDaihyouBango.includes(Object.keys(o)[0]));
        }
        tmpMap2[type] = value || { '': 'なし' };
    });

    sortedMap = Object.entries(tmpMap2).sort((a, b) => {
        const aOrder = LineOptionsPgModel.getDisplayOrderForLineType(parseInt(a[0]));
        const bOrder = LineOptionsPgModel.getDisplayOrderForLineType(parseInt(b[0]));
        return aOrder - bOrder;
    });

    const oldKaisenOptions = sortedMap.map((obj) => {
        const id = obj[0],
            planObj = obj[1];
        const type = parseInt(id);
        return {
            line_option_type: type,
            line_option_title: DaihyouBangoConstants.MVNO_LINE_OPTION_TYPE_TITLES[type],
            line_option_id: Object.keys(planObj)[0],
            line_option_name: Object.values(planObj)[0],
        };
    });

    // use _.zip to grouped newPlan to oldPlan, to see the differences
    // _.zip([{newPlanA}, {newPlanB}, {newPlanC}, ...], [{oldPlan1}, {oldPlan2}, {oldPlan3}, ...]}
    //       = [[{newPlanA}, {oldPlan1}], [{newPlanB}, {oldPlan2}], ...]
    const diffOptions = _.zip(newKaisenOptions, oldKaisenOptions).map((value) => {
        const [newOption, oldOption] = value;
        return {
            kaisenOptionType: newOption.line_option_type || 0,
            currentKaisenOptionId: oldOption.line_option_id || '',
            updateKaisenOptionId: newOption.line_option_id || '',
        };
    });

    return parseDiffKaisenOptionForAladinServiceBus(diffOptions);
};

// STEP21: 3G(SMS) (FOMA_YUPIKITASU_PLAN)の場合、LTE(SMS)としてALADIN連携するようにした
const getContractType = (ryoukinPlan) => {
    switch (ryoukinPlan) {
        case DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN:
            return 'PC004';
        case DaihyouBangoConstants.FOMA_YUPIKITASU_PLAN:
        case DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN:
        case DaihyouBangoConstants.XI_YUPIKITASU:
        case DaihyouBangoConstants.TYPE_XI:
            return 'PCJ04';
        case DaihyouBangoConstants.FIVE_G_TOKUTEI_SETSUZOKU:
        case DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN:
        case DaihyouBangoConstants.OROSHI_TYPE_FIVE_G:
            return DaihyouBangoConstants.FIVE_G_CONTRACT_CODE; //PCS04
        default:
            return '';
    }
};

// Step14: getContractType for normal fullmvno
const getContractTypeOfFULLMVNO = (ryoukinPlan) => {
    switch (ryoukinPlan) {
        case DaihyouBangoConstants.HLR_HSS_XI_SPECIFIC_CONNECTION:
            return 'PCJ42';
        case DaihyouBangoConstants.HLR_HSS_XI_UBIQUITOUSU:
            return 'PCJ42';
        default:
            return '';
    }
};

/**
 * STEP 7.0では外部APIで新MNVO顧客に向け対応のため追加
 * STEP 18: 5G
 * STEP21: 3G(SMS)の場合、LTE(SMS)としてALADIN連携するようにした
 * @param {string} contractType
 * @returns {string}
 */
const getContractTypeForShinMVNO = (contractType) => {
    switch (contractType) {
        case 'SA':
            return 'PC004';
        case 'SB':
        case 'SC':
        case 'SD':
        case 'SE':
            return 'PCJ04';
        //5G case
        case 'SH':
        case 'SJ':
            return DaihyouBangoConstants.FIVE_G_CONTRACT_CODE; // PCS04
        default:
            return '';
    }
};

/**
 * Get contract type for Full MVNO
 * @param {string} contractType
 */
const getContractTypeFullMVNO = (contractType) => {
    switch (contractType) {
        case 'SF':
            return 'PCJ42';
        case 'SG':
            // 契約種別は将来5G対応を想定し、可変できるようにする。
            return 'PCJ42';
        default:
            return '';
    }
};

/**
 * 旧メソッド: is020Support
 * STEP 8.0 020対応
 * STEP18.0 add 5G(AS021)
 * @param {string} ryoukinPlan
 * @returns {boolean}
 */
const checkIs020Support = (ryoukinPlan) => {
    switch (ryoukinPlan) {
        case DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN:
        case DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN:
        case DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN:
            return true;
        default:
            return false;
    }
};

/**
 * 契約変更時料金プラン必須なので、変更後契約種別(afterContractType)の値で料金プランを特定する。
 * (MVNO_N_M-1134)
 * @param {string} contractType
 * @returns {string|null}
 */
const getRyokinplanForShinMVNO = (contractType) => {
    // STEP21: 3G(SMS)をALADIN連携の際にLTE(SMS)として読み取るように変更しているが、
    // NW契約変更オーダーに関しては、変更後の契約種別が3G(SMS)の場合、
    // ALADIN連携しないので変更は無意味だと思うが念の為変更しておく
    let plan = null;
    switch (contractType) {
        case 'SA':
            plan = DaihyouBangoConstants.FOMA_TOKUTEI_SETSUZOKU_PLAN; // "A1089" 3G
            break;
        case 'SC':
            plan = DaihyouBangoConstants.XI_TOKUSEI_SETSUZOKU_PLAN; // "AJ010" LTE
            break;
        case 'SB':
        case 'SD':
            plan = DaihyouBangoConstants.XI_YUPIKITASU; // "AJ055" LTE(SMS)
            break;
        case 'SE':
            plan = DaihyouBangoConstants.TYPE_XI; // LTE(音声)
            break;
        case 'SH':
            plan = DaihyouBangoConstants.OROSHI_FIVE_G_TOKUTEI_SETSUZOKU_PLAN; // 5G(NSA)
            break;
        case 'SJ':
            plan = DaihyouBangoConstants.OROSHI_TYPE_FIVE_G; // 5G(NSA)(音声)
            break;
    }
    return plan;
};

/**
 *
 * @param {object} context
 * @param {object} data
 * @param {boolean|null} isNewDaihyouBango (boolean or null = not yet checked)
 */
const createOrUpdateDaihyouBango = async (
    context,
    {
        createdUserId,
        statusOfUse,
        tenantId,
        tenantDisplayName,
        daiDaihyouBango,
        daihyouBango,
        ansyobango,
        ryoukinPlan,
        forMNP,
        forAladinAPI,
        mvnoKakinJyoho,
        quotaQuantity,
        tensoDenwa,
        worldWing,
        kokusaiTyakushinTenso,
        wwTeishiMeyasu,
        worldCall,
        wcTeishiMeyasu,
        rusubanDenwa,
        catchPhone,
        activateDate,
        parentNo,
        memo,
        kaisenOptions,
    },
    isNewDaihyouBango
) => {
    context.log('createOrUpdateDaihyouBango start');
    const nowSecs = dayjs().unix();

    const data = {
        statusOfUse,
        lastUpdateUserId: createdUserId,
        updatedAt: nowSecs,
        tenantId,
        tenantDisplayName,
        daiDaihyouBango,
        daihyouBango,
        contractType: getContractType(ryoukinPlan),
        ansyobango,
        ryoukinPlan,
        forMNP,
        forAladinAPI,
        quotaQuantity,
        tensoDenwa: tensoDenwa ?? '',
        worldWing: worldWing ?? '',
        kokusaiTyakushinTenso: kokusaiTyakushinTenso ?? '',
        worldCall: worldCall ?? '',
        rusubanDenwa: rusubanDenwa ?? '',
        catchPhone: catchPhone ?? '',
        activateDate,
        parentNo,
        memo,
    };
    if (mvnoKakinJyoho !== undefined && mvnoKakinJyoho !== null) {
        data.mvnoKakinJyoho = mvnoKakinJyoho;
    }
    if (wwTeishiMeyasu !== undefined && wwTeishiMeyasu !== null) {
        data.wwTeishiMeyasu = wwTeishiMeyasu;
        data.WWriyouteisimeyasugaku = wwTeishiMeyasu;
    }
    if (wcTeishiMeyasu !== undefined && wcTeishiMeyasu !== null) {
        data.wcTeishiMeyasu = wcTeishiMeyasu;
        data.WCriyouteisimeyasugaku = wcTeishiMeyasu;
    }

    const lineOptions = await LineOptionsPgModel.findLineOptionsByTenantId(context, { tenantId });

    /** @type {{[option_type: string]: Array<[option_id: string, option_name: string]>}} */
    const tmpMap = Object.entries(_.groupBy(lineOptions, _.property('line_option_type'))).reduce(
        (prev, [optionType, optionList]) => {
            if (optionList.length < 2) {
                prev[optionType] = optionList.map((r) => [r.line_option_id, 'あり']);
            } else {
                prev[optionType] = optionList.map((r) => [r.line_option_id, r.option_plan_name]);
            }
            return prev;
        },
        {}
    );
    const kaisenOptionsList = Array.isArray(kaisenOptions) ? kaisenOptions : [];
    /** @type {{[option_type: string]: [option_id: string, option_name: string]}} */
    const tmpMap2 = _.mapValues(tmpMap, (options) => {
        const found = options.find((o) => kaisenOptionsList.includes(o[0]));
        return found || ['', 'なし'];
    });
    const tmpSeq = Object.entries(tmpMap2).sort((a, b) => {
        const aOrder = LineOptionsPgModel.getDisplayOrderForLineType(parseInt(a[0]));
        const bOrder = LineOptionsPgModel.getDisplayOrderForLineType(parseInt(b[0]));
        return aOrder - bOrder;
    });
    /** @type {Array<string>} */
    const sousaservice = [];
    tmpSeq.forEach(([optionType, optionIdAndName]) => {
        const optionId = optionIdAndName[0];
        if (optionId !== '') {
            sousaservice.push(DaihyouBangoConstants.MVNO_LINE_OPTION_TO_DOMOCO_VALUE[optionType] || '' + '1');
            switch (optionType) {
                case DaihyouBangoConstants.WORLD_WING:
                    data.WWriyouteisimeyasugaku = DaihyouBangoConstants.MVNO_WW_TO_DOCOMO_VALUES[optionId] || '50000';
                    break;
                case DaihyouBangoConstants.WORLD_CALL:
                    data.WCriyouteisimeyasugaku = DaihyouBangoConstants.MVNO_WC_TO_DOCOMO_VALUES[optionType] || '20000';
                    break;
            }
        }
    });
    if (sousaservice.length > 0) {
        data.sousaservice = sousaservice.join('');
    }
    data.WWtukekenhaiFLG = '1';
    data.WCtukehenhaiFLG = '1';
    data.WWdaisankokuhassinkisei = '0';
    data.WCtuwateisi = '0';
    const quotaQty = parseInt(quotaQuantity);
    data.quotaQuantity = isNaN(quotaQty) ? 0 : quotaQty;
    data.kaisenOptions = kaisenOptionsList;
    if (kaisenOptionsList.length === 0) {
        // reset ALL kaisenOption
        data.kaisenOptions = [];
        data.sousaservice = '';
        data.WWtukekenhaiFLG = '';
        data.WCtukehenhaiFLG = '';
    }
    let newData = false;
    if (typeof isNewDaihyouBango !== 'boolean') {
        // need to check whether daihyou bango exists or not
        const oldDaihyouBango = await DaihyouBangoModel.findByDaihyouBango(context, daihyouBango);
        if (!oldDaihyouBango) {
            newData = true;
        }
    } else {
        newData = isNewDaihyouBango;
    }
    if (newData) {
        // insert
        const daihyouBangoDoc = new DaihyouBangoModel(data);
        // 検索用に id から so_id の値を作成してレコードに入れとく
        daihyouBangoDoc.daihyouId = 'DH' + daihyouBangoDoc._id;
        await daihyouBangoDoc.save();
    } else {
        // update
        await DaihyouBangoModel.updateOne({ daihyouBango: daihyouBango }, { $set: data }).exec();
    }
    return {
        /** @type {string} */
        daihyouBango,
        isNew: newData,
    };
};

module.exports = {
    getModifiedLineOptions,
    getDiffOfKaisenOption,
    getContractType,
    getContractTypeOfFULLMVNO,
    getContractTypeForShinMVNO,
    getContractTypeFullMVNO,
    parseDiffKaisenOptionForAladinServiceBus,
    checkIs020Support,
    getRyokinplanForShinMVNO,
    createOrUpdateDaihyouBango,
};
