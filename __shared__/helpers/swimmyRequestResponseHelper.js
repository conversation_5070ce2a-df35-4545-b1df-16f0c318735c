// used for convert SwimmyApiLog to request parameter

const AppConfig = require('../config/appConfig');
const { KOUTEI_IDS } = require('../constants/orderConstants');
const { isNone } = require('./baseHelper');

const portalConfig = AppConfig.getPortalConfig();

/**
 * @typedef SwimmyRequestParamCommonHeaderInfo RequestResultNotifHeaderInfo
 * @property {string} requestSystemId
 * @property {string} otherSystemSendTime
 * @property {string} [userId]
 * @property {string} [useAuth]
 */

/**
 * @typedef SwimmyRequestParam
 * @property {SwimmyRequestParamCommonHeaderInfo} commonHeaderInfo
 * @property {*} constResultInfo
 */

/**
 * @typedef SwimmyResponseCommonParams
 * @property {string} [resultCode]
 * @property {string} [receiptId]
 * @property {string} [errorCode]
 * @property {object} responseParams
 * @property {number} httpStatusCode
 */

/**
 * @typedef RequestCommonHeaderInfo
 * @property {string} requestFromSystemId
 * @property {string} otherSystemSendDatetime
 * @property {string} [operatorId]
 * @property {string} [operatorGroupId]
 */

/**
 * @typedef SwimmyCommonRequestParams SwimmyRequestParams
 * @property {RequestCommonHeaderInfo} commonHeaderInfo
 * @property {object} appRequestInfo
 * @property {string} appRequestInfo.processType
 * @property {Array<object>} appRequestInfo.appInfoList
 */

/**
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParamCommonHeaderInfo|null}
 */
const getCommonHeaderInfo = (obj) => {
    const header = obj?.requestParam?.commonHeaderInfo;
    if (!isNone(header)) {
        /** @type {SwimmyRequestParamCommonHeaderInfo} */
        return {
            requestSystemId: header.requestSystemId,
            otherSystemSendTime: header.otherSystemSendTime,
            userId: undefined,
            useAuth: undefined,
        };
    } else {
        return null;
    }
};

/**
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParam|null}
 */
const toAladinKekkaTsuchiRequest = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const resultInfo = requestParam.constResultInfo ?? {};
    /** @type {SwimmyRequestParam} */
    const req = {
        commonHeaderInfo: getCommonHeaderInfo(obj),
        constResultInfo: {
            soId: resultInfo.soId,
            soKind: resultInfo.soKind,
            lineNo: resultInfo.lineNo,
            resultCode: resultInfo.resultCode,
        },
    };
    let errorInfoList = [];
    if (!isNone(resultInfo.errorInfoList) && Array.isArray(resultInfo.errorInfoList)) {
        const errorCode = resultInfo.errorInfoList.at(0)?.errorCode;
        errorInfoList = [
            {
                errorCode,
                errorMessage: undefined,
            },
        ];
    }
    req.constResultInfo.errorInfoList = errorInfoList;

    return req;
};

/**
 *
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParam|null}
 */
const toMNPTenshutsuKanryouTsuchiRequest = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const resultInfo = requestParam.constResultInfo ?? {};
    /** @type {SwimmyRequestParam} */
    const req = {
        commonHeaderInfo: getCommonHeaderInfo(obj),
        constResultInfo: {
            soId: resultInfo.soId,
            lineNo: resultInfo.lineNo,
            mnpOutDate: resultInfo.mnpOutDate,
        },
    };

    return req;
};

/**
 *
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParam|null}
 */
const toKurokaKanryouTsuchiRequest = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const resultInfo = requestParam.constResultInfo ?? {};
    /** @type {SwimmyRequestParam} */
    const req = {
        commonHeaderInfo: getCommonHeaderInfo(obj),
        constResultInfo: {
            soId: resultInfo.soId,
            soKind: resultInfo.soKind,
            lineNo: resultInfo.lineNo,
            simNo: resultInfo.simNo,
            resultCode: resultInfo.resultCode,
        },
    };
    let errorInfoList = [];
    if (!isNone(resultInfo.errorInfoList) && Array.isArray(resultInfo.errorInfoList)) {
        const errorCode = resultInfo.errorInfoList.at(0)?.errorCode;
        errorInfoList = [
            {
                errorCode,
                errorMessage: resultInfo.errorInfoList.at(0)?.errorMessage,
            },
        ];
    }
    req.constResultInfo.errorInfoList = errorInfoList;

    return req;
};

/**
 *
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParam|null}
 */
const toOTAKekkaTsuchiRequest = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const resultInfo = requestParam.constResultInfo ?? {};

    const processId = resultInfo.processId;
    const aladinSoNGReason = processId == KOUTEI_IDS.ALADIN_KOUJI_NG ? resultInfo.aladinSoNGReason : '';

    let lineNo = '',
        simNo = '';
    if (
        processId == KOUTEI_IDS.KOUKANKI_SETTEI_NG ||
        processId == KOUTEI_IDS.KOUKANKI_SETTEI_OK ||
        (processId == KOUTEI_IDS.ALADIN_KOUJI_NG && aladinSoNGReason == portalConfig.ALADIN_KOUJI_NG_REASON)
    ) {
        lineNo = resultInfo.lineNo;
        simNo = resultInfo.simNo;
    }

    /** @type {SwimmyRequestParam} */
    const req = {
        commonHeaderInfo: getCommonHeaderInfo(obj),
        constResultInfo: {
            soId: resultInfo.soId,
            processId,
            soKind: resultInfo.soKind,
            lineNo,
            simNo,
            aladinSoNGReason,
        },
    };

    return req;
};

/**
 *
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParam|null}
 */
const toKaisenOptionHenkouKekkaTsuchiRequest = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const resultInfo = requestParam.constResultInfo ?? {};

    /** @type {SwimmyRequestParam} */
    const req = {
        commonHeaderInfo: getCommonHeaderInfo(obj),
        constResultInfo: {
            soId: resultInfo.soId,
            lineNo: resultInfo.lineNo,
            resultCode: resultInfo.resultCode,
        },
    };

    let errorInfoList = [];
    if (!isNone(resultInfo.errorInfoList) && Array.isArray(resultInfo.errorInfoList)) {
        const errorCode = resultInfo.errorInfoList.at(0)?.errorCode;
        errorInfoList = [
            {
                errorCode,
                errorMessage: resultInfo.errorInfoList.at(0)?.errorMessage,
            },
        ];
    }
    req.constResultInfo.errorInfoList = errorInfoList;

    return req;
};

/**
 *
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParam|null}
 */
const toSIMSaihakkouKekkaTsuchiRequest = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const resultInfo = requestParam.constResultInfo ?? {};

    const lineNo = isNone(resultInfo.lineNo) ? undefined : resultInfo.lineNo;
    const simNo = isNone(resultInfo.simNo) ? undefined : resultInfo.simNo;
    const shippingNumber = isNone(resultInfo.shippingNumber) ? undefined : resultInfo.shippingNumber;

    /** @type {SwimmyRequestParam} */
    const req = {
        commonHeaderInfo: getCommonHeaderInfo(obj),
        constResultInfo: {
            soId: resultInfo.soId,
            lineNo,
            simNo,
            shippingNumber,
            resultCode: resultInfo.resultCode,
        },
    };

    let errorInfoList = [];
    if (!isNone(resultInfo.errorInfoList) && Array.isArray(resultInfo.errorInfoList)) {
        const errorCode = resultInfo.errorInfoList.at(0)?.errorCode;
        errorInfoList = [
            {
                errorCode,
                errorMessage: resultInfo.errorInfoList.at(0)?.errorMessage,
            },
        ];
    }
    req.constResultInfo.errorInfoList = errorInfoList;

    return req;
};

/**
 *
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParam|null}
 */
const toESIMKekkaTsuchiSwimmyV2Request = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const resultInfo = requestParam.constResultInfo ?? {};

    const processId = resultInfo.processId;
    const aladinSoNGReason = processId == KOUTEI_IDS.ALADIN_KOUJI_NG ? resultInfo.aladinSoNGReason : '';

    let lineNo = '',
        simNo = '';
    if (
        processId == KOUTEI_IDS.KOUKANKI_SETTEI_NG ||
        processId == KOUTEI_IDS.KOUKANKI_SETTEI_OK ||
        (processId == KOUTEI_IDS.ALADIN_KOUJI_NG && aladinSoNGReason == portalConfig.ALADIN_KOUJI_NG_REASON)
    ) {
        lineNo = resultInfo.lineNo;
        simNo = resultInfo.simNo;
    }

    /** @type {SwimmyRequestParam} */
    const req = {
        commonHeaderInfo: getCommonHeaderInfo(obj),
        constResultInfo: {
            soId: resultInfo.soId,
            processId,
            soKind: resultInfo.soKind,
            lineNo,
            simNo,
            aladinSoNGReason,
        },
    };

    return req;
};

/**
 *
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyRequestParam|null}
 */
const toESIMKekkaTsuchiSwimmyV3Request = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const resultInfo = requestParam.constResultInfo ?? {};

    const processId = resultInfo.processId;
    const aladinSoNGReason = processId == KOUTEI_IDS.ALADIN_KOUJI_NG ? resultInfo.aladinSoNGReason : '';

    let lineNo = '',
        eId = '';
    if (
        processId == KOUTEI_IDS.KOUKANKI_SETTEI_NG ||
        processId == KOUTEI_IDS.KOUKANKI_SETTEI_OK ||
        (processId == KOUTEI_IDS.ALADIN_KOUJI_NG && aladinSoNGReason == portalConfig.ALADIN_KOUJI_NG_REASON)
    ) {
        lineNo = resultInfo.lineNo;
        eId = resultInfo.eId;
    }

    /** @type {SwimmyRequestParam} */
    const req = {
        commonHeaderInfo: getCommonHeaderInfo(obj),
        constResultInfo: {
            soId: resultInfo.soId,
            processId,
            soKind: resultInfo.soKind,
            dummyLineNo: resultInfo.dummyLineNo ?? '',
            lineNo,
            eId,
            aladinSoNGReason,
        },
    };

    return req;
};

/**
 *
 * @param {object} obj SwimmyApiLog
 * @returns {SwimmyCommonRequestParams|null}
 */
const toSwimmyCommonRequest = (obj) => {
    const requestParam = obj?.requestParam;
    if (isNone(requestParam)) return null;

    const header = requestParam.commonHeaderInfo;
    const appRequest = requestParam.appRequestInfo;

    const appInfo = appRequest.appInfoList[0];
    const appBasic = appInfo.appBasic;

    const appBasicDt1 = appBasic.appBasicDtlList[0];
    const appBasicDt2 = appBasic.appBasicDtlList[1];

    /** @type {SwimmyCommonRequestParams} */
    const req = {
        commonHeaderInfo: {
            requestFromSystemId: header?.requestFromSystemId ?? '',
            otherSystemSendDatetime: header?.otherSystemSendDatetime ?? '',
        },
        appRequestInfo: {
            processType: appRequest.processType,
            appInfoList: [
                {
                    appBasic: {
                        inputFromType: appBasic.inputFromType,
                        orderType: appBasic.orderType,
                        appStatus: appBasic.appStatus,
                        appDate: appBasic.appDate,
                        salesChannelCode: appBasic.salesChannelCode,
                        salesChannelName: appBasic.salesChannelName,
                        appBasicDtlList: [
                            {
                                appAttributeCode: appBasicDt1.appAttributeCode,
                                appAttributeValue: appBasicDt1.appAttributeValue,
                            },
                            {
                                appAttributeCode: appBasicDt2.appAttributeCode,
                                appAttributeValue: appBasicDt2.appAttributeValue,
                            },
                        ],
                    },
                },
            ],
        },
    };

    return req;
};

module.exports = {
    toAladinKekkaTsuchiRequest,
    toMNPTenshutsuKanryouTsuchiRequest,
    toKurokaKanryouTsuchiRequest,
    toOTAKekkaTsuchiRequest,
    toKaisenOptionHenkouKekkaTsuchiRequest,
    toSIMSaihakkouKekkaTsuchiRequest,
    toESIMKekkaTsuchiSwimmyV2Request,
    toESIMKekkaTsuchiSwimmyV3Request,
    toSwimmyCommonRequest,
};
