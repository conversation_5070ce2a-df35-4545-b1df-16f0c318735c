const { isNone } = require('../helpers/baseHelper');
const { TANAOROSHI_DATA_SAVE_STATUS } = require('../constants/tanaoroshiDataConstants');
const TanaoroshiDataModel = require('../models/simInventoryService/tanaoroshiData.model');
const SimZaikoModel = require('../models/simInventoryService/simZaiko.model');

/**
 * getTanaoroshiData
 * @param {object} context
 * @param {object} tanaoroshiMonthData
 * @param {string[]} tenantIds
 * @param {string[]} tempoIds
 * @param {number} monthCompareType
 * @returns {string[], number, boolean} resultSeq, searchCount, isAllProcessed
 */
const getTanaoroshiData = async (context, tanaoroshiMonthData, tenantIds, tempoIds, monthCompareType) => {
    context.log('getTanaoroshiData START');

    let tanaoroshiDataSeq = [], tanaoroshiSearchCount = 0;
    if (!isNone(tanaoroshiMonthData)) {
        const { result, resultCount } = await TanaoroshiDataModel.findByMonthId(context, tanaoroshiMonthData._id.toString());
        tanaoroshiDataSeq = result;
        tanaoroshiSearchCount = resultCount;
    }
    return await getTanaoroshiData1(context, tanaoroshiDataSeq, tanaoroshiSearchCount, tenantIds, tempoIds, monthCompareType);
};

/**
 * getTanaoroshiData1
 * @param {object} context
 * @param {tanaoroshiData[]} tanaoroshiDataSeq
 * @param {number} tanaoroshiSearchCount
 * @param {string[]} tenantIds
 * @param {string[]} tempoIds
 * @param {number} monthCompareType
 * @returns {string[], number, boolean} resultSeq=[{TanaoroshiData: {}}, {SimZaiko: {}}, {TanaoroshiZaiko: [{tanaoroshi}, {zaiko}]}, ...], searchCount, isAllProcessed
 */
const getTanaoroshiData1 = async (context, tanaoroshiDataSeq, tanaoroshiSearchCount, tenantIds, tempoIds, monthCompareType) => {
    context.log('getTanaoroshiData1 START');
    // 棚卸処理状況を取得
    let inventory_status = TANAOROSHI_DATA_SAVE_STATUS.MI_HOZON;
    if (!isNone(tanaoroshiDataSeq)) {
        if (tanaoroshiDataSeq.some((tanaoroshiData) => tanaoroshiData.save_status === TANAOROSHI_DATA_SAVE_STATUS.TOCHU_HOZON)) {
            inventory_status = TANAOROSHI_DATA_SAVE_STATUS.TOCHU_HOZON;
        } else {
            inventory_status = TANAOROSHI_DATA_SAVE_STATUS.KEKKA_HOUKOKU;
        }
    }
    switch (monthCompareType) {
        // 過去の場合、棚卸結果コレクションを返す
        case -1: {
            let tanaoroshiDataNewSeq = [];
            tanaoroshiDataSeq.map((data) => tanaoroshiDataNewSeq.push({ TanaoroshiData: data }));
            return {
                resultSeq: tanaoroshiDataNewSeq,
                searchCount: tanaoroshiSearchCount,
                isAllProcessed: inventory_status === TANAOROSHI_DATA_SAVE_STATUS.KEKKA_HOUKOKU,
            };
        }
        case 1:
        case 0: {
            // 今月と未来の場合、結果報告は棚卸結果のデータを返す、以外は在庫データと棚卸結果のデータをマージする
            switch (inventory_status) {
                case TANAOROSHI_DATA_SAVE_STATUS.KEKKA_HOUKOKU: {
                    let tanaoroshiDataNewSeq = [];
                    tanaoroshiDataSeq.map((tdata) => tanaoroshiDataNewSeq.push({ TanaoroshiData: tdata }));
                    return {
                        resultSeq: tanaoroshiDataNewSeq,
                        searchCount: tanaoroshiSearchCount,
                        isAllProcessed: true,
                    };
                }
                default: {
                    const { zaikoSeq, } = await SimZaikoModel.searchTanaoroshiZaikoSims(context, tenantIds, tempoIds, 'sim_info', true);
                    const dataSeq = zaikoSeq.map((zaikoItem) => {
                        const tanaoroshiData = tanaoroshiDataSeq.find((tanaoroshiItem) => tanaoroshiItem.sim_info === zaikoItem.simInfo);
                        return !isNone(tanaoroshiData) ? { TanaoroshiZaiko: [tanaoroshiData, zaikoItem] } : { SimZaiko: zaikoItem };
                    });
                    return {
                        resultSeq: dataSeq,
                        searchCount: dataSeq.length,
                        isAllProcessed: false,
                    };
                }
            }
        }
        // 今月の場合、結果報告は在庫データと棚卸結果のデータをマージする、以外は棚卸結果のデータを返す
        default:
            return {
                resultSeq: [],
                searchCount: 0,
                isAllProcessed: false,
            };
    }
};

module.exports = {
    getTanaoroshiData,
};