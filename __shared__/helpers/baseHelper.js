const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
dayjs.extend(utc);
dayjs.extend(timezone);
const _ = require('lodash');
const { SWIMMY_SENDER_SYSTEM_ID } = require('../constants/specialDefinitions');
const { isFullMVNO } = require('../pgModels/lines.js');
const AddressLogCount = require('../models/addressLogCount.model');
const { SHIN_MVNO_TENANTS } = require('../constants/specialDefinitions');
const { LINE_NO_REGEX } = require('../constants/simConstants');
const { LOWEST_API_PRIORITY, PRIORITY_KEY_STRING } = require('../constants/aladinTransactions');
const { HEADER_KEY } = require('../constants/headerKeys');
const { getParamBooleanValue } = require('../utils/stringUtils');
const appConfig = require('../config/appConfig');
const { TENPO_LOGIN_ID_PREFIX } = require('../constants/userConstants');
const AladinApiRestrictSettingModel = require('../models/aladinService/aladinApiRestrictSetting.model.js');
const portalConfig = appConfig.getPortalConfig();
/**
 *
 * @param {object} context
 * @param {object} req
 * @param {object} options { isNeedSwitchForShinMVNO: boolean, isNeedSwitchForFullMVNO: boolean }
 * @returns {object} { isShinMvnoOrder, isFullMvnoOrder, isFrom9a1z }
 */
const getOrderFlags = async (
    context,
    req,
    { isNeedSwitchForShinMVNO, isNeedCheckFullMVNO } = {
        isNeedSwitchForShinMVNO: false,
        isNeedCheckFullMVNO: false,
    }
) => {
    const { requestHeader } = req.body;
    const isFrom9a1z = SWIMMY_SENDER_SYSTEM_ID.includes(requestHeader.senderSystemId);
    const isShinMvnoOrder = isNeedSwitchForShinMVNO && isFrom9a1z && SHIN_MVNO_TENANTS.includes(req.body.tenantId);
    let isFullMvnoOrder = false;

    if (isNeedCheckFullMVNO) {
        isFullMvnoOrder = await _isFullMVNO(context, req.body);
        return {
            isShinMvnoOrder,
            isFullMvnoOrder,
            isFrom9a1z,
        };
    }

    return {
        isShinMvnoOrder,
        isFullMvnoOrder,
        isFrom9a1z,
    };
};

/**
 * create addressNo coresponding to tenantId
 * ex: TSA2022081900001
 * @param  {string} tenantId
 * @return {string}          - addressNo
 */
const createAddressNo = async (tenantId) => {
    if (!tenantId) throw new Error('tenantId is required');

    const dateStr = dayjs().tz('Asia/Tokyo').format('YYYYMMDD');
    let nextSeq = 0;
    // 万が一、連番取得に失敗したら正常な連番が取れるまで実施する
    do {
        nextSeq = await AddressLogCount.getNextSeq(tenantId, dateStr);
    } while (nextSeq === 0);
    const fiveDigitsNumber = nextSeq.toLocaleString('en-US', { minimumIntegerDigits: 5, useGrouping: false });
    const addressNo = tenantId.substring(0, 3) + dateStr + fiveDigitsNumber;
    if (nextSeq > 99999) {
        console.error('個配申込受付番号が16桁を超えました。' + addressNo);
    }

    return addressNo;
};

const _isFullMVNO = async (context, { planId, lineNo, nBan, soId }) => {
    return await isFullMVNO(context, { planId, kaisenNo: lineNo, nBan, soId });
};

/**
 * remove null value in object
 * @param  {object} object
 * @return {object}        - removed-null-value Object
 */
const removeNullValue = (object) => {
    let obj = _.cloneDeep(object);
    Object.keys(obj).forEach((key) => {
        if (obj[key] === undefined || obj[key] === null || obj[key] === '' || obj[key].length === 0) {
            delete obj[key];
        }
    });

    return obj;
};

/**
 * check if user is from Sxx tempo
 * @param {string|undefined} tempoId
 * @returns {boolean}
 */
const isSxxUser = (tempoId) => {
    // do we need to check roleId too ?
    if (typeof tempoId === 'string') {
        return isSxxTempo(tempoId);
    } else {
        return false;
    }
};

/**
 * check if tempoId is ZMKTempo
 * @param  {string} tempoId
 * @return {boolean}
 */
const isZMKTempo = (tempoId) => {
    return tempoId.startsWith('ZMK');
};

/**
 * check if tempoId is SxxTempo
 * @param  {string} tempoId
 * @return {boolean}
 */
const isSxxTempo = (tempoId) => {
    return tempoId.startsWith('Sxx');
};

/**
 * check if tempoId is TMKTempo
 * @param {string} tempoId
 * @returns {boolean}
 */
const isTMKTempoID = (tempoId) => {
    return tempoId.startsWith('TMK');
};

/**
 * check if tempoId is start with S
 * @param {string} tempoId
 * @returns {boolean}
 */
const isSTempoID = (tempoId) => {
    return typeof tempoId === 'string' && tempoId?.startsWith('S');
};

/**
 * AxxNIFxxxx (NIF店舗) // STEP 7.1で追加
 *
 * - 同一テナント
 * - 店舗跨り
 * @param {string} tempoId
 * @returns {boolean}
 */
const isAxxTempo = (tempoId) => {
    return /^Axx\w{3}x{4}/.test(tempoId);
};

/**
 * check if tempoId is isTMKxxxxxxx
 * (ZZZ000店舗)
 * ・テナント跨り
 * @param  {string} tempoId
 * @return {boolean}
 */
const isTMKxxxxxxx = (tempoId) => {
    return /^TMKx{7}/.test(tempoId);
};

/**
 * check userId
 * @param  {string} userId
 * @return {boolean}
 */
const isOPFUserIdStr = (userId) => {
    return (
        userId === null ||
        userId.startsWith(portalConfig.TEMPO_LOGIN_ID_PREFIX) ||
        userId === portalConfig.API_USER_ID ||
        userId === '' ||
        !userId
    );
};

/**
 * format transactionId from jigyoshacode and nextSeq
 * @param {string} jigyoshacode
 * @param {number} nextSeq
 * @returns {string} formatted transactionId
 * @example
 * formatSeqString('04', 1) => '042022090110101000001'
 * formatSeqString('42', 2) => '422022090110101000002'
 */
const formatSeqString = (jigyoshacode, nextSeq) => {
    const dateStr = dayjs().format('YYYYMMDDHHmmss');
    const fiveDigitsNumber = nextSeq.toLocaleString('en-US', { minimumIntegerDigits: 5, useGrouping: false });
    return `${jigyoshacode}${dateStr}${fiveDigitsNumber}`;
};

/**
 * original: formatSeqStr in CustomerInfo.scala
 * @param tenantId
 * @param nextSeq
 * @returns seqStr
 */
const formatSeqStr = (tenantId, nextSeq) => {
    const fourDigitsNumber = nextSeq.toLocaleString('en-US', { minimumIntegerDigits: 4, useGrouping: false });
    return `${tenantId.substring(0, 3)}-ADDR-${fourDigitsNumber}`;
};

/**
 * If the tenant is low priority, then return the priority of the API multiplied by 1.4, otherwise
 * return the priority of the API.
 * @param requestOrderType - The type of request.
 * @param tenantId - The ID of the tenant that the request is coming from.
 * @returns the priority of the request.
 * @deprecated use `getAladinTransactionPriority`
 */
const getPriority = async (requestOrderType, tenantId) => {
    const priorityKey = PRIORITY_KEY_STRING.get(requestOrderType);
    // if priorityKey is not found, return lowest priority
    if (!priorityKey) {
        return LOWEST_API_PRIORITY;
    }
    const apiPriorityByApiType = portalConfig.ALADIN_API.PRIORITY_LIST[priorityKey];
    // if apiPriorityByApiType is not found, return lowest priority
    if (!apiPriorityByApiType) {
        return LOWEST_API_PRIORITY;
    }

    const isLowPrio = await AladinApiRestrictSettingModel.isLowPrio(tenantId);
    if (isLowPrio) {
        return +(apiPriorityByApiType * 1.4);
    }

    return +apiPriorityByApiType;
};

const getAladinTransactionPriority = async (requestOrderType, tenantId) => {
    const result = {
        /** 低優先度テナントであるか */
        isLowPriority: false,
        /** テナント設定による優先度 */
        priority: LOWEST_API_PRIORITY,
        /** オーダー種の優先度（固定） */
        basePriority: LOWEST_API_PRIORITY,
    };

    const priorityKey = PRIORITY_KEY_STRING.get(requestOrderType);
    // if priorityKey is not found, return lowest priority
    if (!priorityKey) {
        return result;
    }
    const apiPriorityByApiType = portalConfig.ALADIN_API.PRIORITY_LIST[priorityKey];
    // if apiPriorityByApiType is not found, return lowest priority
    if (!apiPriorityByApiType) {
        return result;
    }

    result.priority = +apiPriorityByApiType;
    result.basePriority = +apiPriorityByApiType;

    const isLowPrio = await AladinApiRestrictSettingModel.isLowPrio(tenantId);
    if (isLowPrio) {
        result.priority = +(apiPriorityByApiType * 1.4);
        result.isLowPriority = true;
    }

    return result;
};

/**
 * check whether current process is running from Docker
 */
const isRunningInDocker = () => {
    return process.env.RUN_IN_DOCKER || process.env.RUN_IN_DOCKER == 'true' ? true : false;
};

/**
 *
 * @param {object} context
 */
const getSessionData = (context, req) => {
    const roleId = parseInt(req.headers[HEADER_KEY.ROLE_ID], 10);

    const tenantId = req.headers[HEADER_KEY.TENANT_ID];
    /** @type {string} `tempoId` for tempo user (empty string for non tempo user) */
    const tempoId = req.headers[HEADER_KEY.TEMPO_ID] ?? '';

    const decodedUserName = req.headers[HEADER_KEY.USER_NAME]
        ? Buffer.from(req.headers[HEADER_KEY.USER_NAME] ?? '', 'base64').toString('utf-8')
        : '';
    context.log(`decodedUserName: ${decodedUserName}`);
    const user = {
        /** @type {string} user's `_id` */
        internalId: req.headers[HEADER_KEY.USER_ID] ?? '',
        /** @type {string} user's `plusId` */
        plusId: req.headers[HEADER_KEY.USER_PLUS_ID] ?? '',
        /** @type {string} user's `name` */
        name: decodedUserName,
        /** @type {boolean|undefined} undefined in case role is not tempo user */
        maskPhoneNumber: getParamBooleanValue(req.headers[HEADER_KEY.MASK_PHONE_NUMBER], undefined),
        /** @type {boolean|undefined} user's password is expired or not */
        passwordExpired: getParamBooleanValue(req.headers[HEADER_KEY.PASSWORD_EXPIRED], undefined),
        /** @type {boolean|undefined} undefined in case role is higher role user */
        canDownloadMesai: getParamBooleanValue(req.headers[HEADER_KEY.CAN_DOWNLOAD_MEISA], undefined)
    };

    return { roleId, tenantId, tempoId, user };
};

/**
 * Check whether a value is null, undefined, NaN, empty string, empty array or empty object
 * @param {any} v value to test
 */
const isNone = (v) => {
    return (
        v === null ||
        v === undefined ||
        v === '' ||
        (typeof v === 'number' && isNaN(v)) ||
        (Array.isArray(v) && v.length == 0) ||
        (typeof v === 'object' && v !== null && Object.keys(v).length == 0)
    );
};

/**
 * @param  {...Iterable<any>} iterables
 * @returns {Set<any>}
 */
const unionSets = (...iterables) => {
    const set = new Set();
    for (const iterable of iterables) {
        for (const item of iterable) {
            set.add(item);
        }
    }
    return set;
};

/**
 * Check whether a value is valid kaisenNo
 * @param {*} kaisenNo
 * @returns {boolean}
 */
const isValidKaisenNo = (kaisenNo) => {
    return LINE_NO_REGEX.test(kaisenNo);
};

/**
 * The function checks if a given plusId starts with a specific prefix indicating it belongs to a Tempo
 * user.
 * @param plusId - `plusId` is a string parameter representing the user's ID.
 * @returns boolean - `true` if the `plusId` starts with the prefix, `false` otherwise.
 */
const isTempoUser = (plusId) => {
    return plusId?.startsWith(TENPO_LOGIN_ID_PREFIX);
};

module.exports = {
    getOrderFlags,
    createAddressNo,
    removeNullValue,
    isSxxUser,
    isZMKTempo,
    isSxxTempo,
    isTMKTempoID,
    isSTempoID,
    isAxxTempo,
    isTMKxxxxxxx,
    isOPFUserIdStr,
    formatSeqString,
    formatSeqStr,
    getPriority,
    getAladinTransactionPriority,
    isRunningInDocker,
    getSessionData,
    isNone,
    unionSets,
    isValidKaisenNo,
    isTempoUser,
};
