const { opCoreErrorMsg } = require('../constants/opcoremsg');
const { opferrormsg } = require('../constants/opferrormsg');
const { opferrormsg_en } = require('../constants/opferrormsg_en');
const { erroMsgList } = require('../constants/swimmyErrorMsgList');

/**
 * @param {string} errorCode
 * @returns {string|null}
 */
const findOPFErrorMsgByOPFErrorCode = (errorCode) => {
    return opferrormsg[errorCode] ?? null;
};

/**
 * @param {string} errorCode
 * @returns {string|null}
 */
const getOPFErrorMsgEn = (errorCode) => {
    return opferrormsg_en[errorCode] ?? null;
};

/**
 * @param {string} errorCode
 * @returns {string|null}
 */
const findOPCoreErrorMsgByErrorCode = (errorCode) => {
    return opCoreErrorMsg[errorCode] ?? null;
};

/**
 * @param {string} errorCode
 * @returns {string|null}
 */
const findSwimmyErrorMsgByErrorCode = (errorCode) => {
    return erroMsgList[errorCode] ?? null;
};

/**
 * @deprecated use `config/appConfig.js` to get configuration
 * @param {string} name environment variable name
 * @returns {Array<string>?}
 */
const getArrayTypeEnv = (name) => {
    // TODO find safer way to parse
    const val = process.env[name];
    let result;
    if (val && typeof val == 'string') {
        try {
            result = JSON.parse(val.replace(/'/g, '"'));
        } catch (err) {
            try {
                result = val
                    .replace(/^['"`]*|['"`]*$/g, '') // remove surrounding quote characters
                    .replace(/^\[|\]$/g, '') // remove [ and ]
                    .split(',')
                    // trim quote characters if exists
                    .map((r) => r.trim().replace(/^(['"`])(.*)\1$/g, '$2'));
            } catch (err) {
                result = null;
            }
        }
    }
    return result;
};

// TODO: dummy implementation. need confirm before implement. this function is seem to be unused
const getKariTourokuKaisenUketsukeFukaJikanInfo = (soKind) => {
    return false && soKind;
};

module.exports = {
    findOPFErrorMsgByOPFErrorCode,
    getOPFErrorMsgEn,
    findOPCoreErrorMsgByErrorCode,
    findSwimmyErrorMsgByErrorCode,
    getArrayTypeEnv,
    getKariTourokuKaisenUketsukeFukaJikanInfo,
};
