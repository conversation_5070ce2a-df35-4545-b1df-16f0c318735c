// 受付不可時間チェック
const appConfig = require('../config/appConfig');
const { ORDER_TYPE } = require('../constants/aladinTransactions');
const dtUtils = require('../utils/datetimeUtils');
const { isNone } = require('./baseHelper');

const portalConfig = appConfig.getPortalConfig();

/**
 * Parse fukajikan from config
 * @param {string} configValue format `HH:mm-HH:mm`
 */
const parseUketsukeFukaJikan = (configValue) => {
    const regex = /(\d{1,2}):(\d{1,2})-(\d{1,2}):(\d{1,2})/;
    const now = dtUtils.getTimeInJST().second(0).millisecond(0);
    const config = (configValue || '').match(regex);
    if (config !== null && config.length >= 5) {
        const startDate = now.hour(parseInt(config[1])).minute(parseInt(config[2]));
        const endDate = now.hour(parseInt(config[3])).minute(parseInt(config[4]));
        return { start: startDate, end: endDate };
    } else {
        return null;
    }
};

/**
 * Check if current time is in 受付不可時間
 * @param {{start: import('dayjs').Dayjs, end: import('dayjs').Dayjs}|null} uketsukeFukaJikan
 * @returns {boolean}
 */
const isUketsukeFukaJikan = (uketsukeFukaJikan) => {
    if (uketsukeFukaJikan === null || uketsukeFukaJikan === undefined) return false;

    const now = dtUtils.getTimeInJST(); // same as dayjs()

    // 開始時刻より終了時刻の方が前の時刻が設定されている場合は１日後と見る
    if (uketsukeFukaJikan.end.isBefore(uketsukeFukaJikan.start)) {
        const after1Day = uketsukeFukaJikan.end.add(1, 'day');
        return (now.isAfter(uketsukeFukaJikan.start) && now.isBefore(after1Day)) || now.isBefore(uketsukeFukaJikan.end);
    } else {
        return now.isAfter(uketsukeFukaJikan.start) && now.isBefore(uketsukeFukaJikan.end);
    }
};

/**
 *
 * @param {string} requestOrderType
 */
const canRequestOrderTypeBeExecutedWithinUketsukeFukaJikan = (requestOrderType) => {
    return ![
        ORDER_TYPE.MNP_TENNYU_UKETSUKE_TSUJOU_KAITSU,
        ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_SAKUSEI,
        ORDER_TYPE.MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU,
        ORDER_TYPE.MNP_TENNYU_UKETSUKE_OTA_ROM_KAITSU,
        // TODO confirm allow 一括黒化 too ?
        ORDER_TYPE.IKKATSU_MNP_TENNYU_UKETSUKE_HANKURO_ROM_KAITSU,
        // TODO since no SIM再発行黒化 order here, maybe no need to handle this 
        // ORDER_TYPE.IKKATSU_KISETSU_HENKOU_SAIHAKKOU_SHIRO_ROM_MIKAITSU_TO_KAITSYU,
    ].includes(requestOrderType);
};

/**
 * 旧メソッド：getAladinAPIRequestFukajikanInfo2
 * @param {string|null|undefined} requestOrderType
 * @returns
 */
const getAladinAPIRequestFukajikanInfo = (requestOrderType) => {
    const aladinAPIRequestUketsukeFuka = isUketsukeFukaJikan(
        parseUketsukeFukaJikan(portalConfig.ALADIN_API_REQUEST_UKETSUKE_FUKA_JIKAN)
    );

    if (isNone(requestOrderType)) {
        return aladinAPIRequestUketsukeFuka;
    } else {
        return canRequestOrderTypeBeExecutedWithinUketsukeFukaJikan(requestOrderType)
            ? false
            : aladinAPIRequestUketsukeFuka;
    }
};

module.exports = {
    parseUketsukeFukaJikan,
    canRequestOrderTypeBeExecutedWithinUketsukeFukaJikan,
    getAladinAPIRequestFukajikanInfo,
};
