const dayjs = require('dayjs');
const { TRANSACTION_TYPE, ORDER_TYPE, CARD_KEIJO } = require('../../constants/aladinTransactions');
const { KOUTEI_IDS } = require('../../constants/orderConstants');
const { CONTRACT_TYPES } = require('../../constants/simConstants');
const { M2M_SOUSA_SERVICE } = require('../../constants/specialDefinitions');
const { getDiffOfKaisenOption, checkIs020Support } = require('../../helpers/daihyouBangoHelper');
const TenantPlanLineOptionSettingModel = require('../../models/adminService/tenantPlanLineOptionSetting.model');
const DaihyouBangoModel = require('../../models/daihyouBango.model');
const MNPTennyuSoModel = require('../../models/mnpTennyuSo.model');
const {
    isVoicePlanIdWithLiteMVNO,
    getNetworkInfoByPlanId,
    convertPlanIdToRyoukinPlan,
} = require('../../pgModels/plans');
const { isShanaiTenantType } = require('../../pgModels/tenants');
const { getDefaultPlanByTenantId } = require('../../pgModels/voiceInfo');
const { registerAladinTransaction } = require('../../services/aladin.service');
const appConfig = require('../../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
/**
 * Object to hold new eSIM reception request
 * - Field names are inline with request parameter fields, so no need to remap.
 * - `req.body` needs to be passed to validator before being casted to this type.
 * @typedef {object} NeweSIMOrderData
 * @property {string} NeweSIMOrderData.tenantId
 * @property {string} NeweSIMOrderData.shopId
 * @property {string} NeweSIMOrderData.planId
 * @property {string} NeweSIMOrderData.eid
 * @property {string} [NeweSIMOrderData.contractType]
 * @property {Array<string>} NeweSIMOrderData.lineOptions
 * @property {string} NeweSIMOrderData.nBan
 * @property {string} [NeweSIMOrderData.shopMemo]
 * @property {string} [NeweSIMOrderData.notifyPattern]
 * @property {string} [NeweSIMOrderData.dummyLineNo]
 */

/**
 *
 * @param {object} context
 * @param {string} createdUserId
 * @param {NeweSIMOrderData} data
 * @returns {Promise<string>} 新規受付SOID
 */
const processNeweSIMOrder = async (context, createdUserId, data) => {
    // processNewOTAOrder
    let voicePlanId = null;
    const checkVoicePlanResult = await isVoicePlanIdWithLiteMVNO(context, data.planId);
    if (checkVoicePlanResult) {
        const defaultPlanResult = await getDefaultPlanByTenantId(context, data.tenantId);
        if (defaultPlanResult) {
            voicePlanId = defaultPlanResult.voice_plan_id;
        } else {
            context.log.error(`${data.tenantId} does not have default voice plan.`);
        }
    }

    const planContractType = await getNetworkInfoByPlanId(context, data.planId);
    let contractType = data.contractType;
    if (!data.contractType) {
        if (planContractType === CONTRACT_TYPES.FIVE_G_NSA) {
            contractType = planContractType.voice_flag ? 'SJ' : 'SH';
        } else {
            if (planContractType.voice_flag) {
                contractType = 'SE';
            } else if (planContractType.sms_enable) {
                contractType = 'SD';
            } else {
                contractType = 'SC';
            }
        }
    } // else: 指定されている場合はその値をそのまま使う(バリデーションはValidatorで既にチェック済み)

    // create MNP転入SO
    const retSoId = await MNPTennyuSoModel.createShinkiESIMSO(context, {
        createdUserId,
        tenantId: data.tenantId,
        tempoId: data.shopId,
        planId: parseInt(data.planId),
        eid: data.eid,
        kouteiId: KOUTEI_IDS.UKETSUKE_CHECK_OK,
        uketsukeDateTime: dayjs().unix(),
        kaisenOptions: data.lineOptions,
        contractType,
        Nno: data.nBan,
        shopMemo: data.shopMemo,
        notifyPattern: (await isShanaiTenantType(context, data.tenantId)) ? data.notifyPattern : null,
        dummyLineNo: data.dummyLineNo,
        ngReason: null,
        voicePlanId,
    });

    return retSoId;
};

/**
 *
 * @param {object} context
 * @param {string} soId 新規受付(eSIM)SOID
 */
const sendNeweSIMOrderToAladinService = async (context, soId) => {
    const soObj = await MNPTennyuSoModel.findOne({ so_id: soId }).exec();
    if (!soObj) throw new Error(`SOID ${soId} was not saved`);

    const ryoukinPlan = await convertPlanIdToRyoukinPlan(context, {
        planId: soObj.planId,
        contractType: soObj.contractType,
    });

    const tenantId = soObj.tenantId;
    const tempoId = soObj.tempoId;

    let mvnoKakinJyoho = false;
    if (checkIs020Support(ryoukinPlan)) {
        const tenantLineSetting = await TenantPlanLineOptionSettingModel.getDefaultLineOption(
            soObj.tenantId,
            soObj.planId,
            false, // isShinki
            false, // isMnp
            false, // iseSIM
            false // is020Support
        );
        if (tenantLineSetting) {
            mvnoKakinJyoho = tenantLineSetting.mvnoKakinJyoho;
        }
    }

    const daihyouBangoInfo = await DaihyouBangoModel.findByTenantAndryoukinPlanForShinki(
        tenantId,
        ryoukinPlan,
        mvnoKakinJyoho
    );
    const kaisenOptionForAladinAPI = await getDiffOfKaisenOption(context, {
        tenantId,
        planId: soObj.planId,
        nNo: soObj.Nno,
        kaisenOptionIds: soObj.kaisenOptions ?? [],
        kaisenOptionIdsOfDaihyouBango: daihyouBangoInfo.kaisenOptions,
    });
    // 020 check
    let sousaservice = kaisenOptionForAladinAPI.sousaservice;
    if (soObj.is020Support) {
        const M2MSousaservice = M2M_SOUSA_SERVICE;
        if (M2MSousaservice) {
            if (kaisenOptionForAladinAPI.sousaservice) {
                // STEP 11.0 (020)のパラメタを修正
                sousaservice = `${kaisenOptionForAladinAPI.sousaservice}${M2MSousaservice}`;
            } else {
                sousaservice = M2MSousaservice;
            }
        } else {
            sousaservice = null;
        }
    }

    await registerAladinTransaction(context, {
        tenantId,
        tempoId,
        daihyouBango: daihyouBangoInfo.daihyouBango,
        transactionType: TRANSACTION_TYPE.SHINKI_MOUSHIKOMI,
        requestOrderType: ORDER_TYPE.SHINKI_MOUSHIKOMI_ESIM_KAITSU,
        requestOrderId: soId,
        requestParam: {
            hanbaitencode: portalConfig.ALADIN_API.HANBAITENCODE,
            fukajigyoshacode: null,
            keiyakusyubetu: daihyouBangoInfo.contractType,
            transactionTYPE: TRANSACTION_TYPE.SHINKI_MOUSHIKOMI,
            daihyobango: daihyouBangoInfo.daihyouBango,
            denwabango: null,
            seizobango: null,
            cardkeijo: CARD_KEIJO.ESIM,
            eid: soObj.eid,
            MNPyoyakubango: null,
            MNPzokusei: null,
            MNPyoyakusyakana: null,
            MNPyoyakusyakanji: null,
            MNPseinengappi: null,
            ansyobango: null,
            ryokinplan: null,
            sousaservice,
            WWtukehenhaiFLG: kaisenOptionForAladinAPI.WWtukehenhaiFLG,
            WWriyouteisimeyasugaku: kaisenOptionForAladinAPI.WWriyouteisimeyasugaku,
            WWdaisankokuhassinkisei: null,
            WCtukehenhaiFLG: kaisenOptionForAladinAPI.WCtukehenhaiFLG,
            WCriyouteisimeyasugaku: kaisenOptionForAladinAPI.WCriyouteisimeyasugaku,
            WCtuwateisi: null,
        },
    });
    await DaihyouBangoModel.decreaseQuotaQuantity(daihyouBangoInfo.daihyouId);
};

module.exports = {
    processNeweSIMOrder,
    sendNeweSIMOrderToAladinService,
};
