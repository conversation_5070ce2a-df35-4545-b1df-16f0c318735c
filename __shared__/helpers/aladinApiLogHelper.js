const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

/**
 * It takes a JavaScript object and returns a URLSearchParams object
 * @param req - The request object that contains the data to be sent to the server.
 */
const prepareFormData = (req) => {
    return Object.entries(req)
        .map((pair) => pair.join('='))
        .join('&');
};

/**
 * > It returns a message based on the error code
 * @param syorikekkaKbn - The error code returned by the Aladin API.
 * @returns the error message based on the error code.
 */
const findAladinErrorMsgByErrorCode = (syorikekkaKbn) => {
    if (portalConfig.ALADIN_API.ERROR_MSG_LIST[syorikekkaKbn]) {
        return portalConfig.ALADIN_API.ERROR_MSG_LIST[syorikekkaKbn];
    } else {
        return portalConfig.ALADIN_API.ERROR_MSG_LIST['OTHER'];
    }
};

const isDecisionErrMsg = (errCode) => {
    return portalConfig.ALADIN_API.ERROR_MSG_LIST[errCode];
};

const removeJsonTextAttribute = (value, parentElement) => {
    var keyNo = Object.keys(parentElement._parent).length;
    var keyName = Object.keys(parentElement._parent)[keyNo - 1];
    parentElement._parent[keyName] = value;
};

const xml2js = (xml) => {
    const convert = require('xml-js');
    const options = {
        compact: true,
        trim: true,
        ignoreDeclaration: true,
        ignoreInstruction: true,
        ignoreAttributes: true,
        ignoreComment: true,
        ignoreCdata: true,
        ignoreDoctype: true,
        textFn: removeJsonTextAttribute,
    };

    return convert.xml2js(xml, options);
};

module.exports = { prepareFormData, findAladinErrorMsgByErrorCode, isDecisionErrMsg, xml2js };
