// copied from EXTERNAL_API ProcessChangeExternalAPI/helpers/yakitsukeOTAHelper.js
// TODO refactor EXTERNAL API repo to use this shared file

const { TRANSACTION_TYPE, ORDER_TYPE, ZOKUSEI_CODE } = require('../constants/aladinTransactions');
const { M2M_SOUSA_SERVICE } = require('../constants/specialDefinitions');
const { getDiffOfKaisenOption, checkIs020Support, getContractType } = require('../helpers/daihyouBangoHelper');
const TenantPlanLineOptionSettingModel = require('../models/adminService/tenantPlanLineOptionSetting.model');
const DaihyouBangoModel = require('../models/daihyouBango.model');
const { convertPlanIdToRyoukinPlan, convertPlanIdToRyoukinPlanByKaisenNo } = require('../pgModels/plans');
const AladinService = require('../services/aladin.service');
const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
/**
 *
 * @param {object} context
 * @param {string} createdUserId
 * @param {string} simInfo
 * @param {object} soObj MNP Tennyu SO
 */
const processMNPTennyuOTA = async (context, createdUserId, simInfo, soObj) => {
    const ryoukinPlan = await convertPlanIdToRyoukinPlan(context, {
        planId: soObj.planId,
        contractType: soObj.contractType,
    });
    const tenantId = soObj.tenantId;
    const tempoId = soObj.tempoId;
    const kaisenNo = soObj.kaisenNo;
    const soIdValue = soObj.so_id;
    const requestOrderId = soIdValue;

    const daihyouBangoInfo = await DaihyouBangoModel.findByTenantAndryoukinPlan(tenantId, ryoukinPlan);

    const kaisenOption = await getDiffOfKaisenOption(context, {
        tenantId: soObj.tenantId,
        planId: soObj.planId,
        nNo: soObj.Nno,
        kaisenOptionIds: soObj.kaisenOptions,
        kaisenOptionIdsOfDaihyouBango: daihyouBangoInfo.kaisenOptions,
    });

    await Promise.all([
        AladinService.registerAladinTransaction(context, {
            tenantId,
            tempoId,
            daihyouBango: daihyouBangoInfo.daihyouBango,
            transactionType: TRANSACTION_TYPE.SHINKI_MOUSHIKOMI,
            requestOrderType: ORDER_TYPE.MNP_TENNYU_UKETSUKE_OTA_ROM_KAITSU,
            requestOrderId,
            requestParam: {
                hanbaitencode: portalConfig.ALADIN_API.HANBAITENCODE,
                fukajigyoshacode: null,
                keiyakusyubetu: daihyouBangoInfo.contractType,
                transactionTYPE: TRANSACTION_TYPE.SHINKI_MOUSHIKOMI,
                daihyobango: daihyouBangoInfo.daihyouBango,
                denwabango: kaisenNo,
                seizobango: simInfo,
                cardkeijo: null,
                MNPyoyakubango: soObj.mnpNo,
                MNPzokusei: ZOKUSEI_CODE[soObj.zokusei],
                MNPyoyakusyakana: soObj.contractNameKana,
                MNPyoyakusyakanji: soObj.contractName,
                MNPseinengappi: soObj.zokusei === '法人' ? null : soObj.birthday,
                ansyobango: null,
                ryokinplan: null,
                sousaservice: kaisenOption.sousaservice,
                WWtukehenhaiFLG: kaisenOption.WWtukehenhaiFLG,
                WWriyouteisimeyasugaku: kaisenOption.WWriyouteisimeyasugaku,
                WWdaisankokuhassinkisei: null,
                WCtukehenhaiFLG: kaisenOption.WCtukehenhaiFLG,
                WCriyouteisimeyasugaku: kaisenOption.WCriyouteisimeyasugaku,
                WCtuwateisi: null,
            },
        }),
        DaihyouBangoModel.decreaseQuotaQuantity(daihyouBangoInfo.daihyouId),
    ]);
};

/**
 *
 * @param {object} context
 * @param {string} createdUserId
 * @param {string} simInfo
 * @param {object} soObj MNP Tennyu SO
 * @param {string} kaisenNo kariKaisenNo from SimZaikoModel
 */
const processShinkiOTA = async (context, createdUserId, simInfo, soObj, kaisenNo) => {
    const ryoukinPlan = await convertPlanIdToRyoukinPlan(context, {
        planId: soObj.planId,
        contractType: soObj.contractType,
    });
    const tenantLineSetting = await TenantPlanLineOptionSettingModel.getDefaultLineOption(
        soObj.tenantId,
        soObj.planId,
        false, // isShinki
        false, // isMnp
        null, // eSIM
        null // is020Support
    );
    // https://mobilus.backlog.jp/view/MVNO_N_M-956#comment-47362572
    let mvnoKakinJyoho = false;
    if (checkIs020Support(ryoukinPlan)) {
        if (tenantLineSetting) {
            mvnoKakinJyoho = tenantLineSetting.mvnoKakinJyoho;
        }
    }
    const daihyouBangoInfo = await DaihyouBangoModel.findByTenantAndryoukinPlanForShinki(
        soObj.tenantId,
        ryoukinPlan,
        mvnoKakinJyoho
    );
    const kaisenOption = await getDiffOfKaisenOption(context, {
        tenantId: soObj.tenantId,
        planId: soObj.planId,
        nNo: soObj.Nno,
        kaisenOptionIds: soObj.kaisenOptions,
        kaisenOptionIdsOfDaihyouBango: daihyouBangoInfo.kaisenOptions,
    });
    // STEP 8.0 020対応
    let sousaservice = kaisenOption.sousaservice;
    if (soObj.is020Support) {
        const M2MSousaservice = M2M_SOUSA_SERVICE;
        if (M2MSousaservice) {
            if (kaisenOption.sousaservice) {
                // STEP 11.0 (020)のパラメタを修正
                sousaservice = `${kaisenOption.sousaservice}${M2MSousaservice}`;
            } else {
                sousaservice = M2MSousaservice;
            }
        } else {
            sousaservice = null;
        }
    }
    await Promise.all([
        AladinService.registerAladinTransaction(context, {
            tenantId: soObj.tenantId,
            tempoId: soObj.tempoId,
            daihyouBango: daihyouBangoInfo.daihyouBango,
            transactionType: TRANSACTION_TYPE.SHINKI_MOUSHIKOMI,
            requestOrderType: ORDER_TYPE.SHINKI_MOUSHIKOMI_OTA_ROM,
            requestOrderId: soObj.so_id,
            requestParam: {
                hanbaitencode: portalConfig.ALADIN_API.HANBAITENCODE,
                fukajigyoshacode: null,
                keiyakusyubetu: daihyouBangoInfo.contractType,
                transactionTYPE: TRANSACTION_TYPE.SHINKI_MOUSHIKOMI,
                daihyobango: daihyouBangoInfo.daihyouBango,
                denwabango: kaisenNo,
                seizobango: simInfo,
                cardkeijo: null,
                MNPyoyakubango: null,
                MNPzokusei: null,
                MNPyoyakusyakana: null,
                MNPyoyakusyakanji: null,
                MNPseinengappi: null,
                ansyobango: null,
                ryokinplan: null,
                sousaservice: sousaservice,
                WWtukehenhaiFLG: kaisenOption.WWtukehenhaiFLG,
                WWriyouteisimeyasugaku: kaisenOption.WWriyouteisimeyasugaku,
                WWdaisankokuhassinkisei: null,
                WCtukehenhaiFLG: kaisenOption.WCtukehenhaiFLG,
                WCriyouteisimeyasugaku: kaisenOption.WCriyouteisimeyasugaku,
                WCtuwateisi: null,
            },
        }),
        DaihyouBangoModel.decreaseQuotaQuantity(daihyouBangoInfo.daihyouId),
    ]);
};

/**
 *
 * @param {object} context
 * @param {string} createdUserId
 * @param {string} simInfo
 * @param {object} soObj MNP Tennyu SO
 */
const processSIMSaihakkouOTA = async (context, createdUserId, simInfo, soObj) => {
    const ryoukinPlan = await convertPlanIdToRyoukinPlanByKaisenNo(context, soObj.kaisenNo);
    await Promise.all([
        AladinService.registerAladinTransaction(context, {
            tenantId: soObj.tenantId,
            tempoId: soObj.tempoId,
            daihyouBango: '',
            transactionType: TRANSACTION_TYPE.KISETSU_HENKOU,
            requestOrderType: ORDER_TYPE.KISETSU_HENKOU_SAIHAKKOU_OTA_ROM_KAITSU,
            requestOrderId: soObj.so_id,
            requestParam: {
                hanbaitencode: portalConfig.ALADIN_API.HANBAITENCODE,
                fukajigyoshacode: null,
                keiyakusyubetu: getContractType(ryoukinPlan),
                transactionTYPE: TRANSACTION_TYPE.KISETSU_HENKOU,
                denwabango: soObj.kaisenNo,
                seizobango: simInfo,
                cardsaihakoFLG: '1',
                NWmikaituFLG: null,
                cardkei1jo: null,
                MNPyoyakubango: null,
                PINlockkaijoreset: null,
                MNPzokusei: null,
                MNPyoyakusyakana: null,
                MNPyoyakusyakanji: null,
                MNPseinengappi: null,
                ansyobango: null,
                ryokinplan: null,
                sousaservice: null,
                WWtukehenhaiFLG: null,
                WWriyouteisimeyasugaku: null,
                WWdaisankokuhassinkisei: null,
                WCtukehenhaiFLG: null,
                WCriyouteisimeyasugaku: null,
                WCtuwateisi: null,
            },
        }),
    ]);
};

module.exports = {
    processMNPTennyuOTA,
    processShinkiOTA,
    processSIMSaihakkouOTA,
};
