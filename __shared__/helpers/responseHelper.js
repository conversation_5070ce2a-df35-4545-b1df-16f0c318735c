const dayjs = require('dayjs');
const mongoose = require('mongoose');
const { getCommonErrorName } = require('../constants/commonError');
const responseStatusCodes = require('../constants/responseStatusCodes');
const { findOPFErrorMsgByOPFErrorCode } = require('./configHelper');
const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
const utc = require('dayjs/plugin/utc');
const gaibuAPIProcessIDPrefix = portalConfig.GAIBU_API_PROCESS_ID_PREFIX || 'PFA';
const RESULT_CODE_OK = '000000';
const RESULT_CODE_JSON_PARSE_ERROR = '0999';
dayjs.extend(utc);

/**
 * create error response for CORE/ORDER service
 */
const _createErrorResponseBodyWithErrorMessage = (error, errorMessage, nttcmvnoErrorCode, nttcmvnoErrorMessage) => {
    const error_msg = errorMessage ? errorMessage : getCommonErrorName(error);
    let mvnoErrorMsg = nttcmvnoErrorMessage;
    if (!mvnoErrorMsg) {
        mvnoErrorMsg = findOPFErrorMsgByOPFErrorCode(nttcmvnoErrorCode);
    }
    return {
        error,
        error_msg,
        nttc_mvno_error_code: nttcmvnoErrorCode,
        nttc_mvno_error_msg: mvnoErrorMsg,
    };
};

const _createErrorResponseBody = (sequenceNo, errorCode, errorCodePrefix = '') => {
    const apiProcessID = new mongoose.Types.ObjectId();
    return {
        responseHeader: {
            sequenceNo: sequenceNo || '0000000', //入力項目のrequestHeader.sequenceNoにて設定された値を設定します。
            receivedDate: dayjs().format('YYYY/MM/DD HH:mm:ss'),
            processCode: errorCodePrefix + errorCode,
            apiProcessID: gaibuAPIProcessIDPrefix + apiProcessID,
        },
    };
};

const _createSuccessResponseBody = (sequenceNo, soId, result) => {
    const apiProcessID = new mongoose.Types.ObjectId();
    return {
        responseHeader: {
            sequenceNo: sequenceNo || '0000000', //入力項目のrequestHeader.sequenceNoにて設定された値を設定します。
            receivedDate: dayjs().format('YYYY/MM/DD HH:mm:ss'),
            processCode: RESULT_CODE_OK,
            apiProcessID: soId || gaibuAPIProcessIDPrefix + apiProcessID,
        },
        ...result,
    };
};

const _createApiCallResponseBody = (sequenceNo, soId, resultCode, result) => {
    const apiProcessID = new mongoose.Types.ObjectId();
    return {
        responseHeader: {
            sequenceNo: sequenceNo || '0000000', //入力項目のrequestHeader.sequenceNoにて設定された値を設定します。
            receivedDate: dayjs().format('YYYY/MM/DD HH:mm:ss'),
            processCode: resultCode || RESULT_CODE_OK,
            apiProcessID: soId || gaibuAPIProcessIDPrefix + apiProcessID,
        },
        result,
    };
};

/**
 *
 * @param {object} context
 * @param {string|number} error
 * @param {string} errorMessage
 * @param {string} nttcmvnoErrorCode
 * @param {string} nttcmvnoErrorMessage
 */
const responseWithError = (context, error, errorMessage, nttcmvnoErrorCode, nttcmvnoErrorMessage) => {
    context.res = {
        headers: {
            'Content-Type': 'application/json',
        },
        status: responseStatusCodes.OK,
        body: _createErrorResponseBodyWithErrorMessage(error, errorMessage, nttcmvnoErrorCode, nttcmvnoErrorMessage),
    };
};
/**
 *
 * @param {object} context
 * @param {string} sequenceNo
 * @param {string} errorCode
 * @param {string} errorCodePrefix
 */
const responseExternalAPIWithError = (context, sequenceNo, errorCode, errorCodePrefix) => {
    context.log.info('responseExternalAPIWithError: ', _createErrorResponseBody(sequenceNo, errorCode, errorCodePrefix))
    context.res = {
        headers: {
            'Content-Type': 'application/json',
        },
        status: responseStatusCodes.OK,
        body: _createErrorResponseBody(sequenceNo, errorCode, errorCodePrefix),
    };
};

/**
 *
 * @param {object} context
 * @param {string} sequenceNo
 * @param {string} soID
 * @param {object} result
 */
const responseSuccess = (context, sequenceNo, soID, result) => {
    context.log.info('responseSuccess: ', _createSuccessResponseBody(sequenceNo, soID, result))
    context.res = {
        headers: {
            'Content-Type': 'application/json',
        },
        status: responseStatusCodes.OK,
        body: _createSuccessResponseBody(sequenceNo, soID, result),
    };
};

/**
 *
 * @param {object} context
 * @param {string} sequenceNo
 * @param {string} soID
 * @param {string} processCode
 * @param {Array.<resultInfo>} result
 */
const responseAfterApiCall = (context, sequenceNo, soId, processCode, result) => {
    context.res = {
        headers: {
            'Content-Type': 'application/json',
        },
        status: responseStatusCodes.OK,
        body: _createApiCallResponseBody(sequenceNo, soId, processCode, result),
    };
};

/**
 * The function sets a response status and body with a given result code for an Aladin request.
 * @param context - The `context` parameter is an object that contains information about the current
 * execution context of the Azure Function.
 * @param resultCode - 00 or 99
 */
const responseAladinReq = (context, resultCode) => {
    const currentTimeGMT = dayjs.utc().format('ddd, DD MMM YYYY HH:mm:ss') + ' GMT';

    context.res = {
        status: responseStatusCodes.OK,
        headers: {
            'Content-Type': 'text/plain',
            Date: currentTimeGMT,
            Connection: 'close',
            'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
            'X-Frame-Options': 'DENY',
            'X-Content-Type-Options': 'nosniff',
            'X-XSS-Protection': '1; mode=block',
        },
        body: 'Result=' + resultCode +'\n',
    };
};

module.exports = {
    responseWithError,
    responseExternalAPIWithError,
    responseSuccess,
    responseAfterApiCall,
    responseAladinReq,
    RESULT_CODE_OK,
    RESULT_CODE_JSON_PARSE_ERROR,
};
