const { OIDC } = require('../constants/azure');
const PlusUsersModel = require('../models/userService/plusUser.model');
const UserModel = require('../models/users.model');
const { isTempoUser, isOPFUserIdStr } = require('./baseHelper');

const USER_REGEX = /^[a-zaA-Z0-9\-_.@]+$/;

/**
 * Check if the user id is valid format
 * @param {string} userId
 * @returns {boolean}
 */
const isValidFormatUserId = (userId) => {
    if (!userId || typeof userId !== 'string') {
        return false;
    }

    const AAD_USER_REGEX = new RegExp(`^[A-Za-z0-9._%+-]+(${OIDC.AD_VALID_DOMAIN})`);

    if (AAD_USER_REGEX.test(userId)) {
        return true;
    }

    if (
        userId.length < 3 ||
        userId.length > 16 ||
        !USER_REGEX.test(userId) ||
        userId.charAt(0) === '@' //[@] は先頭には使用できません。
    ) {
        return false;
    }

    return true;
};

/**
 * Check if the tempo user password is valid format
 * @param {string} tempoUserPassword
 * @param {string} tempoUserId
 * @returns {boolean}
 */
const isValidTempoUserPassword = (tempoUserPassword, tempoUserId) => {
    if (
        !tempoUserPassword ||
        typeof tempoUserPassword !== 'string' ||
        !tempoUserId ||
        typeof tempoUserId !== 'string' ||
        tempoUserPassword.includes(tempoUserId) ||
        !isValidFormatPassword(tempoUserPassword)
    ) {
        return false;
    }

    return true;
};

/**
 * Check if the tempo user id is valid format
 * @param {string} userId
 * @returns {boolean}
 */
const isValidTempoUserId = (userId) => {
    if (
        !userId ||
        typeof userId !== 'string' ||
        !isOPFUserIdStr(userId) ||
        userId.length < 3 ||
        userId.length > 16 ||
        !USER_REGEX.test(userId)
    ) {
        return false;
    }

    return true;
};

/**
 * Check if the password is valid format
 * @param {string} password
 * @returns {boolean}
 */
const isValidFormatPassword = (password) => {
    // 半角英数(大文字・小文字)、記号類(! " # $ % & ' ( ) = ~ | - ^ @ [ ; : ] , . / ` { + } >?)
    const PASSWORD_REGEX = /^[a-zA-Z0-9!"#$%&'()=~|\-^@[;:\],./`{+}>?]+$/;

    if (
        !password ||
        typeof password !== 'string' ||
        password.length < 8 ||
        password.length > 16 ||
        !PASSWORD_REGEX.test(password)
    ) {
        return false;
    }

    return true;
};

const getUserInfo = async (context, tenantId, userId) => {
    try {
        let user = null;

        //case tempo user
        if (isTempoUser(userId)) {
            let plusUserModel = PlusUsersModel(tenantId);
            user = await plusUserModel.findNotRemovedUserByPlusId(context, { plusId: userId });
        } else {
            user = await UserModel.findByPlusIdAndTenantId(context, { userId, tenantId });
        }

        return user;
    } catch (error) {
        context.log.error('getUserInfo: error', error);
        return null;
    }
};

module.exports = {
    isValidFormatUserId,
    isValidTempoUserPassword,
    isValidTempoUserId,
    isValidFormatPassword,
    getUserInfo,
};
