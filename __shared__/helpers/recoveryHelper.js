/**
 * helper functions for correct process (リカバリ)
 */
const { TRANSACTION_TYPE } = require('../constants/aladinTransactions');
const { KOUTEI_IDS, SO_KIND_TYPE } = require('../constants/orderConstants');
const { SIM_ORDER_TYPES_REISSUE } = require('../constants/simConstants');
const { isNone } = require('./baseHelper');

const AladinApiLogModel = require('../models/aladinService/aladinApiLog.model');
const KaisenHaishiSoModel = require('../models/kaisenHaishiSo.model');
const MNPTennyuSoModel = require('../models/mnpTennyuSo.model');
const SimBlackingSoModel = require('../models/simBlackingSo.model');
const SwimmySoModel = require('../models/swimmySo.model');

// const esimNotify = require('../models/swimmyService/esimKekkaTsuchi');
const SimSaihakkouKekkaTsuchi = require('../models/swimmyService/simSaihakkouKekkaTsuchi');

/**
 * @param {object} context
 * @param {string} soId
 * @param {string} transactionType
 * @returns {Promise<string>}
 */
const getUserIdFirstKoutei = async (context, soId, transactionType) => {
    const q = {
        so_id: soId,
    };
    let cur;
    switch (transactionType) {
        case TRANSACTION_TYPE.KAITSU:
            cur = await SimBlackingSoModel.findOne(q).exec();
            break;
        case TRANSACTION_TYPE.MOSHIDE_KAIYAKU:
            cur = await KaisenHaishiSoModel.findOne(q).exec();
            break;
        default:
            cur = await MNPTennyuSoModel.findOne(q).exec();
    }
    if (isNone(cur)) return '';
    const kouteis = cur.kouteis;
    if (Array.isArray(kouteis) && kouteis.length > 0) {
        return kouteis.at(0).userId;
    } else {
        return '';
    }
};

/**
 * Update Koutei to Service Order
 * @param {object} context
 * @param {string} soId
 * @param {string} transactionType
 * @param {number} lastKouteiId
 * @param {Array<number>} listKouteiCorrect
 * @param {number} timestamp
 * @param {boolean} isRecoveryChecked リカバリあり (default=true)
 */
// eslint-disable-next-line no-unused-vars
const correctProcessBySoId = async (
    context,
    soId,
    transactionType,
    lastKouteiId,
    listKouteiCorrect,
    timestamp,
    isRecoveryChecked = true
) => {
    const userId = await getUserIdFirstKoutei(context, soId, transactionType);
    const query = { so_id: soId };
    const update = {};

    const maxKoutei = listKouteiCorrect.reduce((high, cur) => (high > cur ? high : cur), 0);
    const listKoutei = await Promise.all(
        listKouteiCorrect.map(async (kouteiId) => {
            const koutei = {
                kouteiId,
                timestamp,
                userId,
            };
            if (kouteiId === KOUTEI_IDS.ALADIN_KOUJI_NG || kouteiId === KOUTEI_IDS.KOUKANKI_SETTEI_NG) {
                // 11 or 21
                koutei.ngReason = 'その他';
            }

            if (kouteiId === maxKoutei) {
                update.$set = { lastKoutei: koutei };
            }

            // STEP25: eSIM結果通知を削除
            // if ([KOUTEI_IDS.KOUKANKI_SETTEI_NG, KOUTEI_IDS.KOUKANKI_SETTEI_OK].includes(kouteiId)) {
            //     const aladinSO = await MNPTennyuSoModel.findSOBySOID(context, soId);
            //     if (
            //         !isNone(aladinSO) &&
            //         !isNone(aladinSO.notifyPattern) &&
            //         aladinSO.soKind == SO_KIND_TYPE.MNP_TENNYU
            //     ) {
            //         //通知パターン：1ならSwimmy-V2(DotaNotice)、通知パターン：2ならSwimmy-V3(EIDNotice)に通知
            //         if (aladinSO.notifyPattern == NOTIFY_PATTERN.SWIMMY_V2) {
            //             await esimNotify.doESIMKekkaTsuChiSwimmyV2Process(context, {
            //                 obj: aladinSO,
            //                 updateToKouteiId: kouteiId,
            //                 forceNotify: true,
            //             });
            //         } else {
            //             await esimNotify.doESIMKekkaTsuChiSwimmyV3Process(context, {
            //                 obj: aladinSO,
            //                 updateToKouteiId: kouteiId,
            //                 forceNotify: true,
            //             });
            //         }
            //     }
            // }

            // PATCH202410 (OTA再発行の通知): システムID：9a1z & [3 : 焼付けSIM選択NG, 11: ALADIN工事NG, 13: SMS開通OK]
            if (
                isRecoveryChecked && // リカバリありの場合のみ通知を行う
                [KOUTEI_IDS.SIM_YAKITSUKE_SENTAKU_NG, KOUTEI_IDS.ALADIN_KOUJI_NG, KOUTEI_IDS.SMS_KAITSU_OK].includes(
                    kouteiId
                )
            ) {
                const aladinSO = await MNPTennyuSoModel.findSOBySOID(context, soId);
                if (
                    !isNone(aladinSO) &&
                    aladinSO.soKind === SO_KIND_TYPE.MNP_SIM_SAIHAKKOU &&
                    aladinSO.hankuro === SIM_ORDER_TYPES_REISSUE.OTA &&
                    aladinSO.needSwimmyNoti
                ) {
                    context.log('CorrectProcessAPI - OTA再発行の通知', soId, kouteiId);
                    const apiLog = await AladinApiLogModel.findOneByRequestOrderId(context, soId);
                    await SimSaihakkouKekkaTsuchi.doSIMSaihakkouKekkaTsuChiProcess(context, apiLog, aladinSO, true);
                }
            }

            return koutei;
        })
    );
    update.$push = { kouteis: { $each: listKoutei } };

    let result;
    switch (transactionType) {
        case TRANSACTION_TYPE.KAITSU:
            result = await SimBlackingSoModel.updateOne(query, update, { runValidators: true }).exec();
            break;
        case TRANSACTION_TYPE.MOSHIDE_KAIYAKU:
            result = await KaisenHaishiSoModel.updateOne(query, update, { runValidators: true }).exec();
            break;
        default:
            result = await MNPTennyuSoModel.updateOne(query, update, { runValidators: true }).exec();
    }

    return result?.modifiedCount !== 0 ? maxKoutei : 0;
};

/**
 * Update Koutei to Swimmy Service Order
 * @param {object} context
 * @param {string} soId
 * @param {string} transactionType
 * @param {number} lastKouteiId
 * @param {Array<number>} listKouteiCorrect
 * @param {number} timestamp
 */
const correctProcessBySoIdForSwimmy = async (context, soId, listKouteiCorrect, timestamp) => {
    let userId = '';
    const swimmySO = await SwimmySoModel.findOne({ so_id: soId }).lean().exec();
    if (!isNone(swimmySO)) {
        if (Array.isArray(swimmySO.kouteis) && swimmySO.kouteis.length > 0) {
            userId = swimmySO.kouteis.at(0).userId;
        }
    }

    const query = { so_id: soId };
    const update = {};

    const maxKoutei = listKouteiCorrect.reduce((high, cur) => (high > cur ? high : cur), 0);
    const listKoutei = listKouteiCorrect.map((kouteiId) => {
        const koutei = {
            kouteiId,
            timestamp,
            userId,
        };
        if (kouteiId === KOUTEI_IDS.ALADIN_KOUJI_NG || kouteiId === KOUTEI_IDS.KOUKANKI_SETTEI_NG) {
            // 11 or 21
            koutei.ngReason = 'その他';
        }

        if (kouteiId === maxKoutei) {
            update.$set = { lastKoutei: koutei };
        }

        return koutei;
    });
    update.$push = { kouteis: { $each: listKoutei } };

    const result = await SwimmySoModel.updateOne(query, update, { runValidators: true }).exec();

    return result?.modifiedCount !== 0 ? maxKoutei : 0;
};

module.exports = {
    correctProcessBySoId,
    correctProcessBySoIdForSwimmy,
};
