const appConfig = require('../config/appConfig');
const { isNone } = require('./baseHelper');
const { KOUTEI_IDS, SO_KIND_TYPE } = require('../constants/orderConstants');
const {
    SWIMMY_REQUEST_TYPE_PRIORITY_KEY,
    SWIMMY_API_LOG_LOWEST_PRIORITY,
    SWIMMY_REQUEST_TYPES,
    SWIMMY_CONSTANTS,
    SWIMMY_API_LOG_COMPLETE_STATUS,
    SWIMMY_API_LOG_STATUS,
} = require('../constants/swimmyTransactions');
const { NOTIFY_PATTERN } = require('../constants/simConstants');

const portalConfig = appConfig.getPortalConfig();

/**
 * getPriority for Swimmy API Log
 * @param {object} context
 * @param {string} SWIMMY_REQUEST_TYPES
 * @returns {number}
 */
const getSwimmyApiPriority = (context, swimmyRequestType) => {
    const priorityKey = SWIMMY_REQUEST_TYPE_PRIORITY_KEY[swimmyRequestType];
    if (priorityKey === undefined || priorityKey === null) {
        context.log('Cannot get key string in config file for order type:', swimmyRequestType);
        return SWIMMY_API_LOG_LOWEST_PRIORITY;
    } else if (priorityKey === '') {
        // in case key is not yet specified in SWIMMY_REQUEST_TYPE_PRIORITY_KEY
        context.log('Cannot get key string in config file for order type (empty string):', swimmyRequestType);
        return SWIMMY_API_LOG_LOWEST_PRIORITY;
    } else {
        /** @type {number|undefined} */
        let priority = portalConfig.SWIMMY.API_PRIORITY_LIST[priorityKey];
        if (priority === undefined || priority === null) {
            context.log('Cannot get value in config file for', priorityKey);
            priority = SWIMMY_API_LOG_LOWEST_PRIORITY;
        }
        return priority;
    }
};

/**
 * Create request parameter for Swimmy API Log
 * @param {string} swimmyType
 * @param {string} otherSystemSendDatetime
 * @param {string} [userId]
 * @param {string} [useAuth]
 * @param {string} requestOrderId
 * @param {string} soKind
 * @param {string} lineNo
 * @param {string} resultCode
 * @param {Array<{errorCode:string,errorMessage:string}>} aladinErr
 * @param {string} appDate
 * @param {string} salesChannelCode
 * @param {Array<{appAttributeCode:string,appAttributeValue:string}>} appBasicDtlList
 * @param {string} [mnpOutDate]
 * @param {string} simNo
 * @param {string} processId
 * @param {string} [aladinSoNGReason]
 * @param {string} [shippingNumber]
 * @param {string} [dummyLineNo]
 * @param {string} [eId]
 * @param {string} [notifyPattern]
 */
const createRequestParamsForSwimmyApiLog = (
    swimmyType,
    otherSystemSendDatetime,
    userId,
    useAuth,
    requestOrderId,
    soKind,
    lineNo,
    resultCode,
    aladinErr,
    appDate,
    salesChannelCode,
    appBasicDtlList,
    mnpOutDate,
    simNo,
    processId,
    aladinSoNGReason,
    shippingNumber,
    dummyLineNo,
    eId,
    notifyPattern
) => {
    // NOTE `processId` is string so need to use `==` to compare with `KOUTEI_IDS` (number)
    /** @type {{commonHeaderInfo: object, constResultInfo?: object, appRequestInfo?: object}|string} */
    let result = {};
    const simNoAreNeeded =
        soKind == SO_KIND_TYPE.MNP_NEW_ESIM_SERVICE_ORDER &&
        processId == KOUTEI_IDS.ALADIN_KOUJI_NG &&
        (aladinSoNGReason ?? '') == portalConfig.ALADIN_KOUJI_NG_SPECIAL_REASON_FOR_NEW_ESIM;
    const koujiCheck =
        processId == KOUTEI_IDS.KOUKANKI_SETTEI_NG ||
        processId == KOUTEI_IDS.KOUKANKI_SETTEI_OK ||
        (processId == KOUTEI_IDS.ALADIN_KOUJI_NG && (aladinSoNGReason ?? '') == portalConfig.ALADIN_KOUJI_NG_REASON);

    switch (swimmyType) {
        case SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI:
            if (!isNone(notifyPattern) && notifyPattern === NOTIFY_PATTERN.SWIMMY_V2) {
                // SendESIMKekkaTsuChiSwimmyV2RequestParams
                result = {
                    // RequestResultNotifHeaderInfo
                    commonHeaderInfo: {
                        requestSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                        otherSystemSendTime: otherSystemSendDatetime,
                    },
                    //ConstResultInfoESIMKekkaTsuchiSwimmyV2
                    constResultInfo: {
                        soId: requestOrderId,
                        processId,
                        soKind,
                        lineNo: _NoneIfFalse(lineNo, koujiCheck || simNoAreNeeded),
                        simNo: _NoneIfFalse(simNo, koujiCheck || simNoAreNeeded),
                        // aladinSoNGReason,
                    },
                };
            } else {
                // SendESIMKekkaTsuChiSwimmyV3RequestParams
                result = {
                    commonHeaderInfo: {
                        requestSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                        otherSystemSendTime: otherSystemSendDatetime,
                    },
                    constResultInfo: {
                        soId: requestOrderId,
                        processId,
                        soKind,
                        // dummyLineNo: dummyLineNo ?? undefined,
                        lineNo: _NoneIfFalse(lineNo, koujiCheck || simNoAreNeeded),
                        eId: _NoneIfFalse(eId, koujiCheck || simNoAreNeeded),
                        // aladinSoNGReason,
                    },
                };
                if (!isNone(dummyLineNo)) result.constResultInfo.dummyLineNo = dummyLineNo;
            }
            if (!isNone(aladinSoNGReason)) {
                result.constResultInfo.aladinSoNGReason = aladinSoNGReason;
            }
            break;
        case SWIMMY_REQUEST_TYPES.SIM_SAIHAKKOU_KEKKA_TSUCHI:
            // SendSIMSaihakkouKekkaTsuChiRequestParams
            result = {
                commonHeaderInfo: {
                    requestSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                    otherSystemSendTime: otherSystemSendDatetime,
                    // userId,
                    // useAuth,
                },
                constResultInfo: {
                    soId: requestOrderId,
                    lineNo,
                    // simNo: isNone(simNo) ? undefined : simNo,
                    // shippingNumber,
                    resultCode,
                    errorInfoList: aladinErr,
                },
            };
            if (!isNone(userId)) result.commonHeaderInfo.userId = userId;
            if (!isNone(useAuth)) result.commonHeaderInfo.useAuth = useAuth;
            if (!isNone(simNo)) result.constResultInfo.simNo = simNo;
            if (!isNone(shippingNumber)) result.constResultInfo.shippingNumber = shippingNumber;
            break;
        case SWIMMY_REQUEST_TYPES.KAISEN_OPTION_HENKOU_KEKKA_TSUCHI:
            // SendKaisenOptionHenkouKekkaTsuChiRequestParams
            result = {
                commonHeaderInfo: {
                    requestSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                    otherSystemSendTime: otherSystemSendDatetime,
                    // userId,
                    // useAuth,
                },
                constResultInfo: {
                    soId: requestOrderId,
                    lineNo,
                    resultCode,
                    errorInfoList: aladinErr,
                },
            };
            if (!isNone(userId)) result.commonHeaderInfo.userId = userId;
            if (!isNone(useAuth)) result.commonHeaderInfo.useAuth = useAuth;
            break;
        case SWIMMY_REQUEST_TYPES.OTA_KEKKA_TSUCHI:
            result = {
                commonHeaderInfo: {
                    requestSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                    otherSystemSendTime: otherSystemSendDatetime,
                },
                constResultInfo: {
                    soId: requestOrderId,
                    processId,
                    soKind,
                    lineNo: _NoneIfFalse(lineNo, koujiCheck),
                    simNo: _NoneIfFalse(simNo, koujiCheck),
                    // aladinSoNGReason,
                },
            };
            if (!isNone(aladinSoNGReason)) {
                result.constResultInfo.aladinSoNGReason = aladinSoNGReason;
            }
            break;
        case SWIMMY_REQUEST_TYPES.KUROKA_KANRYOU_TSUCHI:
            result = {
                commonHeaderInfo: {
                    requestSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                    otherSystemSendTime: otherSystemSendDatetime,
                    // userId,
                    // useAuth,
                },
                constResultInfo: {
                    soId: requestOrderId,
                    soKind,
                    lineNo,
                    simNo,
                    resultCode,
                    errorInfoList: aladinErr,
                },
            };
            if (!isNone(userId)) result.commonHeaderInfo.userId = userId;
            if (!isNone(useAuth)) result.commonHeaderInfo.useAuth = useAuth;
            break;
        case SWIMMY_REQUEST_TYPES.MNP_TENSHUTSU_KANRYOU_TSUCHI:
            result = {
                commonHeaderInfo: {
                    requestSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                    otherSystemSendTime: otherSystemSendDatetime,
                    userId,
                    useAuth,
                },
                constResultInfo: {
                    soId: requestOrderId,
                    lineNo,
                    mnpOutDate,
                },
            };
            break;
        case SWIMMY_REQUEST_TYPES.ALADIN_KEKKA_TSUCHI:
            result = {
                commonHeaderInfo: {
                    requestSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                    otherSystemSendTime: otherSystemSendDatetime,
                    userId,
                    useAuth,
                },
                constResultInfo: {
                    soId: requestOrderId,
                    soKind,
                    lineNo,
                    resultCode,
                    errorInfoList: aladinErr,
                },
            };
            break;
        case SWIMMY_REQUEST_TYPES.KUROKA:
        case SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND:
        case SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND_KAIJO:
        case SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_CHUUDAN:
        case SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_SAIKAI:
            result = {
                commonHeaderInfo: {
                    requestFromSystemId: SWIMMY_CONSTANTS.REQUEST_FROM_SYSTEM_ID,
                    otherSystemSendDatetime: otherSystemSendDatetime,
                    userId,
                    useAuth,
                },
                appRequestInfo: {
                    processType: SWIMMY_CONSTANTS.PROCESS_TYPE,
                    appInfoList: [
                        {
                            appBasic: {
                                inputFromType: SWIMMY_CONSTANTS.INPUT_FROM_TYPE,
                                orderType: SWIMMY_CONSTANTS.ORDER_TYPE,
                                appStatus: SWIMMY_CONSTANTS.APP_STATUS,
                                appDate,
                                salesChannelCode,
                                salesChannelName: SWIMMY_CONSTANTS.SALES_CHANNEL_NAME,
                                appBasicDtlList,
                            },
                        },
                    ],
                },
            };
            break;
        default:
            result = 'requestParams';
    }
    return result;
};

const _NoneIfFalse = (value, condition) => (condition ? value : undefined);

/**
 * Get completeStatus based on request type and status
 * @param {string} requestType - SWIMMY_REQUEST_TYPES
 * @param {number} status - SWIMMY_API_LOG_STATUS
 * @returns {string} SWIMMY_API_LOG_COMPLETE_STATUS
 */
const getCompleteStatus = (requestType, status) => {
    let result = SWIMMY_API_LOG_COMPLETE_STATUS.UNKNOWN;
    switch (status) {
        case SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_NOT_YET_SENT:
            result = SWIMMY_API_LOG_COMPLETE_STATUS.IN_PROCESS;
            break;
        case SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_SENT_OK:
            switch (requestType) {
                case SWIMMY_REQUEST_TYPES.ALADIN_KEKKA_TSUCHI:
                case SWIMMY_REQUEST_TYPES.MNP_TENSHUTSU_KANRYOU_TSUCHI:
                case SWIMMY_REQUEST_TYPES.KUROKA_KANRYOU_TSUCHI:
                case SWIMMY_REQUEST_TYPES.OTA_KEKKA_TSUCHI:
                case SWIMMY_REQUEST_TYPES.KAISEN_OPTION_HENKOU_KEKKA_TSUCHI:
                case SWIMMY_REQUEST_TYPES.SIM_SAIHAKKOU_KEKKA_TSUCHI:
                case SWIMMY_REQUEST_TYPES.ESIM_KEKKA_TSUCHI:
                    result = SWIMMY_API_LOG_COMPLETE_STATUS.COMPLETE_OK;
                    break;
                default:
                    if (requestType !== SWIMMY_REQUEST_TYPES.ALADIN_KEKKA_TSUCHI) {
                        result = SWIMMY_API_LOG_COMPLETE_STATUS.IN_PROCESS;
                    }
                    break;
            }
            break;
        case SWIMMY_API_LOG_STATUS.TOUROKU_IRAI_SENT_NG:
            result = SWIMMY_API_LOG_COMPLETE_STATUS.COMPLETE_NG;
            break;
        case SWIMMY_API_LOG_STATUS.TOUROKU_YOUKYUU_RESPONSE_NG:
            result = SWIMMY_API_LOG_COMPLETE_STATUS.COMPLETE_NG;
            break;
        case SWIMMY_API_LOG_STATUS.TOUROKU_YOUKYUU_RESPONSE_OK:
            if (requestType != SWIMMY_REQUEST_TYPES.ALADIN_KEKKA_TSUCHI) {
                result = SWIMMY_API_LOG_COMPLETE_STATUS.COMPLETE_OK;
            }
            break;
    }
    return result;
};

module.exports = {
    getSwimmyApiPriority,
    createRequestParamsForSwimmyApiLog,
    getCompleteStatus,
};
