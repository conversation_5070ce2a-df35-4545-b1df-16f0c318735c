const { KOUTEI_IDS } = require('../constants/orderConstants');
const { SWIMMY_REQUEST_TYPES } = require('../constants/swimmyTransactions');

const MNPTennyuSoModel = require('../models/mnpTennyuSo.model');
const SimBlackingSoModel = require('../models/simBlackingSo.model');

const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();

/**
 * Need to call this before calling `registerSwimmyTransaction` except for notification related order
 * @param {object} context
 * @param {string} swimmyType `SWIMMY_REQUEST_TYPES`
 * @param {string} requestOrderId SimBlackingSo or MNPTennyuSo so_id
 */
const updateKouteiForRegisterSwimmyTransaction = async (context, swimmyType, requestOrderId) => {
    const koutei = {
        kouteiId: KOUTEI_IDS.ALADIN_KOUJI_WAITING,
    };
    switch (swimmyType) {
        case SWIMMY_REQUEST_TYPES.KUROKA:
            await SimBlackingSoModel.updateKouteiInfo(context, {
                so_id: requestOrderId,
                updatedUserId: portalConfig.API_USER_ID,
                koutei,
            });
            break;
        case SWIMMY_REQUEST_TYPES.SIM_SAIHAKKO:
        case SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND:
        case SWIMMY_REQUEST_TYPES.KAISEN_SUPPEND_KAIJO:
        case SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_CHUUDAN:
        case SWIMMY_REQUEST_TYPES.KAISEN_RIYOU_SAIKAI:
            await MNPTennyuSoModel.updateKouteiInfo(context, {
                so_id: requestOrderId,
                updatedUserId: portalConfig.API_USER_ID,
                koutei,
            });
            break;
    }
};

module.exports = {
    updateKouteiForRegisterSwimmyTransaction,
};
