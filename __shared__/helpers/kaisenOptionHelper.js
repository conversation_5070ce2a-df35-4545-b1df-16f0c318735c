const _ = require('lodash');

/**
 *
 * @param {Array<string>} kaisenOptionSeq
 * @param {Array<string>} zaikoOptionSeq
 */
const isMatchingKaisenOption = (kaisenOptionSeq, zaikoOptionSeq) => {
    const s1 = kaisenOptionSeq.filter((r) => typeof r === 'string' && r !== '');
    const s2 = zaikoOptionSeq.filter((r) => typeof r === 'string' && r !== '');
    const RUSUBAN_OPTION_ID1 = 'AO143';
    const RUSUBAN_OPTION_ID2 = 'AO151';
    const CATCHHON_OPTION_ID1 = 'AO142';
    const CATCHHON_OPTION_ID2 = 'AO150';

    const rusubanOpt1 = s1.find((x) => x === RUSUBAN_OPTION_ID1 || x === RUSUBAN_OPTION_ID2);
    const catchhonOpt1 = s1.find((x) => x === CATCHHON_OPTION_ID1 || x === CATCHHON_OPTION_ID2);
    const optionWithoutRusubanSeq1 = s1.filter((x) => x !== RUSUBAN_OPTION_ID1 && x !== RUSUBAN_OPTION_ID2);
    const optionWithoutRusubanAndCatchhonSeq1 = optionWithoutRusubanSeq1.filter(
        (x) => x !== CATCHHON_OPTION_ID1 && x !== CATCHHON_OPTION_ID2
    );

    const rusubanOpt2 = s2.find((x) => x === RUSUBAN_OPTION_ID1 || x === RUSUBAN_OPTION_ID2);
    const catchhonOpt2 = s2.find((x) => x === CATCHHON_OPTION_ID1 || x === CATCHHON_OPTION_ID2);
    const optionWithoutRusubanSeq2 = s2.filter((x) => x !== RUSUBAN_OPTION_ID1 && x !== RUSUBAN_OPTION_ID2);
    const optionWithoutRusubanAndCatchhonSeq2 = optionWithoutRusubanSeq2.filter(
        (x) => x !== CATCHHON_OPTION_ID1 && x !== CATCHHON_OPTION_ID2
    );

    // (T, T) = true, (F, F) = true, else = false
    const isRusubanMatching = (rusubanOpt1 !== undefined) === (rusubanOpt2 !== undefined);
    const isCatchhonMatching = (catchhonOpt1 !== undefined) === (catchhonOpt2 !== undefined);

    const remainOptionIsMatching = _.isEqual(
        _.sortBy(optionWithoutRusubanAndCatchhonSeq1),
        _.sortBy(optionWithoutRusubanAndCatchhonSeq2)
    );
    return isRusubanMatching && isCatchhonMatching && remainOptionIsMatching;
};

module.exports = {
    isMatchingKaisenOption,
};
