const { createHash } = require("crypto");

const getSha256Hash = (string) => {
    return createHash("sha256").update(string).digest("hex");
};

/**
 * The function returns the SHA-512 hash of a given string in hexadecimal format.
 * @param string - The input string that needs to be hashed using the SHA-512 algorithm.
 * @returns The function `getSha521Hash` is returning the SHA-512 hash of the input string in
 * hexadecimal format.
 */
const getSha521Hash = (string) => {
    return createHash("sha512").update(string).digest("hex");
};

module.exports = {
    getSha256Hash,
    getSha521Hash,
};
