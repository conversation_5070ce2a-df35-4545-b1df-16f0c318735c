const dayjs = require('dayjs');
const Long = require('long');
const { isNone } = require('../helpers/baseHelper');
const {
    MAINTENANCE_EVENT_TITLE,
    MAINTENANCE_MODE,
    MAINTENANCE_EVENT_DELETION,
    MAINTENANCE_SERVICE_TYPE,
} = require('../constants/maintenanceEventConstant');
const MaintenanceEventServiceBus = require('../services/sbCalendarMaintenanceEvent.service');
const CalendarEventModel = require('../models/calendarEvents.model');

/**
 * @typedef UpdateCalendarEvent
 * @property {string} eventId
 * @property {string} status
 * @property {string} [title]
 * @property {string} [description]
 * @property {object} [start]
 * @property {object} [end]
 * @property {object} [originalStartTime]
 * @property {string} [location]
 * @property {string} [owner]
 * @property {string} [colorId]
 * @property {string} [color]
 * @property {string} [recurrence]
 * @property {string} [recurringEventId]
 * @property {boolean} [isMaintenanceEvent]
 */

/**
 * Insert or update service bus message if event is maintenance event
 * @param {object} context
 * @param {UpdateCalendarEvent} event
 * @param {boolean} isNew
 */
const pushMaintenanceEventToServiceBus = async (context, event, isNew) => {
    context.log('pushMaintenanceEventToServiceBus start');
    const result = {
        isOk: true,
        error: {
            errorMessage: '',
        },
    };
    if (!event.isMaintenanceEvent) return result;

    const service = getServiceTypeForMaintenanceEvent(event.title);

    if (isNew && event.status !== MAINTENANCE_EVENT_DELETION) {
        // newly created event and not cancelled
        context.log(`pushMaintenanceEventToServiceBus new event: [${event.eventId}] ${event.title} (${event.status})`);
        const [startSequence, endSequence] = await Promise.all([
            MaintenanceEventServiceBus.registerMaintenanceEvent(
                context,
                event.eventId,
                service,
                MAINTENANCE_MODE.ON,
                getTime(event.start)
            ),
            MaintenanceEventServiceBus.registerMaintenanceEvent(
                context,
                event.eventId,
                service,
                MAINTENANCE_MODE.OFF,
                getTime(event.end)
            ),
        ]);
        // update sequence numbers to DB
        await CalendarEventModel.updateSequenceNumber(context, event.eventId, startSequence[0], endSequence[0]);
    } else {
        // update or delete
        context.log(
            `pushMaintenanceEventToServiceBus update event: [${event.eventId}] ${event.title} (${event.status})`
        );

        const old = await CalendarEventModel.getSequenceNumbers(context, event.eventId);
        if (!isNone(old)) {
            // remove previously scheduled messages
            await Promise.all([
                MaintenanceEventServiceBus.cancelScheduledMessages(context, Long.fromValue(old.startSequence)),
                MaintenanceEventServiceBus.cancelScheduledMessages(context, Long.fromValue(old.endSequence)),
            ]);
        }

        if (event.status !== MAINTENANCE_EVENT_DELETION) {
            // if not cancelled, schedule new messages
            const [startSequence, endSequence] = await Promise.all([
                MaintenanceEventServiceBus.registerMaintenanceEvent(
                    context,
                    event.eventId,
                    service,
                    MAINTENANCE_MODE.ON,
                    getTime(event.start)
                ),
                MaintenanceEventServiceBus.registerMaintenanceEvent(
                    context,
                    event.eventId,
                    service,
                    MAINTENANCE_MODE.OFF,
                    getTime(event.end)
                ),
            ]);
            // update sequence numbers to DB
            await CalendarEventModel.updateSequenceNumber(context, event.eventId, startSequence[0], endSequence[0]);
        } else {
            // set isRemovedFromQueue=true
            await CalendarEventModel.markRemovedFromQueue(context, event.eventId);
        }
    }

    return result;
};

const getTime = (timeObj) => {
    if (isNone(timeObj)) throw new Error('Empty start/end object');
    const key = Object.keys(timeObj).at(0);
    const value = timeObj[key];
    if (isNone(value)) throw new Error(`start/end value is empty ${value}`);

    if (typeof value === 'number') {
        // assuming it is already unix seconds
        return value;
    } else {
        return dayjs(value).unix();
    }
};

/**
 * Check whether an event is a maintenance event based on event's title
 * @param {string} title
 */
const isMaintenanceEvent = (title) => {
    return !isNone(title) && Object.values(MAINTENANCE_EVENT_TITLE).some((pattern) => pattern.test(title));
};

/**
 * Get service type
 * @param {string} title
 */
const getServiceTypeForMaintenanceEvent = (title) => {
    let service = 'fumei';
    if (MAINTENANCE_EVENT_TITLE.ALADIN_SCHEDULE_SUBJECT_REGEX.test(title)) {
        service = MAINTENANCE_SERVICE_TYPE.ALADIN;
    } else if (MAINTENANCE_EVENT_TITLE.SWIMMY_SCHEDULE_SUBJECT_REGEX.test(title)) {
        service = MAINTENANCE_SERVICE_TYPE.SWIMMY;
    } else if (MAINTENANCE_EVENT_TITLE.SWIMMY_VLM_SCHEDULE_SUBJECT_REGEX.test(title)) {
        service = MAINTENANCE_SERVICE_TYPE.SWIMMY_VLM;
    }
    return service;
};

/**
 * Remove non-maintenance events (if any) which have been scheduled into ASB in past
 * @param {object} context
 * @param {Array<string>} eventIds
 */
const fixInvalidMaintenanceEvents = async (context, eventIds) => {
    if (!Array.isArray(eventIds) || eventIds.length === 0) return;

    const invalidEvents = await CalendarEventModel.findInvalidMaintenanceEvents(context, eventIds);
    if (!invalidEvents || invalidEvents.length === 0) return;

    context.log(
        'fixInvalidMaintenanceEvents:',
        invalidEvents.map((e) => e.eventId)
    );

    for (const event of invalidEvents) {
        await Promise.all([
            MaintenanceEventServiceBus.cancelScheduledMessages(context, Long.fromValue(event.startSequence)),
            MaintenanceEventServiceBus.cancelScheduledMessages(context, Long.fromValue(event.endSequence)),
            CalendarEventModel.markRemovedFromQueue(context, event.eventId),
        ]);
    }
};

module.exports = {
    pushMaintenanceEventToServiceBus,
    isMaintenanceEvent,
    getServiceTypeForMaintenanceEvent,
    fixInvalidMaintenanceEvents,
};
