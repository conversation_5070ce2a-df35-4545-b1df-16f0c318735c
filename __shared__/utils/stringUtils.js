const urlconv = require('iconv-urlencode');

const { getPortalConfig } = require('../config/appConfig');
const { SUPER_TEMPO_OPERATOR_MANAGEMENT_ID } = require('../constants/specialDefinitions');
const { MAWP_ROLE_ID } = require('../constants/userRole');
const { getTimeInJST } = require('./datetimeUtils');
const portalConfig = getPortalConfig();

const createPrefixTempoFind = (prefixTempoIds) => {
    return '^(' + prefixTempoIds.join('|') + ')';
};

const formatDateForAladin = (dateText, defaultValue = null) => {
    // dateText should have been validated by <PERSON><PERSON> so it must be a valid date
    if (typeof dateText !== 'string') return defaultValue;
    if (/^\d{8}$/.test(dateText)) return dateText; // YYYYMMDD
    if (/^\d{4}\/\d{2}\/\d{2}$/.test(dateText)) {
        // YYYY/MM/DD
        return `${dateText.substring(0, 4)}${dateText.substring(5, 7)}${dateText.substring(8)}`;
    }
    return defaultValue;
};

/**
 *
 * @param {any} value
 * @param {boolean} defaultValue
 */
const getParamBooleanValue = (value, defaultValue) => {
    if (typeof value == 'boolean') {
        return value;
    } else if (typeof value == 'string') {
        if (value.toLowerCase() == 'false') {
            return false;
        } else if (value.toLowerCase() == 'true') {
            return true;
        }
    }
    return defaultValue;
};

/**
 *
 * @param {any} value
 * @param {number} defaultValue
 */
const getParamIntValue = (value, defaultValue) => {
    if (typeof value == 'number') {
        return isNaN(value) ? defaultValue : value;
    } else {
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? defaultValue : parsed;
    }
};

/**
 *
 * @param {string} superTempoId
 */
const getSuperTempoOperatorManagementIdAndTenantId = (superTempoId) => {
    let managementId = '';
    let superTenantId = '';
    if (superTempoId.startsWith('S')) {
        const matches = superTempoId.matchAll(/S(\w{2})(\w{3})\d+/g);
        for (const match of matches) {
            managementId = match[1];
            superTenantId = match[2];
        }
    }

    return { managementId, superTenantId };
};

const getSuperTempoCode = (superTempoId) => {
    const { managementId, superTenantId } = getSuperTempoOperatorManagementIdAndTenantId(superTempoId);
    if (!managementId || !superTenantId) {
        return null;
    } else if (managementId === SUPER_TEMPO_OPERATOR_MANAGEMENT_ID) {
        return superTenantId;
    } else {
        return `${managementId}${superTenantId}`;
    }
};

/**
 * @param {string} tempoId
 * @returns {boolean} true if tempoId starts with Sxx
 */
const isSxxTempoID = (tempoId) => {
    return tempoId.startsWith('Sxx');
};

/**
 * maskingPhoneNumber
 * @param {string} phoneNumber
 */
const maskPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return phoneNumber;
    const maskingChar = portalConfig.MASKING_CHAR ?? '*';
    return phoneNumber.replace(/.(?=(?:\D*\d){4})/g, maskingChar);
};

/**
 *
 * @param {string} phoneNumber
 * @param {number} roleId
 * @param {string?} userTempoId
 * @param {boolean} userMaskingEnabled
 */
const maskingPhoneNumberIfNeeded = (phoneNumber, roleId, userTempoId, userMaskingEnabled = true) => {
    let result = phoneNumber;
    if (roleId == MAWP_ROLE_ID.TEMPO_USER && !isSxxTempoID(userTempoId ?? '')) {
        if (!isSxxTempoID(userTempoId ?? '')) {
            //「回線番号をマスク」が必要なユーザを判断する
            result = userMaskingEnabled ? maskPhoneNumber(phoneNumber) : phoneNumber;
        }
    }
    return result;
};

/**
 *
 * @param {string} syorikekkaKbnErrCode
 * @param {string} [MNPErrCode]
 */
const combineErrorCode = (syorikekkaKbnErrCode, MNPErrCode) => {
    return MNPErrCode ? `${syorikekkaKbnErrCode}_${MNPErrCode}` : syorikekkaKbnErrCode;
};

/**
 * @param {string} string
 * @returns {string}
 */
function escapeRegex(string) {
    return string.replace(/[/\-\\^$*+?.()|[\]{}]/g, '\\$&');
}

const isOverflowDateNumber = (value) => {
    switch (typeof value) {
        case 'string':
        case 'number':
            return value <= 0 || value > 2147483647;
        default:
            return false;
    }
};

/**
 * @param {string} month
 * @returns {number|null}
 */
const monthCompareType = (month) => {
    if (!month) {
        return month;
    } else {
        if (month.length !== 6) return null;
        const date = month.slice(0, 4) + '/' + month.slice(4, 6) + '/' + '01';
        const currentTime = getTimeInJST();
        const currentDate = currentTime.format('YYYY/MM/DD');
        if (date < currentDate) {
            const dMonth = new Date(date).getMonth();
            const currentMonth = currentTime.month();
            if (dMonth < currentMonth) {
                return -1;
            } else if (dMonth === currentMonth) {
                return 0;
            } else {
                return 1;
            }
        } else if (date === currentDate) {
            return 0;
        } else {
            return 1;
        }
    }
};

/**
 * Generate a random alphanumeric string
 * @param {number} length
 * @returns {string}
 */
const randomAlphanumericString = (length) => {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    let result = '';
    for (let i = length; i > 0; --i) result += chars[Math.floor(Math.random() * chars.length)];
    return result;
};

/**
 *
 * @param {string} text
 * @returns {string}
 */
const encodeShiftJIS = (text) => {
    return urlconv.encode(text, 'Shift_JIS');
};

module.exports = {
    createPrefixTempoFind,
    formatDateForAladin,
    getParamBooleanValue,
    getParamIntValue,
    getSuperTempoOperatorManagementIdAndTenantId,
    getSuperTempoCode,
    isSxxTempoID,
    maskPhoneNumber,
    maskingPhoneNumberIfNeeded,
    combineErrorCode,
    escapeRegex,
    isOverflowDateNumber,
    monthCompareType,
    randomAlphanumericString,
    encodeShiftJIS,
};
