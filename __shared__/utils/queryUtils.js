/**
 * Create a constant subquery with using multi values (PostgreSQL extension)
 * ```sql
 * ( VALUES ($1), ($2), ($3), ... )
 * ```
 *
 * or list of values for small set of items (SQL standard)
 *
 * ```sql
 * ( $1, $2, $3, ... )
 * ```
 * @param {Array<any>} items Non-zero length array
 * @param {number=} startIndex starting index for parameterized query placeholder (default=1)
 * @param {number=} sizeForSubQuery
 * @returns {string}
 */
const buildListOfValues = (items, startIndex = 1, sizeForSubQuery = 80) => {
    if (!Array.isArray(items) || items.length === 0) throw new Error('items must not be empty');
    if (items.length > sizeForSubQuery) {
        return `( VALUES ${items.map((_, i) => `($${i + startIndex})`).join(', ')} )`;
    } else {
        return `( ${items.map((_, i) => `$${i + startIndex}`).join(', ')} )`;
    }
};

module.exports = {
    buildListOfValues,
};
