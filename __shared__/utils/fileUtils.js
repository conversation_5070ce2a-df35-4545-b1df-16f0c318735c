const _ = require('lodash');
const path = require('path');
const storage = require('@azure/storage-blob');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const { isNone } = require('../helpers/baseHelper');
dayjs.extend(utc);

/**
 * Get file URL with SAS token from Storage Blob
 * @param {object} context
 * @param {string} storageConnectionString
 * @param {string} containerName
 * @param {string} filename blob filename
 * @param {number} [expireAfter=300] expiration date in seconds (default to 300 seconds)
 * @param {boolean} [encodeFilename=false] encode filename in content disposition as URL encoded
 * @param {string?} [customFilename] use custom filename instead of blob filename
 * @returns {Promise<{exists: boolean, url: string}>}
 */
const getStorageBlobFileUrl = async (
    context,
    storageConnectionString,
    containerName,
    filename,
    expireAfter = 300,
    encodeFilename = false,
    customFilename = null
) => {
    const blobName = filename;
    const blobServiceClient = storage.BlobServiceClient.fromConnectionString(storageConnectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(blobName);

    if (!(await blobClient.exists())) {
        context.log('File does not exists!', blobName);
        return { exists: false, url: '' };
    }

    const startsOn = dayjs.utc();
    const expiresOn = startsOn.add(expireAfter, 'seconds');

    let attachmentName = !isNone(customFilename) ? customFilename : filename;
    if (encodeFilename) {
        attachmentName = encodeURIComponent(attachmentName);
    }

    const blobSAS = storage
        .generateBlobSASQueryParameters(
            {
                containerName,
                blobName,
                permissions: storage.AccountSASPermissions.parse('r'), // read
                startsOn: startsOn.toDate(),
                expiresOn: expiresOn.toDate(),
                services: 'b', // blob
                resourceTypes: 'o', // object
                contentDisposition: `attachment; filename=${attachmentName}`,
            },
            blobServiceClient.credential
        )
        .toString();

    return { url: `${blobClient.url}?${blobSAS}`, exists: true };
};

/**
 * Get file as Buffer from Storage Blob, null if it does not exist
 * @param {object} context
 * @param {string} storageConnectionString
 * @param {string} containerName
 * @param {string} filename blob filename
 * @returns {Promise<Buffer|null>}
 */
const getStorageBlobBuffer = async (context, storageConnectionString, containerName, filename) => {
    const blobName = filename;
    const blobServiceClient = storage.BlobServiceClient.fromConnectionString(storageConnectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(blobName);

    if (!(await blobClient.exists())) {
        context.log('File does not exists!', blobName);
        return null;
    }
    const downloadBlockBlobResponse = await blobClient.downloadToBuffer();
    return downloadBlockBlobResponse;
};

/**
 * Get file as Buffer from Storage Blob and its properties, null if it does not exist
 * @param {object} context
 * @param {string} storageConnectionString
 * @param {string} containerName
 * @param {string} filename blob filename
 * @returns {Promise<{buffer: Buffer, properties: storage.BlobGetPropertiesResponse}|null>}
 */
const getStorageBlobBufferWithProperties = async (context, storageConnectionString, containerName, filename) => {
    const blobName = filename;
    const blobServiceClient = storage.BlobServiceClient.fromConnectionString(storageConnectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(blobName);

    if (!(await blobClient.exists())) {
        context.log('File does not exists!', blobName);
        return null;
    }

    const properties = await blobClient.getProperties();

    const downloadBlockBlobResponse = await blobClient.downloadToBuffer();
    return { buffer: downloadBlockBlobResponse, properties };
};

/**
 * @typedef GetFilesResult
 * @property {Array<string>} filelist list of files with absolute path
 * @property {{[name: string]: GetFilesResult_File}} files map of file path and its properties
 */

/**
 * @typedef GetFilesResult_File
 * @property {string} filename basename
 * @property {string} fullname absolute path
 * @property {string} content_type content type
 * @property {number} created file created in unix timestamp
 * @property {number} modified last modified in unix timestamp
 * @property {number} size file size in bytes
 */

/**
 * @param {object} context
 * @param {string} storageConnectionString
 * @param {string} containerName
 * @param {string} prefix empty string for root or folder name **with** trailing slash
 *                        (e.g. `''`, `manager/`, `manager/API仕様書/`)
 * @returns {Promise<GetFilesResult>}
 */
const getFiles = async (context, storageConnectionString, containerName, prefix) => {
    const blobServiceClient = storage.BlobServiceClient.fromConnectionString(storageConnectionString);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // use hierarchical method so that result are sorted based on kind (file/folder) then item name
    const res = await _listBlobHierarchical(context, containerClient, prefix);
    return {
        files: res.result,
        filelist: res.filelist,
    };
};

/**
 * get hierarchical file list but store in a flat object
 * @param {object} context
 * @param {storage.ContainerClient} containerClient
 * @param {string} prefixStr
 * @returns {Promise<{result: {}, filelist: Array<string>}>}
 */
const _listBlobHierarchical = async (context, containerClient, prefixStr) => {
    let result = {};
    let filelist = [];
    const listOptions = {
        includeMetadata: false,
        includeSnapshots: false,
        includeTags: false,
        includeVersions: false,
        prefix: prefixStr,
    };
    const delimiter = '/';
    for await (const response of containerClient.listBlobsByHierarchy(delimiter, listOptions).byPage()) {
        const segment = response.segment;

        if (segment.blobPrefixes) {
            for await (const prefix of segment.blobPrefixes) {
                // const lastPrefix = path.basename(prefix.name);
                const sub = await _listBlobHierarchical(context, containerClient, prefix.name);
                // result[lastPrefix] = sub.result;
                result = _.assign({}, result, sub.result);
                filelist = filelist.concat(sub.filelist);
            }
        }

        for (const blob of response.segment.blobItems) {
            filelist.push(blob.name);
            const filename = path.basename(blob.name);

            result[blob.name] = {
                filename,
                fullname: blob.name,
                content_type: blob.properties.contentType,
                created: dayjs(blob.properties.createdOn).unix(),
                modified: dayjs(blob.properties.lastModified).unix(),
                size: blob.properties.contentLength, // bytes
            };
        }
    }
    return { result, filelist };
};

module.exports = {
    getStorageBlobFileUrl,
    getStorageBlobBuffer,
    getStorageBlobBufferWithProperties,
    getFiles,
};
