const { isNone } = require('../helpers/baseHelper');

/**
 * Calculate modulo for large number
 *
 * ref: https://stackoverflow.com/a/16019504
 * @param {string} divident
 * @param {number} divisor
 * @returns {number}
 */
const modulo = (divident, divisor) => {
    const partLength = 10;

    while (divident.length > partLength) {
        const part = divident.substring(0, partLength);
        divident = (part % divisor) + divident.substring(partLength);
    }

    return divident % divisor;
};

/**
 * @param {number?} x
 */
const formatIntNumberThousandSeparator = (x) => {
    if (isNone(x)) return '';
    return x.toLocaleString('ja-JP');
};

module.exports = {
    modulo,
    formatIntNumberThousandSeparator,
};
