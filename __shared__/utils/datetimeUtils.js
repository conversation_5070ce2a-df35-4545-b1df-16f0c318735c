const _ = require('lodash');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
dayjs.extend(utc);
dayjs.extend(timezone);
const { SO_KIND_TYPE } = require('../constants/orderConstants');

/**
 * An object containing startTime and endTime
 * @typedef {object} ConfigTime
 * @property {string} startTime
 * @property {string} endTime
 */

//TODO: check this func and refactor it to use portalConfig
/**
 * Convert config string to configRestrictedTime object
 * @param {string} configRestrictedTimeStr
 * @returns {ConfigTime} converted object
 */
const parseConfigPeriodOfTime = (configRestrictedTimeStr) => {
    const regex = new RegExp(/^(\d+):(\d+)-(\d+):(\d+)$/);
    const period = process.env[configRestrictedTimeStr];
    if (!regex.test(period)) return {};

    const timeArr = period.split('-');
    return {
        startTime: timeArr[0],
        endTime: timeArr[1],
    };
};

/**
 * Compare currentTime with configRestrictedTime to check whether currentTime is restrictedTime or not
 * @param {Date} currentTime
 * @param {ConfigTime} configRestrictedTime
 * @returns {boolean} true - indicate currentTime is restrictedTime
 *                    false - indicate currentTime is allowed to place order
 */
const isRestrictedTime = (currentTime, configRestrictedTime) => {
    const currentTimeStr = currentTime.format('YYYY-MM-DD');
    if (_.isEmpty(configRestrictedTime)) return false;

    const startTime = dayjs(currentTimeStr + ' ' + configRestrictedTime.startTime, 'YYYY-MM-DD HH:mm');
    const endTime = dayjs(currentTimeStr + ' ' + configRestrictedTime.endTime, 'YYYY-MM-DD HH:mm');

    if (endTime < startTime) {
        // 開始時刻より終了時刻の方が前の時刻が設定されている場合は１日後と見る
        startTime.isBefore(currentTime) && currentTime.isBefore(endTime.add(1, 'day'));
    } else {
        startTime.isBefore(currentTime) && currentTime.isBefore(endTime);
    }
};

/**
 * Base on SoType, get configRestrictedTime's info and check restrictedTime
 * @param {string} soKindType - オーダー種別
 * @returns {object} { string:currentTime, string:configRestrictedTime, boolean:restrictedFlag }
 */
const getRestrictedTimeInfo = (soKindType) => {
    const currentTime = dayjs().tz('Asia/Tokyo');
    let configRestrictedTime, restrictedFlag;

    //STEP6.0でオーダー種別によって受付不可時間が変わる
    switch (soKindType) {
        case SO_KIND_TYPE.SHINKI_KUROKA:
        case SO_KIND_TYPE.MNP_SIM_SAIHAKKOU:
        case SO_KIND_TYPE.MNP_KAISEN_OPTION_HENKOU:
        case SO_KIND_TYPE.MNP_NWPASSWORD_HENKOU:
        case SO_KIND_TYPE.MNP_NEW_SERVICE_ORDER:
        case SO_KIND_TYPE.GAIBU_API_SHINKI_KUROKA:
        case SO_KIND_TYPE.KAISEN_HAISHI_SERVICE_ORDER:
        case SO_KIND_TYPE.STOP_SERVICE_ORDER:
        case SO_KIND_TYPE.RESUME_SERVICE_ORDER:
        case SO_KIND_TYPE.NEW_OTA_SERVICE_ORDER: {
            //仮登録回線追加受付不可時間
            configRestrictedTime = process.env['KariTourokuKaisenTsuikaUketsukeFukaJikan'];
            const configRestrictedTimeObj = parseConfigPeriodOfTime(configRestrictedTime);
            restrictedFlag = isRestrictedTime(currentTime, configRestrictedTimeObj);
            break;
        }
        case SO_KIND_TYPE.MNP_TENNYU:
        case SO_KIND_TYPE.KUROKA:
        case SO_KIND_TYPE.MNP_TENSHUTSU:
        case SO_KIND_TYPE.MNP_TENSHUTSU_CANCEL: {
            //#MNP転入、転出予約/キャンセルの受付不可時間
            configRestrictedTime = process.env['MNPUketsukeFukaJikan'];
            const configRestrictedTimeObj = parseConfigPeriodOfTime(configRestrictedTime);
            restrictedFlag = isRestrictedTime(currentTime, configRestrictedTimeObj);
            break;
        }
        default:
            configRestrictedTime = '';
            restrictedFlag = false;
    }

    return {
        currentTime: currentTime.format('HH:mm:ss'),
        configRestrictedTime,
        restrictedFlag,
    };
};

/**
 * Convert or get current time in JST
 * @param {dayjs.Dayjs|number} [time] use unix time (milliseconds) if given in number
 */
const getTimeInJST = (time, keepLocalTime = false) => {
    if (time) {
        return dayjs(time).tz('Asia/Tokyo', keepLocalTime);
    } else {
        return dayjs().tz('Asia/Tokyo');
    }
};

/**
 * Return `startTime` - `beforeDay` day(s)
 * @param {dayjs.Dayjs} startTime base datetime for calculation
 * @param {number} beforeDay number of days to subtract
 * @param {number} [hour] override hour (e.g. hour=13 then 11:23:58 -> 13:23:58)
 * @param {number} [minute] override minute (e.g. minute=13 then 11:23:58 -> 11:13:58)
 * @param {number} [second] override second (e.g. second=13 then 11:23:58 -> 11:23:13)
 * @returns {dayjs.Dayjs} subtracted dayjs object (preserving tz)
 */
const getTimeBeforeXDay = (startTime, beforeDay, hour, minute, second) => {
    let startObj = startTime.subtract(beforeDay, 'day');
    if (hour && hour > 0) {
        startObj = startObj.hour(hour);
    }
    if (minute && minute > 0) {
        startObj = startObj.minute(minute);
    }
    if (second && second > 0) {
        startObj = startObj.second(second);
    }
    return startObj;
};

/**
 * startTime の afterDay 日後の前日の23:59:59 JSTを返す
 * @param {dayjs.Dayjs} startTime base date obj for calculation (JST)
 * @param {number} afterDay number of days to add
 * @returns {dayjs.Dayjs}
 */
const getTimeAfterXDay = (startTime, afterDay) => {
    let startObj = startTime.add(afterDay, 'day');
    startObj = startObj.hour(0).minute(0).second(0);
    startObj = startObj.subtract(1, 'second');
    return startObj;
};

/**
 * @param {number} timestamp
 * @returns {string}
 */
const timestampToLongDateString = (timestamp) => {
    if (!timestamp > 0) {
        return '';
    } else {
        const timeobj = getTimeInJST(timestamp * 1000, false);
        return timeobj.format('YYYY/MM/DD HH:mm:ss');
    }
};

/**
 * If the day of the week is 0 or 6, return true, otherwise return false.
 * @param dayOfWeek - 0-6 (0 = Sunday, 1 = Monday, 2 = Tuesday, ...)
 * @returns A boolean value.
 */
const isWeekend = (dayOfWeek) => {
    return dayOfWeek === 0 || dayOfWeek === 6;
};

/**
 * If the day is a holiday, return true, otherwise return false.
 * @param {dayjs.Dayjs} targetTime - The time you want to check.
 */
const isHoliday = (targetTime) => {
    const targetMonthDate = targetTime.format('MM/DD');
    switch (targetMonthDate) {
        // 年末年始の休み
        case '01/02':
        case '01/03':
            return true;
        // 年末年始の休み
        case '12/29':
        case '12/30':
        case '12/31':
            return true;
        default:
            // eslint-disable-next-line no-case-declarations
            const JapaneseHolidays = require('japanese-holidays');
            return JapaneseHolidays.isHoliday(targetTime.toDate());
    }
};

/**
 * decrease startTime to business day (old function: increaseToBusinessDay)
 * @param {number} startTime - unix time (seconds)
 * @returns {number} businessDay - (unix time seconds)
 */
const decreaseToBusinessDay = (startTime) => {
    const startTimeJST = getTimeInJST(startTime * 1000);
    const targetTime = getTimeBeforeXDay(startTimeJST, 1);

    // If the day of the week is 0 or 6, return the previous business day.
    if (isWeekend(targetTime.day())) {
        return decreaseToBusinessDay(targetTime.unix());
    }

    // If the day is a holiday, return the previous business day.
    if (isHoliday(targetTime)) {
        return decreaseToBusinessDay(targetTime.unix());
    }

    // If the day is a business day, return the day.
    return targetTime.unix();
};

module.exports = {
    getRestrictedTimeInfo,
    getTimeInJST,
    getTimeBeforeXDay,
    getTimeAfterXDay,
    timestampToLongDateString,
    decreaseToBusinessDay,
};
