const urlconv = require('iconv-urlencode');
const crypto = require('crypto');
const appConfig = require('../config/appConfig');
const portalConfig = appConfig.getPortalConfig();
// AES/CBC/PKCS5Padding
const CIPHER_KEY = portalConfig.CIPHER_KEY;

/**
 * @param {Buffer} key
 * @returns {string}
 */
const getAlg = (key) => {
    switch (key.length) {
        case 16:
            return 'aes-128-cbc';
        case 32:
            return 'aes-256-cbc';
    }
    throw new Error(`Invalid key length: ${key.length}`);
};

/**
 * @returns {Buffer}
 */
const getKey = () => {
    // cipher key should be parsed as it is (not base64)
    return Buffer.from(CIPHER_KEY);
};

/**
 * @param {Buffer} ptextBuf
 * @returns {Buffer}
 */
const encrypt = (ptextBuf) => { // eslint-disable-line no-unused-vars
    const key = getKey();
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(getAlg(key), key, iv);
    cipher.setAutoPadding(true);
    const encryptedBuf = Buffer.concat([cipher.update(ptextBuf), cipher.final()]);
    return Buffer.concat([iv, encryptedBuf]).toString('base64');
};

/**
 * @param {Buffer} ctextBuf
 * @returns {Buffer}
 */
const decrypt = (ctextBuf) => {
    const key = getKey();
    const iv = ctextBuf.subarray(0, 16);
    const ctext = ctextBuf.subarray(16);
    const decipher = crypto.createDecipheriv(getAlg(key), key, iv);
    decipher.setAutoPadding(true);
    return Buffer.concat([decipher.update(ctext), decipher.final()]);
};

/**
 * Encode `plaintext` to shift-jis and encrypt
 * @param {string} plaintext
 * @returns {string}
 */
const encryptText = (plaintext) => {
    // const textBuf = Buffer.from(urlconv.encode(plaintext, 'Shift_JIS'));
    // return encrypt(textBuf).toString('base64');
    // STEP 25 remove encryption
    return plaintext;
};

/**
 * Decrypt `ciphertext` and decode from shift-jis
 * @param {string} ciphertext
 * @returns {string}
 */
const decryptText = (ciphertext) => {
    if (!ciphertext) return null;
    try {
        const textBuf = Buffer.from(ciphertext, 'base64');
        const decryptedBuf = decrypt(textBuf);
        return urlconv.decode(decryptedBuf.toString(), 'Shift_JIS');
    } catch (exception) {
        return ciphertext;
    }
};

/**
 * Decrypt `ciphertext` (keep Shift_JIS encoding)
 * or convert plaintext to Shift_JIS encoded string
 * @param {string} ciphertext
 * @returns {string}
 */
const decryptTextToEncodeShiftJIS = (ciphertext) => {
    if (!ciphertext) return null;
    try {
        const textBuf = Buffer.from(ciphertext, 'base64');
        const decryptedBuf = decrypt(textBuf);
        return decryptedBuf.toString();
    } catch (exception) {
        if (typeof ciphertext === 'string') {
            return urlconv.encode(ciphertext, 'Shift_JIS');
        }
        return ciphertext;
    }
};

module.exports = {
    decryptText,
    encryptText,
    decryptTextToEncodeShiftJIS,
};
