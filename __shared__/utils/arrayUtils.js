/**
 * While the array has elements, push a new array of the first n elements of the array into the
 * splitList array.
 * @param arr - The array to be split
 * @param splitBy - The number of items to split the array by.
 * @param [splitList] - This is the array that will be returned.
 * @returns const splitter = (arr, splitBy, splitList = []) => {
 *     const tmp = [...arr];
 *     while (tmp.length) splitList.push(tmp.splice(0, splitBy));
 *     return splitList;
 * }
 */
const splitter = (arr, splitBy, splitList = []) => {
    const tmp = [...arr];
    while (tmp.length) splitList.push(tmp.splice(0, splitBy));
    return splitList;
};

/**
 * Group array items based on condition into two arrays
 * @param {Array<*>} arr
 * @param {(x: any) => boolean} fn
 * @returns {[Array<*>, Array<*>]} first array = items which evaluate to true using `fn` condition, second = false
 */
const bifurcate = (arr, fn) => {
    return arr.reduce(
        (acc, cur) => {
            if (fn(cur)) {
                acc[0].push(cur);
            } else {
                acc[1].push(cur);
            }
            return acc;
        },
        [[], []]
    );
};

module.exports = {
    splitter,
    bifurcate,
};
