# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Dispatch update submodule to all related repos

on:
    push:
        branches: ['develop']

jobs:
    dispatch-to-repos:
        strategy:
            matrix:
                repo:
                    [
                        'playnext-lab/MVNO_0035_SERVICE',
                        'playnext-lab/MVNO_ADMIN_SERVICE',
                        'playnext-lab/MVNO_ALADIN_SERVICE',
                        'playnext-lab/MVNO_CORE_SERVICE',
                        'playnext-lab/MVNO_CRON_SERVICE',
                        'playnext-lab/MVNO_EXTERNAL_API',
                        'playnext-lab/MVNO_DASHBOARD_SERVICE',
                        'playnext-lab/MVNO_ORDER_SERVICE',
                        'playnext-lab/MVNO_SIM_INVENTORY_SERVICE',
                        'playnext-lab/MVNO_SWIMMY_SERVICE',
                        'playnext-lab/MVNO_USER_SERVICE',
                        'playnext-lab/MVNO_STORAGE_SERVICE',
                        'playnext-lab/MVNO_MICROSERVICES',
                    ]
        runs-on: ubuntu-latest
        steps:
            - name: Repository Dispatch
              uses: peter-evans/repository-dispatch@v2
              with:
                  token: ${{ secrets.PAT }}
                  repository: ${{ matrix.repo }}
                  event-type: update-submodule-version
                  client-payload: '{"ref": "${{ github.ref }}", "sha": "${{ github.sha }}", "message": "${{ github.repository }}"}'
