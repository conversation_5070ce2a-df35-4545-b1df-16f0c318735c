# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Automation update config submodule version

on:
    repository_dispatch:
        types: [update-submodule-version]

jobs:
    pull-and-push-submodule:
        runs-on: ubuntu-latest
        steps:
            - name: 'Checkout GitHub Action'
              uses: actions/checkout@v2
              with:
                  token: ${{ secrets.PAT }}
                  submodules: recursive
            - name: Submodule update
              run: git submodule update --remote --recursive
            - name: Run git status
              id: status
              run: echo "::set-output name=status::$(git status -s)"
            - name: Add and commit files
              env: 
                TZ: "Asia/Tokyo" # タイムゾーンを指定
              run: |
                git add .
                git config --local user.email "<EMAIL>"
                git config --local user.name "GitHub Action"
                git commit -m "Update submodules at $(date "+DATE: %Y-%m-%d TIME: %H:%M:%S")"
              if: ${{ steps.status.outputs.status }}
            - name: Push changes
              uses: ad-m/github-push-action@master
              with:
                github_token: ${{ secrets.PAT }}
                branch: develop