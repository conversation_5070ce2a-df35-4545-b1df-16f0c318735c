# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Automation run lint check

on:
    push:
        branches: [develop]
    pull_request:
        branches: [develop]

jobs:
    build:
        runs-on: ubuntu-latest

        strategy:
            matrix:
                node-version: [20.x]
                # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

        steps:
            - uses: actions/checkout@v3
            - name: Use Node.js ${{ matrix.node-version }}
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ matrix.node-version }}
                  cache: 'npm'
            - run: npm install
            - run: npm run lint:check
            - name: Test has failure
              id: slack
              if: ${{ failure() }}
              uses: slackapi/slack-github-action@v1.19.0
              with:
                  channel-id: 'mvno-auto-tests'
                  payload: |
                      {
                        "attachments": [
                            {
                                "color": "#ff0000",
                                "author_name": "[Common Shared] Lint Check",
                                "title": "Check Failed !",
                                "footer": "Link: ${{ github.event.pull_request.html_url || github.event.head_commit.url }}"
                            }
                        ]
                      }
              env:
                  SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
