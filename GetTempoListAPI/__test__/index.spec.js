const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
let expect = chai.expect;

describe('GetTempoList', function () {
    const __request = {
        headers: getDefaultHeader(), // <- super user
        body: {
            tenant_id: 'TSA000',
        },
    };

    describe('GetTempoList with normal request', function () {
        it('Get tenants using super user', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            // return more than 1 tenants
            expect(context.res.body.result.length).above(1);
        });

        it('Get tenants using tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000', null, 'TAATSA0001');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            // return current tenant id
            expect(context.res.body.result.length).equal(1);
            expect(context.res.body.result[0].tempo_id).equal('TAATSA0001');
        });

        it('Get tenants using S-tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000', null, 'SAATSA0001');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(1);
            /** @type Array<string> */
            const tempoIds = context.res.body.result.map((r) => r.tempo_id);
            // check all tempo starts with TAA or SAA
            expect(tempoIds.every((r) => r.startsWith('TAA') || r.startsWith('SAA'))).equal(true);
        });

        it('Get tenants using Sxx-tempo user (super tempo)', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000', null, 'SxxTSA0001');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(1);

            /** @type Array<string> */
            const tempoIds = context.res.body.result.map((r) => r.tempo_id);
            // check all tempo starts with T or S
            expect(tempoIds.every((r) => r.startsWith('T') || r.startsWith('S'))).equal(true);
            // check some tempo is AA系
            expect(tempoIds.some((r) => r.startsWith('TAA') || r.startsWith('SAA'))).equal(true);
            // check some tempo is BB系
            expect(tempoIds.some((r) => r.startsWith('TBB') || r.startsWith('SBB'))).equal(true);
            // check Sxx tempo
            expect(tempoIds.some((r) => r.startsWith('Sxx'))).equal(true);
        });

        it('Get tenants using tenant operator', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TSA000', null, null);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(1);

            /** @type Array<string> */
            const tempoIds = context.res.body.result.map((r) => r.tempo_id);
            // check all tempo starts with T or S
            expect(tempoIds.every((r) => r.startsWith('T') || r.startsWith('S'))).equal(true);
            // check some tempo is AA系
            expect(tempoIds.some((r) => r.startsWith('TAA') || r.startsWith('SAA'))).equal(true);
            // check some tempo is BB系
            expect(tempoIds.some((r) => r.startsWith('TBB') || r.startsWith('SBB'))).equal(true);
            // check Sxx tempo
            expect(tempoIds.some((r) => r.startsWith('Sxx'))).equal(true);
        });
    });
});
