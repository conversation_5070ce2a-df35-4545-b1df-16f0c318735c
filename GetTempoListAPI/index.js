const { CommonError } = require('../__shared__/constants/CommonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const { retrieveMAWPTempoList } = require('../__shared__/pgModels/shops');
const { getSuperTempoCode } = require('../__shared__/utils/stringUtils');
const mongoose = require('../__shared__/database/mongoose');

module.exports = async function (context, req) {
    try {
        context.log('GetTempoListAPI START');
        const { roleId, tenantId, tempoId } = getSessionData(context, req);
        const paramTenantId = req.body ? req.body.tenant_id : null;

        let tenant = null;

        switch (roleId) {
            case MAWP_ROLE_ID.TEMPO_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                tenant = tenantId;
                break;
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                tenant = paramTenantId;
                break;
        }

        if (isNone(tenant)) {
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', '990002', null);
            return;
        }

        context.log('GetTempoListAPI using tenant:', tenant);
        await mongoose.init(context);
        let tempoSeq = await retrieveMAWPTempoList(context, tenant); // case roleId != TEMPO_USER
        // 店舗オペレータの場合は、自分の店舗の情報のみ返却する
        if (roleId === MAWP_ROLE_ID.TEMPO_USER) {
            if (tempoId.startsWith('S')) {
                const superTempoCode = getSuperTempoCode(tempoId);
                if (!superTempoCode) {
                    tempoSeq = [];
                } else {
                    tempoSeq = tempoSeq.filter((r) => r.shop_id.includes(superTempoCode));
                }
            } else {
                tempoSeq = tempoSeq.filter((r) => r.shop_id === tempoId);
            }
        }

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                result: tempoSeq.map((row) => ({ tempo_id: row.shop_id, tempo_name: row.shop_name })),
            },
        };
    } catch (exception) {
        context.log.error('GetTempoListAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
