const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseGetKaisenDetail } = require('./helpers/validateParse');
const pgLineOpts = require('../__shared__/pgModels/lineOptions');
const pgLines = require('../__shared__/pgModels/lines');
const pgPlans = require('../__shared__/pgModels/plans');
const pgVoiceInfo = require('../__shared__/pgModels/voiceInfo');
const CoreService = require('../__shared__/services/core.service');
const { ERROR_MAP } = require('./helpers/errorHelper');
const { SUPER_TENANT_ID } = require('../__shared__/constants/specialDefinitions');
const { CONTRACT_TYPES } = require('../__shared__/constants/simConstants');
const { findOPFErrorMsgByOPFErrorCode } = require('../__shared__/helpers/configHelper');
const { OPTION_PLAN } = require('../__shared__/constants/lineConstants');

module.exports = async function (context, req) {
    try {
        context.log('GetKaisenDetailAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, errorMessage, params } = validateAndParseGetKaisenDetail(req.body ?? {});
        if (error) {
            context.log('GetKaisenDetailAPI validation error', error, errorMessage);
            responseWithError(context, CommonError.BAD_REQUEST, errorMessage, error, null);
            return;
        }

        await mongoose.init(context);

        const kaisenInfo = await pgLines.getKaisenInfoDetail(context, params.lineNo);
        if (!kaisenInfo) {
            context.log('GetKaisenDetailAPI - lineDetail is not found!', params.lineNo);
            responseWithError(
                context,
                CommonError.BAD_REQUEST,
                'lineDetail is not found!',
                ERROR_MAP.LINE_DETAIL_NOT_FOUND,
                null
            );
            return;
        }

        if (tenantId !== SUPER_TENANT_ID && kaisenInfo.tenantId !== tenantId) {
            context.log('GetKaisenDetailAPI - Invalid tenant');
            responseWithError(context, CommonError.BAD_REQUEST, 'Invalid Tenant', ERROR_MAP.TENANT_INVALID, null);
            return;
        }

        // 5G(NSA) or LTE
        const currentNetwork = [CONTRACT_TYPES.FIVE_G_NSA, CONTRACT_TYPES.FIVE_G_NSA_VOICE].includes(
            kaisenInfo.contractType
        )
            ? CONTRACT_TYPES.FIVE_G_NSA
            : CONTRACT_TYPES.LTE;

        const [planList, hasUpdatePer, additionalCoupon, couponOnOff, kaisenUnyoAPI, newVoicePlans] = await Promise.all(
            [
                pgPlans.getHenkoPlanList(context, kaisenInfo.tenantId, kaisenInfo.planId, currentNetwork),
                pgLines.hasUpdateKaisen(context, roleId),
                pgLineOpts.getOptionPlans(context, kaisenInfo.planId, OPTION_PLAN.COUPON_TSUIKA),
                pgLineOpts.getOptionPlans(context, kaisenInfo.planId, OPTION_PLAN.COUPON_ONOFF),
                CoreService.callAPICoreLineUnyo(context, { tenantId: kaisenInfo.tenantId, lineNo: params.lineNo }),
                getNewVoicePlans(context, kaisenInfo.tenantId, kaisenInfo.voicePlanId),
            ]
        );

        if (kaisenUnyoAPI.responseHeader.processCode !== '000000') {
            context.log('ProcessCode Call API UNYO ', kaisenUnyoAPI.responseHeader.processCode);
            responseWithError(
                context,
                CommonError.SERVER_ERROR,
                null,
                kaisenUnyoAPI.responseHeader.processCode,
                findOPFErrorMsgByOPFErrorCode(ERROR_MAP.API_CALL_FAILED)
            );
            return;
        }
        const couponOnOffList = couponOnOff.length > 1 ? [] : couponOnOff;
        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                henkoPlans: planList,
                isUpdatePermit: hasUpdatePer,
                aditionalCouponList: additionalCoupon,
                couponOnOffList,
                voicePlans: newVoicePlans,
                ...kaisenInfo,
                ...kaisenUnyoAPI,
            },
        };
    } catch (exception) {
        context.log.error('GetKaisenDetailAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, ERROR_MAP.GET_KAISEN_DETAIL_FAILED, null);
        return;
    }
};

/**
 * @param {number} roleId
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
            return true;
        default:
            return false;
    }
};

/**
 * @param {object} context
 * @param {string} tenantId
 * @param {string|null} currentVoicePlanId
 */
const getNewVoicePlans = async (context, tenantId, currentVoicePlanId) => {
    if (isNone(tenantId) || isNone(currentVoicePlanId)) return [];
    const voicePlans = await pgVoiceInfo.getTenantVoicePlans(context, tenantId);
    return voicePlans
        .filter((r) => r.voicePlanId !== currentVoicePlanId)
        .map((o) => ({
            voicePlanId: o.voicePlanId,
            voicePlanName: o.voicePlanName,
        }));
};
