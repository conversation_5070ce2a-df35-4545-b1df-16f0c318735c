const Joi = require('joi');
const { ERROR_MAP } = require('./errorHelper');

const getKaisenDetailSchema = Joi.object({
    line_no: Joi.string().required().messages({
        'any.required': ERROR_MAP.LINE_NO_EMPTY,
        'string.empty': ERROR_MAP.LINE_NO_EMPTY,
    }),
}).unknown();

const validateAndParseGetKaisenDetail = (body) => {
    const result = { error: '', errorMessage: '', params: { lineNo: '' } };
    const { error } = getKaisenDetailSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
        if (result.error == ERROR_MAP.LINE_NO_EMPTY) {
            result.errorMessage = 'lineNo param is not valid!';
        } else {
            result.errorMessage = result.error;
            result.error = '991022';
        }
    } else {
        result.params.lineNo = body.line_no;
    }
    return result;
};

module.exports = {
    validateAndParseGetKaisenDetail,
};
