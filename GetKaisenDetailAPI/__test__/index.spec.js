const _ = require('lodash');
let chai = require('chai');
const sinon = require('sinon');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const pgLines = require('../../__shared__/pgModels/lines');
const CoreService = require('../../__shared__/services/core.service');
let expect = chai.expect;

describe('GetKaisenDetailAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            line_no: '08020210840', // TSA000
        },
    };

    describe('GetKaisenDetailAPI with normal request', function () {
        it('returns kaisen detail for valid request (super user)', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.responseHeader).not.undefined;
            expect(context.res.body.responseHeader.processCode).equal('000000');
        });

        it('returns kaisen detail for valid request (tenant user)', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'TSA000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.responseHeader).not.undefined;
            expect(context.res.body.responseHeader.processCode).equal('000000');
        });
    });

    describe('GetKaisenDetailAPI with bad request', function () {
        const sandbox = sinon.createSandbox();

        afterEach(() => {
            sandbox.restore();
        });

        it('rejects request by tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
        });

        it('returns error if line number is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.line_no = '';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.line_no;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if lineDetail is not found', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(pgLines, 'getKaisenInfoDetail').resolves(null);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994084');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if line tenant is not equal session tenant', async () => {
            const request = _.cloneDeep(__request); // TSA line
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'CON000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('991002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if API processCode is not zero', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(CoreService, 'callAPICoreLineUnyo').resolves({
                responseHeader: {
                    processCode: '123456',
                    apiProcessID: 'AP09002320513',
                    isCoreAPICalled: true,
                },
            });

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(200); // server error
            expect(context.res.body.nttc_mvno_error_code).equal('123456');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if API call failed', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(CoreService, 'callAPICoreLineUnyo').throws('[test] api call failed');
            sandbox.stub(context.log, 'error'); // suppress exception trace

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(200); // server error
            expect(context.res.body.nttc_mvno_error_code).equal('994084');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
