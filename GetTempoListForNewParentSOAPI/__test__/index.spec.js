const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { HEADER_KEY } = require('../../__shared__/constants/headerKeys');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
let expect = chai.expect;

describe('GetTempoListForNewParentSOAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'TSA000',
        },
    };

    describe('GetTempoListForNewParentSOAPI with normal request', function () {
        it('Get tempos using super user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.TENANT_ID] = 'ZZZ000';
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.SUPER_USER;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
        });

        it('Get tempos using daihyou user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TENANT_DAIHYO_USER;
            request.headers[HEADER_KEY.USER_ID] = 'TSAtest';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);

            /** @type Array<string> */
            const tempoIds = context.res.body.result.map((r) => r.tempo_id);
            // check Sxx or ZMK tempo
            expect(tempoIds.some((r) => r.startsWith('Sxx') || r.startsWith('ZMK'))).equal(true);
        });

        it('Get tempos using operator user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TENANT_OPERATOR_USER;
            request.headers[HEADER_KEY.USER_ID] = 'test_ope';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);

            /** @type Array<string> */
            const tempoIds = context.res.body.result.map((r) => r.tempo_id);
            // check Sxx or ZMK tempo
            expect(tempoIds.some((r) => r.startsWith('Sxx') || r.startsWith('ZMK'))).equal(true);
        });

        it('Get tempos using Sxx tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            request.headers[HEADER_KEY.TENANT_ID] = request.body.tenant_id;
            request.headers[HEADER_KEY.TEMPO_ID] = 'SxxTSA0001';
            request.headers[HEADER_KEY.USER_ID] = '@TSASxx';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);

            /** @type Array<string> */
            const tempoIds = context.res.body.result.map((r) => r.tempo_id);
            // check Sxx or ZMK tempo
            expect(tempoIds.some((r) => r.startsWith('Sxx') || r.startsWith('ZMK'))).equal(true);
        });
    });

    describe.skip('GetTempoListForNewParentSOAPI with bad request', function () {
        it('returns invalid user if roleId is not set', async () => {
            const request = _.cloneDeep(__request);
            delete request.headers[HEADER_KEY.ROLE_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code, '990002');
        });

        it('returns invalid user if tenantId is not set', async () => {
            const request = _.cloneDeep(__request);
            delete request.headers[HEADER_KEY.TENANT_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code, '990002');
        });

        it('returns invalid user if tempoId is not set for tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            delete request.headers[HEADER_KEY.TEMPO_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code, '990002');
        });

        it('returns invalid user if tempo user is not Sxx tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            const testData = ['SyyTSA0001', 'SAATSA0001', 'SBBTSA0001'];
            for (const tempoId of testData) {
                request.headers[HEADER_KEY.TEMPO_ID] = tempoId;

                await httpFunction(context, request);
                //status code should be 200
                expect(context.res.status).equal(200);
                expect(context.res.body.error).equal(101);
                expect(context.res.body.nttc_mvno_error_code, '990002');
            }
        });
    });
});
