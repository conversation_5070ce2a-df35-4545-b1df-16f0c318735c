const { CommonError } = require('../__shared__/constants/CommonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const { isSxxTempoID } = require('../__shared__/utils/stringUtils');
const { retrieveMAWPTempoListForNewSo } = require('../__shared__/pgModels/shops');
const mongoose = require('../__shared__/database/mongoose');

module.exports = async function (context, req) {
    try {
        context.log('GetTempoListForNewParentSOAPI START');
        const { roleId, tenantId, tempoId } = getSessionData(context, req);
        const paramTenantId = req.body ? req.body.tenant_id : null;

        // MVNOシステムの場合は tenantId, user, roleId が設定されていなければエラーにする
        if (isNone(roleId) || isNone(tenantId)) {
            context.log.error('GetTempoListForNewParentSOAPI sessionVar empty');
            responseWithError(context, CommonError.INVALID_USER, 'tenant id is not set', '990002');
            return;
        }

        await mongoose.init(context);
        let isAllowed = false;
        switch (roleId) {
            case MAWP_ROLE_ID.TEMPO_USER:
                if (isNone(tempoId)) {
                    context.log.error('GetTempoListForNewParentSOAPI tempoId is not set');
                    isAllowed = false;
                } else {
                    //Sxxで始まる店舗のユーザーの場合は実行させる
                    isAllowed = isSxxTempoID(tempoId);
                }
                break;
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                isAllowed = true;
                break;
            default:
                isAllowed = false;
                break;
        }
        if (!isAllowed) {
            responseWithError(context, CommonError.INVALID_USER, 'invalid role id', '990002', null);
            return;
        }

        let tenantID = null;
        switch (roleId) {
            case MAWP_ROLE_ID.TEMPO_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                tenantID = tenantId;
                break;
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                tenantID = paramTenantId;
                break;
            default:
                tenantID = null;
                break;
        }
        if (isNone(tenantID)) {
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', '992002', null);
            return;
        } else {
            const tempoSeq = await retrieveMAWPTempoListForNewSo(context, tenantID);
            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    result: tempoSeq.map((tempo) => ({
                        tempo_id: tempo.shop_id,
                        tempo_name: tempo.shop_name,
                    })),
                },
            };
        }
    } catch (exception) {
        context.log.error('GetTempoListForNewParentSOAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
