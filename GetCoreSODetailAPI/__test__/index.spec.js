const _ = require('lodash');
const sinon = require('sinon');
const httpFunction = require('../index');
const helpers = require('../../__shared__/helpers/baseHelper');
const context = require('../../__shared__/__tests__/defaultContext');
const { expect } = require('chai');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const serviceOrder = require('../../__shared__/pgModels/serviceOrder');

describe('GetCoreSODetailAPI', () => {
    const sandbox = sinon.createSandbox();

    const __request = {
        headers: getDefaultHeader(),
        body: {
            so_id: 'test12345678901',
            tenant_id: 'TSA000',
        },
    };

    afterEach(() => {
        sandbox.restore();
    });

    describe('GetCoreSODetailAPI with normal request', () => {
        it('should not return an error when the execution was successful', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(serviceOrder, 'getSoDetail').resolves({
                tenant_id: 'test',
                so_id: 'test',
                order_date: '1693508400.000000',
                reserve_date: 'test',
                exec_date: 'test',
                order_type: 'test',
                order_status: 'test',
                line_no: 'test',
                order_content: 'test',
                exec_user_id: 'test',
                exec_tenant_id: 'test',
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body).to.deep.equal({
                error: 0,
                tenant_id: 'test',
                so_id: 'test',
                order_date: 1693508400,
                reserve_date: '',
                exec_date: '',
                order_type: 'test',
                order_status: 'test',
                line_no: 'test',
                order_content: 'test',
                exec_user_id: 'test',
                exec_tenant_id: 'test',
            });
        });
    });

    describe('GetCoreDetailAPI with bad request', () => {
        it('should return an error when soId is not provided', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.so_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('SO-IDが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994056');
        });

        it('should return an error when soId is invalid', async () => {
            let request = _.cloneDeep(__request);
            request.body.so_id = 'test123456789012';

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('SO-IDが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994056');
        });

        it('should return an error when tenantId is not provided', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('テナントIDが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
        });

        it('should return an error when tenantId in paramter is not equal to the tenantId in session  and it is not ZZZ000', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSB000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('この操作を実行する権限がありません。');
            expect(context.res.body.nttc_mvno_error_code).equal('991002');
        });

        it('should return an error when soInfo cannot be found.', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(serviceOrder, 'getSoDetail').resolves(null);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('SO一覧の取得に失敗しました。');
            expect(context.res.body.nttc_mvno_error_code).equal('994062');
        });
    });
});
