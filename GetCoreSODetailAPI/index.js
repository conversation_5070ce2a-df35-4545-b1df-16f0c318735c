const baseHelper = require('../__shared__/helpers/baseHelper');
const { CommonError } = require('../__shared__/constants/commonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const { SUPER_TENANT_ID } = require('../__shared__/constants/specialDefinitions');
const serviceOrder = require('../__shared__/pgModels/serviceOrder');

module.exports = async function (context, req) {
    try {
        context.log('GetCoreSODetailAPI START');
        const { roleId, tenantId, user } = baseHelper.getSessionData(context, req);

        let isAllowed;

        switch (roleId) {
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                isAllowed = true;
                break;
            default:
                isAllowed = false;
                break;
        }

        if (baseHelper.isNone(roleId) || baseHelper.isNone(tenantId) || baseHelper.isNone(user) || !isAllowed) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const soId = req.body['so_id'];
        const tenantID = req.body['tenant_id'];

        if (baseHelper.isNone(soId)) {
            responseWithError(context, CommonError.BAD_REQUEST, 'invalid SO-ID', '994056', null);
            return;
        }

        if (!soId.match(/^[a-zA-Z0-9]{1,15}$/)) {
            responseWithError(context, CommonError.BAD_REQUEST, 'invalid SO-ID', '994056', null);
            return;
        }

        if (baseHelper.isNone(tenantID)) {
            responseWithError(context, CommonError.BAD_REQUEST, 'invalid tenant ID', '994057', null);
            return;
        }
        if (tenantId !== tenantID && tenantId !== SUPER_TENANT_ID) {
            responseWithError(context, CommonError.BAD_REQUEST, 'invalid tenant ID', '991002', null);
            return;
        }

        const soInfo = await serviceOrder.getSoDetail(context, tenantID, soId);
        if (baseHelper.isNone(soInfo)) {
            responseWithError(context, CommonError.BAD_REQUEST, 'Cannot find so detail', '994062', null);
            return;
        } else {
            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    tenant_id: soInfo.tenant_id,
                    so_id: soInfo.so_id,
                    order_date: isNumber(soInfo.order_date) ? +soInfo.order_date : '',
                    reserve_date: isNumber(soInfo.reserve_date) ? +soInfo.reserve_date : '',
                    exec_date: isNumber(soInfo.exec_date) ? +soInfo.exec_date : '',
                    order_type: soInfo.order_type,
                    order_status: soInfo.order_status,
                    line_no: soInfo.line_no,
                    order_content: soInfo.order_content,
                    exec_user_id: soInfo.exec_user_id,
                    exec_tenant_id: soInfo.exec_tenant_id,
                },
            };
            return;
        }
    } catch (exception) {
        context.log.error('GetCoreSODetailAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};

const isNumber = (text) => {
    return !baseHelper.isNone(text) && !baseHelper.isNone(+text);
};
