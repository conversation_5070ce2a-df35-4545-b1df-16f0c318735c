const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseGetHaishiKaisenDetail } = require('./helpers/validateParse');
const pgLines = require('../__shared__/pgModels/lines');
const { ERROR_MAP, ERROR_TEXT } = require('./helpers/errorHelper');

module.exports = async function (context, req) {
    try {
        context.log('GetHaishiKaisenDetailAPI START');
        const { roleId } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, errorMessage, params } = validateAndParseGetHaishiKaisenDetail(req.body ?? {});
        if (error) {
            context.log('GetHaishiKaisenDetailAPI parameter error', errorMessage);
            responseWithError(context, CommonError.BAD_REQUEST, errorMessage, error, null);
            return;
        }

        await mongoose.init(context);
        const result = await pgLines.getHaishiKaisenInfo(context, params.line_no, params.created_at);
        if (isNone(result)) {
            context.log('GetHaishiKaisenDetailAPI: lineDetail is not found!');
            responseWithError(
                context,
                CommonError.BAD_REQUEST,
                ERROR_TEXT[ERROR_MAP.DETAIL_NOT_FOUND],
                ERROR_MAP.DETAIL_NOT_FOUND,
                null
            );
            return;
        }
        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                ...result,
            },
        };
    } catch (exception) {
        context.log.error('GetHaishiKaisenDetailAPI - exception: ', exception);
        responseWithError(
            context,
            CommonError.SERVER_ERROR,
            'Cannot connect to api server ',
            ERROR_MAP.DETAIL_NOT_FOUND,
            null
        );
        return;
    }
};

/**
 * @param {number} roleId
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
