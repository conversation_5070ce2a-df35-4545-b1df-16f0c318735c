const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
let expect = chai.expect;

describe('GetHaishiKaisenDetailAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            line_no: '08010129981',
            created_at: 1465209011,
        },
    };

    describe('GetHaishiKaisenDetailAPI with normal request', function () {
        it('returns line detail if found', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            // line number should be same with request param
            expect(context.res.body.lineNo).equal(request.body.line_no);

            // check other fields exist
            const fields = [
                'contractType',
                'nBan',
                'oldPlanId',
                'oldPlanName',
                'openDate',
                'serviceDate',
                'simType',
                'startDate',
            ];
            const keys = Object.keys(context.res.body);
            expect(fields.every((val) => keys.includes(val))).equal(true);
        });
    });

    describe('GetHaishiKaisenDetailAPI with bad request', function () {
        it('reject requests by tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
        });

        it('returns error if line_no parameter is not defined or empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.line_no = '';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.line_no;
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if created_at parameter is not defined', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.created_at;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994085');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if line detail is not found', async () => {
            const request = _.cloneDeep(__request);
            request.body.line_no = '08090129981';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994084');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
