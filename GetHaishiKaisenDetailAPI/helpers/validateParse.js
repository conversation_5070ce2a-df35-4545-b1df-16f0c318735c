const Joi = require('joi');
const { ERROR_MAP, ERROR_TEXT } = require('./errorHelper');

const getHaishiKaisenDetailSchema = Joi.object({
    line_no: Joi.string().required().messages({
        'any.required': ERROR_MAP.LINENO_EMPTY,
        'string.empty': ERROR_MAP.LINENO_EMPTY,
    }),
    created_at: Joi.number().required().messages({
        'any.required': ERROR_MAP.CREATED_AT_EMPTY,
    }),
}).unknown();

/**
 *
 * @param {object} body
 * @returns {{error?: string, errorMessage?: string, params?: {line_no: string, created_at: number}}}
 */
const validateAndParseGetHaishiKaisenDetail = (body) => {
    const { error } = getHaishiKaisenDetailSchema.validate(body);
    if (error) {
        if (Object.keys(ERROR_TEXT).includes(error.details[0].message)) {
            return { error: error.details[0].message, errorMessage: ERROR_TEXT[error.details[0].message] };
        } else {
            return { error: ERROR_MAP.OTHER, errorMessage: error.details[0].message };
        }
    } else {
        return {
            params: {
                line_no: body.line_no,
                created_at: body.created_at,
            },
        };
    }
};

module.exports = {
    validateAndParseGetHaishiKaisenDetail,
};
