## 概要 (Overview)

管理者機能 > テナント管理 > 「BBユニバ変更」を追加する
「BBユニバ」チェックを持つテナントのみ「BBユニバ変更」に関する機能が使用できます。

## ゴール (Objective)

管理者は、テナントが「BBユニバ変更」機能を使用できるかどうかを制御できるようにします。
ユーザーは、テナントに「BBユニバ」チェックがある場合、「BBユニバ変更」に関連する機能を使用できます。

## 仕様 (Specifications)

### フロー概要 (Flow Overview)

### 詳細仕様 (Detailed Specifications)

MVNO_ADMIN_SERVICE
- API: UpdateAladinApiSettingsAPI, GetAladinApiSettingsAPI
- データベースのスキーマ: ServicesRestrictSettingModel
- 実装内容：「BBユニバ変更」の有効/無効を切り替えるため、ロジックを追加, 「BBユニバ変更」設定を取得するため、ロジックを追加

MVNO_CORE_SERVICE
- API: GetTenantListAPI
- データベースのスキーマ: ServicesRestrictSettingModel
- 実装内容：テナントの「BBユニバ」チェックを取得するため、ロジックを追加

MVNO_GUI_V2
- 画面: 管理者画面 > テナント管理
- 実装内容：テナントの「BBユニバ変更」設定を表示・編集するため、UIを追加

## データ構造 (Database References)

新しいカラムをServicesRestrictSettingModelに追加して、「BBユニバ変更」の状態を保存します。

## 対応内容 (What to do)

- コーディング (Coding): 
  - MVNO_ADMIN_SERVICE
    - API: UpdateAladinApiSettingsAPI, GetAladinApiSettingsAPI
    - データベースのスキーマ: ServicesRestrictSettingModel
    - 実装内容：「BBユニバ変更」の有効/無効を切り替えるため、ロジックを追加, 「BBユニバ変更」設定を取得するため、ロジックを追加
  - MVNO_CORE_SERVICE
    - API: GetTenantListAPI
    - データベースのスキーマ: ServicesRestrictSettingModel
    - 実装内容：テナントの「BBユニバ」チェックを取得するため、ロジックを追加
  - MVNO_GUI_V2
    - 画面: 管理者画面 > テナント管理
    - 実装内容：テナントの「BBユニバ変更」設定を表示・編集するため、UIを追加
- テスト (Testing): 
  - 単体テスト (Unit Tests)
  - 結合テスト (Integration Tests)
- ドキュメンテーション (Documentation): 
  - このドキュメントを更新

## テスト観点 (Test Scenarios)

管理者は、テナントごとに「BBユニバ変更」機能を有効/無効にできます。
ユーザーは、テナントに「BBユニバ」チェックがある場合、「BBユニバ変更」に関連する機能を使用できます。
ユーザーは、テナントに「BBユニバ」チェックがない場合、「BBユニバ変更」に関連する機能を使用できません。

## 対応済 (Implemented)

修正コード
- MVNO_ADMIN_SERVICE
    - processUpdateAladinApiSettings.js:
      BBユニバのデータを更新
    - updateAladinApiSettingsValidator.js:
      BBユニバのvalidation追加
    - GetAladinApiSettingsAPI/index.js
      BBユニバのデータをDBから取得して送る
    - servicesRestrictSetting.model.js:
      BBユニバ columnを追加

  - MVNO_CORE_SERVICE
    - GetTenantListAPI/index.js:
      BBユニバのデータをDBから取得して送る
    - servicesRestrictSetting.model.js:
      BBユニバ columnを追加
      
  - MVNO_GUI_V2
    - 画面: 管理者画面 > テナント管理
    - 実装内容：テナントの「BBユニバ変更」設定を表示・編集するため、UIを追加