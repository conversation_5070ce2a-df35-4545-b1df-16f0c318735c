{"version": "2.0.0", "tasks": [{"type": "func", "command": "host start", "problemMatcher": "$func-node-watch", "isBackground": true, "dependsOn": "npm install (functions)", "options": {"cwd": "${workspaceFolder}/api"}}, {"type": "shell", "label": "npm install (functions)", "command": "npm install", "options": {"cwd": "${workspaceFolder}/api"}}, {"type": "shell", "label": "npm prune (functions)", "command": "npm prune --production", "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/api"}}]}