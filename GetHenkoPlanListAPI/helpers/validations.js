const Joi = require('joi');
const { CONTRACT_TYPES } = require('../../__shared__/constants/simConstants');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const { isNone } = require('../../__shared__/helpers/baseHelper');

const { ERROR_MAP } = require('./errorHelper');

const GetHenkoPlanListSchema = Joi.object({
    tenant_id: Joi.string().allow(''),
    plan_id: Joi.number().required(),
    contract_type: Joi.string(),
}).unknown();

const validateAndParseGetHenkoPlanListRequest = (body, roleId, sessionTenantId) => {
    const schemaVal = GetHenkoPlanListSchema.validate(body);
    if (schemaVal.error) {
        return {
            error: ERROR_MAP.INVALID_PARAMETER,
            errorMessage: schemaVal.error.details[0].message,
        };
    }

    const currentNetwork = [CONTRACT_TYPES.FIVE_G_NSA, CONTRACT_TYPES.FIVE_G_NSA_VOICE].includes(body.contract_type)
        ? CONTRACT_TYPES.FIVE_G_NSA
        : CONTRACT_TYPES.LTE;

    let tenantId = null;
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
            tenantId = body.tenant_id;
            break;
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
            tenantId = sessionTenantId;
            break;
    }
    if (isNone(tenantId)) {
        return {
            error: ERROR_MAP.INVALID_PARAMETER,
            errorMessage: `tenantId is empty! ${tenantId} ${roleId}`,
        };
    }
    return {
        data: {
            tenantId,
            planId: +body.plan_id,
            currentNetwork,
        },
    };
};

module.exports = {
    validateAndParseGetHenkoPlanListRequest,
};
