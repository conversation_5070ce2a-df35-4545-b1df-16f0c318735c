const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');

const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');

const { ERROR_MAP } = require('../helpers/errorHelper');

let expect = chai.expect;

describe('GetHenkoPlanListAPI', function () {
    const planNameTest = {
        TSA: /開発試験用/,
        UNM: /Universal\s?One/,
    };
    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'UNM000',
            plan_id: '30081',
            contract_type: 'LTE',
        },
    };

    describe('GetHenkoPlanListAPI with normal request', function () {
        it('should response plan list [super user]', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.henkoPlanList).to.be.an('array').not.empty;
            // all plans should not include TSA plan
            expect(context.res.body.henkoPlanList.some((r) => planNameTest.TSA.test(r.planName))).equal(false);
        });

        it('should response plan list based on session tenant [daihyou user]', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'TSA000');

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.henkoPlanList).to.be.an('array').not.empty;
            // all plans should not include UNM plan (from session)
            expect(context.res.body.henkoPlanList.some((r) => planNameTest.UNM.test(r.planName))).equal(false);
        });

        it('should response plan list based on session tenant [daihyou user] and empty tenant body', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'TSA000');
            request.body.tenant_id = '';

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.henkoPlanList).to.be.an('array').not.empty;
            // all plans should not include UNM plan (from session)
            expect(context.res.body.henkoPlanList.some((r) => planNameTest.UNM.test(r.planName))).equal(false);
        });
    });

    describe('GetHenkoPlanListAPI with bad request', function () {
        it('should reject request from tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000', { internalId: 123 });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.NOT_ALLOWED);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('should return error if tenant id empty [super user / nttc op]', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.NTTCOM_OPERATOR_USER, 'TSA000', { internalId: 123 });
            request.body.tenant_id = '';

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.INVALID_PARAMETER);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('should return error if session tenant id empty [tenant daihyou]', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, '', { internalId: 123 });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.INVALID_PARAMETER);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('should return error if plan ID is empty', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'TSA000', { internalId: 123 });
            delete request.body.plan_id;

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.INVALID_PARAMETER);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('should return error if plan ID is not numeric', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'TSA000', { internalId: 123 });
            request.body.plan_id = 'plan123';

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.INVALID_PARAMETER);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
