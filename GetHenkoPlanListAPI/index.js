const { CommonError } = require('../__shared__/constants/CommonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');

const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');

const pgPlans = require('../__shared__/pgModels/plans');

const { ERROR_MAP } = require('./helpers/errorHelper');
const { validateAndParseGetHenkoPlanListRequest } = require('./helpers/validations');

// get henko plan details for 一括プラン変更 GUI
module.exports = async function (context, req) {
    try {
        context.log('GetHenkoPlanListAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'not allowed', ERROR_MAP.NOT_ALLOWED, null);
            return;
        }

        const parsed = validateAndParseGetHenkoPlanListRequest(req.body ?? {}, roleId, tenantId);
        if (parsed.error) {
            responseWithError(context, CommonError.BAD_REQUEST, parsed.errorMessage, parsed.error, null);
            return;
        }

        const henkoPlanList = await pgPlans.getHenkoPlanList(
            context,
            parsed.data.tenantId,
            parsed.data.planId,
            parsed.data.currentNetwork
        );

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                henkoPlanList,
            },
        };
    } catch (exception) {
        context.log.error('GetHenkoPlanListAPI - exception:', exception);
        responseWithError(context, CommonError.SERVER_ERROR, 'server error', ERROR_MAP.SERVER_ERROR, null);
    }
};

const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
