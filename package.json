{"name": "api", "version": "1.0.0", "description": "", "scripts": {"start": "func start --javascript", "test-unit": "env-cmd --file .env.json mocha --recursive '**/__tests__/*' --timeout 30000", "test-func": "env-cmd --file .env.json mocha --recursive '**/__test__/*' --require __shared__/__tests__/hooks.js --ignore __ExternalAPITemplate/__test__/* --timeout 30000", "test": "npm run test-unit & npm run test-func", "test-unit-file": "env-cmd --file .env.json mocha --recursive **/__tests__/$npm_config_file --require __shared__/__tests__/hooks.js --timeout 30000", "test-func-single": "env-cmd --file .env.json mocha --recursive $npm_config_api/__test__/* --require __shared__/__tests__/hooks.js --timeout 30000", "test-unit-with-coverage": "env-cmd --file .env.json nyc --reporter=html --lines 90 mocha --recursive '**/__tests__/*' --timeout 30000", "test-func-with-coverage": "env-cmd --file .env.json nyc --reporter=html --lines 90 mocha --recursive '**/__test__/*' --require __shared__/__tests__/hooks.js --timeout 30000", "test-with-coverage": "npm run test-unit-with-coverage & npm run test-func-with-coverage", "format:check": "prettier --check .", "format:write": "prettier --write .", "lint:check": "eslint .", "lint:fix": "eslint --fix .", "test-unit-t1": "env-cmd --file .env.t1.json mocha --recursive '**/__tests__/*' --timeout 60000", "test-func-t1": "env-cmd --file .env.t1.json mocha --recursive '**/__test__/*' --require __shared__/__tests__/hooks.js --timeout 60000", "test-degre-t1": "env-cmd --file .env.t1.json mocha --recursive '**/__test_t1__/*' --require __shared__/__tests__/hooks.js --timeout 60000", "test-t1": "npm run test-unit-t1 & npm run test-func-t1"}, "dependencies": {"@azure/app-configuration": "^1.3.1", "@azure/service-bus": "^7.6.0", "@azure/storage-blob": "^12.11.0", "@joi/date": "^2.1.0", "axios": "^0.27.2", "dayjs": "^1.11.2", "iconv-urlencode": "^1.0.0", "ioredis": "^5.3.1", "joi": "^17.6.0", "js-md2": "^0.2.2", "lodash": "^4.17.21", "mongoose": "^6.3.4", "pg": "^8.7.3", "xml-js": "^1.6.11"}, "devDependencies": {"axios-mock-adapter": "^1.21.1", "chai": "^4.3.6", "env-cmd": "^10.1.0", "eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "sinon": "^14.0.0"}}