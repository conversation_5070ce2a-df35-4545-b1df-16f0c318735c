const { CommonError } = require('../__shared__/constants/CommonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const DaihyouBangoModel = require('../__shared__/models/daihyouBango.model');
const mongoose = require('../__shared__/database/mongoose');

module.exports = async function (context, req) {
    try {
        context.log('GetRyoukinPlanListAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        let isAllowed = false;
        switch (roleId) {
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                isAllowed = true;
                break;
            default:
                isAllowed = false;
                break;
        }
        if (isNone(tenantId) || isNone(roleId) || !isAllowed) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        await mongoose.init(context);
        const ryoukinPlanList = await DaihyouBangoModel.getRyoukinPlans(context);
        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                result: ryoukinPlanList,
            },
        };
    } catch (exception) {
        context.log.error('GetRyoukinPlanListAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
