const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { HEADER_KEY } = require('../../__shared__/constants/headerKeys');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
let expect = chai.expect;

describe('GetRyoukinPlanListAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {},
    };

    describe('GetRyoukinPlanListAPI with normal request', function () {
        it('Get ryoukin plans', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.TENANT_ID] = 'ZZZ000';
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.SUPER_USER;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            // return more than 1 ryoukin plan
            expect(context.res.body.result.length).greaterThan(0);
        });
    });

    describe.skip('GetRyoukinPlanListAPI with bad request', function () {
        it('returns permission error if roleId is empty or null', async () => {
            const request = _.cloneDeep(__request);
            delete request.headers[HEADER_KEY.ROLE_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
            expect(context.res.body.nttc_mvno_error_code, '991002');
        });

        it('returns permission error if roleId is not allowed', async () => {
            const request = _.cloneDeep(__request);
            const testData = [MAWP_ROLE_ID.TENANT_DAIHYO_USER, MAWP_ROLE_ID.TENANT_OPERATOR_USER, MAWP_ROLE_ID.TENANT_ETSURAN_USER, MAWP_ROLE_ID.TEMPO_USER];

            for (const roleId of testData) {
                request.headers[HEADER_KEY.ROLE_ID] = roleId;

                await httpFunction(context, request);
                //status code should be 200
                expect(context.res.status).equal(200);
                expect(context.res.body.error).equal(402);
                expect(context.res.body.nttc_mvno_error_code, '991002');
            }
        });

        it('returns permission error if tenantId is empty or null', async () => {
            const request = _.cloneDeep(__request);
            delete request.headers[HEADER_KEY.TENANT_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
            expect(context.res.body.nttc_mvno_error_code, '991002');
        });
    });
});
