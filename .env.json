{"AzureWebJobsStorage": "", "FUNCTIONS_WORKER_RUNTIME": "node", "MONGO_CONNECTION_STRING": "mongodb://localhost:27017/", "ORDER_SERVICE__DB": "konnect", "SIM_INVENTORY_SERVICE__DB": "konnect", "ADMIN_SERVICE__DB": "konnect", "SWIMMY_SERVICE__DB": "konnect", "CORE_SERVICE__DB": "konnect", "CORE_SERVICE__API_ROOT": "http://localhost:7701/core-api", "CORE_SERVICE__OPF_TENANTID": "OPF000", "CORE_SERVICE__PASSWORD": "OPF000", "CORE_SERVICE__SENDER_SYSTEM_ID": "0001", "CORE_SERVICE__MOCK_MODE": "true", "POSTGRE_CONNECTION_STRING": "postgresql://127.0.0.1:5432/mawp", "SERVICE_BUS_CONNECTION_STRING": "Endpoint=sb://mvno-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=3Zm2091QN8Z6x5UBmADm+rBtJcJ1vbrFGtu2pNR6zHs=", "REGISTER_ALADIN_QUEUE_NAME": "sb-register-aladin-transaction", "REGISTER_SWIMMY_QUEUE_NAME": "sb-register-swimmy-transaction", "REGISTER_SWIMMY_VLM_QUEUE_NAME": "sb-register-swimmy-vlm-transaction", "GAIBU_API_PROCESS_ID_PREFIX": "PF0", "GAIBU_API_FULL_PROCESS_ID_PREFIX": "FM0", "COCN_DAIHYOU_NNUMBER": "['N141096449']", "COCN_DAIHYOU_NNUMBER_FOR_FULL_MVNO": "['N190173862']", "KariTourokuKaisenTsuikaUketsukeFukaJikan": "00:00-04:00", "MNPUketsukeFukaJikan": "20:30-09:00", "AladinAPIRequestUketsukeFukaJikan": "20:58-09:00", "ApiUserId": "API", "STORAGE_CONNECTION_STRING": "DefaultEndpointsProtocol=https;AccountName=mvnoexternalapi;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "KAISEN_INFO_CONTAINER": "kaiseninfo", "KAISEN_INFO_FILENAME_FORMAT": "nnumber.zip", "TempoLoginIDPrefix": "@", "ALADIN_API_INFO_HANBAITENCODE": "*************", "ALADIN_API_INFO_HANBAITENCODE_HLRHSS": "*************", "GaibuAPISearchSOReturnLimit": "10000", "KAISEN_HAISHI_CANCEL_IS_OK_BEFORE_X_DAY": "1", "KAISEN_HAISHI_RESERVATION_ACCEPTABLE_PERIOD_DAYS": "60", "KAISEN_HAISHI_RESERVATION_ACCEPTABLE_BEFORE_X_DAY": "5", "CIPHER_KEY": "NsSE6srKAOJYdvU=", "LOCAL_SETTINGS_CONFIG": true, "Database:MongoDB:ConnectionString": "mongodb://localhost:27017/", "Database:PostgreSQL:ConnectionString": "postgresql://127.0.0.1:5432/mawp", "Api:Core:URL": "http://localhost:7703/core-api", "APP_CONFIGURATION_CONNECTION_STRING": "<app configuration here>", "DATABASE_CONFIG_LABEL": "local", "API_CONFIG_LABEL": "local"}