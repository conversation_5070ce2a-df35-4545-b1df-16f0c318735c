const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const { MVNO_SERVICES } = require('../../__shared__/constants/mvnoServicesConstants');
let expect = chai.expect;

describe('SearchHaishiKaisenAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            limit: 50,
            offset: 0,
            target_line: '廃止回線',
        },
    };

    describe('SearchHaishiKaisenAPI with normal request', function () {
        it('super user', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 9,
                tenant_id: ['CON000', 'UNM000'],
                search_type: '1',
                start_date_from: 1483196400,
                start_date_to: 1514646000,
                target_line: '廃止回線',
                limit: 50,
                offset: 0,
                sort_by: 'oldPlanName',
                sort_order: 'desc',
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
            expect(context.res.body.results.some((r) => r.tenantId == 'CON000')).equal(true);
            expect(context.res.body.results.some((r) => r.tenantId == 'UNM000')).equal(true);
        });

        it('tenant user CON', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'CON000');
            request.body = {
                full_mvno: 9,
                // tenant_id: ['CON000', 'UNM000'],
                search_type: '1',
                start_date_from: 1483196400,
                start_date_to: 1514646000,
                target_line: '廃止回線',
                limit: 50,
                offset: 0,
                sort_by: 'oldPlanName',
                sort_order: 'desc',
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
            expect(context.res.body.results.some((r) => r.tenantId !== 'CON000')).equal(false);
        });

        it('search by line number', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 9,
                search_value: '99',
                search_type: '1',
                contract_type: '3G',
                target_line: '廃止回線',
                limit: 50,
                offset: 0,
            };
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);

            expect(context.res.body.results.every((r) => r.lineId.includes('99'))).equal(true);
        });

        it('search by SIM number', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 9,
                search_value: '222',
                search_type: '3',
                start_date_from: 1483196400,
                sim_type: 'microSIM',
                start_date_to: 1514646000,
                target_line: '廃止回線',
                limit: 50,
                offset: 0,
            };
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);

            expect(context.res.body.results.every((r) => r.simNumber.includes('222'))).equal(true);
        });

        it('search by IMSI', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: MVNO_SERVICES.FULL,
                search_value: '951',
                search_type: '2',
                sim_type: 'フルマルチカットSIM(N)',
                target_line: '廃止回線',
                limit: 50,
                offset: 0,
            };
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);

            expect(context.res.body.results.every((r) => r.imsi.includes('951'))).equal(true);
        });

        it('search by BBユニバ対象外', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 2,
                search_type: '1',
                bb_uniba_flag: '対象外',
                target_line: '廃止回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.count).greaterThanOrEqual(0);
        });

        it('search by BBユニバ対象', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 2,
                search_type: '1',
                bb_uniba_flag: '対象',
                target_line: '廃止回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.count).greaterThanOrEqual(0);
        });
    });

    describe('SearchHaishiKaisenAPI with bad request', function () {
        it('rejects request by tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
        });

        it('returns error if tenant_id is not session tenant', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'CON000');
            request.body = {
                full_mvno: 1,
                tenant_id: ['UNM000'],
                search_type: '1',
                target_line: '廃止回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
        });

        it('returns error if target line is empty or not kaisen haishi', async () => {
            const request = _.cloneDeep(__request);
            request.body.target_line = '登録済回線';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994081');

            delete request.body.target_line;
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994081');
        });

        it('returns error if start_date_from is not number', async () => {
            const request = _.cloneDeep(__request);
            request.body.start_date_from = '2022-12-31';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });

        it('returns error if start_date_to is not number', async () => {
            const request = _.cloneDeep(__request);
            request.body.start_date_to = '2022-12-31';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });

        it('returns error if sim flag is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.sim_flag = 'aa';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
        });

        it('returns error if contract type is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.contract_type = '5G(NSA)';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('993022');
        });

        it('returns error if sim type is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.sim_type = 'normal SIM';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('997012');
        });

        it('returns error if sort key invalid is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.sort_by = 'lineNumber';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('994080');
        });

        it('returns error if bb_uniba_flag is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.bb_uniba_flag = null;
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('991022');
        });
    });
});
