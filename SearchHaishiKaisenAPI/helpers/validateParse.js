const Joi = require('joi');
const { MVNO_SERVICES } = require('../../__shared__/constants/mvnoServicesConstants');
const {
    CONTRACT_TYPES,
    CONTRACT_TYPES_FULL,
    SIM_TYPES,
    SIM_TYPES_ESIM,
    SIM_TYPES_FULL,
} = require('../../__shared__/constants/simConstants');
const { SUPER_TENANT_ID } = require('../../__shared__/constants/specialDefinitions');
const { removeNullValue, isNone } = require('../../__shared__/helpers/baseHelper');
const pgLines = require('../../__shared__/pgModels/lines');
const { ERROR_MAP, ERROR_TEXT } = require('./errorHelper');

const VALID_CONTRACT = [
    CONTRACT_TYPES.THREE_G,
    CONTRACT_TYPES.THREE_G_SMS,
    CONTRACT_TYPES.LTE,
    CONTRACT_TYPES.LTE_SMS,
    CONTRACT_TYPES.LTE_VOICE,

    CONTRACT_TYPES_FULL.LTE,
    CONTRACT_TYPES_FULL.LTE_SMS,
];

// eslint-disable-next-line no-unused-vars
const searchHaishiKaisenV2Schema = Joi.object({
    start_date_from: Joi.number(),
    start_date_to: Joi.number(),
    tenant_id: Joi.array().items(Joi.string()),
    sim_flag: Joi.string()
        .valid(...Object.keys(pgLines.SIM_FLAG))
        .messages({
            'any.only': ERROR_MAP.SIM_FLAG_INVALID,
        }),
    bb_uniba_flag: Joi.string()
        .valid(...Object.keys(pgLines.BB_UNIBA_FLAG))
        .messages({
            'any.only': ERROR_MAP.OTHER,
        }),
    full_mvno: Joi.number(),
    contract_type: Joi.string()
        .valid(...VALID_CONTRACT)
        .messages({
            'any.only': ERROR_MAP.CONTRACT_TYPE_INVALID,
        }),
    sim_type: Joi.string()
        .valid(...Object.values(SIM_TYPES), SIM_TYPES_ESIM, ...Object.values(SIM_TYPES_FULL))
        .messages({
            'any.only': ERROR_MAP.SIM_TYPE_INVALID,
        }),
    plan_id: Joi.array().items(Joi.number()),
    search_value: Joi.string(),
    search_type: Joi.string(),
    sort_by: Joi.string()
        .valid(...Object.keys(pgLines.HAISHI_SORT_CONDITION_LIST))
        .messages({
            'any.only': ERROR_MAP.SORT_BY_INVALID,
        }),
    sort_order: Joi.string(),
    limit: Joi.number(),
    offset: Joi.number(),

    target_line: Joi.string().required().valid(pgLines.TARGET_LINE.HAISHIKAISEN).messages({
        'any.only': ERROR_MAP.TARGET_LINE_INVALID,
        'any.required': ERROR_MAP.TARGET_LINE_INVALID,
    }),
}).unknown();

const validateAndParseSearchHaishiKaisenV2 = (context, body, userTenant) => {
    let result = { error: null, errorMessage: '', param: {} };
    const { error } = searchHaishiKaisenV2Schema.validate(body);
    if (error) {
        result.error = error.details[0].message;
    }

    let tenantError = false;
    for (const tenant of body.tenant_id || []) {
        if (userTenant !== SUPER_TENANT_ID && tenant !== userTenant) {
            tenantError = true;
        }
    }
    if (tenantError) {
        context.log('SearchHaishiKaisenAPI tenant_id param is not valid');
        result.error = ERROR_MAP.TENANT_INVALID;
        result.errorMessage = ERROR_TEXT[ERROR_MAP.TENANT_INVALID];
        return result;
    }

    if (result.error) {
        if (Object.keys(ERROR_TEXT).includes(result.error)) {
            result.errorMessage = ERROR_TEXT[result.error];
        } else {
            result.errorMessage = result.error;
            result.error = ERROR_MAP.OTHER;
        }
        return result;
    }
    // prepare param object

    let tenantIds = [userTenant];
    if (userTenant === SUPER_TENANT_ID) {
        tenantIds = body.tenant_id || [];
    }
    let fullMvnoFlag = true; // default FullMVNO
    switch (body.full_mvno) {
        case MVNO_SERVICES.LITE:
            fullMvnoFlag = false;
            break;
        case MVNO_SERVICES.FULL:
            fullMvnoFlag = true;
            break;
        case MVNO_SERVICES.ALL:
            fullMvnoFlag = null;
            break;
    }

    let param = removeNullValue({
        startDateST: body.start_date_from,
        startDateEN: body.start_date_to,
        contractType: body.contract_type,
        simType: body.sim_type,
        planID: body.plan_id,
        searchValue: body.search_value,
        tenantID: tenantIds,
        sortBy: body.sort_by,
        sortOrder: body.sort_order,
        simFlag: body.sim_flag,
        bbUnibaFlag: body.bb_uniba_flag,
        limit: body.limit,
        offset: body.offset,
        searchType: body.search_type,
    });
    // fields which should not be null
    param.fullMvnoFlag = fullMvnoFlag;
    if (isNone(param.tenantID)) param.tenantID = [];
    if (isNone(param.planID)) param.planID = [];
    if (isNone(param.limit)) param.limit = pgLines.LIMIT;
    if (isNone(param.offset)) param.offset = pgLines.OFFSET;

    // 終了日＋１日
    if (!isNone(param.startDateEN)) {
        param.startDateEN += 86400;
    }
    result.param = param;
    return result;
};

module.exports = {
    validateAndParseSearchHaishiKaisenV2,
};
