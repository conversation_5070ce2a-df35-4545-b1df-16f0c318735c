const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseSearchHaishiKaisenV2 } = require('./helpers/validateParse');
const pgLines = require('../__shared__/pgModels/lines');
const { ERROR_MAP, ERROR_TEXT } = require('./helpers/errorHelper');

// /api/v2/haishi_kaisen_list
module.exports = async function (context, req) {
    try {
        context.log('SearchHaishiKaisenAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, errorMessage, param } = validateAndParseSearchHaishiKaisenV2(context, req.body || {}, tenantId);
        if (error) {
            context.log('SearchHaishiKaisenAPI validation error:', errorMessage);
            responseWithError(context, CommonError.BAD_REQUEST, errorMessage, error, null);
            return;
        }

        await mongoose.init(context);
        const { count, results } = await pgLines.getHaishiKaisenList(context, param);

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                results,
                count,
                limit: param.limit,
                offset: param.offset,
            },
        };
    } catch (exception) {
        context.log.error('SearchHaishiKaisenAPI - exception: ', exception);
        responseWithError(
            context,
            CommonError.SERVER_ERROR,
            ERROR_TEXT[ERROR_MAP.SEARCH_FAILED],
            ERROR_MAP.SEARCH_FAILED,
            null
        );
        return;
    }
};

/**
 * @param {number} roleId
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
