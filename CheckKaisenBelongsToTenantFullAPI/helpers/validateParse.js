const Joi = require('joi');

const checkKaisenTenantFullSchema = Joi.object({
    tenant_id: Joi.string().required(),
    kaisen_no: Joi.string().required(),
}).unknown();

/**
 *
 * @param {object} context
 * @param {object} body
 * @returns {{error: null|string, params: {tenant_id: string, kaisen_no: string}}}
 */
const validateAndParseCheckKaisenTenantFullParams = (context, body) => {
    let result = { error: null, params: {} };
    const { error } = checkKaisenTenantFullSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
    } else {
        result.params.tenant_id = body.tenant_id;
        result.params.kaisen_no = body.kaisen_no;
    }
    return result;
};

module.exports = {
    validateAndParseCheckKaisenTenantFullParams,
};
