const { CommonError } = require('../__shared__/constants/CommonError');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { validateAndParseCheckKaisenTenantFullParams } = require('./helpers/validateParse');
const pgLines = require('../__shared__/pgModels/lines');

module.exports = async function (context, req) {
    try {
        context.log('CheckKaisenBelongsToTenantFullAPI START');

        const { error, params } = validateAndParseCheckKaisenTenantFullParams(context, req.body ?? {});
        if (error) {
            context.log('CheckKaisenBelongsToTenantFullAPI invalid parameter', req.body);
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', '991022', null);
            return;
        }

        await mongoose.init(context);
        const [is_belongs, is_fullmvno, usageStatus] = await Promise.all([
            pgLines.isKaisenNoBelongToTenant(context, {
                tenantId: params.tenant_id,
                kaisenNo: params.kaisen_no,
            }),
            pgLines.isFullMVNO(context, { kaisenNo: params.kaisen_no }),
            pgLines.getUsageStatusByLineNo(context, params.kaisen_no),
        ]);

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                is_belongs,
                is_fullmvno,
                usageStatus,
            },
        };
    } catch (exception) {
        context.log.error('CheckKaisenBelongsToTenantFullAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
