const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
let expect = chai.expect;

describe('CheckKaisenBelongsToTenantFullAPI', function () {
    const lineOkUNM = '08010136787';
    const lineOkFullTST = '02006102062';
    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'TSA000',
            kaisen_no: '08006135680', // doesn't belong to tenant
        },
    };

    describe('CheckKaisenBelongsToTenantFullAPI with normal request', function () {
        it('returns is_belongs as true if kaisenNo belongs to tenant (full)', async () => {
            const request = _.cloneDeep(__request);
            request.body.kaisen_no = lineOkFullTST;
            request.body.tenant_id = 'TST000';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.is_belongs).equal(true);
            expect(context.res.body.is_fullmvno).equal(true);
        });

        it('returns is_belongs as true if kaisenNo belongs to tenant (lite)', async () => {
            const request = _.cloneDeep(__request);
            request.body.kaisen_no = lineOkUNM;
            request.body.tenant_id = 'UNM000';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.is_belongs).equal(true);
            expect(context.res.body.is_fullmvno).equal(false);
        });

        it('returns is_belongs as false if kaisenNo does not belong to tenant', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.is_belongs).equal(false);
        });
    });

    describe('CheckKaisenBelongsToTenantFullAPI with bad request', function () {
        it('returns error if tenant_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });

        it('returns error if kaisen_no is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.kaisen_no;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });
    });
});
