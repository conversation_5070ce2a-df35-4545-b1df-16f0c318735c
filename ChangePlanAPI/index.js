const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseChangePlanRequest, validateChangePlanWithDbConnection } = require('./helpers/validateParse');
const CoreService = require('../__shared__/services/core.service');
const pgServiceOrder = require('../__shared__/pgModels/serviceOrder');
const { ERROR_TEXT, ERROR_MAP } = require('./helpers/errorHelper');

module.exports = async function (context, req) {
    try {
        context.log('ChangePlanAPI START');
        const { roleId, tenantId, user } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, errorMessage, params } = validateAndParseChangePlanRequest(req.body ?? {});
        if (error) {
            context.log('ChangePlanAPI parameter error: ', error);
            responseWithError(context, CommonError.BAD_REQUEST, errorMessage, error, null);
            return;
        }

        await mongoose.init(context);

        const dbValidation = await validateChangePlanWithDbConnection(context, params.tenantID, params.lineNo);
        if (dbValidation.error) {
            context.log('ChangePlanAPI db validation error: ', dbValidation.error);
            responseWithError(context, CommonError.BAD_REQUEST, dbValidation.errorMessage, dbValidation.error, null);
            return;
        }

        const apiResponse = await CoreService.callAPICoreLinePlanHenko(context, params);
        const { processCode, apiProcessID } = apiResponse.responseHeader;

        let userId = user.plusId;
        if (typeof userId === 'string' && userId.length > 16) {
            userId = userId.slice(0, 16);
        }

        // if (roleId == MAWP_ROLE_ID.TEMPO_USER) {
        //     await pgServiceOrder.updateExecSO(context, apiProcessID, tenantId, user.internalId);
        // } else {
        //     await pgServiceOrder.updateExecSO(context, apiProcessID, tenantId, user.name);
        // }
        await pgServiceOrder.updateExecSO(context, apiProcessID, tenantId, userId);

        if (processCode !== '000000') {
            context.log('ChangePlanAPI API Call Fail: ', processCode);
            responseWithError(
                context,
                CommonError.SERVER_ERROR,
                ERROR_TEXT[ERROR_MAP.CHANGE_PLAN_FAILED],
                ERROR_MAP.CHANGE_PLAN_FAILED,
                null
            );
            return;
        }

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
            },
        };
    } catch (exception) {
        context.log.error('ChangePlanAPI - exception: ', exception);
        responseWithError(
            context,
            CommonError.SERVER_ERROR,
            ERROR_TEXT[ERROR_MAP.CHANGE_PLAN_FAILED],
            ERROR_MAP.CHANGE_PLAN_FAILED,
            null
        );
        return;
    }
};

/**
 * @param {number} roleId
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
