const _ = require('lodash');
let chai = require('chai');
const sinon = require('sinon');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const pgLines = require('../../__shared__/pgModels/lines');
const pgServiceOrder = require('../../__shared__/pgModels/serviceOrder');
const CoreService = require('../../__shared__/services/core.service');
let expect = chai.expect;

describe('ChangePlanAPI', function () {
    const __request = {
        headers: getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TSA000', { name: 'TSAtest' }),
        body: {
            tenant_id: 'TSA000',
            line_no: '08006135081',
            potal_plan_id_pre: 90015,
            potal_plan_id: 90023,
            reserve_date: '2023/03/01',
        },
    };

    describe('ChangePlanAPI with normal request', function () {
        const sandbox = sinon.createSandbox();

        // eslint-disable-next-line no-undef
        before(() => {
            // make updateExecSO do nothing
            sandbox.stub(pgServiceOrder, 'updateExecSO').resolves();
        });

        // eslint-disable-next-line no-undef
        after(() => {
            sandbox.restore();
        });

        it('returns no error for valid request', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
        });
    });

    describe('ChangePlanAPI with bad request', function () {
        const sandbox = sinon.createSandbox();

        afterEach(() => {
            sandbox.restore();
        });

        it('rejects request for tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000', { internalId: '@testtsa' });

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
        });

        it('returns error if line_no is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.line_no = '';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.line_no;
            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if tenant_id is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = '';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.tenant_id;
            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if pre-plan param is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.potal_plan_id_pre;

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994091');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if new param is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.potal_plan_id;

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994091');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if reserve date format is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.reserve_date = '2022-03-01';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994087');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if lineNo is not linked to tenant', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(pgLines, 'isKaisenNoBelongToTenant').resolves(false);

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994088');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if lineNo is already scheduled for change', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(pgServiceOrder, 'checkPlanHenkoChuu').resolves(true);

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994095');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if call to core service failed', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(pgServiceOrder, 'updateExecSO').resolves();
            sandbox.stub(CoreService, 'callAPICoreLinePlanHenko').rejects('timeout error');
            sandbox.stub(context.log, 'error'); // suppress exception trace

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(200); // server error
            expect(context.res.body.nttc_mvno_error_code).equal('994089');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
