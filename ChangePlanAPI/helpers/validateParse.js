const Joi = require('joi').extend(require('@joi/date'));
const pgLines = require('../../__shared__/pgModels/lines');
const pgServiceOrder = require('../../__shared__/pgModels/serviceOrder');
const { ERROR_MAP, ERROR_TEXT } = require('./errorHelper');

const changePlanSchema = Joi.object({
    line_no: Joi.string().required().messages({
        'any.required': ERROR_MAP.LINE_NO_EMPTY,
        'string.empty': ERROR_MAP.LINE_NO_EMPTY,
    }),
    tenant_id: Joi.string().required().messages({
        'any.required': ERROR_MAP.TENANT_ID_EMPTY,
        'string.empty': ERROR_MAP.TENANT_ID_EMPTY,
    }),
    potal_plan_id_pre: Joi.number().required().messages({
        'any.required': ERROR_MAP.PRE_PLAN_EMPTY,
        'string.empty': ERROR_MAP.PRE_PLAN_EMPTY,
    }),
    potal_plan_id: Joi.number().required().messages({
        'any.required': ERROR_MAP.PLAN_ID_EMPTY,
        'string.empty': ERROR_MAP.PLAN_ID_EMPTY,
    }),
    reserve_date: Joi.date().format('YYYY/MM/DD').raw().messages({
        'string.base': ERROR_MAP.RESERVE_DATE_INVALID,
        'date.format': ERROR_MAP.RESERVE_DATE_INVALID,
    }),
    csv_unnecessary_flag: Joi.string(),
}).unknown();

/**
 *
 * @param {object} body
 * @returns {{error: string?, errorMessage: string,
 *  params: {
 *    lineNo: string,
 *    tenantID: string,
 *    potalPlanID_pre: string,
 *    potalPlanID: string,
 *    csvUnnecessaryFlag: string,
 *    reserveDate: string | undefined,
 * }}}
 */
const validateAndParseChangePlanRequest = (body) => {
    const result = { error: null, errorMessage: '', params: {} };
    const { error } = changePlanSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
        if (ERROR_TEXT[result.error]) {
            result.errorMessage = ERROR_TEXT[result.error];
        } else {
            result.errorMessage = result.error;
            result.error = '991022';
        }
        return result;
    }

    result.params = {
        lineNo: body.line_no,
        tenantID: body.tenant_id,
        potalPlanID_pre: body.potal_plan_id_pre,
        potalPlanID: body.potal_plan_id,
        csvUnnecessaryFlag: body.csv_unnecessary_flag,
    };
    if (body.reserve_date) {
        result.params.reserveDate = `${body.reserve_date} 04:00`;
    }
    return result;
};

const validateChangePlanWithDbConnection = async (context, tenantID, lineNo) => {
    const result = { error: null, errorMessage: '' };
    const [isOwned, henkoChuu] = await Promise.all([
        pgLines.isKaisenNoBelongToTenant(context, { tenantId: tenantID, kaisenNo: lineNo }),
        pgServiceOrder.checkPlanHenkoChuu(context, lineNo),
    ]);

    if (!isOwned) {
        result.error = ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT;
        result.errorMessage = ERROR_TEXT[ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT];
    } else if (henkoChuu) {
        result.error = ERROR_MAP.ALREADY_RESERVED;
        result.errorMessage = ERROR_TEXT[ERROR_MAP.ALREADY_RESERVED];
    }

    return result;
};

module.exports = {
    validateAndParseChangePlanRequest,
    validateChangePlanWithDbConnection,
};
