const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseGetKaisenPlanListRequest } = require('./helpers/validateParse');
const { SUPER_TENANT_ID } = require('../__shared__/constants/specialDefinitions');
const pgPlans = require('../__shared__/pgModels/plans');

module.exports = async function (context, req) {
    try {
        context.log('GetKaisenPlanListAPI START');
        const { roleId, tenantId } = getSessionData(context, req);
        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, params } = validateAndParseGetKaisenPlanListRequest(req.body ?? {});
        if (error) {
            context.log('GetKaisenPlanListAPI parameter error: ', error);
            responseWithError(context, CommonError.BAD_REQUEST, error, '991022', null);
            return;
        }

        await mongoose.init(context);
        /** @type {Array<pgPlans.MAWPPlan>} */
        let planList = [];
        if (tenantId === SUPER_TENANT_ID) {
            planList = await pgPlans.retrieveMAWPPlanListByMultiTenantId(
                context,
                params.tenant_id,
                params.service_code
            );
        } else {
            planList = await pgPlans.retrieveMAWPPlanList(context, tenantId, params.service_code);
        }

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                results: planList,
            },
        };
    } catch (exception) {
        context.log.error('GetKaisenPlanListAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, 'Cannot connect to api server', '994083', null);
        return;
    }
};

/**
 * @param {number} roleId
 * @returns {boolean}
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
