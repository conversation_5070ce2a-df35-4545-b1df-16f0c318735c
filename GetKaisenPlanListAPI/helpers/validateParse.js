const Joi = require('joi');
const { MVNO_SERVICES } = require('../../__shared__/constants/mvnoServicesConstants');

const getKaisenPlanListSchema = Joi.object({
    tenant_id: Joi.array().items(Joi.string()),
    service_code: Joi.number(),
}).unknown();

/**
 *
 * @param {object} body
 * @returns {{error: null|string, params: {tenant_id: Array<string>, service_code: number}}}
 */
const validateAndParseGetKaisenPlanListRequest = (body) => {
    let result = {
        error: null,
        params: {
            tenant_id: [],
            service_code: MVNO_SERVICES.LITE, // default to <PERSON><PERSON> because we are /mawpfapi2/
        },
    };
    const { error } = getKaisenPlanListSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
    } else {
        if (body.tenant_id) {
            result.params.tenant_id = body.tenant_id;
        }
        if (Object.values(MVNO_SERVICES).includes(body.service_code)) {
            result.params.service_code = body.service_code;
        }
    }
    return result;
};

module.exports = {
    validateAndParseGetKaisenPlanListRequest,
};
