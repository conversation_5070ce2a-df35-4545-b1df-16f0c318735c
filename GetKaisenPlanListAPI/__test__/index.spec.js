const _ = require('lodash');
let chai = require('chai');
let sinon = require('sinon');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MVNO_SERVICES } = require('../../__shared__/constants/mvnoServicesConstants');
const { HEADER_KEY } = require('../../__shared__/constants/headerKeys');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const pgPlans = require('../../__shared__/pgModels/plans');
let expect = chai.expect;

describe('GetKaisenPlanListAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: ['TSA000'],
            service_code: MVNO_SERVICES.ALL,
        },
    };

    describe('GetKaisenPlanListAPI with normal request', function () {
        const sandbox = sinon.createSandbox();

        afterEach(() => {
            sandbox.restore();
        });

        it('get plans using super user', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
        });

        it('get plans using tenant daihyo user', async () => {
            const spy = sandbox.spy(pgPlans, 'retrieveMAWPPlanList');

            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TENANT_DAIHYO_USER;
            request.headers[HEADER_KEY.TENANT_ID] = 'CON000';

            // for checking body.tenant_id is not used
            // request.body.tenant_id = ['TSA000'];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);

            expect(spy.calledOnce).equal(true);
            expect(spy.getCall(0).args[1]).equal('CON000'); // should ignore parameter
        });

        it('returns empty array if no parameter given by super user', async () => {
            const request = _.cloneDeep(__request);
            delete request.body;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).equal(0);
        });
    });

    describe('GetKaisenPlanListAPI with bad request', function () {
        const sandbox = sinon.createSandbox();

        afterEach(() => {
            sandbox.restore();
        });

        it('returns error if request role is tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            request.headers[HEADER_KEY.TENANT_ID] = 'TSA000';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
        });

        it('returns error if tenant_id parameter is not array', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = 'TSA000';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });

        it('returns error if service_code parameter is not number', async () => {
            const request = _.cloneDeep(__request);
            request.body.service_code = 'a';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });

        it('use LITE as service code if invalid code is given', async () => {
            const spy = sandbox.spy(pgPlans, 'retrieveMAWPPlanListByMultiTenantId');

            const request = _.cloneDeep(__request);
            request.body.service_code = 999;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);

            expect(spy.calledOnce).equal(true);
            expect(spy.getCall(0).args[2]).equal(MVNO_SERVICES.LITE); // should ignore parameter
        });
    });
});
