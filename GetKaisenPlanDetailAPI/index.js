const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseGetKaisenPlanDetailRequest } = require('./helpers/validateParse');
const { ERROR_CODE, ERROR_TEXT } = require('./helpers/errorHelper');
const { getKaisenPlanDetail } = require('../__shared__/pgModels/plans');

module.exports = async function (context, req) {
    try {
        context.log('GetKaisenPlanDetailAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        if (isNone(tenantId) || isNone(roleId) || !isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, params } = validateAndParseGetKaisenPlanDetailRequest(req.body ?? {});
        if (error) {
            context.log('GetKaisenPlanDetailAPI parameter error: ', error);
            responseWithError(context, CommonError.BAD_REQUEST, ERROR_TEXT[error], error, null);
            return;
        }

        await mongoose.init(context);
        const planDetail = await getKaisenPlanDetail(context, params.plan_id, params.tenant_id, params.current_network);
        if (isNone(planDetail)) {
            context.log('GetKaisenPlanDetailAPI: planDetail is not found!');
            responseWithError(context, CommonError.BAD_REQUEST, ERROR_TEXT[ERROR_CODE.GET_KAISEN_PLAN_DETAIL_API_FAIL], ERROR_CODE.GET_KAISEN_PLAN_DETAIL_API_FAIL, null);
            return;
        }

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                ...planDetail,
            },
        };
    } catch (exception) {
        context.log.error('GetKaisenPlanDetailAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, 'Cannot connect to api server', ERROR_CODE.GET_KAISEN_PLAN_DETAIL_API_FAIL, null);
        return;
    }
};

/**
 * @param {number} roleId
 * @returns {boolean}
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
