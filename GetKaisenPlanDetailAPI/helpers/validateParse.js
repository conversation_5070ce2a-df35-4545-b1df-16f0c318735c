const Joi = require('joi');
const { ERROR_CODE } = require('./errorHelper');

const getKaisenPlanDetailSchema = Joi.object({
    plan_id: Joi.alternatives()
        .try(Joi.number(), Joi.string())
        .required()
        .messages({
            'any.required': ERROR_CODE.PLAN_ID_EMPTY, //プランIDの値がない
            'string.empty': ERROR_CODE.PLAN_ID_EMPTY, //プランIDの値がない
        }),
    tenant_id: Joi.string()
        .required()
        .messages({
            'any.required': ERROR_CODE.TENANT_ID_EMPTY, //テナントIDの値がない
            'string.empty': ERROR_CODE.TENANT_ID_EMPTY, //テナントIDの値がない
        }),
    current_network: Joi.string()
        .optional(),
}).unknown();

/**
 *
 * @param {object} body
 * @returns {{error: null|string, params: {plan_id: string or number, tenant_id: string, current_network: option(string)}}}
 */
const validateAndParseGetKaisenPlanDetailRequest = (body) => {
    let result = { error: null, params: {} };
    const { error } = getKaisenPlanDetailSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
    } else {
        result.params.plan_id = parseInt(body.plan_id);
        result.params.tenant_id = body.tenant_id;
        result.params.current_network = body.current_network ?? 'LTE';
    }
    return result;
};

module.exports = {
    validateAndParseGetKaisenPlanDetailRequest,
};
