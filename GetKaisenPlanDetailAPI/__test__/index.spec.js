const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { HEADER_KEY } = require('../../__shared__/constants/headerKeys');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const { isNone } = require('../../__shared__/helpers/baseHelper');
let expect = chai.expect;

describe('GetKaisenPlanDetailAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            plan_id: '90025',
            tenant_id: 'TSA000',
            current_network: 'LTE',
        },
    };

    describe('GetKaisenPlanDetailAPI with normal request', function () {
        it('gets kasisen plan detail', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
        });
    });

    describe('GetKaisenPlanDetailAPI with bad request', function () {
        it('returns permission error if tenantId is not set', async () => {
            const request = _.cloneDeep(__request);
            delete request.headers[HEADER_KEY.TENANT_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
            expect(context.res.body.nttc_mvno_error_code).equal('991002');
        });

        it('returns permission error if roleId is not set', async () => {
            const request = _.cloneDeep(__request);
            delete request.headers[HEADER_KEY.ROLE_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
            expect(context.res.body.nttc_mvno_error_code).equal('991002');
        });

        it('returns permission error if roleId is tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            request.headers[HEADER_KEY.TENANT_ID] = request.body.tenant_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
            expect(context.res.body.nttc_mvno_error_code).equal('991002');
        });

        it('returns 994057 if tenant_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
            //error message should not be empty
            expect(isNone(context.res.body.nttc_mvno_error_msg)).equal(false);
        });

        it('returns error if tenant_id is not string', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = 15;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
        });

        it('returns 994091 if plan_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.plan_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994091');
            //error message should not be empty
            expect(isNone(context.res.body.nttc_mvno_error_msg)).equal(false);
        });

        it('returns error if plan_id is not number or string', async () => {
            const request = _.cloneDeep(__request);
            request.body.plan_id = ['90012'];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
        });
    });
});
