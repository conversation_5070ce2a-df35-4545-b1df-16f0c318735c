//read content from txt file
const fs = require('fs');
const path = require('path');

module.exports = async function (context) {
    try {
        let versionHash = '',
            versionSharedHash = '',
            buildDate = '';
        try {
            versionHash = fs.readFileSync(path.join(__dirname, '../__version__'), 'utf8');
            versionSharedHash = fs.readFileSync(path.join(__dirname, '../__version_shared__'), 'utf8');
            buildDate = fs.readFileSync(path.join(__dirname, '../__build_date__'), 'utf8');
        } catch (e) {
            versionHash = 'No version file found';
            versionSharedHash = 'No version file found';
            buildDate = 'No build date file found';
        }
        const healthcheck = {
            uptime: process.uptime(),
            message: 'OK',
            timestamp: Date.now(),
            ver: versionHash.trim().replace(/\r?\n|\r/g, ''),
            verShared: versionSharedHash.trim().replace(/\r?\n|\r/g, ''),
            buildDate: buildDate.trim().replace(/\r?\n|\r/g, ''),
        };
        context.res = {
            // status: 200, /* Defaults to 200 */
            body: healthcheck,
        };
    } catch (e) {
        context.res = {
            status: 503,
            body: e,
        };
    }
};
