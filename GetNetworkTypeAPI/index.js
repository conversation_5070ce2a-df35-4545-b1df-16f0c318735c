const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseGetNetworkType } = require('./helpers/validateParse');
const pgLines = require('../__shared__/pgModels/lines');
const pgPlans = require('../__shared__/pgModels/plans');
const { ERROR_MAP } = require('./helpers/errorHelper');

module.exports = async function (context, req) {
    try {
        context.log('GetNetworkTypeAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        const { error, errorMessage, params } = validateAndParseGetNetworkType(req.body ?? {});
        if (error) {
            context.log('GetNetworkTypeAPI invalid parameter', error, errorMessage);
            responseWithError(context, CommonError.BAD_REQUEST, errorMessage, error, null);
            return;
        }
        const tenant = getAllowedTenant(roleId, tenantId, params.tenantId);
        if (!tenant) {
            context.log('GetNetworkTypeAPI invalid tenant parameter');
            responseWithError(
                context,
                CommonError.INVALID_USER,
                'invalid parameter',
                ERROR_MAP.INVALID_PARAMETER,
                null
            );
            return;
        }

        await mongoose.init(context);
        const [nwType, kaisenBelongsToTenant] = await Promise.all([
            pgPlans.getNWType(context, params.lineNo),
            pgLines.isKaisenNoBelongToTenant(context, { tenantId: tenant, kaisenNo: params.lineNo }),
        ]);

        if (isNone(nwType) || !kaisenBelongsToTenant) {
            context.log('GetNetworkTypeAPI line id not found', {
                params,
                tenantId,
                roleId,
            });
            responseWithError(
                context,
                CommonError.INVALID_USER,
                'line id not found',
                ERROR_MAP.KAISEN_INFO_NOT_FOUND,
                null
            );
            return;
        }

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                contract_type: nwType.contractType ?? '',
                network: nwType.network,
                sms_enabled: nwType.smsEnabled,
                voice_flag: nwType.voiceFlag,
                sim_flag: nwType.simFlag,
            },
        };
    } catch (exception) {
        context.log.error('GetNetworkTypeAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};

/**
 * @param {number} roleId
 * @param {string} sessionTenant
 * @param {string} paramTenant
 */
const getAllowedTenant = (roleId, sessionTenant, paramTenant) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
            return paramTenant;
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
        case MAWP_ROLE_ID.TEMPO_USER:
            return sessionTenant;
        default:
            return null;
    }
};
