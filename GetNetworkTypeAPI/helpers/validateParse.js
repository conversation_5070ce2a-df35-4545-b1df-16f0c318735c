const Joi = require('joi');
const { ERROR_MAP } = require('./errorHelper');

const getNetworkTypeSchema = Joi.object({
    tenant_id: Joi.string().required().messages({
        'any.required': ERROR_MAP.INVALID_PARAMETER,
        'string.empty': ERROR_MAP.INVALID_PARAMETER,
    }),
    kaisen_no: Joi.string().required().messages({
        'any.required': ERROR_MAP.INVALID_PARAMETER,
        'string.empty': ERROR_MAP.INVALID_PARAMETER,
    }),
}).unknown();

const validateAndParseGetNetworkType = (body) => {
    const result = {
        error: '',
        errorMessage: '',
        params: {
            lineNo: '',
            tenantId: '',
        },
    };
    const { error } = getNetworkTypeSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
        if (result.error != ERROR_MAP.INVALID_PARAMETER) {
            result.errorMessage = result.error;
            result.error = ERROR_MAP.INVALID_PARAMETER;
        } else {
            result.errorMessage = 'invalid parameter';
        }
    } else {
        result.params.tenantId = body.tenant_id;
        result.params.lineNo = body.kaisen_no;
    }
    return result;
};

module.exports = {
    validateAndParseGetNetworkType,
};
