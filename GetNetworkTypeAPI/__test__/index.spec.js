const _ = require('lodash');
let chai = require('chai');
const sinon = require('sinon');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const pgLines = require('../../__shared__/pgModels/lines');
const pgPlans = require('../../__shared__/pgModels/plans');
let expect = chai.expect;

describe('GetNetworkTypeAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'TSA000',
            kaisen_no: '08006135643',
            // kaisen_no: '09020210913', 5G(NSA)
        },
    };

    describe('GetNetworkTypeAPI with normal request', function () {
        it('returns 200', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            // check response object structure is proper
            const keys = ['sim_flag', 'network', 'sms_enabled', 'voice_flag', 'contract_type'];
            const responseKeys = Object.keys(context.res.body);
            expect(keys.every((k) => responseKeys.includes(k))).equal(true);
        });
    });

    describe('GetNetworkTypeAPI with bad request', function () {
        const sandbox = sinon.createSandbox();

        afterEach(() => {
            sandbox.restore();
        });

        it('returns error if tenant parameter is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = '';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('991022');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.tenant_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('991022');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if lineNo parameter is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.kaisen_no = '';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('991022');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.kaisen_no;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('991022');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if tenant parameter is different with session data', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'CON000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101); // invalid user
            expect(context.res.body.nttc_mvno_error_code).equal('993015'); // line idnot found
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if line is not belong to tenant', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(pgLines, 'isKaisenNoBelongToTenant').resolves(false);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993015');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if line nwtype is not found', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(pgPlans, 'getNWType').resolves({});

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993015');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
