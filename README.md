# MVNO 外部 API- AZURE

## ローカル開発環境

### pull code with submodules

```
<NAME_EMAIL>:playnext-lab/MVNO_CORE_SERVICE.git --recursive
```

### 環境を準備

```
Azure Functions Core Tools v4
node v20.18.3
npm 10.8.2
mongo 4.2
postgre 14
```

-   Nodejs: https://nodejs.org/en/
-   Mongo: https://www.mongodb.com/docs/manual/installation/
-   Azure Functions Core Tools: https://github.com/Azure/azure-functions-core-tools
-   Postgre: https://www.postgresql.org/download/

### プロジェクトのパッケージをインストール

```
npm install
```

### ローカルで関数を実行する

create local.settings.json file from local.settings.json\_\_sample

```
npm start
```

http://localhost:7702/api/core-service/healthcheck
にアクセスして確認できます。

### 単体テスト

1. 共通の関数（関数レベル）

```
__tests__
```

フォルダに、**shared**の中の各関数それぞれに関数を開発終わってから、単体テストを整備する。

→ 全てテスト実行:

```
npm run test-unit
```

→ 指定ファイルをテスト実行:

```
npm run test-unit-file --file=<file name>

ex:
npm run test-unit-file --file=validations.requestHeaeder.spec.js
```

2. API の関数（API レベル）

```
<関数名>Funcの__test___
```

フォルダに、API のロジックを開発終わってから、単体テストを整備する。

→ 全てテスト実行:

```
npm run test-func
```

→ 指定 API をテスト実行:

```
npm run test-func-single --api=<api name>

ex:
npm run test-func-single --api=MNPInExternalAPI
```

## 利用パッケージ

-   [axios](https://github.com/axios/axios). Promise based HTTP client for the browser and node.js
-   [Day.js](https://day.js.org/en/). Parses, validates, manipulates, and displays dates and times
-   [joi](https://joi.dev/api/?v=17.6.0). Describe your data using a simple, intuitive, and readable language.
-   [mongoose](https://mongoosejs.com/). Elegant MongoDB object modeling for Node.js

-   [mocha](https://www.npmjs.com/package/mocha). Simple, flexible, fun JavaScript test framework for Node.js & The Browser.
-   [chai](https://github.com/axios/axios). Chai is an assertion library, similar to Node's built-in assert. It makes testing much easier by giving you lots of assertions you can run against your code.
-   [sinon](https://sinonjs.org/). Standalone test spies, stubs and mocks for JavaScript.

## フォルダ-構造

    .
    ├── __shared__                      # 関数に共有フォルダ
    │   ├── constants                   # 定数値定義
    │   ├── database                    # データベース定義
    │   └── helpers                     # 独立したロジックでビジネスロジック
    │   └── models                      # Mongooseのモデル定義
    │   └── pgModels                    # Postgreの関数定義
    │   └── services                    # 外部のサービス定義
    │   └── utils                       # 独立したロジック
    │   └── validations                 # バリデーション定義
    ├── __tests__                       # Test files (alternatively `spec`)
    │   ├── hooks.js                    # mocha hook
    │   ├── defaultContext.js           # Azure Function Test context 定義
    │   ├── xxx.spect.js                # 各単体テストファイル
    │   ├──
    ├── <xxx>Function                   # Azure Function folder
    │   ├── __test__                    # Test files (alternatively `spec`)
    │   ├──    └── index.spec.js        # Test file (Function Level (API))
    │   ├──
    └── ...