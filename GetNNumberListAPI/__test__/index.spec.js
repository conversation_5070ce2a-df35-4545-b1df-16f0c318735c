const _ = require('lodash');
const sinon = require('sinon');
const httpFunction = require('../index');
const helpers = require('../../__shared__/helpers/baseHelper');
const context = require('../../__shared__/__tests__/defaultContext');
const { expect } = require('chai');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const tenantNNumber = require('../../__shared__/pgModels/tenantNNumbers');

describe('GetNNumberListAPI', () => {
    const sandbox = sinon.createSandbox();

    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'TSA000',
            service: 0,
        },
    };

    afterEach(() => {
        sandbox.restore();
    });

    describe('GetNNumberListAPI with normal request', () => {
        it('should not return an error when the execution was successful', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(tenantNNumber, 'findDaihyoNNumbersByTenantId').resolves(['N000000000']);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.deep.equal(['N000000000']);
        });

        it('should not return an error when the execution was successful (CON000 user and FULL)', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = 'CON000';
            request.body.service = 2;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'CON000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.deep.equal(['N190173862']);
        });

        it('should not return an error when the execution was successful (CON000 user and LITE)', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = 'CON000';
            request.body.service = 1;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'CON000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.deep.equal(['N141096449']);
        });

        it('should be LITE is a default value (CON000 user and service parameter does not exist)', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = 'CON000';
            delete request.body.service;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'CON000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.deep.equal(['N141096449']);
        });

        it('should not return an error when the execution was successful (CON000 user and ALL)', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = 'CON000';
            request.body.service = 9;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'CON000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.deep.equal(['N141096449', 'N190173862']);
        });
    });

    describe('GetNNumberListAPI with bad request', () => {
        it('should return an error when tenantId does not exist in session', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('サーバにて処理できません。');
            expect(context.res.body.nttc_mvno_error_code).equal('990002');
        });

        it('should return an error when tenantId is not sent and the user is a super user', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('正しくないパラメータが含まれています。');
            expect(context.res.body.nttc_mvno_error_code).equal('991024');
        });
    });
});
