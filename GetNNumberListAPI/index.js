const baseHelper = require('../__shared__/helpers/baseHelper');
const { CommonError } = require('../__shared__/constants/commonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { mvnoServiceFromCode, MVNO_SERVICES } = require('../__shared__/constants/mvnoServicesConstants');
const tenantNNumber = require('../__shared__/pgModels/tenantNNumbers');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const appConfig = require('../__shared__/config/appConfig');
const portalConfig = appConfig.getPortalConfig();

module.exports = async function (context, req) {
    try {
        context.log('GetNNumberAPI START');
        const { roleId, tenantId, user } = baseHelper.getSessionData(context, req);

        if (baseHelper.isNone(roleId) || baseHelper.isNone(tenantId) || baseHelper.isNone(user)) {
            context.log.error('GetNNumberAPI session error');
            responseWithError(context, CommonError.INVALID_USER, 'Session is empty.', '990002', null);
            return;
        }

        const tenantID = req.body['tenant_id'];
        const serviceCode = req.body['service'] || 0;
        const serviceObj = mvnoServiceFromCode(serviceCode);

        let tenant;

        switch (roleId) {
            case MAWP_ROLE_ID.TEMPO_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                tenant = tenantId;
                break;
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                tenant = tenantID;
                break;
            default:
                break;
        }

        if (baseHelper.isNone(tenant)) {
            context.log('GetNNumberAPI invalid parameter');
            responseWithError(context, CommonError.INVALID_USER, 'Invalid parameter', '991024', null);
            return;
        } else {
            if (tenant == 'CON000') {
                let nBanArr;
                const nBan = portalConfig.COCN_DAIHYOU_NNUMBER || 'N141096449';
                const nBanFullMVNO = portalConfig.COCN_DAIHYOU_NNUMBER_FOR_FULL_MVNO.at(0) || 'N190173862';

                switch (serviceObj) {
                    case MVNO_SERVICES.FULL:
                        nBanArr = [nBanFullMVNO];
                        break;
                    case MVNO_SERVICES.LITE:
                        nBanArr = [nBan];
                        break;
                    case MVNO_SERVICES.ALL:
                        nBanArr = [nBan, nBanFullMVNO];
                        break;
                    default:
                        nBanArr = [nBan];
                        break;
                }
                context.res = {
                    status: 200,
                    body: {
                        error: CommonError.NO_ERROR,
                        result: nBanArr,
                    },
                };
                return;
            } else {
                const nBanArr = await tenantNNumber.findDaihyoNNumbersByTenantId(context, {
                    tenantId: tenant,
                    serviceType: serviceObj,
                });
                context.log('findDaihyoNNumbersByTenantId: ', JSON.stringify(nBanArr));
                context.res = {
                    status: 200,
                    body: {
                        error: CommonError.NO_ERROR,
                        result: nBanArr,
                    },
                };
                return;
            }
        }
    } catch (exception) {
        context.log.error('GetNNumberAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
