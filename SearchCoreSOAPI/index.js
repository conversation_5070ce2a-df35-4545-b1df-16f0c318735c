const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { SUPER_TENANT_ID } = require('../__shared__/constants/specialDefinitions');
const { ERROR_MAP } = require('./helpers/errorHelper');
const { validateAndParseSearchCoreSo } = require('./helpers/validateParse');
const { searchServiceOrder } = require('../__shared__/pgModels/serviceOrder');

module.exports = async function (context, req) {
    try {
        context.log('SearchCoreSOAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }
        const isSuperTenant = tenantId === SUPER_TENANT_ID;
        let tenantIds = [];
        switch (roleId) {
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                // 代表ユーザー、オペレーターと閲覧ユーザーは自テナントのみ選択できる
                tenantIds = [tenantId];
                break;
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                tenantIds = (req.body ?? {}).tenant_ids;
                break;
            default:
                context.log.error('SearchCoreSOAPI Invalid Tenant ID');
                responseWithError(CommonError.BAD_REQUEST, 'Invalid Parameter', ERROR_MAP.TENANT_ERROR, null);
                return;
        }

        const { error, params } = validateAndParseSearchCoreSo(req.body ?? {}, tenantIds, isSuperTenant);
        if (error) {
            context.log('SearchCoreSOAPI parameter error ', error);
            const errorCode = error.length == 6 ? error : '991022';
            const errorMsg = error.length == 6 ? null : error;
            responseWithError(context, CommonError.BAD_REQUEST, 'Invalid Parameter', errorCode, errorMsg);
            return;
        }

        await mongoose.init(context);
        const { results, count } = await searchServiceOrder(context, params);

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                results,
                count,
            },
        };
    } catch (exception) {
        context.log.error('SearchCoreSOAPI - exception: ', exception);
        responseWithError(
            context,
            CommonError.SERVER_ERROR,
            'Cannot connect to api server ',
            ERROR_MAP.SO_SEARCH_FAIL,
            null
        );
        return;
    }
};

/**
 * @param {number} roleId
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
            return true;
        default:
            return false;
    }
};
