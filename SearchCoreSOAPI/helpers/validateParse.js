const Joi = require('joi');
const { ORDER_STATUS_LIST, FULL_ORDER_TYPE, SUB_ORDER_TYPE } = require('../../__shared__/constants/orderConstants');
const { LINE_NO_REGEX } = require('../../__shared__/constants/simConstants');
const { isNone, removeNullValue } = require('../../__shared__/helpers/baseHelper');
const { ERROR_MAP } = require('./errorHelper');

/** @typedef {import('../../__shared__/pgModels/serviceOrder').SoSearchData} SoSearchData */

const searchCoreSOSchema = Joi.object({
    line_no: Joi.string().pattern(LINE_NO_REGEX).messages({
        'string.pattern.base': ERROR_MAP.LINENO_FORMAT,
    }),
    so_id: Joi.string()
        .pattern(/^[a-zA-Z0-9]{1,15}$/)
        .messages({
            'string.pattern.base': ERROR_MAP.SOID_FORMAT,
        }),
    tenant_ids: Joi.array().items(Joi.string()),
    order_status: Joi.array()
        .items(Joi.string().valid(...ORDER_STATUS_LIST))
        .messages({
            'any.only': ERROR_MAP.ORDER_STATUS_NOT_EXISTS,
        }),
    order_type: Joi.string(),
    order_date_from: Joi.number(),
    order_date_to: Joi.number(),
    exec_date_from: Joi.number(),
    exec_date_to: Joi.number(),

    // ページングまたはソートのパラメーター
    limit: Joi.number(),
    offset: Joi.number(),
    sort_by: Joi.string(),
    sort_order: Joi.string(),
}).unknown();

/**
 *
 * @param {object} body
 * @param {Array<string>} tenantIds
 * @param {boolean} isSuperTenant
 * @returns {{error: string?, params: SoSearchData}}
 */
const validateAndParseSearchCoreSo = (body, tenantIds, isSuperTenant) => {
    let result = { error: null, params: {} };
    const { error } = searchCoreSOSchema.validate({ ...body, tenant_ids: tenantIds });
    if (error) {
        result.error = error.details[0].message;
    } else {
        const orderTypeList = isSuperTenant ? FULL_ORDER_TYPE : SUB_ORDER_TYPE;

        if (!isNone(body.order_type) && !orderTypeList.includes(body.order_type)) {
            result.error = ERROR_MAP.ORDER_TYPE_NOT_EXISTS;
        } else if (!validDateRange(body.order_date_from, body.order_date_to)) {
            result.error = ERROR_MAP.ORDER_DATE_RANGE_ERROR;
        } else if (!validDateRange(body.exec_date_from, body.exec_date_to)) {
            result.error = ERROR_MAP.EXEC_DATE_RANGE_ERROR;
        }
    }

    if (!result.error) {
        result.params = createParams(body, tenantIds, isSuperTenant);
    }
    return result;
};

/**
 *
 * @param {object} body
 * @param {Array<string>} tenantIds
 * @param {boolean} isSuperTenant
 */
const createParams = (body, tenantIds, isSuperTenant) => {
    /** @type {SoSearchData} */
    let params = removeNullValue({
        ...body,
        tenant_ids: tenantIds,
        isSuperTenant,
    });
    // 終了日＋1日
    if (!isNone(params.order_date_to)) {
        params.order_date_to += 86400;
    }
    if (!isNone(params.exec_date_to)) {
        params.exec_date_to += 86400;
    }
    return params;
};

/**
 * returns `false` if both `from` and `to` are defined,
 * _and_ `from > to`; otherwise returns `true`
 * @param {number} from
 * @param {number} to
 */
const validDateRange = (from, to) => {
    return isNone(from) || isNone(to) || from <= to + 86400; // 終了日＋1日
};

module.exports = {
    validateAndParseSearchCoreSo,
};
