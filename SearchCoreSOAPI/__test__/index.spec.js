const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const dayjs = require('dayjs');
const { HEADER_KEY } = require('../../__shared__/constants/headerKeys');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const { CommonError } = require('../../__shared__/constants/CommonError');
let expect = chai.expect;

describe('SearchCoreSOAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            limit: 50,
            offset: 0,
        },
    };

    describe('SearchCoreSOAPI with normal request', function () {
        it('returns SO array (super user)', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                so_id: '22', // %22%
                tenant_ids: ['CON000'],
                order_status: ['完了', '失敗'],
                order_type: 'クーポン追加',
                order_date_from: 1609426800, // 2021/01/01
                order_date_to: 1640876400, // 2021/12/31
                exec_date_from: 1609426800, // 2021/01/01
                exec_date_to: 1640876400, // 2021/12/31
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
        });

        it('filters SO based on session data for tenant roles', async () => {
            // ignoring tenant_ids parameter in body
            // first get SO for CON and TSA using super user
            const request = _.cloneDeep(__request);
            request.body.tenant_ids = ['CON000', 'TSA000'];
            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            const count = context.res.body.count; // TSA + CON

            // change login user
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TENANT_OPERATOR_USER;
            request.headers[HEADER_KEY.TENANT_ID] = 'TSA000';
            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');

            // length must be less than count (assuming CON > 0)
            expect(context.res.body.count).lessThan(count);
            expect(context.res.body.results.every((r) => r.tenant_id === 'TSA000')).equal(true);
        });
    });

    describe('SearchCoreSOAPI with bad request', function () {
        it('rejects invalid line number', async () => {
            const request = _.cloneDeep(__request);
            const testData = ['', '12345', '02012345', '080abcd1234'];
            for (const val of testData) {
                request.body.line_no = val;
                await httpFunction(context, request);
                expect(context.res.status).equal(200);
                expect(context.res.body.error).equal(CommonError.BAD_REQUEST);
            }
        });

        it('rejects invalid SO ID', async () => {
            const request = _.cloneDeep(__request);
            const testData = ['', '1234567890123456', '1234#$%'];
            for (const val of testData) {
                request.body.so_id = val;
                await httpFunction(context, request);
                expect(context.res.status).equal(200);
                expect(context.res.body.error).equal(CommonError.BAD_REQUEST);
            }
        });

        it('rejects invalid order status', async () => {
            const request = _.cloneDeep(__request);
            request.body.order_status = ['完了', 'cancelled'];
            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(CommonError.BAD_REQUEST);
        });

        it('rejects invalid order type', async () => {
            const request = _.cloneDeep(__request);
            request.body.order_type = 'some order type';
            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(CommonError.BAD_REQUEST);
        });

        it('rejects invalid order date range', async () => {
            const request = _.cloneDeep(__request);
            request.body.order_date_from = dayjs('2023-02-01T08:00:00').unix();
            request.body.order_date_to = dayjs('2023-01-01T08:00:00').unix();
            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(CommonError.BAD_REQUEST);
        });

        it('rejects invalid exec date range', async () => {
            const request = _.cloneDeep(__request);
            request.body.exec_date_from = dayjs('2023-02-01T08:00:00').unix();
            request.body.exec_date_to = dayjs('2023-01-01T08:00:00').unix();
            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(CommonError.BAD_REQUEST);
        });

        it('rejects API access for tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            request.headers[HEADER_KEY.TENANT_ID] = 'TSA000';

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(CommonError.PERMISSION_ERROR);
        });
    });
});
