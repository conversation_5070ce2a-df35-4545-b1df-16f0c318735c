const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
let expect = chai.expect;

describe('CheckKaisenNoBelongsToTenant', function () {
    // tenant TSA
    const lineOk = '08006135681';
    const lineNotOk = '08006135680';

    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'TSA000',
            kaisen_no: lineOk,
        },
    };

    describe('CheckKaisenNoBelongsToTenant with normal request', function () {
        it("returns true for tenant's kaisen", async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.is_belongs).equal(true);
        });

        it('returns false if other kaisenNo is given', async () => {
            const request = _.cloneDeep(__request);
            request.body.kaisen_no = lineNotOk;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.is_belongs).equal(false);
        });
    });

    describe('CheckKaisenNoBelongsToTenant with bad request', function () {
        it('returns error if tenant_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });

        it('returns error if kaisen_no is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.kaisen_no;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });
    });
});
