const { CommonError } = require('../__shared__/constants/CommonError');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { validateAndParseCheckKaisenNoParams } = require('./helpers/validateParse');
const pgLines = require('../__shared__/pgModels/lines');

module.exports = async function (context, req) {
    try {
        context.log('CheckKaisenNoBelongsToTenant START');

        // const { roleId, tenantId, user } = getSessionData(context, req);

        const { error, params } = validateAndParseCheckKaisenNoParams(context, req.body ?? {});
        if (error) {
            context.log('CheckKaisenNoBelongsToTenant invalid parameter', req.body);
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', '991022', null);
            return;
        }

        await mongoose.init(context);
        const isOwned = await pgLines.isKaisenNoBelongToTenant(context, {
            tenantId: params.tenant_id,
            kaisenNo: params.kaisen_no,
        });

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                is_belongs: isOwned,
            },
        };
    } catch (exception) {
        context.log.error('CheckKaisenNoBelongsToTenant - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
