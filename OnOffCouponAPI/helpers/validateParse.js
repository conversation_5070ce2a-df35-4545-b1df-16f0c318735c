const Joi = require('joi').extend(require('@joi/date'));
const { ACTIVATION_STATUS, USAGE_STATUS } = require('../../__shared__/constants/lineConstants');
const pgLines = require('../../__shared__/pgModels/lines');
const { ERROR_MAP, ERROR_TEXT } = require('./errorHelper');

const onOffCouponSchema = Joi.object({
    line_no: Joi.string().required().messages({
        'any.required': ERROR_MAP.LINE_NO_EMPTY,
        'string.empty': ERROR_MAP.LINE_NO_EMPTY,
    }),
    tenant_id: Joi.string().required().messages({
        'any.required': ERROR_MAP.TENANT_ID_EMPTY,
        'string.empty': ERROR_MAP.TENANT_ID_EMPTY,
    }),
    coupon_on_off: Joi.string().required().valid(...Object.values(ACTIVATION_STATUS)).messages({
        'any.required': ERROR_MAP.COUPON_EMPTY,
        'string.empty': ERROR_MAP.COUPON_EMPTY,
        'any.only': ERROR_MAP.COUPON_INVALID,
    }),
    plan_id: Joi.number().required().messages({
        'any.required': ERROR_MAP.PLAN_ID_EMPTY,
    }),
    reserve_date: Joi.date().format('YYYY/MM/DD').raw().messages({
        'string.base': ERROR_MAP.RESERVE_DATE_INVALID,
        'date.format': ERROR_MAP.RESERVE_DATE_INVALID,
    }),
    option_plan_id: Joi.number().required().messages({
        'any.required': ERROR_MAP.OPTION_PLAN_EMPTY,
    }),
}).unknown();

/**
 * Validate request parameters
 * @param {object} context
 * @param {object} body
 * @returns {{error: string?, errorMessage: string, params: {
 *  tenantId: string,
 *  lineNo: string,
 *  potalPlanID: string,
 *  optionPlanId: string,
 *  couponOnOff: string,
 *  reserve_date: string | undefined,
 * }}}
 */
const validateAndParseOnOffCouponParams = (body) => {
    const result = { error: null, errorMessage: '', params: {} };
    const { error } = onOffCouponSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
        if (ERROR_TEXT[result.error]) {
            result.errorMessage = ERROR_TEXT[result.error];
        } else {
            result.errorMessage = result.error;
            result.error = '991022';
        }
        return result;
    }

    // build params object
    result.params = {
        tenantId: body.tenant_id,
        lineNo: body.line_no,
        potalPlanID: `${body.plan_id}`,
        optionPlanId: `${body.option_plan_id}`,
        couponOnOff: body.coupon_on_off,
    };
    if (body.reserve_date) {
        result.params.reserve_date = `${body.reserve_date} 04:00`;
    }
    return result;
};

/**
 * @param {object} context
 * @param {string} lineNo
 * @param {string} tenantId
 * @returns {Promise<{error: string?, errorMessage: string}>}
 */
const validateOnOffCouponParamsWithDbConnection = async (context, lineNo, tenantId) => {
    const result = { error: null, errorMessage: '' };
    const [isOwned, usageStatusInfo] = await Promise.all([
        pgLines.isKaisenNoBelongToTenant(context, { tenantId, kaisenNo: lineNo }),
        pgLines.getUsageStatusByLineNo(context, lineNo),
    ]);

    if (!isOwned) {
        result.error = ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT;
        result.errorMessage = ERROR_TEXT[ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT];
    } else if (usageStatusInfo == USAGE_STATUS.SUSPENDED) {
        // STEP 14.0 if lineNo is full mvno and usageStatus is 2, not to allow using couponOnOff api
        result.error = ERROR_MAP.USAGE_STATUS_IS_SUSPEND;
        result.errorMessage = ERROR_TEXT[ERROR_MAP.USAGE_STATUS_IS_SUSPEND];
    }
    return result;
};

module.exports = {
    validateAndParseOnOffCouponParams,
    validateOnOffCouponParamsWithDbConnection,
};
