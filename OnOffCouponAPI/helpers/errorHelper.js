const ERROR_MAP = {
    LINE_NO_EMPTY: '994055',
    TENANT_ID_EMPTY: '994057',
    COUPON_EMPTY: '994090',
    COUPON_INVALID: '994090',
    LINE_NO_NOT_LINKED_TO_TENANT: '994088',
    PLAN_ID_EMPTY: '994091',
    OPTION_PLAN_EMPTY: '994092',
    RESERVE_DATE_INVALID: '994087',
    USAGE_STATUS_IS_SUSPEND: '997011',
    COUPON_API_ERROR: '994089',
};

const ERROR_TEXT = {
    [ERROR_MAP.LINE_NO_EMPTY]: 'line_no param is required!',
    [ERROR_MAP.TENANT_ID_EMPTY]: 'tenant_id param is required!',
    [ERROR_MAP.COUPON_EMPTY]: 'status param is required!',
    [ERROR_MAP.COUPON_INVALID]: 'status param is not valid',
    [ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT]: 'テナントに回線番号が紐づいていません！',
    [ERROR_MAP.PLAN_ID_EMPTY]: 'planId param is required!',
    [ERROR_MAP.OPTION_PLAN_EMPTY]: 'status param is required!',
    [ERROR_MAP.RESERVE_DATE_INVALID]: 'invalid reserve_date',
    [ERROR_MAP.USAGE_STATUS_IS_SUSPEND]: 'usageStatus is suspendChuu',
    [ERROR_MAP.COUPON_API_ERROR]: 'Cannot connect to api server',
};

module.exports = {
    ERROR_MAP,
    ERROR_TEXT,
};
