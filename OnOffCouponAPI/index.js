const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const {
    validateAndParseOnOffCouponParams,
    validateOnOffCouponParamsWithDbConnection,
} = require('./helpers/validateParse');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const CoreService = require('../__shared__/services/core.service');
const pgServiceOrder = require('../__shared__/pgModels/serviceOrder');
const { ERROR_MAP } = require('./helpers/errorHelper');
const { findOPFErrorMsgByOPFErrorCode } = require('../__shared__/helpers/configHelper');

module.exports = async function (context, req) {
    try {
        context.log('OnOffCouponAPI START');
        const { roleId, tenantId, user } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, errorMessage, params } = validateAndParseOnOffCouponParams(req.body ?? {});
        if (error) {
            context.log('OnOffCouponAPI validation error: ', errorMessage);
            responseWithError(context, CommonError.BAD_REQUEST, errorMessage, error, null);
            return;
        }

        await mongoose.init(context);

        const dbValidation = await validateOnOffCouponParamsWithDbConnection(context, params.lineNo, params.tenantId);
        if (dbValidation.error) {
            context.log('OnOffCouponAPI validation error: ', dbValidation.errorMessage);
            responseWithError(context, CommonError.BAD_REQUEST, dbValidation.errorMessage, dbValidation.error, null);
            return;
        }

        const apiResponse = await CoreService.callAPICoreLineCoupon(context, params);
        const { processCode, apiProcessID } = apiResponse.responseHeader;

        // if (roleId == MAWP_ROLE_ID.TEMPO_USER) {
        //     // this block won't be executed because tempo user isn't allowed
        //     await pgServiceOrder.updateExecSO(context, apiProcessID, tenantId, user.internalId);
        // } else {
        //     await pgServiceOrder.updateExecSO(context, apiProcessID, tenantId, user.name);
        // }
        let userId = user.plusId;
        if (typeof userId === 'string' && userId.length > 16) {
            userId = userId.slice(0, 16);
        }
        await pgServiceOrder.updateExecSO(context, apiProcessID, tenantId, userId);

        if (processCode !== '000000') {
            context.log('OnOffCouponAPI API Call Fail: ', processCode);
            responseWithError(
                context,
                CommonError.SERVER_ERROR,
                'Cannot connect to api server',
                processCode,
                findOPFErrorMsgByOPFErrorCode(ERROR_MAP.COUPON_API_ERROR)
            );
            return;
        }

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
            },
        };
    } catch (exception) {
        context.log.error('OnOffCouponAPI - exception: ', exception);
        responseWithError(
            context,
            CommonError.SERVER_ERROR,
            'Cannot connect to api server',
            ERROR_MAP.COUPON_API_ERROR,
            null
        );
        return;
    }
};

/**
 * @param {number} roleId
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
