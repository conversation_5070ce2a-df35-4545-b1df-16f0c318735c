const _ = require('lodash');
let chai = require('chai');
const sinon = require('sinon');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const pgServiceOrder = require('../../__shared__/pgModels/serviceOrder');
let expect = chai.expect;

describe('OnOffCouponAPI', function () {
    const __request = {
        headers: getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TSA000', { name: 'TSAtest' }),
        body: {
            tenant_id: 'TSA000',
            line_no: '08020210339',
            coupon_on_off: '0',
            plan_id: 90025,
            option_plan_id: 40401,
            reserve_date: '2023/03/01',
        },
    };

    describe('OnOffCouponAPI with normal request', function () {
        const sandbox = sinon.createSandbox();

        // eslint-disable-next-line no-undef
        before(() => {
            // make updateExecSO do nothing
            sandbox.stub(pgServiceOrder, 'updateExecSO').resolves();
        });

        // eslint-disable-next-line no-undef
        after(() => {
            sandbox.restore();
        });

        it('returns no error for valid request', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
        });
    });

    describe('OnOffCouponAPI with bad request', function () {
        it('rejects request for tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000', { internalId: '2209' });

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
        });

        it('returns error if line_no is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.line_no = '';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.line_no;
            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if tenant_id is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = '';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.tenant_id;
            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if coupon status is not defined', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.coupon_on_off;

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994090');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if coupon status is not valid', async () => {
            const request = _.cloneDeep(__request);
            request.body.coupon_on_off = '5';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994090');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if plan_id is not defined', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.plan_id;

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994091');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if reserve_date format is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.reserve_date = '2022-03-01';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994087');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if option_plan_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.option_plan_id;

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994092');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if lineNo is not linked to tenant', async () => {
            const request = _.cloneDeep(__request);
            request.body.line_no = '08006134000'; // TST000

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994088');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if line is suspended', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.SUPER_USER, 'ZZZ000', { name: 'ZZZ@test0' });
            request.body.tenant_id = 'TST000';
            request.body.line_no = '02006101961';
            request.body.coupon_on_off = '0';
            request.body.plan_id = 40176;

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('997011');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
