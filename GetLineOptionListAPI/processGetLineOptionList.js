const _ = require('lodash');
const { LINE_OPTION_TYPE_NAME } = require('../__shared__/constants/daihyouBangoConstants');

const LineOptionsPg = require('../__shared__/pgModels/lineOptions');
const { getModifiedLineOptions } = require('../__shared__/helpers/daihyouBangoHelper');

const processGetLineOptionList = async (context, tenantId, planId, nNo) => {
    const { modifiedLineOptions } = await getModifiedLineOptions(context, {
        tenantId,
        planId,
        nNo,
    });
    return getFormattedLineOptions(modifiedLineOptions);
};

/**
 * returns line options for `/lineoptions` API
 * @param {Array<import('../__shared__/pgModels/lineOptions').lineOptionInfo>} modifiedLineOptions
 */
const getFormattedLineOptions = (modifiedLineOptions) => {
    const lineTypeGroup = _.groupBy(modifiedLineOptions, _.property('line_option_type'));
    const tmpMap = _.transform(
        lineTypeGroup,
        (result, options, type) => {
            if (options.length < 2) {
                result[type] = options.map((o) => [o.line_option_id, 'あり']).concat([['', 'なし']]);
            } else {
                result[type] = options.map((o) => [o.line_option_id, o.option_plan_name]).concat([['', 'なし']]);
            }
        },
        {} // initial result value
    );

    // tmpMap = {
    //  '1': [["LO001": "LO001Name"], ["LO002": "LO002Name"], ["": "なし"]],
    //  '2': [["LO010": "あり"], ["": "なし"]],
    //  ...
    // }

    const sortedMap = Object.entries(tmpMap).sort((a, b) => {
        const aOrder = LineOptionsPg.getDisplayOrderForLineType(parseInt(a[0]));
        const bOrder = LineOptionsPg.getDisplayOrderForLineType(parseInt(b[0]));
        return aOrder - bOrder;
    });

    // sortedMap = [
    //   [ '2', [ [ 'LO010', 'あり' ], [ '', 'なし' ] ] ],
    //   [ '3', [ [ 'LO011', 'あり' ], [ '', 'なし' ] ] ],
    //   [ '1', [ ["LO001": "LO001Name"], ["LO002": "LO002Name"], ["": "なし"] ] ],
    //   ...
    // ]

    return sortedMap.map((row) => {
        /** @type {[string, string[]]} */
        const [optionTypeId, optionTypeValues] = row;
        /** @type {string} */
        const optionTitle = LINE_OPTION_TYPE_NAME[optionTypeId] || '';
        return {
            line_option_type: parseInt(optionTypeId),
            line_option_title: optionTitle,
            line_option_values: optionTypeValues.map((val) => ({
                line_option_id: val[0],
                line_option_name: val[1],
            })),
        };
    });
};

module.exports = {
    processGetLineOptionList,
    getFormattedLineOptions,
};
