const Joi = require('joi');

const lineOptionListSchema = Joi.object({
    tenant_id: Joi.string().required(),
    plan_id: Joi.number().required(),
    N_no: Joi.string(),
}).unknown();

/**
 *
 * @param {object} context
 * @param {object} body
 * @returns {{error:string|null, params:{tenant_id:string,plan_id:number,N_no:string?}}}
 */
const validateAndParseGetLineOptionListParams = (context, body) => {
    let result = { error: null, params: {} };
    const { error } = lineOptionListSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
    } else {
        result.params = {
            tenant_id: body.tenant_id,
            plan_id: body.plan_id,
            N_no: body.N_no,
        };
    }
    return result;
};

module.exports = {
    validateAndParseGetLineOptionListParams,
};
