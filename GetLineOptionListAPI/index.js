const { CommonError } = require('../__shared__/constants/CommonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');

const { validateAndParseGetLineOptionListParams } = require('./helpers/validateParse');
const { processGetLineOptionList } = require('./processGetLineOptionList');

module.exports = async function (context, req) {
    try {
        context.log('GetLineOptionListAPI START');
        const { roleId, tenantId } = getSessionData(context, req);

        const { error, params } = validateAndParseGetLineOptionListParams(context, req.body ?? {});
        if (error) {
            context.log('GetLineOptionListAPI parameter error:', error);
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', '991025', null);
            return;
        }

        let tenant = null;
        switch (roleId) {
            case MAWP_ROLE_ID.TEMPO_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                tenant = tenantId;
                break;
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                tenant = params.tenant_id;
        }
        if (isNone(tenant) || isNone(params.plan_id)) {
            context.log('GetLineOptionListAPI invalid parameter', { tenant, planId: params.plan_id });
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', '991025', null);
            return;
        }

        await mongoose.init(context);
        const result = await processGetLineOptionList(context, tenant, params.plan_id, params.N_no);

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                result,
            },
        };
    } catch (exception) {
        context.log.error('GetLineOptionListAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
