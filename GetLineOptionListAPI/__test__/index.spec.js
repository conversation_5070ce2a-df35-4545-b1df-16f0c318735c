const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { HEADER_KEY } = require('../../__shared__/constants/headerKeys');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
let expect = chai.expect;

describe('GetLineOptionListAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            tenant_id: 'TSA000',
            plan_id: 99045,
            N_no: 'N999999901',
        },
    };

    describe('GetLineOptionListAPI with normal request', function () {
        it('returns line options for super user', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
        });

        it('returns line options for normal user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            request.headers[HEADER_KEY.TENANT_ID] = 'TSA000';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).greaterThan(0);
        });

        it('returns line options for normal user for tenant based on session data', async () => {
            // assuming no line options for ICX000 with 99045 
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            request.headers[HEADER_KEY.TENANT_ID] = 'ICX000';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            expect(context.res.body.result.length).equal(0);
        });
    });

    describe('GetLineOptionListAPI with bad request', function () {
        it('returns error if tenant_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('991025');
        });

        it('returns error if plan_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.plan_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('991025');
        });
    });
});
