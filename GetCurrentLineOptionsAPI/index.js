const mongoose = require('../__shared__/database/mongoose');
const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const { ERROR_MAP } = require('./helpers/errorHelper');
const {
    validateAndParseLineOptionsRequest,
    validateLineOptionsRequestWithDbConn,
} = require('./validations/lineOptionsValidator');
const { getLineOptionsWithCurrentLineOptions } = require('./getLineOptions');

module.exports = async function (context, req) {
    try {
        context.log('GetCurrentLineOptionsAPI START');
        const { roleId, tenantId, tempoId } = getSessionData(context, req);

        const { error, errorMessage, params } = validateAndParseLineOptionsRequest(req.body ?? {});
        if (error) {
            context.log('GetCurrentLineOptionsAPI invalid parameter', errorMessage);
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', error, null);
            return;
        }

        await mongoose.init(context);

        const dbValidation = await validateLineOptionsRequestWithDbConn(context, params, tenantId, roleId, tempoId);
        if (!dbValidation.ok) {
            context.log('GetCurrentLineOptionsAPI validation error', dbValidation.errorMessage);
            responseWithError(context, CommonError.INVALID_USER, dbValidation.errorMessage, dbValidation.errorCode);
            return;
        }

        // API出力データ中にSQLにしか入っていないデータがあるのでここでアクセスしてデータ取得する
        const { currentLineOptions, selectableLineOptions } = await getLineOptionsWithCurrentLineOptions(
            context,
            params.tenantId,
            dbValidation.kaisenInfo.planId,
            dbValidation.kaisenInfo.nBan,
            params.kaisenNo
        );

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                current_kaisen_options: currentLineOptions,
                selectable_kaisen_options: selectableLineOptions,
                plan_id: dbValidation.kaisenInfo.planId,
                plan_name: dbValidation.kaisenInfo.planName,
            },
        };
    } catch (exception) {
        context.log.error('GetCurrentLineOptionsAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, ERROR_MAP.SERVER_ERROR, null);
        return;
    }
};
