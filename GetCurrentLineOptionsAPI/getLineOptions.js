const _ = require('lodash');
const { LINE_OPTION_TYPE_NAME, LINE_OPTION_TYPE } = require('../__shared__/constants/daihyouBangoConstants');
const { isNone } = require('../__shared__/helpers/baseHelper');
const Dai<PERSON>ouBangoHelper = require('../__shared__/helpers/daihyouBangoHelper');
const pgLineOptions = require('../__shared__/pgModels/lineOptions');
const pgLines = require('../__shared__/pgModels/lines');

/**
 * 選択可能な回線オプションと利用中の回線オプションを取得する
 * @param {object} context
 * @param {string} tenantId
 * @param {number|string} planId
 * @param {string} nNo
 * @param {string} lineNo
 */
const getLineOptionsWithCurrentLineOptions = async (context, tenantId, planId, nNo, lineNo) => {
    const [{ modifiedLineOptions, allLineOptions }, currentLineOptions] = await Promise.all([
        DaihyouBangoHelper.getModifiedLineOptions(context, { tenantId, planId, nNo }),
        pgLines.retrieveUsingMAWPLineOptions(context, lineNo),
    ]);

    const lineTypeGroup = _.groupBy(modifiedLineOptions, _.property('line_option_type'));

    // get selectable line options:
    /** @type {Object<string, Array<[string, string]>>} values [0: line_option_id, 1: option_plan_name] */
    const tmpMap = _.transform(
        lineTypeGroup,
        (result, options, type) => {
            if (options.length < 2) {
                result[type] = options.map((o) => [o.line_option_id, 'あり']).concat([['', 'なし']]);
            } else {
                result[type] = options.map((o) => [o.line_option_id, o.option_plan_name]).concat([['', 'なし']]);
            }
        },
        {} // initial result value
    );

    // tmpMap = {
    //  '1': [["LO001", "LO001Name"], ["LO002", "LO002Name"], ["", "なし"]],
    //  '2': [["LO010", "あり"], ["", "なし"]],
    //  ...
    // }

    const sortedMap = sortMap(tmpMap);

    // sortedMap = [
    //   [ '2', [ [ 'LO010', 'あり' ], [ '', 'なし' ] ] ],
    //   [ '3', [ [ 'LO011', 'あり' ], [ '', 'なし' ] ] ],
    //   [ '1', [ ["LO001": "LO001Name"], ["LO002": "LO002Name"], ["": "なし"] ] ],
    //   ...
    // ]

    const selectableLineOptions = sortedMap.map((row) => {
        /** @type {[string, string[]]} */
        const [optionTypeId, optionTypeValues] = row;
        /** @type {string} */
        const optionTitle = LINE_OPTION_TYPE_NAME[optionTypeId] || '';
        return {
            line_option_type: parseInt(optionTypeId),
            line_option_title: optionTitle,
            line_option_values: optionTypeValues.map((val) => ({
                line_option_id: val[0],
                line_option_name: val[1],
            })),
        };
    });

    // get current selected line options:
    /** @type {Object<string, [string, string]>} values [0: line_option_id, 1: option_plan_name] */
    const selected_tmpMap = _.transform(
        tmpMap,
        (result, lineOptions, optionType) => {
            /** @type {[string,string]|undefined} */
            let found;
            if (optionType == LINE_OPTION_TYPE.RUSUBAN || optionType == LINE_OPTION_TYPE.CATCH_HON) {
                const filteredLineOptions = allLineOptions.filter((r) => currentLineOptions.includes(r.line_option_id));
                if (filteredLineOptions.some((r) => r.line_option_type == optionType)) {
                    found = lineOptions.find((r) => !isNone(r[0]));
                }
            } else {
                found = lineOptions.find((r) => currentLineOptions.includes(r[0]));
            }
            result[optionType] = isNone(found) ? ['', 'なし'] : found;
        },
        {}
    );
    const selected_sortedMap = sortMap(selected_tmpMap);
    const currentKaisenOptionsFormatted = selected_sortedMap.map((row) => {
        /** @type {[string, string[]]} */
        const [optionTypeId, optionTypeValues] = row;
        /** @type {string} */
        const optionTitle = LINE_OPTION_TYPE_NAME[optionTypeId] || '';
        return {
            line_option_type: parseInt(optionTypeId),
            line_option_title: optionTitle,
            line_option_id: optionTypeValues[0],
            line_option_name: optionTypeValues[1],
        };
    });

    return {
        currentLineOptions: currentKaisenOptionsFormatted,
        selectableLineOptions,
    };
};

const sortMap = (mapObj) => {
    return Object.entries(mapObj).sort((a, b) => {
        const aOrder = pgLineOptions.getDisplayOrderForLineType(parseInt(a[0]));
        const bOrder = pgLineOptions.getDisplayOrderForLineType(parseInt(b[0]));
        return aOrder - bOrder;
    });
};

module.exports = {
    getLineOptionsWithCurrentLineOptions,
};
