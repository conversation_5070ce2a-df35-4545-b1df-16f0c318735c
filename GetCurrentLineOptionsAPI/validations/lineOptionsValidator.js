const Joi = require('joi');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const { isNone } = require('../../__shared__/helpers/baseHelper');
const pgPlans = require('../../__shared__/pgModels/plans');
const { ERROR_MAP } = require('../helpers/errorHelper');

const LineOptionsRequestSchema = Joi.object({
    kaisen_no: Joi.string().required(),
    tenant_id: Joi.string().required(),
}).unknown();

const validateAndParseLineOptionsRequest = (body) => {
    const { error } = LineOptionsRequestSchema.validate(body);
    if (error) {
        return {
            error: ERROR_MAP.INVALID_PARAMETER,
            errorMessage: error.details[0].message,
        };
    }
    return {
        params: {
            /** @type {string} */
            kaisenNo: body.kaisen_no,
            /** @type {string} */
            tenantId: body.tenant_id,
        },
    };
};

/**
 *
 * @param {object} context
 * @param {{kaisenNo: string, tenantId: string}} params
 * @param {string} sessionTenantId
 * @param {number} userRoleId
 * @returns {Promise<{
 *   ok: boolean,
 *   errorCode: string,
 *   errorMessage: string,
 *   kaisenInfo?: {
 *     planId: number,
 *     nBan: string,
 *     planName: string,
 *   }
 * }>}
 */
const validateLineOptionsRequestWithDbConn = async (context, params, sessionTenantId, userRoleId, userTempoId) => {
    const result = {
        ok: false,
        errorCode: '',
        errorMessage: '',
        kaisenInfo: undefined,
    };
    const kaisenInfo = await pgPlans.retrieveKaisenInfo(context, params.kaisenNo);
    if (isNone(kaisenInfo)) {
        // 該当データなし
        result.errorCode = ERROR_MAP.LINE_NO_NOT_EXISTS;
        result.errorMessage = `line id not found`;
        return result;
    }
    result.kaisenInfo = kaisenInfo;

    // アクセス元とは別のテナントが作成したSO情報等を返却するとまずいので、ここでチェックする
    let targetTenant = null,
        targetTempo = null;
    switch (userRoleId) {
        case MAWP_ROLE_ID.TEMPO_USER:
            targetTenant = sessionTenantId;
            targetTempo = userTempoId;
            break;
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
            targetTenant = sessionTenantId;
            break;
        // case SUPER_USER and NTTCOM_OPERATOR: always OK
    }
    
    const isNotPermitted = (!isNone(targetTenant) && targetTenant != params.tenantId) || !isNone(targetTempo);

    if (isNotPermitted) {
        result.errorCode = ERROR_MAP.NO_PERMISSION;
        result.errorMessage = 'line_id not permitted';
        return result;
    }
    // else OK
    result.ok = true;
    return result;
};

module.exports = {
    validateAndParseLineOptionsRequest,
    validateLineOptionsRequestWithDbConn,
};
