const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
let expect = chai.expect;

describe('GetCurrentLineOptionsAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            kaisen_no: '08006135194',
            tenant_id: 'TSA000',
        },
    };

    describe('GetCurrentLineOptionsAPI with normal request', function () {
        it('returns line options and selected line options', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.current_kaisen_options).to.be.a('Array');
            expect(context.res.body.current_kaisen_options.length).greaterThan(0);
            expect(context.res.body.selectable_kaisen_options).to.be.a('Array');
            expect(context.res.body.selectable_kaisen_options.length).greaterThan(0);
            expect(context.res.body.plan_id).not.undefined;
            expect(context.res.body.plan_name).to.be.a('string').not.empty;
        });

        it('returns empty array if line number is owned by other tenant', async () => {
            const request = _.cloneDeep(__request);
            request.body.kaisen_no = '08006131000'; // TST

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.current_kaisen_options).to.be.a('Array');
            expect(context.res.body.current_kaisen_options.length).equal(0);
            expect(context.res.body.selectable_kaisen_options).to.be.a('Array');
            expect(context.res.body.selectable_kaisen_options.length).equal(0);
            expect(context.res.body.plan_id).not.undefined;
            expect(context.res.body.plan_name).to.be.a('string').not.empty;
        });
    });

    describe('GetCurrentLineOptionsAPI with bad request', function () {
        it('returns error if line number is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.kaisen_no = '';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.kaisen_no;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if tenant id is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.tenant_id = '';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            delete request.body.tenant_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993002');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if parameter tenant is different from session tenant', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'CON000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993003');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if parameter tenant is different from session tenant', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'CON000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993003');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if request is made by tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000', null, 'TAATSA0001');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993003');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if line number is not found in database', async () => {
            const request = _.cloneDeep(__request);
            request.body.kaisen_no = '08010102020';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code).equal('993015');
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
