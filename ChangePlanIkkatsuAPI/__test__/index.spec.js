const _ = require('lodash');
let chai = require('chai');
const dayjs = require('dayjs');
const sinon = require('sinon');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
let expect = chai.expect;

const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const { isNone } = require('../../__shared__/helpers/baseHelper');

const pgLines = require('../../__shared__/pgModels/lines');
const pgServiceOrder = require('../../__shared__/pgModels/serviceOrder');
const CoreService = require('../../__shared__/services/core.service');

const { ERROR_MAP, ERROR_TEXT } = require('../helpers/errorHelper');

describe('ChangePlanIkkatsuAPI', function () {
    const lines = [
        { line_no: '0801234001', tenant_id: 'TSA000', potal_plan_id_pre: 90012 },
        { line_no: '0801234002', tenant_id: 'TSA000', potal_plan_id_pre: 90012 },
        { line_no: '0801234003', tenant_id: 'TSA000', potal_plan_id_pre: 90012 },
        { line_no: '0801234004', tenant_id: 'TSA000', potal_plan_id_pre: 90012 },
        { line_no: '0801234005', tenant_id: 'TSA000', potal_plan_id_pre: 90012 },
    ];
    const __request = {
        headers: getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TSA000', { name: 'TSAtest' }),
        body: {
            lines,
            potal_plan_id: 90013,
            reserve_date: dayjs().add(10, 'days').format('YYYY/MM/DD'),
            csv_unnecessary_flag: '1',
            check_only: true,
        },
    };

    describe('ChangePlanIkkatsuAPI with normal request', function () {
        const sandbox = sinon.createSandbox();

        beforeEach(() => {
            // make updateExecSO do nothing
            sandbox.stub(pgServiceOrder, 'updateExecSO').resolves();
        });

        afterEach(() => {
            sandbox.restore();
        });

        it('returns error messages for each invalid line data', async () => {
            const request = _.cloneDeep(__request);
            const lines = request.body.lines.map((r) => r.line_no);
            const notOwned = [lines[0], lines[1]];
            const henkoChuu = [lines[1], lines[2]];

            sandbox
                .stub(pgLines, 'isKaisenNoBelongToTenantMultiple')
                .resolves(new Set(lines.filter((r) => !notOwned.includes(r))));
            sandbox.stub(pgServiceOrder, 'checkPlanHenkoChuuMultiple').resolves(new Set(henkoChuu));
            const planHenkoSpy = sandbox.spy(CoreService, 'callAPICoreLinePlanHenko');

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            // Core API should not be called
            expect(planHenkoSpy.called).equal(false);

            expect(context.res.body.hasError).equal(true);
            expect(context.res.body.result).to.be.an('array').not.empty;

            expect(context.res.body.result[0].error).equal(ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT);
            expect(context.res.body.result[0].errorMessage).equal(ERROR_TEXT[ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT]);

            // isOwned is checked first, so henkoChuu error won't be returned
            expect(context.res.body.result[1].error).equal(ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT);
            expect(context.res.body.result[1].errorMessage).equal(ERROR_TEXT[ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT]);

            expect(context.res.body.result[2].error).equal(ERROR_MAP.ALREADY_RESERVED);
            expect(context.res.body.result[2].errorMessage).equal(ERROR_TEXT[ERROR_MAP.ALREADY_RESERVED]);

            expect(context.res.body.result[3].error).to.be.null;
            expect(context.res.body.result[3].errorMessage).to.be.empty;
        });

        it('did not call Core API if there is db validation error', async () => {
            const request = _.cloneDeep(__request);
            request.body.check_only = false;
            const lines = request.body.lines.map((r) => r.line_no);
            const notOwned = [lines[0], lines[1]];
            const henkoChuu = [lines[1], lines[2]];

            sandbox
                .stub(pgLines, 'isKaisenNoBelongToTenantMultiple')
                .resolves(new Set(lines.filter((r) => !notOwned.includes(r))));
            sandbox.stub(pgServiceOrder, 'checkPlanHenkoChuuMultiple').resolves(new Set(henkoChuu));
            const planHenkoSpy = sandbox.spy(CoreService, 'callAPICoreLinePlanHenko');

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            // Core API should not be called
            expect(planHenkoSpy.called).equal(false);

            expect(context.res.body.hasError).equal(true);
            expect(context.res.body.result).to.be.an('array').not.empty;
            expect(context.res.body.result.some((r) => !isNone(r.error))).equal(true);
            // should be no response data
            expect(context.res.body.result.every((r) => isNone(r.response))).equal(true);
        });

        it('returns response from Core API', async () => {
            const request = _.cloneDeep(__request);
            request.body.check_only = false;
            const lines = request.body.lines.map((r) => r.line_no);

            sandbox.stub(pgLines, 'isKaisenNoBelongToTenantMultiple').resolves(new Set(lines));
            sandbox.stub(pgServiceOrder, 'checkPlanHenkoChuuMultiple').resolves(new Set());
            const planHenkoSpy = sandbox.spy(CoreService, 'callAPICoreLinePlanHenko');

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            // Core API should be called
            expect(planHenkoSpy.called).equal(true);
            expect(planHenkoSpy.callCount).equal(lines.length);

            expect(context.res.body.hasError).equal(false);
            expect(context.res.body.result).to.be.an('array').not.empty;
            expect(context.res.body.result.every((r) => isNone(r.error))).equal(true);
            expect(context.res.body.result.every((r) => !isNone(r.response))).equal(true);
        });

        it('returns error if Core API process code returns NG process code', async () => {
            const request = _.cloneDeep(__request);
            request.body.check_only = false;
            const lines = request.body.lines.map((r) => r.line_no);

            sandbox.stub(pgLines, 'isKaisenNoBelongToTenantMultiple').resolves(new Set(lines));
            sandbox.stub(pgServiceOrder, 'checkPlanHenkoChuuMultiple').resolves(new Set());
            const planHenkoStub = sandbox
                .stub(CoreService, 'callAPICoreLinePlanHenko')
                .resolves({ responseHeader: { processCode: '999999', apiProcessID: 'AP09008929561' } });

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            // Core API should be called
            expect(planHenkoStub.called).equal(true);
            expect(planHenkoStub.callCount).equal(lines.length);

            expect(context.res.body.hasError).equal(true);
            expect(context.res.body.result).to.be.an('array').not.empty;
            expect(context.res.body.result.every((r) => !isNone(r.error))).equal(true);
            expect(context.res.body.result.every((r) => !isNone(r.response))).equal(true);
        });

        it('returns error if exception occurs during Core API call', async () => {
            const request = _.cloneDeep(__request);
            request.body.check_only = false;
            const lines = request.body.lines.map((r) => r.line_no);

            sandbox.stub(pgLines, 'isKaisenNoBelongToTenantMultiple').resolves(new Set(lines));
            sandbox.stub(pgServiceOrder, 'checkPlanHenkoChuuMultiple').resolves(new Set());
            const planHenkoStub = sandbox
                .stub(CoreService, 'callAPICoreLinePlanHenko')
                .rejects(new Error('timeout error [unittest]'));
            // suppress context.log.error
            const logerror = sandbox.stub(context.log, 'error').returns(undefined);

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            // Core API should be called
            expect(planHenkoStub.called).equal(true);
            expect(planHenkoStub.callCount).equal(lines.length);

            // error happened (log.error called at least once)
            expect(logerror.called).equal(true);

            expect(context.res.body.hasError).equal(true);
            expect(context.res.body.result).to.be.an('array').not.empty;
            expect(context.res.body.result.every((r) => !isNone(r.error))).equal(true);
            expect(context.res.body.result.every((r) => isNone(r.response))).equal(true);
        });
    });

    describe('ChangePlanIkkatsuAPI with bad request', function () {
        it('rejects request from tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000', { internalId: '@testtsa' });

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
        });

        it('returns error if lines array is not valid', async () => {
            const request = _.cloneDeep(__request);
            request.body.lines = '123';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.LINES_ARRAY_INVALID);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;

            request.body.lines = [];

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.LINES_ARRAY_INVALID);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if lines array has duplicate line numbers', async () => {
            const request = _.cloneDeep(__request);
            request.body.lines.push(__request.body.lines.at(0));

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.hasError).equal(true);
            expect(context.res.body.result).to.be.an('array').not.empty;
            expect(context.res.body.result.at(0).errorMessage).equal(ERROR_TEXT.DUPLICATE_LINES);
            expect(context.res.body.result.at(-1).errorMessage).equal(ERROR_TEXT.DUPLICATE_LINES);
            // other lines are ok
            expect(context.res.body.result.at(1).errorMessage).to.be.a('string').empty;
            expect(context.res.body.result.at(2).errorMessage).to.be.a('string').empty;
            expect(context.res.body.result.at(3).errorMessage).to.be.a('string').empty;
            expect(context.res.body.result.at(4).errorMessage).to.be.a('string').empty;
        });

        it('returns error if line_no is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.lines[0].line_no = '';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.LINE_NO_EMPTY);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if pre_plan is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.lines[1].potal_plan_id_pre = '';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.PRE_PLAN_EMPTY);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if tenant ID is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.lines[2].tenant_id = '';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.TENANT_ID_EMPTY);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if not all pre-plan is same', async () => {
            const request = _.cloneDeep(__request);
            request.body.lines[0].potal_plan_id_pre = 90011;
            request.body.lines[3].potal_plan_id_pre = 90013;

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.PRE_PLAN_MULTIPLE);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if new plan ID is empty', async () => {
            const request = _.cloneDeep(__request);
            request.body.potal_plan_id = '';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.PLAN_ID_EMPTY.trim());
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if reserve_date format is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.reserve_date = dayjs().add(10, 'days').format('DD/MM/YYYY');

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.RESERVE_DATE_INVALID);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if csv_unnecessary_flag is not a string', async () => {
            const request = _.cloneDeep(__request);
            request.body.csv_unnecessary_flag = [{ test: 123 }];

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.INVALID_PARAMETER);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });

        it('returns error if check_only is not a boolean', async () => {
            const request = _.cloneDeep(__request);
            request.body.check_only = 'asdf';

            await httpFunction(context, request);
            // status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal(ERROR_MAP.INVALID_PARAMETER);
            expect(context.res.body.nttc_mvno_error_msg).to.be.a('string').not.empty;
        });
    });
});
