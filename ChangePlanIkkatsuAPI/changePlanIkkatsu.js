const _ = require('lodash');

const CoreService = require('../__shared__/services/core.service');
const pgServiceOrder = require('../__shared__/pgModels/serviceOrder');

const { isNone } = require('../__shared__/helpers/baseHelper');
const { splitter } = require('../__shared__/utils/arrayUtils');
const { ERROR_MAP, ERROR_TEXT } = require('./helpers/errorHelper');

/**
 * @typedef {{error?: string, errorMessage: string, response?: {processCode: string, apiProcessID: string}}} ChangePlanIkkatsuResult
 */

/**
 *
 * @param {object} context
 * @param {import("./validations/changePlanIkkatsuValidator").ChangePlanIkkatsuData} params
 * @param {number} batchSize number of simultaneous call to Core API
 * @param {number} roleId
 * @param {string} sessionTenantId
 * @param {{internalId?: string, name?: string}} user
 */
const changePlanIkkatsu = async (context, params, batchSize, roleId, sessionTenantId, user) => {
    context.log('ChangePlanIkkatsuAPI start processing with batch size:', batchSize);
    /** @type {Array<ChangePlanIkkatsuResult>} */
    const result = [];
    /** @type {Array<Array<{lineNo: string, tenantId: string}>>} */
    const splitted = splitter(params.lines, batchSize);

    // use internalId (no need to check tempo user); limit length to 16 characters (psql column def)
    let userId = user.internalId;
    if (typeof userId === 'string' && userId.length > 16) {
        userId = userId.slice(0, 16);
    }

    for (const lineBatch of splitted) {
        const planHenkoParameters = lineBatch.map((r) => ({
            lineNo: r.lineNo,
            tenantID: r.tenantId,
            potalPlanID_pre: params.oldPlanId,
            potalPlanID: params.newPlanId,
            csvUnnecessaryFlag: params.csvUnnecessaryFlag,
            reserveDate: params.reserveDate,
        }));
        const apiResponseArray = await Promise.allSettled(
            planHenkoParameters.map((p) => CoreService.callAPICoreLinePlanHenko(context, p))
        );

        /** @type {Array<string>} for updating execSO */
        const apiProcessIDList = [];
        for (const [request, response] of _.zip(planHenkoParameters, apiResponseArray)) {
            /** @type {ChangePlanIkkatsuResult} */
            const res = {
                errorMessage: '',
            };
            if (response.status === 'fulfilled') {
                res.response = {};
                res.response.apiProcessID = response.value?.responseHeader?.apiProcessID;
                res.response.processCode = response.value?.responseHeader?.processCode;

                // save only if not empty
                if (!isNone(res.response.apiProcessID)) apiProcessIDList.push(res.response.apiProcessID);

                if (res.response.processCode !== '000000') {
                    context.log('ChangePlanIkkatsu API Call Fail: ', request.lineNo, res.response.processCode);
                    res.error = ERROR_MAP.CHANGE_PLAN_FAILED;
                    res.errorMessage = ERROR_TEXT[ERROR_MAP.CHANGE_PLAN_FAILED];
                }
            } else {
                context.log.error('ChangePlanIkkatsuAPI call to Core error:', request.lineNo, response.reason);
                res.error = ERROR_MAP.CHANGE_PLAN_FAILED;
                res.errorMessage = ERROR_TEXT.API_SERVER_ERROR;
            }
            result.push(res);
        }

        // update execSO before moving to next batch
        await pgServiceOrder.updateExecSOMultiple(context, apiProcessIDList, sessionTenantId, userId);
    }

    return result;
};

module.exports = {
    changePlanIkkatsu,
};
