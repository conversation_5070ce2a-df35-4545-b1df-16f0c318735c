const _ = require('lodash');
const Joi = require('joi').extend(require('@joi/date'));

const pgLines = require('../../__shared__/pgModels/lines');
const pgTenants = require('../../__shared__/pgModels/tenants');
const pgServiceOrder = require('../../__shared__/pgModels/serviceOrder');

const { unionSets } = require('../../__shared__/helpers/baseHelper');
const { getParamBooleanValue } = require('../../__shared__/utils/stringUtils');

const { ERROR_MAP, ERROR_TEXT } = require('../helpers/errorHelper');

/**
 * @typedef ChangePlanIkkatsuData
 * @property {Array<{lineNo: string, tenantId: string}>} lines
 * @property {number} oldPlanId
 * @property {number} newPlanId
 * @property {string|undefined} reserveDate
 * @property {string} csvUnnecessaryFlag
 * @property {boolean} checkOnly
 */

const lineItemSchema = Joi.object({
    line_no: Joi.string().required().messages({
        'any.required': ERROR_MAP.LINE_NO_EMPTY,
        'string.empty': ERROR_MAP.LINE_NO_EMPTY,
        'string.base': ERROR_MAP.LINE_NO_EMPTY,
    }),
    tenant_id: Joi.string().required().messages({
        'any.required': ERROR_MAP.TENANT_ID_EMPTY,
        'string.empty': ERROR_MAP.TENANT_ID_EMPTY,
        'string.base': ERROR_MAP.TENANT_ID_EMPTY,
    }),
    potal_plan_id_pre: Joi.number().required().messages({
        'any.required': ERROR_MAP.PRE_PLAN_EMPTY,
        'number.base': ERROR_MAP.PRE_PLAN_EMPTY,
    }),
}).unknown();

const changePlanIkkatsuSchema = Joi.object({
    lines: Joi.array().items(lineItemSchema.required()).required().messages({
        'array.base': ERROR_MAP.LINES_ARRAY_INVALID,
        'any.required': ERROR_MAP.LINES_ARRAY_INVALID,
        'array.includesRequiredUnknowns': ERROR_MAP.LINES_ARRAY_INVALID,
    }),
    potal_plan_id: Joi.number().required().messages({
        'any.required': ERROR_MAP.PLAN_ID_EMPTY,
        'number.base': ERROR_MAP.PLAN_ID_EMPTY,
    }),
    reserve_date: Joi.date().format('YYYY/MM/DD').raw().messages({
        'string.base': ERROR_MAP.RESERVE_DATE_INVALID,
        'date.format': ERROR_MAP.RESERVE_DATE_INVALID,
    }),
    csv_unnecessary_flag: Joi.string(),
    check_only: Joi.boolean(),
});

/**
 * @param {object} body
 */
const validateAndParseChangePlanIkkatsuRequest = (body) => {
    const result = {
        /** @type {string|null} */
        error: null,
        errorMessage: '',
        /** @type {ChangePlanIkkatsuData} */
        params: {},
    };
    const { error } = changePlanIkkatsuSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
        if (ERROR_TEXT[result.error]) {
            result.errorMessage = ERROR_TEXT[result.error];
        } else {
            result.errorMessage = result.error;
            result.error = ERROR_MAP.INVALID_PARAMETER;
        }
        result.error = result.error.trim();
        return result;
    }

    /** @type {Array<number>} */
    const prePlanArray = Array.from(new Set(body.lines.map((r) => r.potal_plan_id_pre)));
    if (prePlanArray.length !== 1) {
        result.error = ERROR_MAP.PRE_PLAN_MULTIPLE;
        result.errorMessage = ERROR_TEXT[ERROR_MAP.PRE_PLAN_MULTIPLE];
        return result;
    }

    result.params = {
        lines: body.lines.map((r) => ({ lineNo: r.line_no, tenantId: r.tenant_id })),
        oldPlanId: prePlanArray.at(0),
        newPlanId: body.potal_plan_id,
        // reserveDate: body.reserve_date,
        csvUnnecessaryFlag: body.csv_unnecessary_flag,
        checkOnly: getParamBooleanValue(body.check_only, true),
    };
    if (body.reserve_date) {
        result.params.reserveDate = `${body.reserve_date} 04:00`;
    }
    return result;
};

/**
 *
 * @param {object} context
 * @param {Array<{lineNo: string, tenantId: string}>} linesArray
 */
const validateChangePlanIkkatsuWithDbConn = async (context, linesArray) => {
    /** @type {Array<{error: string?, errorMessage: string}>} */
    let errorList = [];

    // check for duplicate line numbers before we do db validation
    const duplicates = linesArray.reduce((acc, cur) => {
        if (acc[cur.lineNo] !== undefined) {
            acc[cur.lineNo] += 1;
        } else {
            acc[cur.lineNo] = 1;
        }
        return acc;
    }, {});
    if (Object.values(duplicates).some((r) => r !== 1)) {
        for (const lineItem of linesArray) {
            const result = { error: null, errorMessage: '' };
            if (duplicates[lineItem.lineNo] !== 1) {
                result.error = ERROR_MAP.LINES_ARRAY_INVALID;
                result.errorMessage = ERROR_TEXT.DUPLICATE_LINES;
            }
            errorList.push(result);
        }
        return errorList;
    }

    const tenantList = Array.from(new Set(linesArray.map((r) => r.tenantId)));
    const [isOwnedSet, isHenkoChuuSet] = await Promise.all([
        checkOwnedLineNumbers(context, linesArray, tenantList),
        pgServiceOrder.checkPlanHenkoChuuMultiple(
            context,
            linesArray.map((r) => r.lineNo)
        ),
    ]);

    for (const lineItem of linesArray) {
        const result = { error: null, errorMessage: '' };
        if (!isOwnedSet.has(lineItem.lineNo)) {
            result.error = ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT;
            result.errorMessage = ERROR_TEXT[ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT];
        } else if (isHenkoChuuSet.has(lineItem.lineNo)) {
            result.error = ERROR_MAP.ALREADY_RESERVED;
            result.errorMessage = ERROR_TEXT[ERROR_MAP.ALREADY_RESERVED];
        }
        errorList.push(result);
    }
    return errorList;
};

/**
 *
 * @param {object} context
 * @param {Array<{tenantId: string, lineNo: string}>} data
 * @param {Array<string>} tenantList
 * @returns {Promise<Set<string>>}
 */
const checkOwnedLineNumbers = async (context, data, tenantList) => {
    const shanaiFlags = await pgTenants.getShanaiFlagMultiple(context, tenantList);
    const groupedData = _.groupBy(data, _.property('tenantId'));
    // check kaisen belongs to tenant for each tenant ID
    const future = tenantList.map((tenantId) => {
        return pgLines.isKaisenNoBelongToTenantMultiple(
            context,
            tenantId,
            groupedData[tenantId].map((r) => r.lineNo),
            shanaiFlags[tenantId]
        );
    });
    const result = await Promise.all(future);
    // merge result sets
    return unionSets(...result);
};

module.exports = {
    validateAndParseChangePlanIkkatsuRequest,
    validateChangePlanIkkatsuWithDbConn,
};
