const _ = require('lodash');
const appConfig = require('../__shared__/config/appConfig');
const mongoose = require('../__shared__/database/mongoose');
const { CommonError } = require('../__shared__/constants/commonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const { ERROR_TEXT, ERROR_MAP } = require('./helpers/errorHelper');
const {
    validateChangePlanIkkatsuWithDbConn,
    validateAndParseChangePlanIkkatsuRequest,
} = require('./validations/changePlanIkkatsuValidator');
const { changePlanIkkatsu } = require('./changePlanIkkatsu');

const portalConfig = appConfig.getPortalConfig();

// 一括プラン変更
module.exports = async function (context, req) {
    try {
        context.log('ChangePlanIkkatsuAPI START');
        const { roleId, tenantId, user } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(
                context,
                CommonError.PERMISSION_ERROR,
                'permission error',
                ERROR_MAP.INVALID_PARAMETER,
                null
            );
            return;
        }

        const { error, errorMessage, params } = validateAndParseChangePlanIkkatsuRequest(req.body);
        if (error) {
            context.log('ChangePlanIkkatsuAPI parameter error:', error);
            responseWithError(context, CommonError.BAD_REQUEST, errorMessage, error, null);
            return;
        }

        if (params.lines.length > portalConfig.KAISEN_IKKATSU.MAX_LINES) {
            context.log(
                'ChangePlanIkkatsuAPI kaisen count is over limit',
                params.lines.length,
                portalConfig.KAISEN_IKKATSU.MAX_LINES
            );
            responseWithError(
                context,
                CommonError.BAD_REQUEST,
                'invalid parameter',
                ERROR_MAP.INVALID_PARAMETER,
                `対象回線番号数の上限が超えています。（${portalConfig.KAISEN_IKKATSU.MAX_LINES}回線まで可）`
            );
            return;
        }

        await mongoose.init(context);

        const dbVal = await validateChangePlanIkkatsuWithDbConn(context, params.lines);
        const hasError = dbVal.some((r) => !isNone(r.error));
        if (params.checkOnly || hasError) {
            const resultArray = _.zip(params.lines, dbVal).map(([lineItem, validation]) => {
                return {
                    lineNo: lineItem.lineNo,
                    tenantId: lineItem.tenantId,
                    error: validation.error,
                    errorMessage: validation.errorMessage,
                };
            });

            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    hasError,
                    result: resultArray,
                },
            };
        } else {
            const apiResult = await changePlanIkkatsu(
                context,
                params,
                portalConfig.KAISEN_IKKATSU.PLAN_CHANGE_RATE,
                roleId,
                tenantId,
                user
            );
            const hasApiError = apiResult.some((r) => !isNone(r.error));
            const resultArray = _.zip(params.lines, apiResult).map(([lineItem, apiRes]) => {
                return {
                    lineNo: lineItem.lineNo,
                    tenantId: lineItem.tenantId,
                    error: apiRes.error,
                    errorMessage: apiRes.errorMessage,
                    response: apiRes.response,
                };
            });

            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    hasError: hasApiError,
                    result: resultArray,
                },
            };
        }
    } catch (exception) {
        context.log.error('ChangePlanIkkatsuAPI - exception: ', exception);
        responseWithError(
            context,
            CommonError.SERVER_ERROR,
            ERROR_TEXT[ERROR_MAP.CHANGE_PLAN_FAILED],
            ERROR_MAP.CHANGE_PLAN_FAILED,
            null
        );
        return;
    }
};

const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
