const ERROR_MAP = {
    INVALID_PARAMETER: '991022',
    LINES_ARRAY_INVALID: '991001',
    PRE_PLAN_MULTIPLE: '991005',
    LINE_NO_EMPTY: '994055',
    TENANT_ID_EMPTY: '994057',
    PRE_PLAN_EMPTY: '994091',
    LINE_NO_NOT_LINKED_TO_TENANT: '994088',
    PLAN_ID_EMPTY: '994091 ', // add space so we can use different ERROR_TEXT for same opferror code
    RESERVE_DATE_INVALID: '994087',
    ALREADY_RESERVED: '994095',
    CHANGE_PLAN_FAILED: '994089',
};

const ERROR_TEXT = {
    [ERROR_MAP.LINES_ARRAY_INVALID]: '回線番号データが不正。',
    [ERROR_MAP.PRE_PLAN_MULTIPLE]: '変更前のプランが揃っていません。',
    [ERROR_MAP.LINE_NO_EMPTY]: '回線番号が不正です。',
    [ERROR_MAP.TENANT_ID_EMPTY]: 'テナント情報が未指定です。',
    [ERROR_MAP.PRE_PLAN_EMPTY]: '変更前のプランが指定されていません。',
    [ERROR_MAP.LINE_NO_NOT_LINKED_TO_TENANT]: 'テナントに回線番号が紐づいていません!',
    [ERROR_MAP.PLAN_ID_EMPTY]: '変更後のプランが指定されていません。',
    [ERROR_MAP.RESERVE_DATE_INVALID]: '実施日付のフォーマットが不正です。',
    [ERROR_MAP.ALREADY_RESERVED]: 'プラン変更は予約中！',
    [ERROR_MAP.CHANGE_PLAN_FAILED]: 'プラン変更が失敗しました。',
    DUPLICATE_LINES: '回線番号が重複している',
    API_SERVER_ERROR: 'サーバーエラーが発生しました。',
};

module.exports = {
    ERROR_MAP,
    ERROR_TEXT,
};
