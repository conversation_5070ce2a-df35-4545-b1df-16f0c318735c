const { CommonError } = require('../__shared__/constants/CommonError');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const baseHelper = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const lines = require('../__shared__/pgModels/lines');
const serviceOrder = require('../__shared__/pgModels/serviceOrder');
const CoreService = require('../__shared__/services/core.service');
const { validateDateFormat } = require('./helpers');
const { USAGE_STATUS } = require('../__shared__/constants/lineConstants');

module.exports = async function (context, req) {
    try {
        context.log('ActiveKaisenAPI START');
        const { roleId, tenantId, user } = baseHelper.getSessionData(context, req);

        let tenant = null;

        switch (roleId) {
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                tenant = tenantId;
                break;
            default:
                break;
        }

        if (baseHelper.isNone(tenant)) {
            context.log('ActiveKaisenAPI invalid parameter 993008');
            responseWithError(context, CommonError.INVALID_USER, 'invalid parameter', '993008', null);
            return;
        }

        const lineNo = req.body['line_no'];
        const tenantID = req.body['tenant_id'];
        const status = req.body['status'];
        const reserveDate = req.body['reserve_date'];

        if (baseHelper.isNone(lineNo)) {
            context.log('ActiveKaisenAPI: line_no param is required.');
            responseWithError(context, CommonError.BAD_REQUEST, 'line_no param is required.', '994055', null);
            return;
        } else if (baseHelper.isNone(tenantID)) {
            context.log('ActiveKaisenAPI: tenant_id param is required.');
            responseWithError(context, CommonError.BAD_REQUEST, 'tenant_id param is required.', '994057', null);
            return;
        } else if (baseHelper.isNone(status) || (status != '0' && status != '1')) {
            context.log('ActiveKaisenAPI: status param is required.');
            responseWithError(context, CommonError.BAD_REQUEST, 'status param is required.', '994086', null);
            return;
        }

        // Check reserve date
        let reserveDateAtFour;
        if (!baseHelper.isNone(reserveDate)) {
            const validateDateResult = validateDateFormat(reserveDate);

            if (!validateDateResult) {
                context.log('ActiveKaisenAPI: reserve_date format is invalid.');
                responseWithError(context, CommonError.BAD_REQUEST, 'reserve_date is invalid.', '994087', null);
                return;
            } else {
                reserveDateAtFour = `${reserveDate} 04:00`;
            }
        }

        const isOwned = await lines.isKaisenNoBelongToTenant(context, {
            tenantId: tenantID,
            kaisenNo: lineNo,
        });

        if (!isOwned) {
            context.log('ActiveKaisenAPI: 回線所属テナントの情報の取得に失敗しました。');
            responseWithError(
                context,
                CommonError.BAD_REQUEST,
                'kaisen does not belong to the tenant.',
                '994088',
                null
            );
            return;
        }

        const usageStatusInfo = await lines.getUsageStatusByLineNo(context, lineNo);

        if (usageStatusInfo == USAGE_STATUS.SUSPENDED) {
            context.log('ActiveKaisenAPI: Line is suspended.');
            responseWithError(context, CommonError.BAD_REQUEST, 'this line is suspened.', '997011', null);
            return;
        } else if (baseHelper.isNone(usageStatusInfo)) {
            // 元のコードにはないチェックだが一応追加する
            context.log('ActiveKaisenAPI: cannot get line usage.');
            responseWithError(context, CommonError.BAD_REQUEST, 'cannot get line usage.', '994057', null);
            return;
        }

        const activationLineRequestParam = {
            tenantId: tenantID,
            lineNo: lineNo,
            status: status,
            reserve_date: reserveDateAtFour,
        };

        try {
            const result = await CoreService.callAPIKaisenActivation(context, activationLineRequestParam);
            context.log('Called Kaisen Activation Core API: ', JSON.stringify(result));

            // MEMO: API呼び出し後の店舗ユーザをチェックするifブロックは実行されないと思うのでスキップする(isAllowed内で店舗ユーザを許容してないので)
            let userId = user.plusId;
            if (typeof userId === 'string' && userId.length > 16) {
                userId = userId.slice(0, 16);
            }
            await serviceOrder.updateExecSO(context, result.responseHeader.apiProcessID, tenantID, userId);

            if (result.responseHeader.processCode == '000000') {
                context.res = {
                    status: 200,
                    body: {
                        error: CommonError.NO_ERROR,
                    },
                };
            } else {
                context.log('ActiveKaisenAPI: API call failed:', result?.responseHeader?.processCode);
                responseWithError(context, CommonError.SERVER_ERROR, 'API call failed.', '994089', null);
                return;
            }
        } catch (exception) {
            context.log.error('ActiveKaisenAPI - exception: Cannot connect API server: ', exception);
            responseWithError(context, CommonError.SERVER_ERROR, exception, '994089', null);
            return;
        }
    } catch (exception) {
        context.log.error('ActiveKaisenAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
