const _ = require('lodash');
const sinon = require('sinon');
const httpFunction = require('../index');
const helpers = require('../../__shared__/helpers/baseHelper');
const context = require('../../__shared__/__tests__/defaultContext');
const { expect } = require('chai');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const lines = require('../../__shared__/pgModels/lines');
const serviceOrder = require('../../__shared__/pgModels/serviceOrder');
const CoreService = require('../../__shared__/services/core.service');

describe('ActiveKaisenAPI', () => {
    const sandbox = sinon.createSandbox();

    const __request = {
        headers: getDefaultHeader(),
        body: {
            line_no: '12345678901',
            tenant_id: 'TSA000',
            status: '0',
            reserve_date: '2023/12/31',
        },
    };

    afterEach(() => {
        sandbox.restore();
    });

    describe('ActiveKaisenAPI with normal request', () => {
        it('should not return an error when the execution was successful', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);
            sandbox.stub(lines, 'getUsageStatusByLineNo').resolves(1);
            sandbox.stub(CoreService, 'callAPIKaisenActivation').resolves({
                responseHeader: {
                    apiProcessID: 'test',
                    processCode: '000000',
                },
            });
            sandbox.stub(serviceOrder, 'updateExecSO');

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
        });
    });

    describe('ActiveKaisenAPI with bad request', () => {
        it('should return permission error when the role is not allowed to execute the API (店舗ユーザ)', async () => {
            const request = _.cloneDeep(__request);
            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 999,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('正しくないパラメータが含まれています。');
            expect(context.res.body.nttc_mvno_error_code).equal('993008');
        });

        it('should return parameter error if line_no does not exist', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.line_no;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('回線番号が不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
        });

        it('should return parameter error if tenant_id does not exist', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('テナントIDが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
        });

        it('should return parameter error if status does not exist', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.status;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('アクティベートステータスが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994086');
        });

        it('should return parameter error if status is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.status = '2';

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('アクティベートステータスが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994086');
        });

        it('should return parameter error if reserve_date is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.reserve_date = '12/31/2023';

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('予約日が不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994087');
        });

        it('should return parameter error if the kaisen does not belong to the tenant', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(false);

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('回線所属テナント情報の取得に失敗しました。');
            expect(context.res.body.nttc_mvno_error_code).equal('994088');
        });

        it('should return parameter error if the line is suspended', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);
            sandbox.stub(lines, 'getUsageStatusByLineNo').resolves(2);

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('回線番号の利用状態はサスペンド中です。');
            expect(context.res.body.nttc_mvno_error_code).equal('997011');
        });

        it('should return parameter error if the line usage cannot be retrieved', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);
            sandbox.stub(lines, 'getUsageStatusByLineNo').resolves(null);

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('テナントIDが不正です。');
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
        });

        it('should return API error when calling Core service failed', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            sandbox.stub(lines, 'isKaisenNoBelongToTenant').resolves(true);
            sandbox.stub(lines, 'getUsageStatusByLineNo').resolves(1);
            sandbox.stub(CoreService, 'callAPIKaisenActivation').resolves({
                responseHeader: {
                    apiProcessID: 'test',
                    processCode: '999999',
                },
            });
            sandbox.stub(serviceOrder, 'updateExecSO');

            await httpFunction(context, request);
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('SO実行が失敗しました。');
            expect(context.res.body.nttc_mvno_error_code).equal('994089');
        });
    });
});
