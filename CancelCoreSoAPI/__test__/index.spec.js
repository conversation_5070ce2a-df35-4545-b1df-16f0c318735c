const _ = require('lodash');
let chai = require('chai');
const sinon = require('sinon');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { HEADER_KEY } = require('../../__shared__/constants/headerKeys');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
const CoreService = require('../../__shared__/services/core.service');
const { isNone } = require('../../__shared__/helpers/baseHelper');
let expect = chai.expect;

describe('CancelCoreSoAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            so_id: 'ABCDEF123456789',
            tenant_id: 'TSA000',
        },
    };

    describe('CancelCoreSoAPI with normal request', function () {
        it('returns no error for normal request', async () => {
            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
        });
    });

    describe('CancelCoreSoAPI with bad request', function () {
        const sandbox = sinon.createSandbox();

        afterEach(() => {
            sandbox.restore();
        });

        it('returns permission error for not allowed role', async () => {
            const request = _.cloneDeep(__request);
            const testData = [MAWP_ROLE_ID.TEMPO_USER, MAWP_ROLE_ID.TENANT_ETSURAN_USER];

            for (const role of testData) {
                request.headers[HEADER_KEY.ROLE_ID] = role;

                await httpFunction(context, request);
                //status code should be 200
                expect(context.res.status).equal(200);
                expect(context.res.body.error).equal(402);
            }
        });

        it('returns parameter error if tenant_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.tenant_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
            //error message should not be empty
            expect(isNone(context.res.body.nttc_mvno_error_msg)).equal(false);
        });

        it('returns parameter error if so_id is empty', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.so_id;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994056');
            //error message should not be empty
            expect(isNone(context.res.body.nttc_mvno_error_msg)).equal(false);
        });

        it('returns parameter error if so_id format is wrong', async () => {
            const request = _.cloneDeep(__request);
            request.body.so_id = 'ABCDEF123';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994056');
            //error message should not be empty
            expect(isNone(context.res.body.nttc_mvno_error_msg)).equal(false);
        });

        it('returns API call error if cannot connect to api server', async () => {
            sandbox.stub(CoreService, 'callAPICoreSoCancel').throws('some connection error (test)');
            sandbox.stub(context.log, 'error'); // suppress exception trace

            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('994064');
            //error message should not be empty
            expect(isNone(context.res.body.nttc_mvno_error_msg)).equal(false);
        });

        it('returns API call error if process code is not 000000', async () => {
            sandbox.stub(CoreService, 'callAPICoreSoCancel').resolves({
                responseHeader: {
                    processCode: '123456',
                },
            });
            sandbox.stub(context.log, 'error'); // suppress exception trace

            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('123456');
            //error message should not be empty
            expect(isNone(context.res.body.nttc_mvno_error_msg)).equal(false);
        });

        it('returns API call error if core service returns empty', async () => {
            sandbox.stub(CoreService, 'callAPICoreSoCancel').resolves(undefined);
            sandbox.stub(context.log, 'error'); // suppress exception trace

            const request = _.cloneDeep(__request);

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(200);
            //error message should not be empty
            expect(isNone(context.res.body.nttc_mvno_error_msg)).equal(false);
        });
    });
});
