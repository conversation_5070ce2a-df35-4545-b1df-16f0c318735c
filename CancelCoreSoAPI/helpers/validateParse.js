const Joi = require('joi');
const { ERROR_MAP } = require('./errorHelper');

const cancelCoreSoSchema = Joi.object({
    so_id: Joi.string()
        .pattern(/[a-zA-Z0-9]{15}/)
        .required()
        .messages({
            'any.required': ERROR_MAP.SOID_FORMAT_ERROR,
            'string.empty': ERROR_MAP.SOID_FORMAT_ERROR,
            'string.pattern.base': ERROR_MAP.SOID_FORMAT_ERROR,
        }),
    tenant_id: Joi.string().required().messages({
        'any.required': ERROR_MAP.TENANT_EMPTY,
        'string.empty': ERROR_MAP.TENANT_EMPTY,
    }),
}).unknown();

/**
 * 
 * @param {object} context 
 * @param {object} body 
 * @returns {{error: null|string, params: {so_id: string, tenant_id: string}}}
 */
const validateAndParseCancelCoreSoParams = (context, body) => {
    let result = { error: null, params: {} };
    const { error } = cancelCoreSoSchema.validate(body);
    if (error) {
        result.error = error.details[0].message;
    } else {
        result.params.so_id = body.so_id;
        result.params.tenant_id = body.tenant_id;
    }
    return result;
};

module.exports = {
    validateAndParseCancelCoreSoParams,
};
