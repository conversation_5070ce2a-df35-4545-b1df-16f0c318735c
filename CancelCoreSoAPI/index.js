const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseCancelCoreSoParams } = require('./helpers/validateParse');
const { ERROR_TEXT, ERROR_MAP } = require('./helpers/errorHelper');
const CoreService = require('../__shared__/services/core.service');

module.exports = async function (context, req) {
    try {
        context.log('CancelCoreSoAPI START');
        // eslint-disable-next-line no-unused-vars
        const { roleId } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, params } = validateAndParseCancelCoreSoParams(context, req.body ?? {});
        if (error) {
            // returns 991022 if it is not custom error code
            context.log('CancelCoreSoAPI parameter error ', error);
            responseWithError(
                context,
                CommonError.BAD_REQUEST,
                ERROR_TEXT[error] ?? error,
                error.length == 6 ? error : '991022',
                null
            );
            return;
        }

        if (await processCancelCoreSo(context, params.tenant_id, params.so_id)) {
            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                },
            };
        }
    } catch (exception) {
        context.log.error('CancelCoreSoAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};

/**
 *
 * @param {number} roleId
 * @returns {boolean}
 */
const isAllowed = (roleId) => {
    // session variables are always defined
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};

/**
 * Call so_cancel API from core service
 * @param {object} context
 * @param {string} tenantId
 * @param {string} soId
 * @returns {Promise<boolean>} true if no error occured
 */
const processCancelCoreSo = async (context, tenantId, soId) => {
    // catch for error during API call
    let no_error = true;
    try {
        const { responseHeader } = await CoreService.callAPICoreSoCancel(context, {
            tenantId,
            soId,
        });
        const processCode = responseHeader ? responseHeader.processCode : '';
        if (processCode !== '000000') {
            context.log(`CancelCoreSoAPI API Call fail: ${processCode}`);
            responseWithError(
                context,
                CommonError.SERVER_ERROR,
                'Cannot connect to api server',
                processCode,
                'API取得できませんでした。'
            );
            no_error = false;
        }
    } catch (exception) {
        context.log.error('CancelCoreSoAPI - API Call error: ', exception);
        responseWithError(
            context,
            CommonError.SERVER_ERROR,
            'Cannot connect to api server',
            ERROR_MAP.SO_CANCEL_API_FAIL,
            null
        );
        no_error = false;
    }
    return no_error;
};
