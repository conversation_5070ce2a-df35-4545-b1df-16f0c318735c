const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
let expect = chai.expect;

describe('SearchKaisenListAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            limit: 50,
            offset: 0,
            target_line: '登録済回線',
        },
    };

    describe('SearchKaisenListAPI with normal request', function () {
        it('登録済回線 LTE 半黒 標準SIM', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 9,
                search_type: '1',
                contract_type: 'LTE',
                sim_type: '標準SIM',
                sim_flag: '半黒',
                target_line: '登録済回線',
                limit: 50,
                offset: 0,
                sort_by: 'lineId',
                sort_order: 'asc',
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
        });

        it('登録済回線 フルMVNO、フルLTE黒、サスペンド中', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 2,
                search_type: '1',
                contract_type: 'フルLTE',
                sim_type: 'フルマルチカットSIM(N)',
                sim_flag: '黒',
                usage_status: '2',
                target_line: '登録済回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
        });

        it('登録済回線 BON+UNM', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 1,
                tenant_id: ['BON000', 'UNM000'],
                search_type: '1',
                contract_type: '3G(SMS)',
                sim_type: '標準SIM',
                sim_flag: '黒',
                target_line: '登録済回線',
                limit: 50,
                offset: 0,
                sort_by: 'planName',
                sort_order: 'asc',
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
            expect(context.res.body.results.some((r) => r.tenantId == 'BON000')).equal(true);
            expect(context.res.body.results.some((r) => r.tenantId == 'UNM000')).equal(true);
        });

        it('登録済回線 テナントオペレたー TST', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TST000');
            request.body = {
                full_mvno: 9,
                // tenant_id: ['TST000'],
                search_type: '1',
                contract_type: 'LTE(音声)',
                sim_type: 'microSIM',
                target_line: '登録済回線',
                limit: 10,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
            expect(context.res.body.results.every((r) => r.tenantId == 'TST000')).equal(true);
        });

        it('仮登録回線 5G(NSA) nanoSIM', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 9,
                search_type: '1',
                contract_type: '5G(NSA)',
                sim_type: 'nanoSIM',
                sim_flag: '黒',
                target_line: '仮登録回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
        });

        it('仮登録回線 フルMVNO 利用中断中', async () => {
            const request = _.cloneDeep(__request);
            request.body = {
                full_mvno: 2,
                search_type: '1',
                usage_status: '1',
                target_line: '仮登録回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
        });

        it('仮登録回線 テナントオペレたー TST', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TST000');
            request.body = {
                full_mvno: 9,
                // tenant_id: ['TST000'],
                search_type: '1',
                contract_type: 'LTE(音声)',
                sim_type: 'microSIM',
                target_line: '仮登録回線',
                limit: 10,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
            expect(context.res.body.results.every((r) => r.tenantId == 'TST000')).equal(true);
        });

        it('登録済/仮登録回線、5G(NSA)(音声)、マルチカットSIM、黒、TST search by super user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.SUPER_USER, 'ZZZ000');
            request.body = {
                full_mvno: 9,
                tenant_id: ['TST000'],
                search_type: '1',
                contract_type: '5G(NSA)(音声)',
                sim_type: 'マルチカットSIM',
                target_line: '登録済/仮登録回線',
                sim_flag: '黒',
                limit: 10,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
        });

        it('登録済/仮登録回線、LTE(音声)、nanoSIM、黒 search by TST operator user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TST000');
            request.body = {
                full_mvno: 9,
                search_type: '1',
                contract_type: 'LTE(音声)',
                sim_type: 'nanoSIM',
                target_line: '登録済/仮登録回線',
                sim_flag: '黒',
                limit: 10,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.length).greaterThan(0);
            expect(context.res.body.results.every((r) => r.tenantId == 'TST000')).equal(true);
        });

        it('黒化期限超過回線', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TST000');
            request.body = {
                full_mvno: 9,
                search_type: '1',
                target_line: '黒化期限超過回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.every((r) => r.simFlag == '半黒')).equal(true);
            expect(context.res.body.count).greaterThanOrEqual(0);
        });

        it('黒化期限超過回線, 黒', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.SUPER_USER, 'ZZZ000');
            request.body = {
                full_mvno: 9,
                search_type: '1',
                target_line: '黒化期限超過回線',
                sim_flag: '黒',
                limit: 20,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.count).equal(0);
        });

        it('黒化期限超過回線, 半黒, 当月', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.SUPER_USER, 'ZZZ000');
            request.body = {
                full_mvno: 2,
                search_type: '1',
                target_line: '黒化期限超過回線',
                sim_flag: '半黒',
                start_date_from: 1696086000,
                start_date_to: 1698678000,
                limit: 20,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.count).equal(0);
        });

        it('黒化期限超過回線, 半黒, 前月', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.SUPER_USER, 'ZZZ000');
            request.body = {
                full_mvno: 9,
                search_type: '1',
                target_line: '黒化期限超過回線',
                sim_flag: '半黒',
                start_date_from: 1693494000,
                start_date_to: 1695999600,
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.count).greaterThanOrEqual(0);
        });

        it('BBユニバ対象外', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TST000');
            request.body = {
                full_mvno: 9,
                search_type: '1',
                bb_uniba_flag: '対象外',
                target_line: '登録済/仮登録回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.every((r) => r.userBbuniExFlag == true)).equal(true);
            expect(context.res.body.count).greaterThanOrEqual(0);
        });

        it('BBユニバ対象', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_OPERATOR_USER, 'TST000');
            request.body = {
                full_mvno: 9,
                search_type: '1',
                bb_uniba_flag: '対象',
                target_line: '登録済/仮登録回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.results).to.be.a('Array');
            expect(context.res.body.results.every((r) => r.userBbuniExFlag == null)).equal(true);
            expect(context.res.body.count).greaterThanOrEqual(0);
        });
    });

    describe('SearchKaisenListAPI with bad request', function () {
        it('rejects request by tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TEMPO_USER, 'TSA000');

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(402);
        });

        it('returns error if tenant_id is not session tenant', async () => {
            const request = _.cloneDeep(__request);
            request.headers = getDefaultHeader(MAWP_ROLE_ID.TENANT_DAIHYO_USER, 'CON000');
            request.body = {
                full_mvno: 1,
                tenant_id: ['UNM000'],
                search_type: '1',
                target_line: '仮登録回線',
                limit: 50,
                offset: 0,
            };

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994057');
        });

        it('returns error if target line is empty or not 登録済回線 or 仮登録回線', async () => {
            const request = _.cloneDeep(__request);
            request.body.target_line = '廃止回線';

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994081');

            delete request.body.target_line;
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(150);
            expect(context.res.body.nttc_mvno_error_code).equal('994081');
        });

        it('returns error if start_date_from is not number', async () => {
            const request = _.cloneDeep(__request);
            request.body.start_date_from = '2022-12-31';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });

        it('returns error if start_date_to is not number', async () => {
            const request = _.cloneDeep(__request);
            request.body.start_date_to = '2022-12-31';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).not.equal(0);
        });

        it('returns error if sim flag is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.sim_flag = 'aa';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('994055');
        });

        it('returns error if contract type is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.contract_type = '5G';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('993022');
        });

        it('returns error if sim type is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.sim_type = 'normal SIM';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('997012');
        });

        it('returns error if sort key invalid is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.sort_by = 'lineNumber';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('994080');
        });

        it('returns error if bb_uniba_flag is invalid', async () => {
            const request = _.cloneDeep(__request);
            request.body.bb_uniba_flag = 'test';
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_code).equal('991022');
        });
    });
});
