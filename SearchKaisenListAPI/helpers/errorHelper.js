const ERROR_MAP = {
    SIM_FLAG_INVALID: '994055',
    TENANT_INVALID: '994057',
    CONTRACT_TYPE_INVALID: '993022',
    SIM_TYPE_INVALID: '997012',
    SORT_BY_INVALID: '994080',
    TARGET_LINE_INVALID: '994081',
    SEARCH_FAILED: '994082',
    OTHER: '991022',
};

const ERROR_TEXT = {
    [ERROR_MAP.SIM_FLAG_INVALID]: 'sim_flag param is not valid!',
    [ERROR_MAP.TENANT_INVALID]: 'tenant_id param is not valid!',
    [ERROR_MAP.CONTRACT_TYPE_INVALID]: 'contract_type param is not valid!',
    [ERROR_MAP.SIM_TYPE_INVALID]: 'sim_type param is not valid!',
    [ERROR_MAP.SORT_BY_INVALID]: 'sort_by param is not valid!',
    [ERROR_MAP.TARGET_LINE_INVALID]: 'target_line param is not valid!',
    [ERROR_MAP.SEARCH_FAILED]: 'Cannot connect to api server',
};

module.exports = {
    ERROR_MAP,
    ERROR_TEXT,
};
