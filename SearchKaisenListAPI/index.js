const { CommonError } = require('../__shared__/constants/CommonError');
const { getSessionData } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const mongoose = require('../__shared__/database/mongoose');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { validateAndParseSearchKaisenListV2 } = require('./helpers/validateParse');
const pgLines = require('../__shared__/pgModels/lines');

module.exports = async function (context, req) {
    try {
        context.log('SearchKaisenListAPI START');
        // eslint-disable-next-line no-unused-vars
        const { roleId, tenantId, user } = getSessionData(context, req);

        if (!isAllowed(roleId)) {
            responseWithError(context, CommonError.PERMISSION_ERROR, 'permission error', '991002', null);
            return;
        }

        const { error, errorMessage, param } = validateAndParseSearchKaisenListV2(context, req.body || {}, tenantId);
        if (error) {
            context.log('SearchKaisenListAPI validation error:', errorMessage);
            responseWithError(context, CommonError.BAD_REQUEST, errorMessage, error, null);
            return;
        }

        await mongoose.init(context);
        const { count, results } = await pgLines.getKaisenList(context, param);

        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                results,
                count,
                limit: param.limit,
                offset: param.offset,
            },
        };
    } catch (exception) {
        context.log.error('SearchKaisenListAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};

/**
 * @param {number} roleId
 */
const isAllowed = (roleId) => {
    switch (roleId) {
        case MAWP_ROLE_ID.SUPER_USER:
        case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
        case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
        case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
        case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            return true;
        default:
            return false;
    }
};
