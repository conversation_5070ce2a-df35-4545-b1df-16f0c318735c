const _ = require('lodash');
const sinon = require('sinon');
const httpFunction = require('../index');
const helpers = require('../../__shared__/helpers/baseHelper');
const context = require('../../__shared__/__tests__/defaultContext');
const { expect } = require('chai');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const plans = require('../../__shared__/pgModels/plans');

describe('GetDefaultHankuroFlagAPI', () => {
    const sandbox = sinon.createSandbox();

    const __request = {
        headers: getDefaultHeader(),
        body: {
            plan_id: 'test',
        },
    };

    afterEach(() => {
        sandbox.restore();
    });

    describe('GetDefaultHankuroFlagAPI with normal request', () => {
        it('should not return an error when the execution was successful (半黒)', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(plans, 'retrieveDefaultSIMFlag').resolves(true);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.hankuro).equal('半黒');
        });

        it('should not return an error when the execution was successful (黒)', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(plans, 'retrieveDefaultSIMFlag').resolves(false);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.hankuro).equal('黒');
        });

        it('should not return an error when the execution was successful (plan was not found)', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(plans, 'retrieveDefaultSIMFlag').resolves(null);

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.hankuro).equal('');
        });
    });

    describe('GetDefaultHankuroFlagAPI with bad request', () => {
        it('should return an error when tenantId does not exist in session', async () => {
            const request = _.cloneDeep(__request);

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('サーバにて処理できません。');
            expect(context.res.body.nttc_mvno_error_code).equal('990002');
        });

        it('should return an error when planId was not provided', async () => {
            const request = _.cloneDeep(__request);
            delete request.body.plan_id;

            sandbox.stub(helpers, 'getSessionData').returns({
                roleId: 0,
                tenantId: 'TSA000',
                user: {
                    internalId: 'test',
                },
            });

            await httpFunction(context, request);

            expect(context.res.status).equal(200);
            expect(context.res.body.nttc_mvno_error_msg).equal('正しくないパラメータが含まれています。');
            expect(context.res.body.nttc_mvno_error_code).equal('991024');
        });
    });
});
