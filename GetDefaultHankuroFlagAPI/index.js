const baseHelper = require('../__shared__/helpers/baseHelper');
const { CommonError } = require('../__shared__/constants/commonError');
const { SIM_ORDER_TYPES } = require('../__shared__/constants/simConstants');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const plans = require('../__shared__/pgModels/plans');

module.exports = async function (context, req) {
    try {
        context.log('GetDefaultHankuroFlagAPI START');
        const { roleId, tenantId, user } = baseHelper.getSessionData(context, req);

        if (baseHelper.isNone(roleId) || baseHelper.isNone(tenantId) || baseHelper.isNone(user)) {
            context.log.error('GetDefaultHankuroFlagAPI session error');
            responseWithError(context, CommonError.INVALID_USER, 'Session is empty.', '990002', null);
            return;
        }

        const planID = req.body['plan_id'];

        if (baseHelper.isNone(planID)) {
            context.log('GetDefaultHankuroFlagAPI invalid parameter');
            responseWithError(context, CommonError.INVALID_USER, 'Invalid parameter', '991024', null);
            return;
        } else {
            const flag = await plans.retrieveDefaultSIMFlag(context, planID);
            let flagStr;

            switch (flag) {
                case true:
                    flagStr = SIM_ORDER_TYPES.HANKURO;
                    break;
                case false:
                    flagStr = SIM_ORDER_TYPES.KURO;
                    break;
                default:
                    flagStr = '';
                    break;
            }

            context.res = {
                status: 200,
                body: {
                    error: CommonError.NO_ERROR,
                    hankuro: flagStr,
                },
            };
            return;
        }
    } catch (exception) {
        context.log.error('GetDefaultHankuroFlagAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
