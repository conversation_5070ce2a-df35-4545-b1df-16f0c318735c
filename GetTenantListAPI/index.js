const { CommonError } = require('../__shared__/constants/CommonError');
const { COCN_TENANT_ID } = require('../__shared__/constants/specialDefinitions');
const { MAWP_ROLE_ID } = require('../__shared__/constants/userRole');
const { getSessionData, isNone } = require('../__shared__/helpers/baseHelper');
const { responseWithError } = require('../__shared__/helpers/responseHelper');
const ServicesRestrictSettingModel = require('../__shared__/models/adminService/servicesRestrictSetting.model');
const { retrieveOneMAWPTenant, retrieveMAWPTenantList } = require('../__shared__/pgModels/tenants');
const { getParamBooleanValue } = require('../__shared__/utils/stringUtils');
const mongoose = require('../__shared__/database/mongoose');

module.exports = async function (context, req) {
    try {
        context.log('GetTenantListAPI START');
        const { roleId, tenantId } = getSessionData(context, req);
        const flagGetAllKey = getParamBooleanValue(!isNone(req.body) ? req.body.flag : false, false);

        // MVNOシステムの場合は tenantId, user, roleId が設定されていなければエラーにする
        // NOTE seems we are not using `usero`
        if (isNone(roleId) || isNone(tenantId)) {
            context.log.error('GetTenantListAPI sessionVar empty');
            responseWithError(context, CommonError.INVALID_USER, 'tenant id is not set', '990002');
            return;
        }
        let mawpTenantList = [];
        switch (roleId) {
            case MAWP_ROLE_ID.TEMPO_USER:
            case MAWP_ROLE_ID.TENANT_DAIHYO_USER:
            case MAWP_ROLE_ID.TENANT_OPERATOR_USER:
            case MAWP_ROLE_ID.TENANT_ETSURAN_USER:
                mawpTenantList = await retrieveOneMAWPTenant(context, tenantId);
                mawpTenantList = mawpTenantList ? [mawpTenantList] : [];
                break;
            case MAWP_ROLE_ID.SUPER_USER:
            case MAWP_ROLE_ID.NTTCOM_OPERATOR_USER:
                mawpTenantList = await retrieveMAWPTenantList(context, false, flagGetAllKey);
                break;
        }
        await mongoose.init(context);
        const setting = await ServicesRestrictSettingModel.getServicesRestrictSetting();
        /** @type Array<string> */
        const fiveGEnabledTenants = setting ? setting.fiveGEnabledTenants || [] : [];
        /** @type Array<string> */
        const eSIMEnabledTenants = setting ? setting.eSIMEnabledTenants || [] : [];
        /** @type Array<string> */
        const bbUnibaEnabledTenants = setting ? setting.bbUnibaEnabledTenants || [] : [];
        context.res = {
            status: 200,
            body: {
                error: CommonError.NO_ERROR,
                result: mawpTenantList.map((tenant) => ({
                    tenant_id: tenant.tenantId,
                    tenant_name: tenant.tenantName,
                    shanai_flag: tenant.shanaiFlag,
                    tpc: tenant.tpc,
                    customize_max_connection: tenant.customizeMaxConnection,
                    full_flag: tenant.tenantId === COCN_TENANT_ID ? true : tenant.fullFlag,
                    five_g_enabled: fiveGEnabledTenants.includes(tenant.tenantId),
                    e_sim_enabled: eSIMEnabledTenants.includes(tenant.tenantId),
                    bb_uniba_enabled: bbUnibaEnabledTenants.includes(tenant.tenantId),
                })),
            },
        };
    } catch (exception) {
        context.log.error('GetTenantListAPI - exception: ', exception);
        responseWithError(context, CommonError.SERVER_ERROR, exception, '990002', null);
        return;
    }
};
