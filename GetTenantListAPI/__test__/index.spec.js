const _ = require('lodash');
let chai = require('chai');
const httpFunction = require('../index');
const context = require('../../__shared__/__tests__/defaultContext');
const { getDefaultHeader } = require('../../__shared__/__tests__/defaultHeader');
const { HEADER_KEY } = require('../../__shared__/constants/headerKeys');
const { MAWP_ROLE_ID } = require('../../__shared__/constants/userRole');
let expect = chai.expect;

describe('GetTenantListAPI', function () {
    const __request = {
        headers: getDefaultHeader(),
        body: {
            flag: 'false',
        },
    };

    describe('GetTenantListAPI with normal request', function () {
        it('Get tenants using tempo user', async () => {
            const request = _.cloneDeep(__request);
            request.headers[HEADER_KEY.TENANT_ID] = 'TSA000';
            request.headers[HEADER_KEY.ROLE_ID] = MAWP_ROLE_ID.TEMPO_USER;
            delete request.body.flag;

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            // return current tenant id
            expect(context.res.body.result.length).equal(1);
        });

        it('Get tenants using super user', async () => {
            const request = _.cloneDeep(__request);
            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(0);
            expect(context.res.body.result).to.be.a('Array');
            // return more than 1 tenants
            expect(context.res.body.result.length).above(1);
        });
    });

    describe.skip('GetTenantListAPI with bad request', function () {
        it('Role id is not set', async () => {
            const request = _.cloneDeep(__request);
            delete request.headers[HEADER_KEY.ROLE_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code, '990002');
        });

        it('tenant id is not set', async () => {
            const request = _.cloneDeep(__request);
            delete request.headers[HEADER_KEY.TENANT_ID];

            await httpFunction(context, request);
            //status code should be 200
            expect(context.res.status).equal(200);
            expect(context.res.body.error).equal(101);
            expect(context.res.body.nttc_mvno_error_code, '990002');
        });
    });
});
